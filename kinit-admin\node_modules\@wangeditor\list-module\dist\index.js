!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@wangeditor/core"),require("slate"),require("snabbdom"),require("dom7")):"function"==typeof define&&define.amd?define(["@wangeditor/core","slate","snabbdom","dom7"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).WangEditorListModule=e(t.core,t.slate,t.snabbdom,t.$)}(this,(function(t,e,r,n){"use strict";function o(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=o(n);t.i18nAddResources("en",{listModule:{unOrderedList:"Unordered list",orderedList:"Ordered list"}}),t.i18nAddResources("zh-CN",{listModule:{unOrderedList:"无序列表",orderedList:"有序列表"}});var u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function a(t){var e={exports:{}};return t(e,e.exports),e.exports}var c,f,l=function(t){return t&&t.Math==Math&&t},s=l("object"==typeof globalThis&&globalThis)||l("object"==typeof window&&window)||l("object"==typeof self&&self)||l("object"==typeof u&&u)||function(){return this}()||Function("return this")(),d=Function.prototype,v=d.bind,p=d.call,y=v&&v.bind(p),h=v?function(t){return t&&y(p,t)}:function(t){return t&&function(){return p.apply(t,arguments)}},g=function(t){try{return!!t()}catch(t){return!0}},b=h({}.toString),m=h("".slice),w=function(t){return m(b(t),8,-1)},x=s.Object,S=h("".split),O=g((function(){return!x("z").propertyIsEnumerable(0)}))?function(t){return"String"==w(t)?S(t,""):x(t)}:x,E=s.TypeError,T=function(t){if(null==t)throw E("Can't call method on "+t);return t},j=function(t){return O(T(t))},A=Object.defineProperty,I=function(t,e){try{A(s,t,{value:e,configurable:!0,writable:!0})}catch(r){s[t]=e}return e},P="__core-js_shared__",R=s[P]||I(P,{}),L=a((function(t){(t.exports=function(t,e){return R[t]||(R[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),k=s.Object,M=function(t){return k(T(t))},N=h({}.hasOwnProperty),_=Object.hasOwn||function(t,e){return N(M(t),e)},z=0,D=Math.random(),C=h(1..toString),F=function(t){return"Symbol("+(void 0===t?"":t)+")_"+C(++z+D,36)},H=function(t){return"function"==typeof t},B=function(t){return H(t)?t:void 0},G=function(t,e){return arguments.length<2?B(s[t]):s[t]&&s[t][e]},$=G("navigator","userAgent")||"",V=s.process,W=s.Deno,U=V&&V.versions||W&&W.version,q=U&&U.v8;q&&(f=(c=q.split("."))[0]>0&&c[0]<4?1:+(c[0]+c[1])),!f&&$&&(!(c=$.match(/Edge\/(\d+)/))||c[1]>=74)&&(c=$.match(/Chrome\/(\d+)/))&&(f=+c[1]);var K,Y=f,X=!!Object.getOwnPropertySymbols&&!g((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),J=X&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Q=L("wks"),Z=s.Symbol,tt=Z&&Z.for,et=J?Z:Z&&Z.withoutSetter||F,rt=function(t){if(!_(Q,t)||!X&&"string"!=typeof Q[t]){var e="Symbol."+t;X&&_(Z,t)?Q[t]=Z[t]:Q[t]=J&&tt?tt(e):et(e)}return Q[t]},nt=function(t){return"object"==typeof t?null!==t:H(t)},ot=s.String,it=s.TypeError,ut=function(t){if(nt(t))return t;throw it(ot(t)+" is not an object")},at=!g((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),ct=s.document,ft=nt(ct)&&nt(ct.createElement),lt=function(t){return ft?ct.createElement(t):{}},st=!at&&!g((function(){return 7!=Object.defineProperty(lt("div"),"a",{get:function(){return 7}}).a})),dt=Function.prototype.call,vt=dt.bind?dt.bind(dt):function(){return dt.apply(dt,arguments)},pt=h({}.isPrototypeOf),yt=s.Object,ht=J?function(t){return"symbol"==typeof t}:function(t){var e=G("Symbol");return H(e)&&pt(e.prototype,yt(t))},gt=s.String,bt=function(t){try{return gt(t)}catch(t){return"Object"}},mt=s.TypeError,wt=function(t){if(H(t))return t;throw mt(bt(t)+" is not a function")},xt=function(t,e){var r=t[e];return null==r?void 0:wt(r)},St=s.TypeError,Ot=s.TypeError,Et=rt("toPrimitive"),Tt=function(t,e){if(!nt(t)||ht(t))return t;var r,n=xt(t,Et);if(n){if(void 0===e&&(e="default"),r=vt(n,t,e),!nt(r)||ht(r))return r;throw Ot("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&H(r=t.toString)&&!nt(n=vt(r,t)))return n;if(H(r=t.valueOf)&&!nt(n=vt(r,t)))return n;if("string"!==e&&H(r=t.toString)&&!nt(n=vt(r,t)))return n;throw St("Can't convert object to primitive value")}(t,e)},jt=function(t){var e=Tt(t,"string");return ht(e)?e:e+""},At=s.TypeError,It=Object.defineProperty,Pt={f:at?It:function(t,e,r){if(ut(t),e=jt(e),ut(r),st)try{return It(t,e,r)}catch(t){}if("get"in r||"set"in r)throw At("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},Rt=Math.ceil,Lt=Math.floor,kt=function(t){var e=+t;return e!=e||0===e?0:(e>0?Lt:Rt)(e)},Mt=Math.max,Nt=Math.min,_t=function(t,e){var r=kt(t);return r<0?Mt(r+e,0):Nt(r,e)},zt=Math.min,Dt=function(t){return t>0?zt(kt(t),9007199254740991):0},Ct=function(t){return Dt(t.length)},Ft=function(t){return function(e,r,n){var o,i=j(e),u=Ct(i),a=_t(n,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},Ht={includes:Ft(!0),indexOf:Ft(!1)},Bt={},Gt=Ht.indexOf,$t=h([].push),Vt=function(t,e){var r,n=j(t),o=0,i=[];for(r in n)!_(Bt,r)&&_(n,r)&&$t(i,r);for(;e.length>o;)_(n,r=e[o++])&&(~Gt(i,r)||$t(i,r));return i},Wt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ut=Object.keys||function(t){return Vt(t,Wt)},qt=at?Object.defineProperties:function(t,e){ut(t);for(var r,n=j(e),o=Ut(e),i=o.length,u=0;i>u;)Pt.f(t,r=o[u++],n[r]);return t},Kt=G("document","documentElement"),Yt=L("keys"),Xt=function(t){return Yt[t]||(Yt[t]=F(t))},Jt=Xt("IE_PROTO"),Qt=function(){},Zt=function(t){return"<script>"+t+"</"+"script>"},te=function(t){t.write(Zt("")),t.close();var e=t.parentWindow.Object;return t=null,e},ee=function(){try{K=new ActiveXObject("htmlfile")}catch(t){}var t,e;ee="undefined"!=typeof document?document.domain&&K?te(K):((e=lt("iframe")).style.display="none",Kt.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Zt("document.F=Object")),t.close(),t.F):te(K);for(var r=Wt.length;r--;)delete ee.prototype[Wt[r]];return ee()};Bt[Jt]=!0;var re=Object.create||function(t,e){var r;return null!==t?(Qt.prototype=ut(t),r=new Qt,Qt.prototype=null,r[Jt]=t):r=ee(),void 0===e?r:qt(r,e)},ne=rt("unscopables"),oe=Array.prototype;null==oe[ne]&&Pt.f(oe,ne,{configurable:!0,value:re(null)});var ie=function(t){oe[ne][t]=!0},ue={},ae=h(Function.toString);H(R.inspectSource)||(R.inspectSource=function(t){return ae(t)});var ce,fe,le,se=R.inspectSource,de=s.WeakMap,ve=H(de)&&/native code/.test(se(de)),pe=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},ye=at?function(t,e,r){return Pt.f(t,e,pe(1,r))}:function(t,e,r){return t[e]=r,t},he="Object already initialized",ge=s.TypeError,be=s.WeakMap;if(ve||R.state){var me=R.state||(R.state=new be),we=h(me.get),xe=h(me.has),Se=h(me.set);ce=function(t,e){if(xe(me,t))throw new ge(he);return e.facade=t,Se(me,t,e),e},fe=function(t){return we(me,t)||{}},le=function(t){return xe(me,t)}}else{var Oe=Xt("state");Bt[Oe]=!0,ce=function(t,e){if(_(t,Oe))throw new ge(he);return e.facade=t,ye(t,Oe,e),e},fe=function(t){return _(t,Oe)?t[Oe]:{}},le=function(t){return _(t,Oe)}}var Ee,Te,je,Ae={set:ce,get:fe,has:le,enforce:function(t){return le(t)?fe(t):ce(t,{})},getterFor:function(t){return function(e){var r;if(!nt(e)||(r=fe(e)).type!==t)throw ge("Incompatible receiver, "+t+" required");return r}}},Ie={}.propertyIsEnumerable,Pe=Object.getOwnPropertyDescriptor,Re={f:Pe&&!Ie.call({1:2},1)?function(t){var e=Pe(this,t);return!!e&&e.enumerable}:Ie},Le=Object.getOwnPropertyDescriptor,ke={f:at?Le:function(t,e){if(t=j(t),e=jt(e),st)try{return Le(t,e)}catch(t){}if(_(t,e))return pe(!vt(Re.f,t,e),t[e])}},Me=Function.prototype,Ne=at&&Object.getOwnPropertyDescriptor,_e=_(Me,"name"),ze={EXISTS:_e,PROPER:_e&&"something"===function(){}.name,CONFIGURABLE:_e&&(!at||at&&Ne(Me,"name").configurable)},De=a((function(t){var e=ze.CONFIGURABLE,r=Ae.get,n=Ae.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var a,c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,d=u&&void 0!==u.name?u.name:r;H(i)&&("Symbol("===String(d).slice(0,7)&&(d="["+String(d).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!_(i,"name")||e&&i.name!==d)&&ye(i,"name",d),(a=n(i)).source||(a.source=o.join("string"==typeof d?d:""))),t!==s?(c?!l&&t[r]&&(f=!0):delete t[r],f?t[r]=i:ye(t,r,i)):f?t[r]=i:I(r,i)})(Function.prototype,"toString",(function(){return H(this)&&r(this).source||se(this)}))})),Ce=Wt.concat("length","prototype"),Fe={f:Object.getOwnPropertyNames||function(t){return Vt(t,Ce)}},He={f:Object.getOwnPropertySymbols},Be=h([].concat),Ge=G("Reflect","ownKeys")||function(t){var e=Fe.f(ut(t)),r=He.f;return r?Be(e,r(t)):e},$e=function(t,e){for(var r=Ge(e),n=Pt.f,o=ke.f,i=0;i<r.length;i++){var u=r[i];_(t,u)||n(t,u,o(e,u))}},Ve=/#|\.prototype\./,We=function(t,e){var r=qe[Ue(t)];return r==Ye||r!=Ke&&(H(e)?g(e):!!e)},Ue=We.normalize=function(t){return String(t).replace(Ve,".").toLowerCase()},qe=We.data={},Ke=We.NATIVE="N",Ye=We.POLYFILL="P",Xe=We,Je=ke.f,Qe=function(t,e){var r,n,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?s:f?s[a]||I(a,{}):(s[a]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(u=Je(r,n))&&u.value:r[n],!Xe(c?n:a+(f?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;$e(i,o)}(t.sham||o&&o.sham)&&ye(i,"sham",!0),De(r,n,i,t)}},Ze=!g((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),tr=Xt("IE_PROTO"),er=s.Object,rr=er.prototype,nr=Ze?er.getPrototypeOf:function(t){var e=M(t);if(_(e,tr))return e[tr];var r=e.constructor;return H(r)&&e instanceof r?r.prototype:e instanceof er?rr:null},or=rt("iterator"),ir=!1;[].keys&&("next"in(je=[].keys())?(Te=nr(nr(je)))!==Object.prototype&&(Ee=Te):ir=!0);var ur=null==Ee||g((function(){var t={};return Ee[or].call(t)!==t}));ur&&(Ee={}),H(Ee[or])||De(Ee,or,(function(){return this}));var ar={IteratorPrototype:Ee,BUGGY_SAFARI_ITERATORS:ir},cr=Pt.f,fr=rt("toStringTag"),lr=function(t,e,r){t&&!_(t=r?t:t.prototype,fr)&&cr(t,fr,{configurable:!0,value:e})},sr=ar.IteratorPrototype,dr=function(){return this},vr=s.String,pr=s.TypeError,yr=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=h(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return ut(r),function(t){if("object"==typeof t||H(t))return t;throw pr("Can't set "+vr(t)+" as a prototype")}(n),e?t(r,n):r.__proto__=n,r}}():void 0),hr=ze.PROPER,gr=ze.CONFIGURABLE,br=ar.IteratorPrototype,mr=ar.BUGGY_SAFARI_ITERATORS,wr=rt("iterator"),xr="keys",Sr="values",Or="entries",Er=function(){return this},Tr=function(t,e,r,n,o,i,u){!function(t,e,r,n){var o=e+" Iterator";t.prototype=re(sr,{next:pe(+!n,r)}),lr(t,o,!1),ue[o]=dr}(r,e,n);var a,c,f,l=function(t){if(t===o&&y)return y;if(!mr&&t in v)return v[t];switch(t){case xr:case Sr:case Or:return function(){return new r(this,t)}}return function(){return new r(this)}},s=e+" Iterator",d=!1,v=t.prototype,p=v[wr]||v["@@iterator"]||o&&v[o],y=!mr&&p||l(o),h="Array"==e&&v.entries||p;if(h&&(a=nr(h.call(new t)))!==Object.prototype&&a.next&&(nr(a)!==br&&(yr?yr(a,br):H(a[wr])||De(a,wr,Er)),lr(a,s,!0)),hr&&o==Sr&&p&&p.name!==Sr&&(gr?ye(v,"name",Sr):(d=!0,y=function(){return vt(p,this)})),o)if(c={values:l(Sr),keys:i?y:l(xr),entries:l(Or)},u)for(f in c)(mr||d||!(f in v))&&De(v,f,c[f]);else Qe({target:e,proto:!0,forced:mr||d},c);return v[wr]!==y&&De(v,wr,y,{name:o}),ue[e]=y,c},jr="Array Iterator",Ar=Ae.set,Ir=Ae.getterFor(jr),Pr=Tr(Array,"Array",(function(t,e){Ar(this,{type:jr,target:j(t),index:0,kind:e})}),(function(){var t=Ir(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values");ue.Arguments=ue.Array,ie("keys"),ie("values"),ie("entries");var Rr={};Rr[rt("toStringTag")]="z";var Lr="[object z]"===String(Rr),kr=rt("toStringTag"),Mr=s.Object,Nr="Arguments"==w(function(){return arguments}()),_r=Lr?w:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Mr(t),kr))?r:Nr?w(e):"Object"==(n=w(e))&&H(e.callee)?"Arguments":n},zr=Lr?{}.toString:function(){return"[object "+_r(this)+"]"};Lr||De(Object.prototype,"toString",zr,{unsafe:!0});var Dr=s.String,Cr=function(t){if("Symbol"===_r(t))throw TypeError("Cannot convert a Symbol value to a string");return Dr(t)},Fr=h("".charAt),Hr=h("".charCodeAt),Br=h("".slice),Gr=function(t){return function(e,r){var n,o,i=Cr(T(e)),u=kt(r),a=i.length;return u<0||u>=a?t?"":void 0:(n=Hr(i,u))<55296||n>56319||u+1===a||(o=Hr(i,u+1))<56320||o>57343?t?Fr(i,u):n:t?Br(i,u,u+2):o-56320+(n-55296<<10)+65536}},$r={codeAt:Gr(!1),charAt:Gr(!0)},Vr=$r.charAt,Wr="String Iterator",Ur=Ae.set,qr=Ae.getterFor(Wr);Tr(String,"String",(function(t){Ur(this,{type:Wr,string:Cr(t),index:0})}),(function(){var t,e=qr(this),r=e.string,n=e.index;return n>=r.length?{value:void 0,done:!0}:(t=Vr(r,n),e.index+=t.length,{value:t,done:!1})}));var Kr=function(t,e,r){for(var n in e)De(t,n,e[n],r);return t},Yr=s.Array,Xr=Math.max,Jr=Fe.f,Qr="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Zr=function(t){try{return Jr(t)}catch(t){return function(t,e,r){for(var n,o,i,u,a=Ct(t),c=_t(e,a),f=_t(void 0===r?a:r,a),l=Yr(Xr(f-c,0)),s=0;c<f;c++,s++)n=l,o=s,i=t[c],u=void 0,(u=jt(o))in n?Pt.f(n,u,pe(0,i)):n[u]=i;return l.length=s,l}(Qr)}},tn={f:function(t){return Qr&&"Window"==w(t)?Zr(t):Jr(j(t))}},en=g((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),rn=Object.isExtensible,nn=g((function(){rn(1)}))||en?function(t){return!!nt(t)&&((!en||"ArrayBuffer"!=w(t))&&(!rn||rn(t)))}:rn,on=!g((function(){return Object.isExtensible(Object.preventExtensions({}))})),un=a((function(t){var e=Pt.f,r=!1,n=F("meta"),o=0,i=function(t){e(t,n,{value:{objectID:"O"+o++,weakData:{}}})},u=t.exports={enable:function(){u.enable=function(){},r=!0;var t=Fe.f,e=h([].splice),o={};o[n]=1,t(o).length&&(Fe.f=function(r){for(var o=t(r),i=0,u=o.length;i<u;i++)if(o[i]===n){e(o,i,1);break}return o},Qe({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:tn.f}))},fastKey:function(t,e){if(!nt(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!_(t,n)){if(!nn(t))return"F";if(!e)return"E";i(t)}return t[n].objectID},getWeakData:function(t,e){if(!_(t,n)){if(!nn(t))return!0;if(!e)return!1;i(t)}return t[n].weakData},onFreeze:function(t){return on&&r&&nn(t)&&!_(t,n)&&i(t),t}};Bt[n]=!0})),an=h(h.bind),cn=function(t,e){return wt(t),void 0===e?t:an?an(t,e):function(){return t.apply(e,arguments)}},fn=rt("iterator"),ln=Array.prototype,sn=rt("iterator"),dn=function(t){if(null!=t)return xt(t,sn)||xt(t,"@@iterator")||ue[_r(t)]},vn=s.TypeError,pn=function(t,e,r){var n,o;ut(t);try{if(!(n=xt(t,"return"))){if("throw"===e)throw r;return r}n=vt(n,t)}catch(t){o=!0,n=t}if("throw"===e)throw r;if(o)throw n;return ut(n),r},yn=s.TypeError,hn=function(t,e){this.stopped=t,this.result=e},gn=hn.prototype,bn=function(t,e,r){var n,o,i,u,a,c,f,l,s=r&&r.that,d=!(!r||!r.AS_ENTRIES),v=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),y=cn(e,s),h=function(t){return n&&pn(n,"normal",t),new hn(!0,t)},g=function(t){return d?(ut(t),p?y(t[0],t[1],h):y(t[0],t[1])):p?y(t,h):y(t)};if(v)n=t;else{if(!(o=dn(t)))throw yn(bt(t)+" is not iterable");if(void 0!==(l=o)&&(ue.Array===l||ln[fn]===l)){for(i=0,u=Ct(t);u>i;i++)if((a=g(t[i]))&&pt(gn,a))return a;return new hn(!1)}n=function(t,e){var r=arguments.length<2?dn(t):e;if(wt(r))return ut(vt(r,t));throw vn(bt(t)+" is not iterable")}(t,o)}for(c=n.next;!(f=vt(c,n)).done;){try{a=g(f.value)}catch(t){pn(n,"throw",t)}if("object"==typeof a&&a&&pt(gn,a))return a}return new hn(!1)},mn=s.TypeError,wn=function(t,e){if(pt(e,t))return t;throw mn("Incorrect invocation")},xn=rt("iterator"),Sn=!1;try{var On=0,En={next:function(){return{done:!!On++}},return:function(){Sn=!0}};En[xn]=function(){return this},Array.from(En,(function(){throw 2}))}catch(t){}var Tn=Array.isArray||function(t){return"Array"==w(t)},jn=function(){},An=[],In=G("Reflect","construct"),Pn=/^\s*(?:class|function)\b/,Rn=h(Pn.exec),Ln=!Pn.exec(jn),kn=function(t){if(!H(t))return!1;try{return In(jn,An,t),!0}catch(t){return!1}},Mn=!In||g((function(){var t;return kn(kn.call)||!kn(Object)||!kn((function(){t=!0}))||t}))?function(t){if(!H(t))return!1;switch(_r(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Ln||!!Rn(Pn,se(t))}:kn,Nn=rt("species"),_n=s.Array,zn=function(t,e){return new(function(t){var e;return Tn(t)&&(e=t.constructor,(Mn(e)&&(e===_n||Tn(e.prototype))||nt(e)&&null===(e=e[Nn]))&&(e=void 0)),void 0===e?_n:e}(t))(0===e?0:e)},Dn=h([].push),Cn=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,f,l,s){for(var d,v,p=M(c),y=O(p),h=cn(f,l),g=Ct(y),b=0,m=s||zn,w=e?m(c,g):r||u?m(c,0):void 0;g>b;b++)if((a||b in y)&&(v=h(d=y[b],b,p),t))if(e)w[b]=v;else if(v)switch(t){case 3:return!0;case 5:return d;case 6:return b;case 2:Dn(w,d)}else switch(t){case 4:return!1;case 7:Dn(w,d)}return i?-1:n||o?o:w}},Fn={forEach:Cn(0),map:Cn(1),filter:Cn(2),some:Cn(3),every:Cn(4),find:Cn(5),findIndex:Cn(6),filterReject:Cn(7)},Hn=un.getWeakData,Bn=Ae.set,Gn=Ae.getterFor,$n=Fn.find,Vn=Fn.findIndex,Wn=h([].splice),Un=0,qn=function(t){return t.frozen||(t.frozen=new Kn)},Kn=function(){this.entries=[]},Yn=function(t,e){return $n(t.entries,(function(t){return t[0]===e}))};Kn.prototype={get:function(t){var e=Yn(this,t);if(e)return e[1]},has:function(t){return!!Yn(this,t)},set:function(t,e){var r=Yn(this,t);r?r[1]=e:this.entries.push([t,e])},delete:function(t){var e=Vn(this.entries,(function(e){return e[0]===t}));return~e&&Wn(this.entries,e,1),!!~e}};var Xn,Jn={getConstructor:function(t,e,r,n){var o=t((function(t,o){wn(t,i),Bn(t,{type:e,id:Un++,frozen:void 0}),null!=o&&bn(o,t[n],{that:t,AS_ENTRIES:r})})),i=o.prototype,u=Gn(e),a=function(t,e,r){var n=u(t),o=Hn(ut(e),!0);return!0===o?qn(n).set(e,r):o[n.id]=r,t};return Kr(i,{delete:function(t){var e=u(this);if(!nt(t))return!1;var r=Hn(t);return!0===r?qn(e).delete(t):r&&_(r,e.id)&&delete r[e.id]},has:function(t){var e=u(this);if(!nt(t))return!1;var r=Hn(t);return!0===r?qn(e).has(t):r&&_(r,e.id)}}),Kr(i,r?{get:function(t){var e=u(this);if(nt(t)){var r=Hn(t);return!0===r?qn(e).get(t):r?r[e.id]:void 0}},set:function(t,e){return a(this,t,e)}}:{add:function(t){return a(this,t,!0)}}),o}},Qn=Ae.enforce,Zn=!s.ActiveXObject&&"ActiveXObject"in s,to=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},eo=function(t,e,r){var n=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),i=n?"set":"add",u=s[t],a=u&&u.prototype,c=u,f={},l=function(t){var e=h(a[t]);De(a,t,"add"==t?function(t){return e(this,0===t?0:t),this}:"delete"==t?function(t){return!(o&&!nt(t))&&e(this,0===t?0:t)}:"get"==t?function(t){return o&&!nt(t)?void 0:e(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!nt(t))&&e(this,0===t?0:t)}:function(t,r){return e(this,0===t?0:t,r),this})};if(Xe(t,!H(u)||!(o||a.forEach&&!g((function(){(new u).entries().next()})))))c=r.getConstructor(e,t,n,i),un.enable();else if(Xe(t,!0)){var d=new c,v=d[i](o?{}:-0,1)!=d,p=g((function(){d.has(1)})),y=function(t,e){if(!e&&!Sn)return!1;var r=!1;try{var n={};n[xn]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r}((function(t){new u(t)})),b=!o&&g((function(){for(var t=new u,e=5;e--;)t[i](e,e);return!t.has(-0)}));y||((c=e((function(t,e){wn(t,a);var r=function(t,e,r){var n,o;return yr&&H(n=e.constructor)&&n!==r&&nt(o=n.prototype)&&o!==r.prototype&&yr(t,o),t}(new u,t,c);return null!=e&&bn(e,r[i],{that:r,AS_ENTRIES:n}),r}))).prototype=a,a.constructor=c),(p||b)&&(l("delete"),l("has"),n&&l("get")),(b||v)&&l(i),o&&a.clear&&delete a.clear}return f[t]=c,Qe({global:!0,forced:c!=u},f),lr(c,t),o||r.setStrong(c,t,n),c}("WeakMap",to,Jn);if(ve&&Zn){Xn=Jn.getConstructor(to,"WeakMap",!0),un.enable();var ro=eo.prototype,no=h(ro.delete),oo=h(ro.has),io=h(ro.get),uo=h(ro.set);Kr(ro,{delete:function(t){if(nt(t)&&!nn(t)){var e=Qn(this);return e.frozen||(e.frozen=new Xn),no(this,t)||e.frozen.delete(t)}return no(this,t)},has:function(t){if(nt(t)&&!nn(t)){var e=Qn(this);return e.frozen||(e.frozen=new Xn),oo(this,t)||e.frozen.has(t)}return oo(this,t)},get:function(t){if(nt(t)&&!nn(t)){var e=Qn(this);return e.frozen||(e.frozen=new Xn),oo(this,t)?io(this,t):e.frozen.get(t)}return io(this,t)},set:function(t,e){if(nt(t)&&!nn(t)){var r=Qn(this);r.frozen||(r.frozen=new Xn),oo(this,t)?uo(this,t,e):r.frozen.set(t,e)}else uo(this,t,e);return this}})}var ao={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},co=lt("span").classList,fo=co&&co.constructor&&co.constructor.prototype,lo=fo===Object.prototype?void 0:fo,so=rt("iterator"),vo=rt("toStringTag"),po=Pr.values,yo=function(t,e){if(t){if(t[so]!==po)try{ye(t,so,po)}catch(e){t[so]=po}if(t[vo]||ye(t,vo,e),ao[e])for(var r in Pr)if(t[r]!==Pr[r])try{ye(t,r,Pr[r])}catch(e){t[r]=Pr[r]}}};for(var ho in ao)yo(s[ho]&&s[ho].prototype,ho);yo(lo,"DOMTokenList");var go=new WeakMap;var bo={type:"list-item",renderElem:function(n,o,i){go.set(n,i);var u=n,a=u.level,c=void 0===a?0:a,f=u.ordered,l={margin:"5px 0 5px "+20*c+"px"},s="";if(void 0!==f&&f){var d=function(r,n){var o=n,i=o.type,u=o.level,a=void 0===u?0:u,c=o.ordered,f=void 0!==c&&c;if(!f)return-1;var l=1,s=n,d=t.DomEditor.findPath(r,s);if(0===d[0])return 1;for(;d[0]>0;){var v=e.Path.previous(d),p=e.Editor.node(r,v);if(null==p)break;var y=p[0],h=y.level,g=void 0===h?0:h,b=y.type,m=y.ordered;if(b!==i)break;if(g<a)break;if(g===a){if(m!==f)break;l++}s=y,d=v}return l}(i,n);s=d+"."}else s=function(t){void 0===t&&(t=0);var e="";switch(t){case 0:e="•";break;case 1:e="◦";break;default:e="▪"}return e}(c);var v=function(t){var r,n=t.children||[],o=n.length;if(0===o)return"";for(var i=0;i<o&&!r;i++){var u=n[i];e.Text.isText(u)&&(r=u)}return null==r?"":r.color||""}(n);return r.jsx("div",{style:l},r.jsx("span",{contentEditable:!1,style:{marginRight:"0.5em",color:v},"data-w-e-reserve":!0},s),r.jsx("span",null,o))}},mo=function(t,e){return mo=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},mo(t,e)};
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */function wo(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}mo(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function xo(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function So(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u}function Oo(r){return e.Editor.nodes(r,{at:r.selection||void 0,match:function(e){return 1===t.DomEditor.findPath(r,e).length}})}var Eo=Ht.includes;Qe({target:"Array",proto:!0},{includes:function(t){return Eo(this,t,arguments.length>1?arguments[1]:void 0)}}),ie("includes");var To,jo,Ao=function(){var t=ut(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Io=s.RegExp,Po=g((function(){var t=Io("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Ro=Po||g((function(){return!Io("a","y").sticky})),Lo={BROKEN_CARET:Po||g((function(){var t=Io("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Ro,UNSUPPORTED_Y:Po},ko=s.RegExp,Mo=g((function(){var t=ko(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),No=s.RegExp,_o=g((function(){var t=No("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),zo=Ae.get,Do=L("native-string-replace",String.prototype.replace),Co=RegExp.prototype.exec,Fo=Co,Ho=h("".charAt),Bo=h("".indexOf),Go=h("".replace),$o=h("".slice),Vo=(jo=/b*/g,vt(Co,To=/a/,"a"),vt(Co,jo,"a"),0!==To.lastIndex||0!==jo.lastIndex),Wo=Lo.BROKEN_CARET,Uo=void 0!==/()??/.exec("")[1];(Vo||Uo||Wo||Mo||_o)&&(Fo=function(t){var e,r,n,o,i,u,a,c=this,f=zo(c),l=Cr(t),s=f.raw;if(s)return s.lastIndex=c.lastIndex,e=vt(Fo,s,l),c.lastIndex=s.lastIndex,e;var d=f.groups,v=Wo&&c.sticky,p=vt(Ao,c),y=c.source,h=0,g=l;if(v&&(p=Go(p,"y",""),-1===Bo(p,"g")&&(p+="g"),g=$o(l,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==Ho(l,c.lastIndex-1))&&(y="(?: "+y+")",g=" "+g,h++),r=new RegExp("^(?:"+y+")",p)),Uo&&(r=new RegExp("^"+y+"$(?!\\s)",p)),Vo&&(n=c.lastIndex),o=vt(Co,v?r:c,g),v?o?(o.input=$o(o.input,h),o[0]=$o(o[0],h),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Vo&&o&&(c.lastIndex=c.global?o.index+o[0].length:n),Uo&&o&&o.length>1&&vt(Do,o[0],r,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&d)for(o.groups=u=re(null),i=0;i<d.length;i++)u[(a=d[i])[0]]=o[a[1]];return o});var qo=Fo;Qe({target:"RegExp",proto:!0,forced:/./.exec!==qo},{exec:qo});var Ko=function(){function r(){this.type="list-item",this.tag="button"}return r.prototype.getListNode=function(e){var r=this.type;return t.DomEditor.getSelectedNodeByType(e,r)},r.prototype.getValue=function(t){return""},r.prototype.isActive=function(t){var e=this.getListNode(t);if(null==e)return!1;var r=e.ordered;return(void 0!==r&&r)===this.ordered},r.prototype.isDisabled=function(r){return null==r.selection||!!t.DomEditor.getSelectedElems(r).some((function(t){if(e.Editor.isVoid(r,t)&&e.Editor.isBlock(r,t))return!0;var n=t.type;return!!["pre","code","table"].includes(n)||void 0}))},r.prototype.exec=function(t,r){this.isActive(t)?e.Transforms.setNodes(t,{type:"paragraph",ordered:void 0,level:void 0}):e.Transforms.setNodes(t,{type:"list-item",ordered:this.ordered,indent:void 0})},r}(),Yo='<svg viewBox="0 0 1024 1024"><path d="M384 64h640v128H384V64z m0 384h640v128H384v-128z m0 384h640v128H384v-128zM0 128a128 128 0 1 1 256 0 128 128 0 0 1-256 0z m0 384a128 128 0 1 1 256 0 128 128 0 0 1-256 0z m0 384a128 128 0 1 1 256 0 128 128 0 0 1-256 0z"></path></svg>',Xo='<svg viewBox="0 0 1024 1024"><path d="M384 832h640v128H384z m0-384h640v128H384z m0-384h640v128H384zM192 0v256H128V64H64V0zM128 526.016v50.016h128v64H64v-146.016l128-60V384H64v-64h192v146.016zM256 704v320H64v-64h128v-64H64v-64h128v-64H64v-64z"></path></svg>',Jo=function(e){function r(){var r=null!==e&&e.apply(this,arguments)||this;return r.ordered=!1,r.title=t.t("listModule.unOrderedList"),r.iconSvg=Yo,r}return wo(r,e),r}(Ko),Qo=function(e){function r(){var r=null!==e&&e.apply(this,arguments)||this;return r.ordered=!0,r.title=t.t("listModule.orderedList"),r.iconSvg=Xo,r}return wo(r,e),r}(Ko),Zo={key:"bulletedList",factory:function(){return new Jo}},ti={key:"numberedList",factory:function(){return new Qo}};var ei=[];var ri,ni={type:"list-item",elemToHtml:function(r,n){var o="",i="",u=r.ordered,a=void 0!==u&&u?"ol":"ul",c=function(r){var n=go.get(r);if(null==n)return 0;var o=r,i=o.type,u=o.ordered,a=void 0!==u&&u,c=o.level,f=void 0===c?0:c,l=t.DomEditor.findPath(n,r);if(0===l[0])return f+1;var s=e.Path.previous(l),d=e.Editor.node(n,s);if(!d)return 0;var v=So(d,1)[0];if(t.DomEditor.getNodeType(v)!==i)return f+1;var p=v,y=p.ordered,h=void 0!==y&&y,g=p.level,b=void 0===g?0:g;return b<f?f-b:b>f?0:b===f?h===a?0:1:0}(r);if(c>0)for(var f=0;f<c;f++)o+="<"+a+">",ei.push(a);var l=function(r){var n=go.get(r);if(null==n)return 0;var o=r,i=o.type,u=o.ordered,a=void 0!==u&&u,c=o.level,f=void 0===c?0:c,l=t.DomEditor.findPath(n,r);if(l[0]===n.children.length-1)return f+1;var s=e.Path.next(l),d=e.Editor.node(n,s);if(!d)return 0;var v=So(d,1)[0];if(t.DomEditor.getNodeType(v)!==i)return f+1;var p=v,y=p.ordered,h=void 0!==y&&y,g=p.level,b=void 0===g?0:g;return b<f?f-b:b>f?0:b===f?h===a?0:1:0}(r);if(l>0)for(f=0;f<l;f++){i+="</"+ei.pop()+">"}return{html:"<li>"+n+"</li>",prefix:o,suffix:i}}},oi=rt("species"),ii=Fn.filter,ui=(ri="filter",Y>=51||!g((function(){var t=[];return(t.constructor={})[oi]=function(){return{foo:1}},1!==t[ri](Boolean).foo})));Qe({target:"Array",proto:!0,forced:!ui},{filter:function(t){return ii(this,t,arguments.length>1?arguments[1]:void 0)}});var ai=Function.prototype,ci=ai.apply,fi=ai.bind,li=ai.call,si="object"==typeof Reflect&&Reflect.apply||(fi?li.bind(ci):function(){return li.apply(ci,arguments)}),di=rt("species"),vi=RegExp.prototype,pi=$r.charAt,yi=function(t,e,r){return e+(r?pi(t,e).length:1)},hi=Math.floor,gi=h("".charAt),bi=h("".replace),mi=h("".slice),wi=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,xi=/\$([$&'`]|\d{1,2})/g,Si=function(t,e,r,n,o,i){var u=r+t.length,a=n.length,c=xi;return void 0!==o&&(o=M(o),c=wi),bi(i,c,(function(i,c){var f;switch(gi(c,0)){case"$":return"$";case"&":return t;case"`":return mi(e,0,r);case"'":return mi(e,u);case"<":f=o[mi(c,1,-1)];break;default:var l=+c;if(0===l)return i;if(l>a){var s=hi(l/10);return 0===s?i:s<=a?void 0===n[s-1]?gi(c,1):n[s-1]+gi(c,1):i}f=n[l-1]}return void 0===f?"":f}))},Oi=s.TypeError,Ei=function(t,e){var r=t.exec;if(H(r)){var n=vt(r,t,e);return null!==n&&ut(n),n}if("RegExp"===w(t))return vt(qo,t,e);throw Oi("RegExp#exec called on incompatible receiver")},Ti=rt("replace"),ji=Math.max,Ai=Math.min,Ii=h([].concat),Pi=h([].push),Ri=h("".indexOf),Li=h("".slice),ki="$0"==="a".replace(/./,"$0"),Mi=!!/./[Ti]&&""===/./[Ti]("a","$0");!function(t,e,r,n){var o=rt(t),i=!g((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!g((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[di]=function(){return r},r.flags="",r[o]=/./[o]),r.exec=function(){return e=!0,null},r[o](""),!e}));if(!i||!u||r){var a=h(/./[o]),c=e(o,""[t],(function(t,e,r,n,o){var u=h(t),c=e.exec;return c===qo||c===vi.exec?i&&!o?{done:!0,value:a(e,r,n)}:{done:!0,value:u(r,e,n)}:{done:!1}}));De(String.prototype,t,c[0]),De(vi,o,c[1])}n&&ye(vi[o],"sham",!0)}("replace",(function(t,e,r){var n=Mi?"$":"$0";return[function(t,r){var n=T(this),o=null==t?void 0:xt(t,Ti);return o?vt(o,t,n,r):vt(e,Cr(n),t,r)},function(t,o){var i=ut(this),u=Cr(t);if("string"==typeof o&&-1===Ri(o,n)&&-1===Ri(o,"$<")){var a=r(e,i,u,o);if(a.done)return a.value}var c=H(o);c||(o=Cr(o));var f=i.global;if(f){var l=i.unicode;i.lastIndex=0}for(var s=[];;){var d=Ei(i,u);if(null===d)break;if(Pi(s,d),!f)break;""===Cr(d[0])&&(i.lastIndex=yi(u,Dt(i.lastIndex),l))}for(var v,p="",y=0,h=0;h<s.length;h++){for(var g=Cr((d=s[h])[0]),b=ji(Ai(kt(d.index),u.length),0),m=[],w=1;w<d.length;w++)Pi(m,void 0===(v=d[w])?v:String(v));var x=d.groups;if(c){var S=Ii([g],m,b,u);void 0!==x&&Pi(S,x);var O=Cr(si(o,void 0,S))}else O=Si(g,u,b,m,x,o);b>=y&&(p+=Li(u,y,b)+O,y=b+g.length)}return p+Li(u,y)}]}),!!g((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!ki||Mi);var Ni=s.TypeError,_i=function(t,e,r,n,o,i,u,a){for(var c,f,l=o,s=0,d=!!u&&cn(u,a);s<n;){if(s in r){if(c=d?d(r[s],s,e):r[s],i>0&&Tn(c))f=Ct(c),l=_i(t,e,c,f,l,i-1)-1;else{if(l>=9007199254740991)throw Ni("Exceed the acceptable array length");t[l]=c}l++}s++}return l},zi=_i;function Di(t){return t.length?t[0].tagName.toLowerCase():""}return Qe({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=M(this),r=Ct(e),n=zn(e,0);return n.length=zi(n,e,e,r,0,void 0===t?1:kt(t)),n}}),ie("flat"),Qe({global:!0},{globalThis:s}),n.append&&(i.default.fn.append=n.append),n.attr&&(i.default.fn.attr=n.attr),n.parent&&(i.default.fn.parent=n.parent),{renderElems:[bo],editorPlugin:function(r){var n=r.deleteBackward,o=r.handleTab,i=r.normalizeNode,u=r;return u.deleteBackward=function(r){var o=u.selection;if(null!=o)if(e.Range.isExpanded(o))n(r);else{var i=t.DomEditor.getSelectedNodeByType(u,"list-item");if(null!=i)if(0!==o.focus.offset)n(r);else{var a=i.level,c=void 0===a?0:a;c>0?e.Transforms.setNodes(u,{level:c-1}):e.Transforms.setNodes(u,{type:"paragraph",ordered:void 0,level:void 0})}else n(r)}else n(r)},u.handleTab=function(){var r,n,i,a,c=u.selection;if(null!=c){if(e.Range.isCollapsed(c)){var f=t.DomEditor.getSelectedNodeByType(u,"list-item");if(null==f)return void o();if(0===c.focus.offset){var l=f.level,s=void 0===l?0:l;return void e.Transforms.setNodes(u,{level:s+1})}}if(e.Range.isExpanded(c)){var d=0,v=!1;try{for(var p=xo(Oo(u)),y=p.next();!y.done;y=p.next()){var h=So(y.value,1)[0];"list-item"===t.DomEditor.getNodeType(h)?d++:v=!0}}catch(t){r={error:t}}finally{try{y&&!y.done&&(n=p.return)&&n.call(p)}finally{if(r)throw r.error}}if(v||d<=1)return void o();try{for(var g=xo(Oo(u)),b=g.next();!b.done;b=g.next()){var m=So(b.value,2),w=(h=m[0],m[1]),x=h.level;s=void 0===x?0:x;e.Transforms.setNodes(u,{level:s+1},{at:w})}}catch(t){i={error:t}}finally{try{b&&!b.done&&(a=g.return)&&a.call(g)}finally{if(i)throw i.error}}}else o()}else o()},u.normalizeNode=function(r){var n=So(r,2),o=n[0],a=n[1],c=t.DomEditor.getNodeType(o);return"bulleted-list"!==c&&"numbered-list"!==c||e.Transforms.unwrapNodes(u,{at:a}),i([o,a])},u},menus:[Zo,ti],elemsToHtml:[ni],parseElemsHtml:[{selector:"ul:not([data-w-e-type]),ol:not([data-w-e-type])",parseElemHtml:function(t,e,r){return e.flat(1/0)}},{selector:"li:not([data-w-e-type])",parseElemHtml:function(t,r,n){var o=i.default(t);0===(r=r.filter((function(t){return!!e.Text.isText(t)||!!n.isInline(t)}))).length&&(r=[{text:o.text().replace(/\s+/gm," ")}]);var u=function(t){return"ol"===Di(t.parent())}(o),a=function(t){for(var e=0,r=t.parent(),n=Di(r);"ul"===n||"ol"===n;)n=Di(r=r.parent()),e++;return e-1}(o);return{type:"list-item",ordered:u,level:a,children:r}}}]}}));
//# sourceMappingURL=index.js.map
