/*!
  * vue-i18n v9.9.1
  * (c) 2024 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode,Text,computed,watch,getCurrentInstance,ref,shallowRef,Fragment,defineComponent,h,effectScope,inject,onMounted,onUnmounted,onBeforeMount,isRef}from"vue";const inBrowser="undefined"!=typeof window,makeSymbol=(e,t=!1)=>t?Symbol.for(e):Symbol(e),generateFormatCacheKey=(e,t,a)=>friendlyJSONstringify({l:e,k:t,s:a}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign;function escapeHtml(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>{if(!isObject(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object},toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,a,n)=>0===n?e+a:e+t+a),"")}function incrementer(e){let t=e;return()=>++t}function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const isNotObjectOrIsArray=e=>!isObject(e)||isArray(e);function deepCopy(e,t){if(isNotObjectOrIsArray(e)||isNotObjectOrIsArray(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:e,des:t}=a.pop();Object.keys(e).forEach((n=>{isNotObjectOrIsArray(e[n])||isNotObjectOrIsArray(t[n])?t[n]=e[n]:a.push({src:e[n],des:t[n]})}))}}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function createCompileError(e,t,a={}){const{domain:n,messages:r,args:s}=a,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=n,l}const pathStateMachine=[];pathStateMachine[0]={w:[0],i:[3,0],"[":[4],o:[7]},pathStateMachine[1]={w:[1],".":[2],"[":[4],o:[7]},pathStateMachine[2]={w:[2],i:[3,0],0:[3,0]},pathStateMachine[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},pathStateMachine[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},pathStateMachine[5]={"'":[4,0],o:8,l:[5,0]},pathStateMachine[6]={'"':[4,0],o:8,l:[6,0]};const literalValueRE=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function isLiteral(e){return literalValueRE.test(e)}function stripQuotes(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}function getPathCharType(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function formatSubPath(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(isLiteral(t)?stripQuotes(t):"*"+t)}function parse(e){const t=[];let a,n,r,s,l,o,i,c=-1,u=0,m=0;const g=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,g[0](),!0}for(g[0]=()=>{void 0===n?n=r:n+=r},g[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},g[2]=()=>{g[0](),m++},g[3]=()=>{if(m>0)m--,u=4,g[0]();else{if(m=0,void 0===n)return!1;if(n=formatSubPath(n),!1===n)return!1;g[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!_()){if(s=getPathCharType(a),i=pathStateMachine[u],l=i[s]||i.l||8,8===l)return;if(u=l[0],void 0!==l[1]&&(o=g[l[1]],o&&(r=a,!1===o())))return;if(7===u)return t}}const cache=new Map;function resolveWithKeyValue(e,t){return isObject(e)?e[t]:null}function resolveValue(e,t){if(!isObject(e))return null;let a=cache.get(t);if(a||(a=parse(t),a&&cache.set(t,a)),!a)return null;const n=a.length;let r=e,s=0;for(;s<n;){const e=r[a[s]];if(void 0===e)return null;if(isFunction(r))return null;r=e,s++}return r}const DEFAULT_MODIFIER=e=>e,DEFAULT_MESSAGE=e=>"",DEFAULT_MESSAGE_DATA_TYPE="text",DEFAULT_NORMALIZE=e=>0===e.length?"":join(e),DEFAULT_INTERPOLATE=toDisplayString;function pluralDefault(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function getPluralIndex(e){const t=isNumber(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(isNumber(e.named.count)||isNumber(e.named.n))?isNumber(e.named.count)?e.named.count:isNumber(e.named.n)?e.named.n:t:t}function normalizeNamed(e,t){t.count||(t.count=e),t.n||(t.n=e)}function createMessageContext(e={}){const t=e.locale,a=getPluralIndex(e),n=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?e.pluralRules[t]:pluralDefault,r=isObject(e.pluralRules)&&isString(t)&&isFunction(e.pluralRules[t])?pluralDefault:void 0,s=e.list||[],l=e.named||{};isNumber(e.pluralIndex)&&normalizeNamed(a,l);function o(t){const a=isFunction(e.messages)?e.messages(t):!!isObject(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):DEFAULT_MESSAGE)}const i=isPlainObject(e.processor)&&isFunction(e.processor.normalize)?e.processor.normalize:DEFAULT_NORMALIZE,c=isPlainObject(e.processor)&&isFunction(e.processor.interpolate)?e.processor.interpolate:DEFAULT_INTERPOLATE,u={list:e=>s[e],named:e=>l[e],plural:e=>e[n(a,e.length,r)],linked:(t,...a)=>{const[n,r]=a;let s="text",l="";1===a.length?isObject(n)?(l=n.modifier||l,s=n.type||s):isString(n)&&(l=n||l):2===a.length&&(isString(n)&&(l=n||l),isString(r)&&(s=r||s));const i=o(t)(u),c="vnode"===s&&isArray(i)&&l?i[0]:i;return l?(m=l,e.modifiers?e.modifiers[m]:DEFAULT_MODIFIER)(c,s):c;var m},message:o,type:isPlainObject(e.processor)&&isString(e.processor.type)?e.processor.type:DEFAULT_MESSAGE_DATA_TYPE,interpolate:c,normalize:i,values:assign({},s,l)};return u}const code$1=CompileErrorCodes.__EXTEND_POINT__,inc$1=incrementer(code$1),CoreErrorCodes={INVALID_ARGUMENT:code$1,INVALID_DATE_ARGUMENT:inc$1(),INVALID_ISO_DATE_ARGUMENT:inc$1(),NOT_SUPPORT_NON_STRING_MESSAGE:inc$1(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:inc$1(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:inc$1(),NOT_SUPPORT_LOCALE_TYPE:inc$1(),__EXTEND_POINT__:inc$1()};function getLocale(e,t){return null!=t.locale?resolveLocale(t.locale):resolveLocale(e.locale)}let _resolveLocale;function resolveLocale(e){if(isString(e))return e;if(isFunction(e)){if(e.resolvedOnce&&null!=_resolveLocale)return _resolveLocale;if("Function"===e.constructor.name){const t=e();if(isPromise(t))throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return _resolveLocale=t}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE)}function fallbackWithSimple(e,t,a){return[...new Set([a,...isArray(t)?t:isObject(t)?Object.keys(t):isString(t)?[t]:[a]])]}function fallbackWithLocaleChain(e,t,a){const n=isString(a)?a:DEFAULT_LOCALE,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let s=r.__localeChainCache.get(n);if(!s){s=[];let e=[a];for(;isArray(e);)e=appendBlockToChain(s,e,t);const l=isArray(t)||!isPlainObject(t)?t:t.default?t.default:null;e=isString(l)?[l]:l,isArray(e)&&appendBlockToChain(s,e,!1),r.__localeChainCache.set(n,s)}return s}function appendBlockToChain(e,t,a){let n=!0;for(let r=0;r<t.length&&isBoolean(n);r++){const s=t[r];isString(s)&&(n=appendLocaleToChain(e,t[r],a))}return n}function appendLocaleToChain(e,t,a){let n;const r=t.split("-");do{n=appendItemToChain(e,r.join("-"),a),r.splice(-1,1)}while(r.length&&!0===n);return n}function appendItemToChain(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(isArray(a)||isPlainObject(a))&&a[r]&&(n=a[r])}return n}const VERSION$1="9.9.1",NOT_REOSLVED=-1,DEFAULT_LOCALE="en-US",MISSING_RESOLVE_VALUE="",capitalize=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function getDefaultLinkedModifiers(){return{upper:(e,t)=>"text"===t&&isString(e)?e.toUpperCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&isString(e)?e.toLowerCase():"vnode"===t&&isObject(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&isString(e)?capitalize(e):"vnode"===t&&isObject(e)&&"__v_isVNode"in e?capitalize(e.children):e}}let _compiler,_resolver,_fallbacker;function registerMessageResolver(e){_resolver=e}function registerLocaleFallbacker(e){_fallbacker=e}const setAdditionalMeta=e=>{};let _fallbackContext=null;const setFallbackContext=e=>{_fallbackContext=e},getFallbackContext=()=>_fallbackContext;let _cid=0;function createCoreContext(e={}){const t=isFunction(e.onWarn)?e.onWarn:warn,a=isString(e.version)?e.version:VERSION$1,n=isString(e.locale)||isFunction(e.locale)?e.locale:DEFAULT_LOCALE,r=isFunction(n)?DEFAULT_LOCALE:n,s=isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||isString(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:r,l=isPlainObject(e.messages)?e.messages:{[r]:{}},o=isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[r]:{}},i=isPlainObject(e.numberFormats)?e.numberFormats:{[r]:{}},c=assign({},e.modifiers||{},getDefaultLinkedModifiers()),u=e.pluralRules||{},m=isFunction(e.missing)?e.missing:null,g=!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,_=!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,f=!!e.fallbackFormat,p=!!e.unresolving,b=isFunction(e.postTranslation)?e.postTranslation:null,E=isPlainObject(e.processor)?e.processor:null,d=!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,v=isFunction(e.messageCompiler)?e.messageCompiler:_compiler,T=isFunction(e.messageResolver)?e.messageResolver:_resolver||resolveWithKeyValue,N=isFunction(e.localeFallbacker)?e.localeFallbacker:_fallbacker||fallbackWithSimple,F=isObject(e.fallbackContext)?e.fallbackContext:void 0,I=e,S=isObject(I.__datetimeFormatters)?I.__datetimeFormatters:new Map,L=isObject(I.__numberFormatters)?I.__numberFormatters:new Map,h=isObject(I.__meta)?I.__meta:{};_cid++;const y={version:a,cid:_cid,locale:n,fallbackLocale:s,messages:l,modifiers:c,pluralRules:u,missing:m,missingWarn:g,fallbackWarn:_,fallbackFormat:f,unresolving:p,postTranslation:b,processor:E,warnHtmlMessage:d,escapeParameter:O,messageCompiler:v,messageResolver:T,localeFallbacker:N,fallbackContext:F,onWarn:t,__meta:h};return y.datetimeFormats=o,y.numberFormats=i,y.__datetimeFormatters=S,y.__numberFormatters=L,y}function handleMissing(e,t,a,n,r){const{missing:s,onWarn:l}=e;if(null!==s){const n=s(e,a,t,r);return isString(n)?n:t}return t}function updateFallbackLocale(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}const isMessageAST=e=>isObject(e)&&(0===e.t||0===e.type)&&("b"in e||"body"in e),NOOP_MESSAGE_FUNCTION=()=>"",isMessageFunction=e=>isFunction(e);function translate(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:r,messageCompiler:s,fallbackLocale:l,messages:o}=e,[i,c]=parseTranslateArgs(...t),u=isBoolean(c.missingWarn)?c.missingWarn:e.missingWarn,m=isBoolean(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,g=isBoolean(c.escapeParameter)?c.escapeParameter:e.escapeParameter,_=!!c.resolvedMessage,f=isString(c.default)||isBoolean(c.default)?isBoolean(c.default)?s?i:()=>i:c.default:a?s?i:()=>i:"",p=a||""!==f,b=getLocale(e,c);g&&escapeParams(c);let[E,d,O]=_?[i,b,o[b]||{}]:resolveMessageFormat(e,i,b,l,m,u),v=E,T=i;if(_||isString(v)||isMessageAST(v)||isMessageFunction(v)||p&&(v=f,T=v),!(_||(isString(v)||isMessageAST(v)||isMessageFunction(v))&&isString(d)))return r?NOT_REOSLVED:i;let N=!1;const F=isMessageFunction(v)?v:compileMessageFormat(e,i,d,v,T,(()=>{N=!0}));if(N)return v;const I=evaluateMessage(e,F,createMessageContext(getMessageContextOptions(e,d,O,c)));return n?n(I,i):I}function escapeParams(e){isArray(e.list)?e.list=e.list.map((e=>isString(e)?escapeHtml(e):e)):isObject(e.named)&&Object.keys(e.named).forEach((t=>{isString(e.named[t])&&(e.named[t]=escapeHtml(e.named[t]))}))}function resolveMessageFormat(e,t,a,n,r,s){const{messages:l,onWarn:o,messageResolver:i,localeFallbacker:c}=e,u=c(e,n,a);let m,g={},_=null;for(let f=0;f<u.length&&(m=u[f],g=l[m]||{},null===(_=i(g,t))&&(_=g[t]),!(isString(_)||isMessageAST(_)||isMessageFunction(_)));f++){const a=handleMissing(e,t,m,s,"translate");a!==t&&(_=a)}return[_,m,g]}function compileMessageFormat(e,t,a,n,r,s){const{messageCompiler:l,warnHtmlMessage:o}=e;if(isMessageFunction(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==l){const e=()=>n;return e.locale=a,e.key=t,e}const i=l(n,getCompileContext(e,a,r,n,o,s));return i.locale=a,i.key=t,i.source=n,i}function evaluateMessage(e,t,a){return t(a)}function parseTranslateArgs(...e){const[t,a,n]=e,r={};if(!(isString(t)||isNumber(t)||isMessageFunction(t)||isMessageAST(t)))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const s=isNumber(t)?String(t):(isMessageFunction(t),t);return isNumber(a)?r.plural=a:isString(a)?r.default=a:isPlainObject(a)&&!isEmptyObject(a)?r.named=a:isArray(a)&&(r.list=a),isNumber(n)?r.plural=n:isString(n)?r.default=n:isPlainObject(n)&&assign(r,n),[s,r]}function getCompileContext(e,t,a,n,r,s){return{locale:t,key:a,warnHtmlMessage:r,onError:e=>{throw s&&s(e),e},onCacheKey:e=>generateFormatCacheKey(t,a,e)}}function getMessageContextOptions(e,t,a,n){const{modifiers:r,pluralRules:s,messageResolver:l,fallbackLocale:o,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:r,pluralRules:s,messages:n=>{let r=l(a,n);if(null==r&&u){const[,,e]=resolveMessageFormat(u,n,t,o,i,c);r=l(e,n)}if(isString(r)||isMessageAST(r)){let a=!1;const s=compileMessageFormat(e,n,t,r,n,(()=>{a=!0}));return a?NOOP_MESSAGE_FUNCTION:s}return isMessageFunction(r)?r:NOOP_MESSAGE_FUNCTION}};return e.processor&&(m.processor=e.processor),n.list&&(m.list=n.list),n.named&&(m.named=n.named),isNumber(n.plural)&&(m.pluralIndex=n.plural),m}function datetime(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:r,onWarn:s,localeFallbacker:l}=e,{__datetimeFormatters:o}=e,[i,c,u,m]=parseDateTimeArgs(...t),g=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const _=!!u.part,f=getLocale(e,u),p=l(e,r,f);if(!isString(i)||""===i)return new Intl.DateTimeFormat(f,m).format(c);let b,E={},d=null;for(let T=0;T<p.length&&(b=p[T],E=a[b]||{},d=E[i],!isPlainObject(d));T++)handleMissing(e,i,b,g,"datetime format");if(!isPlainObject(d)||!isString(b))return n?NOT_REOSLVED:i;let O=`${b}__${i}`;isEmptyObject(m)||(O=`${O}__${JSON.stringify(m)}`);let v=o.get(O);return v||(v=new Intl.DateTimeFormat(b,assign({},d,m)),o.set(O,v)),_?v.formatToParts(c):v.format(c)}const DATETIME_FORMAT_OPTIONS_KEYS=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function parseDateTimeArgs(...e){const[t,a,n,r]=e,s={};let l,o={};if(isString(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();l=new Date(a);try{l.toISOString()}catch(i){throw Error(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT)}}else if(isDate(t)){if(isNaN(t.getTime()))throw Error(CoreErrorCodes.INVALID_DATE_ARGUMENT);l=t}else{if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);l=t}return isString(a)?s.key=a:isPlainObject(a)&&Object.keys(a).forEach((e=>{DATETIME_FORMAT_OPTIONS_KEYS.includes(e)?o[e]=a[e]:s[e]=a[e]})),isString(n)?s.locale=n:isPlainObject(n)&&(o=n),isPlainObject(r)&&(o=r),[s.key||"",l,s,o]}function clearDateTimeFormat(e,t,a){const n=e;for(const r in a){const e=`${t}__${r}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function number(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:r,onWarn:s,localeFallbacker:l}=e,{__numberFormatters:o}=e,[i,c,u,m]=parseNumberArgs(...t),g=isBoolean(u.missingWarn)?u.missingWarn:e.missingWarn;isBoolean(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const _=!!u.part,f=getLocale(e,u),p=l(e,r,f);if(!isString(i)||""===i)return new Intl.NumberFormat(f,m).format(c);let b,E={},d=null;for(let T=0;T<p.length&&(b=p[T],E=a[b]||{},d=E[i],!isPlainObject(d));T++)handleMissing(e,i,b,g,"number format");if(!isPlainObject(d)||!isString(b))return n?NOT_REOSLVED:i;let O=`${b}__${i}`;isEmptyObject(m)||(O=`${O}__${JSON.stringify(m)}`);let v=o.get(O);return v||(v=new Intl.NumberFormat(b,assign({},d,m)),o.set(O,v)),_?v.formatToParts(c):v.format(c)}const NUMBER_FORMAT_OPTIONS_KEYS=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function parseNumberArgs(...e){const[t,a,n,r]=e,s={};let l={};if(!isNumber(t))throw Error(CoreErrorCodes.INVALID_ARGUMENT);const o=t;return isString(a)?s.key=a:isPlainObject(a)&&Object.keys(a).forEach((e=>{NUMBER_FORMAT_OPTIONS_KEYS.includes(e)?l[e]=a[e]:s[e]=a[e]})),isString(n)?s.locale=n:isPlainObject(n)&&(l=n),isPlainObject(r)&&(l=r),[s.key||"",o,s,l]}function clearNumberFormat(e,t,a){const n=e;for(const r in a){const e=`${t}__${r}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}const VERSION="9.9.1",code=CoreErrorCodes.__EXTEND_POINT__,inc=incrementer(code),I18nErrorCodes={UNEXPECTED_RETURN_TYPE:code,INVALID_ARGUMENT:inc(),MUST_BE_CALL_SETUP_TOP:inc(),NOT_INSTALLED:inc(),NOT_AVAILABLE_IN_LEGACY_MODE:inc(),REQUIRED_VALUE:inc(),INVALID_VALUE:inc(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:inc(),NOT_INSTALLED_WITH_PROVIDE:inc(),UNEXPECTED_ERROR:inc(),NOT_COMPATIBLE_LEGACY_VUE_I18N:inc(),BRIDGE_SUPPORT_VUE_2_ONLY:inc(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:inc(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:inc(),__EXTEND_POINT__:inc()};function createI18nError(e,...t){return createCompileError(e,null,void 0)}const TranslateVNodeSymbol=makeSymbol("__translateVNode"),DatetimePartsSymbol=makeSymbol("__datetimeParts"),NumberPartsSymbol=makeSymbol("__numberParts"),SetPluralRulesSymbol=makeSymbol("__setPluralRules"),InejctWithOptionSymbol=makeSymbol("__injectWithOption"),DisposeSymbol=makeSymbol("__dispose"),__VUE_I18N_BRIDGE__="__VUE_I18N_BRIDGE__";function handleFlatJson(e){if(!isObject(e))return e;for(const t in e)if(hasOwn(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let r=e,s=!1;for(let e=0;e<n;e++){if(a[e]in r||(r[a[e]]={}),!isObject(r[a[e]])){s=!0;break}r=r[a[e]]}s||(r[a[n]]=e[t],delete e[t]),isObject(r[a[n]])&&handleFlatJson(r[a[n]])}else isObject(e[t])&&handleFlatJson(e[t]);return e}function getLocaleMessages(e,t){const{messages:a,__i18n:n,messageResolver:r,flatJson:s}=t,l=isPlainObject(a)?a:isArray(n)?{}:{[e]:{}};if(isArray(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(l[t]=l[t]||{},deepCopy(a,l[t])):deepCopy(a,l)}else isString(e)&&deepCopy(JSON.parse(e),l)})),null==r&&s)for(const o in l)hasOwn(l,o)&&handleFlatJson(l[o]);return l}function getComponentOptions(e){return e.type}function adjustI18nResources(e,t,a){let n=isObject(t.messages)?t.messages:{};"__i18nGlobal"in a&&(n=getLocaleMessages(e.locale.value,{messages:n,__i18n:a.__i18nGlobal}));const r=Object.keys(n);if(r.length&&r.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),isObject(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(isObject(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function createTextNode(e){return createVNode(Text,null,e,0)}const DEVTOOLS_META="__INTLIFY_META__",NOOP_RETURN_ARRAY=()=>[],NOOP_RETURN_FALSE=()=>!1;let composerID=0;function defineCoreMissingHandler(e){return(t,a,n,r)=>e(a,n,getCurrentInstance()||void 0,r)}const getMetaInfo=()=>{const e=getCurrentInstance();let t=null;return e&&(t=getComponentOptions(e)[DEVTOOLS_META])?{[DEVTOOLS_META]:t}:null};function createComposer(e={},t){const{__root:a,__injectWithOption:n}=e,r=void 0===a,s=e.flatJson,l=inBrowser?ref:shallowRef;let o=!isBoolean(e.inheritLocale)||e.inheritLocale;const i=l(a&&o?a.locale.value:isString(e.locale)?e.locale:DEFAULT_LOCALE),c=l(a&&o?a.fallbackLocale.value:isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:i.value),u=l(getLocaleMessages(i.value,e)),m=l(isPlainObject(e.datetimeFormats)?e.datetimeFormats:{[i.value]:{}}),g=l(isPlainObject(e.numberFormats)?e.numberFormats:{[i.value]:{}});let _=a?a.missingWarn:!isBoolean(e.missingWarn)&&!isRegExp(e.missingWarn)||e.missingWarn,f=a?a.fallbackWarn:!isBoolean(e.fallbackWarn)&&!isRegExp(e.fallbackWarn)||e.fallbackWarn,p=a?a.fallbackRoot:!isBoolean(e.fallbackRoot)||e.fallbackRoot,b=!!e.fallbackFormat,E=isFunction(e.missing)?e.missing:null,d=isFunction(e.missing)?defineCoreMissingHandler(e.missing):null,O=isFunction(e.postTranslation)?e.postTranslation:null,v=a?a.warnHtmlMessage:!isBoolean(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter;const N=a?a.modifiers:isPlainObject(e.modifiers)?e.modifiers:{};let F,I=e.pluralRules||a&&a.pluralRules;F=(()=>{r&&setFallbackContext(null);const t={version:VERSION,locale:i.value,fallbackLocale:c.value,messages:u.value,modifiers:N,pluralRules:I,missing:null===d?void 0:d,missingWarn:_,fallbackWarn:f,fallbackFormat:b,unresolving:!0,postTranslation:null===O?void 0:O,warnHtmlMessage:v,escapeParameter:T,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=m.value,t.numberFormats=g.value,t.__datetimeFormatters=isPlainObject(F)?F.__datetimeFormatters:void 0,t.__numberFormatters=isPlainObject(F)?F.__numberFormatters:void 0;const a=createCoreContext(t);return r&&setFallbackContext(a),a})(),updateFallbackLocale(F,i.value,c.value);const S=computed({get:()=>i.value,set:e=>{i.value=e,F.locale=i.value}}),L=computed({get:()=>c.value,set:e=>{c.value=e,F.fallbackLocale=c.value,updateFallbackLocale(F,i.value,e)}}),h=computed((()=>u.value)),y=computed((()=>m.value)),C=computed((()=>g.value));const k=(e,t,n,s,l,o)=>{let _;i.value,c.value,u.value,m.value,g.value;try{0,r||(F.fallbackContext=a?getFallbackContext():void 0),_=e(F)}finally{r||(F.fallbackContext=void 0)}if("translate exists"!==n&&isNumber(_)&&_===NOT_REOSLVED||"translate exists"===n&&!_){const[e,n]=t();return a&&p?s(a):l(e)}if(o(_))return _;throw Error(I18nErrorCodes.UNEXPECTED_RETURN_TYPE)};function A(...e){return k((t=>Reflect.apply(translate,null,[t,...e])),(()=>parseTranslateArgs(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>isString(e)))}const P={normalize:function(e){return e.map((e=>isString(e)||isNumber(e)||isBoolean(e)?createTextNode(String(e)):e))},interpolate:e=>e,type:"vnode"};function R(e){return u.value[e]||{}}composerID++,a&&inBrowser&&(watch(a.locale,(e=>{o&&(i.value=e,F.locale=e,updateFallbackLocale(F,i.value,c.value))})),watch(a.fallbackLocale,(e=>{o&&(c.value=e,F.fallbackLocale=e,updateFallbackLocale(F,i.value,c.value))})));const M={id:composerID,locale:S,fallbackLocale:L,get inheritLocale(){return o},set inheritLocale(e){o=e,e&&a&&(i.value=a.locale.value,c.value=a.fallbackLocale.value,updateFallbackLocale(F,i.value,c.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:h,get modifiers(){return N},get pluralRules(){return I||{}},get isGlobal(){return r},get missingWarn(){return _},set missingWarn(e){_=e,F.missingWarn=_},get fallbackWarn(){return f},set fallbackWarn(e){f=e,F.fallbackWarn=f},get fallbackRoot(){return p},set fallbackRoot(e){p=e},get fallbackFormat(){return b},set fallbackFormat(e){b=e,F.fallbackFormat=b},get warnHtmlMessage(){return v},set warnHtmlMessage(e){v=e,F.warnHtmlMessage=e},get escapeParameter(){return T},set escapeParameter(e){T=e,F.escapeParameter=e},t:A,getLocaleMessage:R,setLocaleMessage:function(e,t){if(s){const a={[e]:t};for(const e in a)hasOwn(a,e)&&handleFlatJson(a[e]);t=a[e]}u.value[e]=t,F.messages=u.value},mergeLocaleMessage:function(e,t){u.value[e]=u.value[e]||{};const a={[e]:t};if(s)for(const n in a)hasOwn(a,n)&&handleFlatJson(a[n]);deepCopy(t=a[e],u.value[e]),F.messages=u.value},getPostTranslationHandler:function(){return isFunction(O)?O:null},setPostTranslationHandler:function(e){O=e,F.postTranslation=e},getMissingHandler:function(){return E},setMissingHandler:function(e){null!==e&&(d=defineCoreMissingHandler(e)),E=e,F.missing=d},[SetPluralRulesSymbol]:function(e){I=e,F.pluralRules=I}};return M.datetimeFormats=y,M.numberFormats=C,M.rt=function(...e){const[t,a,n]=e;if(n&&!isObject(n))throw Error(I18nErrorCodes.INVALID_ARGUMENT);return A(t,a,assign({resolvedMessage:!0},n||{}))},M.te=function(e,t){return k((()=>{if(!e)return!1;const a=R(isString(t)?t:i.value),n=F.messageResolver(a,e);return isMessageAST(n)||isMessageFunction(n)||isString(n)}),(()=>[e]),"translate exists",(a=>Reflect.apply(a.te,a,[e,t])),NOOP_RETURN_FALSE,(e=>isBoolean(e)))},M.tm=function(e){const t=function(e){let t=null;const a=fallbackWithLocaleChain(F,c.value,i.value);for(let n=0;n<a.length;n++){const r=u.value[a[n]]||{},s=F.messageResolver(r,e);if(null!=s){t=s;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},M.d=function(...e){return k((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},M.n=function(...e){return k((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>MISSING_RESOLVE_VALUE),(e=>isString(e)))},M.getDateTimeFormat=function(e){return m.value[e]||{}},M.setDateTimeFormat=function(e,t){m.value[e]=t,F.datetimeFormats=m.value,clearDateTimeFormat(F,e,t)},M.mergeDateTimeFormat=function(e,t){m.value[e]=assign(m.value[e]||{},t),F.datetimeFormats=m.value,clearDateTimeFormat(F,e,t)},M.getNumberFormat=function(e){return g.value[e]||{}},M.setNumberFormat=function(e,t){g.value[e]=t,F.numberFormats=g.value,clearNumberFormat(F,e,t)},M.mergeNumberFormat=function(e,t){g.value[e]=assign(g.value[e]||{},t),F.numberFormats=g.value,clearNumberFormat(F,e,t)},M[InejctWithOptionSymbol]=n,M[TranslateVNodeSymbol]=function(...e){return k((t=>{let a;const n=t;try{n.processor=P,a=Reflect.apply(translate,null,[n,...e])}finally{n.processor=null}return a}),(()=>parseTranslateArgs(...e)),"translate",(t=>t[TranslateVNodeSymbol](...e)),(e=>[createTextNode(e)]),(e=>isArray(e)))},M[DatetimePartsSymbol]=function(...e){return k((t=>Reflect.apply(datetime,null,[t,...e])),(()=>parseDateTimeArgs(...e)),"datetime format",(t=>t[DatetimePartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},M[NumberPartsSymbol]=function(...e){return k((t=>Reflect.apply(number,null,[t,...e])),(()=>parseNumberArgs(...e)),"number format",(t=>t[NumberPartsSymbol](...e)),NOOP_RETURN_ARRAY,(e=>isString(e)||isArray(e)))},M}function convertComposerOptions(e){const t=isString(e.locale)?e.locale:DEFAULT_LOCALE,a=isString(e.fallbackLocale)||isArray(e.fallbackLocale)||isPlainObject(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=isFunction(e.missing)?e.missing:void 0,r=!isBoolean(e.silentTranslationWarn)&&!isRegExp(e.silentTranslationWarn)||!e.silentTranslationWarn,s=!isBoolean(e.silentFallbackWarn)&&!isRegExp(e.silentFallbackWarn)||!e.silentFallbackWarn,l=!isBoolean(e.fallbackRoot)||e.fallbackRoot,o=!!e.formatFallbackMessages,i=isPlainObject(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=isFunction(e.postTranslation)?e.postTranslation:void 0,m=!isString(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,g=!!e.escapeParameterHtml,_=!isBoolean(e.sync)||e.sync;let f=e.messages;if(isPlainObject(e.sharedMessages)){const t=e.sharedMessages;f=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return assign(n,t[a]),e}),f||{})}const{__i18n:p,__root:b,__injectWithOption:E}=e,d=e.datetimeFormats,O=e.numberFormats;return{locale:t,fallbackLocale:a,messages:f,flatJson:e.flatJson,datetimeFormats:d,numberFormats:O,missing:n,missingWarn:r,fallbackWarn:s,fallbackRoot:l,fallbackFormat:o,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:g,messageResolver:e.messageResolver,inheritLocale:_,__i18n:p,__root:b,__injectWithOption:E}}function createVueI18n(e={},t){{const t=createComposer(convertComposerOptions(e)),{__extender:a}=e,n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return isBoolean(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=isBoolean(e)?!e:e},get silentFallbackWarn(){return isBoolean(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=isBoolean(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,r]=e,s={};let l=null,o=null;if(!isString(a))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=a;return isString(n)?s.locale=n:isArray(n)?l=n:isPlainObject(n)&&(o=n),isArray(r)?l=r:isPlainObject(r)&&(o=r),Reflect.apply(t.t,t,[i,l||o||{},s])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,n,r]=e,s={plural:1};let l=null,o=null;if(!isString(a))throw Error(I18nErrorCodes.INVALID_ARGUMENT);const i=a;return isString(n)?s.locale=n:isNumber(n)?s.plural=n:isArray(n)?l=n:isPlainObject(n)&&(o=n),isString(r)?s.locale=r:isArray(r)?l=r:isPlainObject(r)&&(o=r),Reflect.apply(t.t,t,[i,l||o||{},s])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1};return n.__extender=a,n}}const baseFormatProps={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function getInterpolateArg({slots:e},t){if(1===t.length&&"default"===t[0]){return(e.default?e.default():[]).reduce(((e,t)=>[...e,...t.type===Fragment?t.children:[t]]),[])}return t.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),{})}function getFragmentableTag(e){return Fragment}const TranslationImpl=defineComponent({name:"i18n-t",props:assign({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>isNumber(e)||!isNaN(e)}},baseFormatProps),setup(e,t){const{slots:a,attrs:n}=t,r=e.i18n||useI18n({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(a).filter((e=>"_"!==e)),l={};e.locale&&(l.locale=e.locale),void 0!==e.plural&&(l.plural=isString(e.plural)?+e.plural:e.plural);const o=getInterpolateArg(t,s),i=r[TranslateVNodeSymbol](e.keypath,o,l),c=assign({},n),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}}),Translation=TranslationImpl,I18nT=Translation;function isVNode(e){return isArray(e)&&!isString(e[0])}function renderFormatter(e,t,a,n){const{slots:r,attrs:s}=t;return()=>{const t={part:!0};let l={};e.locale&&(t.locale=e.locale),isString(e.format)?t.key=e.format:isObject(e.format)&&(isString(e.format.key)&&(t.key=e.format.key),l=Object.keys(e.format).reduce(((t,n)=>a.includes(n)?assign({},t,{[n]:e.format[n]}):t),{}));const o=n(e.value,t,l);let i=[t.key];isArray(o)?i=o.map(((e,t)=>{const a=r[e.type],n=a?a({[e.type]:e.value,index:t,parts:o}):[e.value];return isVNode(n)&&(n[0].key=`${e.type}-${t}`),n})):isString(o)&&(i=[o]);const c=assign({},s),u=isString(e.tag)||isObject(e.tag)?e.tag:getFragmentableTag();return h(u,c,i)}}const NumberFormatImpl=defineComponent({name:"i18n-n",props:assign({value:{type:Number,required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const a=e.i18n||useI18n({useScope:"parent",__useComponent:!0});return renderFormatter(e,t,NUMBER_FORMAT_OPTIONS_KEYS,((...e)=>a[NumberPartsSymbol](...e)))}}),NumberFormat=NumberFormatImpl,I18nN=NumberFormat,DatetimeFormatImpl=defineComponent({name:"i18n-d",props:assign({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},baseFormatProps),setup(e,t){const a=e.i18n||useI18n({useScope:"parent",__useComponent:!0});return renderFormatter(e,t,DATETIME_FORMAT_OPTIONS_KEYS,((...e)=>a[DatetimePartsSymbol](...e)))}}),DatetimeFormat=DatetimeFormatImpl,I18nD=DatetimeFormat;function getComposer$1(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}function vTDirective(e){const t=t=>{const{instance:a,modifiers:n,value:r}=t;if(!a||!a.$)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const s=getComposer$1(e,a.$),l=parseValue(r);return[Reflect.apply(s.t,s,[...makeParams(l)]),s]};return{created:(a,n)=>{const[r,s]=t(n);inBrowser&&e.global===s&&(a.__i18nWatcher=watch(s.locale,(()=>{n.instance&&n.instance.$forceUpdate()}))),a.__composer=s,a.textContent=r},unmounted:e=>{inBrowser&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,n=parseValue(t);e.textContent=Reflect.apply(a.t,a,[...makeParams(n)])}},getSSRProps:e=>{const[a]=t(e);return{textContent:a}}}}function parseValue(e){if(isString(e))return{path:e};if(isPlainObject(e)){if(!("path"in e))throw Error(I18nErrorCodes.REQUIRED_VALUE,"path");return e}throw Error(I18nErrorCodes.INVALID_VALUE)}function makeParams(e){const{path:t,locale:a,args:n,choice:r,plural:s}=e,l={},o=n||{};return isString(a)&&(l.locale=a),isNumber(r)&&(l.plural=r),isNumber(s)&&(l.plural=s),[t,o,l]}function apply(e,t,...a){const n=isPlainObject(a[0])?a[0]:{},r=!!n.useI18nComponentName;(!isBoolean(n.globalInstall)||n.globalInstall)&&([r?"i18n":Translation.name,"I18nT"].forEach((t=>e.component(t,Translation))),[NumberFormat.name,"I18nN"].forEach((t=>e.component(t,NumberFormat))),[DatetimeFormat.name,"I18nD"].forEach((t=>e.component(t,DatetimeFormat)))),e.directive("t",vTDirective(t))}function defineMixin(e,t,a){return{beforeCreate(){const n=getCurrentInstance();if(!n)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const n=r.i18n;if(r.__i18n&&(n.__i18n=r.__i18n),n.__root=t,this===this.$root)this.$i18n=mergeToGlobal(e,n);else{n.__injectWithOption=!0,n.__extender=a.__vueI18nExtend,this.$i18n=createVueI18n(n);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=mergeToGlobal(e,r);else{this.$i18n=createVueI18n({__i18n:r.__i18n,__injectWithOption:!0,__extender:a.__vueI18nExtend,__root:t});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&adjustI18nResources(t,r,r),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),a.__setInstance(n,this.$i18n)},mounted(){},unmounted(){const e=getCurrentInstance();if(!e)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const t=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__disposer&&(t.__disposer(),delete t.__disposer,delete t.__extender),a.__deleteInstance(e),delete this.$i18n}}}function mergeToGlobal(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[SetPluralRulesSymbol](t.pluralizationRules||e.pluralizationRules);const a=getLocaleMessages(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const I18nInjectionKey=makeSymbol("global-vue-i18n");function createI18n(e={},t){const a=!isBoolean(e.legacy)||e.legacy,n=!isBoolean(e.globalInjection)||e.globalInjection,r=!a||!!e.allowComposition,s=new Map,[l,o]=createGlobal(e,a),i=makeSymbol("");{const e={get mode(){return a?"legacy":"composition"},get allowComposition(){return r},async install(t,...r){if(t.__VUE_I18N_SYMBOL__=i,t.provide(t.__VUE_I18N_SYMBOL__,e),isPlainObject(r[0])){const t=r[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let s=null;!a&&n&&(s=injectGlobalFields(t,e.global)),apply(t,e,...r),a&&t.mixin(defineMixin(o,o.__composer,e));const l=t.unmount;t.unmount=()=>{s&&s(),e.dispose(),l()}},get global(){return o},dispose(){l.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}}function useI18n(e={}){const t=getCurrentInstance();if(null==t)throw Error(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(I18nErrorCodes.NOT_INSTALLED);const a=getI18nInstance(t),n=getGlobalComposer(a),r=getComponentOptions(t),s=getScope(e,r);if("legacy"===a.mode&&!e.__useComponent){if(!a.allowComposition)throw Error(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);return useI18nForLegacy(t,s,n,e)}if("global"===s)return adjustI18nResources(n,e,r),n;if("parent"===s){let r=getComposer(a,t,e.__useComponent);return null==r&&(r=n),r}const l=a;let o=l.__getInstance(t);if(null==o){const a=assign({},e);"__i18n"in r&&(a.__i18n=r.__i18n),n&&(a.__root=n),o=createComposer(a),l.__composerExtend&&(o[DisposeSymbol]=l.__composerExtend(o)),setupLifeCycle(l,t,o),l.__setInstance(t,o)}return o}const castToVueI18n=e=>{if(!(__VUE_I18N_BRIDGE__ in e))throw Error(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};function createGlobal(e,t,a){const n=effectScope();{const a=t?n.run((()=>createVueI18n(e))):n.run((()=>createComposer(e)));if(null==a)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);return[n,a]}}function getI18nInstance(e){{const t=inject(e.isCE?I18nInjectionKey:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw createI18nError(e.isCE?I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE:I18nErrorCodes.UNEXPECTED_ERROR);return t}}function getScope(e,t){return isEmptyObject(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function getGlobalComposer(e){return"composition"===e.mode?e.global:e.global.__composer}function getComposer(e,t,a=!1){let n=null;const r=t.root;let s=getParentComponentInstance(t,a);for(;null!=s;){const t=e;if("composition"===e.mode)n=t.__getInstance(s);else{const e=t.__getInstance(s);null!=e&&(n=e.__composer,a&&n&&!n[InejctWithOptionSymbol]&&(n=null))}if(null!=n)break;if(r===s)break;s=s.parent}return n}function getParentComponentInstance(e,t=!1){return null==e?null:t&&e.vnode.ctx||e.parent}function setupLifeCycle(e,t,a){onMounted((()=>{}),t),onUnmounted((()=>{const n=a;e.__deleteInstance(t);const r=n[DisposeSymbol];r&&(r(),delete n[DisposeSymbol])}),t)}function useI18nForLegacy(e,t,a,n={}){const r="local"===t,s=shallowRef(null);if(r&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const l=isBoolean(n.inheritLocale)?n.inheritLocale:!isString(n.locale),o=ref(!r||l?a.locale.value:isString(n.locale)?n.locale:DEFAULT_LOCALE),i=ref(!r||l?a.fallbackLocale.value:isString(n.fallbackLocale)||isArray(n.fallbackLocale)||isPlainObject(n.fallbackLocale)||!1===n.fallbackLocale?n.fallbackLocale:o.value),c=ref(getLocaleMessages(o.value,n)),u=ref(isPlainObject(n.datetimeFormats)?n.datetimeFormats:{[o.value]:{}}),m=ref(isPlainObject(n.numberFormats)?n.numberFormats:{[o.value]:{}}),g=r?a.missingWarn:!isBoolean(n.missingWarn)&&!isRegExp(n.missingWarn)||n.missingWarn,_=r?a.fallbackWarn:!isBoolean(n.fallbackWarn)&&!isRegExp(n.fallbackWarn)||n.fallbackWarn,f=r?a.fallbackRoot:!isBoolean(n.fallbackRoot)||n.fallbackRoot,p=!!n.fallbackFormat,b=isFunction(n.missing)?n.missing:null,E=isFunction(n.postTranslation)?n.postTranslation:null,d=r?a.warnHtmlMessage:!isBoolean(n.warnHtmlMessage)||n.warnHtmlMessage,O=!!n.escapeParameter,v=r?a.modifiers:isPlainObject(n.modifiers)?n.modifiers:{},T=n.pluralRules||r&&a.pluralRules;function N(e){return o.value,i.value,c.value,u.value,m.value,e()}const F={get id(){return s.value?s.value.id:-1},locale:computed({get:()=>s.value?s.value.locale.value:o.value,set:e=>{s.value&&(s.value.locale.value=e),o.value=e}}),fallbackLocale:computed({get:()=>s.value?s.value.fallbackLocale.value:i.value,set:e=>{s.value&&(s.value.fallbackLocale.value=e),i.value=e}}),messages:computed((()=>s.value?s.value.messages.value:c.value)),datetimeFormats:computed((()=>u.value)),numberFormats:computed((()=>m.value)),get inheritLocale(){return s.value?s.value.inheritLocale:l},set inheritLocale(e){s.value&&(s.value.inheritLocale=e)},get availableLocales(){return s.value?s.value.availableLocales:Object.keys(c.value)},get modifiers(){return s.value?s.value.modifiers:v},get pluralRules(){return s.value?s.value.pluralRules:T},get isGlobal(){return!!s.value&&s.value.isGlobal},get missingWarn(){return s.value?s.value.missingWarn:g},set missingWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackWarn(){return s.value?s.value.fallbackWarn:_},set fallbackWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackRoot(){return s.value?s.value.fallbackRoot:f},set fallbackRoot(e){s.value&&(s.value.fallbackRoot=e)},get fallbackFormat(){return s.value?s.value.fallbackFormat:p},set fallbackFormat(e){s.value&&(s.value.fallbackFormat=e)},get warnHtmlMessage(){return s.value?s.value.warnHtmlMessage:d},set warnHtmlMessage(e){s.value&&(s.value.warnHtmlMessage=e)},get escapeParameter(){return s.value?s.value.escapeParameter:O},set escapeParameter(e){s.value&&(s.value.escapeParameter=e)},t:function(...e){return s.value?N((()=>Reflect.apply(s.value.t,null,[...e]))):N((()=>""))},getPostTranslationHandler:function(){return s.value?s.value.getPostTranslationHandler():E},setPostTranslationHandler:function(e){s.value&&s.value.setPostTranslationHandler(e)},getMissingHandler:function(){return s.value?s.value.getMissingHandler():b},setMissingHandler:function(e){s.value&&s.value.setMissingHandler(e)},rt:function(...e){return s.value?Reflect.apply(s.value.rt,null,[...e]):""},d:function(...e){return s.value?N((()=>Reflect.apply(s.value.d,null,[...e]))):N((()=>""))},n:function(...e){return s.value?N((()=>Reflect.apply(s.value.n,null,[...e]))):N((()=>""))},tm:function(e){return s.value?s.value.tm(e):{}},te:function(e,t){return!!s.value&&s.value.te(e,t)},getLocaleMessage:function(e){return s.value?s.value.getLocaleMessage(e):{}},setLocaleMessage:function(e,t){s.value&&(s.value.setLocaleMessage(e,t),c.value[e]=t)},mergeLocaleMessage:function(e,t){s.value&&s.value.mergeLocaleMessage(e,t)},getDateTimeFormat:function(e){return s.value?s.value.getDateTimeFormat(e):{}},setDateTimeFormat:function(e,t){s.value&&(s.value.setDateTimeFormat(e,t),u.value[e]=t)},mergeDateTimeFormat:function(e,t){s.value&&s.value.mergeDateTimeFormat(e,t)},getNumberFormat:function(e){return s.value?s.value.getNumberFormat(e):{}},setNumberFormat:function(e,t){s.value&&(s.value.setNumberFormat(e,t),m.value[e]=t)},mergeNumberFormat:function(e,t){s.value&&s.value.mergeNumberFormat(e,t)}};return onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const a=s.value=e.proxy.$i18n.__composer;"global"===t?(o.value=a.locale.value,i.value=a.fallbackLocale.value,c.value=a.messages.value,u.value=a.datetimeFormats.value,m.value=a.numberFormats.value):r&&function(e){e.locale.value=o.value,e.fallbackLocale.value=i.value,Object.keys(c.value).forEach((t=>{e.mergeLocaleMessage(t,c.value[t])})),Object.keys(u.value).forEach((t=>{e.mergeDateTimeFormat(t,u.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeNumberFormat(t,m.value[t])})),e.escapeParameter=O,e.fallbackFormat=p,e.fallbackRoot=f,e.fallbackWarn=_,e.missingWarn=g,e.warnHtmlMessage=d}(a)})),F}const globalExportProps=["locale","fallbackLocale","availableLocales"],globalExportMethods=["t","rt","d","n","tm","te"];function injectGlobalFields(e,t){const a=Object.create(null);globalExportProps.forEach((e=>{const n=Object.getOwnPropertyDescriptor(t,e);if(!n)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);const r=isRef(n.value)?{get:()=>n.value.value,set(e){n.value.value=e}}:{get:()=>n.get&&n.get()};Object.defineProperty(a,e,r)})),e.config.globalProperties.$i18n=a,globalExportMethods.forEach((a=>{const n=Object.getOwnPropertyDescriptor(t,a);if(!n||!n.value)throw Error(I18nErrorCodes.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${a}`,n)}));return()=>{delete e.config.globalProperties.$i18n,globalExportMethods.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))}}registerMessageResolver(resolveValue),registerLocaleFallbacker(fallbackWithLocaleChain);export{DatetimeFormat,I18nD,I18nInjectionKey,I18nN,I18nT,NumberFormat,Translation,VERSION,castToVueI18n,createI18n,useI18n,vTDirective};
