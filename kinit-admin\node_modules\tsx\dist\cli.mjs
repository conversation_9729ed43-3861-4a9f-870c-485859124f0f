#!/usr/bin/env node
var Rn=Object.defineProperty;var a=(t,e)=>Rn(t,"name",{value:e,configurable:!0});import{constants as lt}from"node:os";import bn from"tty";import{transformSync as vn}from"esbuild";import{v as Sn}from"./package-BgRDTLo0.mjs";import{r as Ie,g as Bn,i as $n}from"./get-pipe-path-BHW2eJdv.mjs";import{pathToFileURL as Tn,fileURLToPath as xn}from"node:url";import On from"child_process";import z from"path";import De from"fs";import{i as mu,m as Nn,t as Hn}from"./node-features-_8ZFwP_x.mjs";import Pn from"node:path";import Ln from"events";import ge from"util";import In from"stream";import _u from"os";import{g as kn,l as Mn,e as Gn,f as Wn,y as me}from"./index-gbaejti9.mjs";import jn from"node:net";import ct from"node:fs";import{t as Un}from"./temporary-directory-CwHp0_NW.mjs";import"module";const Kn="known-flag",Vn="unknown-flag",zn="argument",{stringify:_e}=JSON,Yn=/\B([A-Z])/g,qn=a(t=>t.replace(Yn,"-$1").toLowerCase(),"v$1"),{hasOwnProperty:Xn}=Object.prototype,Ae=a((t,e)=>Xn.call(t,e),"w$2"),Qn=a(t=>Array.isArray(t),"L$2"),Au=a(t=>typeof t=="function"?[t,!1]:Qn(t)?[t[0],!0]:Au(t.type),"b$2"),Zn=a((t,e)=>t===Boolean?e!=="false":e,"d$2"),Jn=a((t,e)=>typeof e=="boolean"?e:t===Number&&e===""?Number.NaN:t(e),"m$1"),er=/[\s.:=]/,tr=a(t=>{const e=`Flag name ${_e(t)}`;if(t.length===0)throw new Error(`${e} cannot be empty`);if(t.length===1)throw new Error(`${e} must be longer than a character`);const u=t.match(er);if(u)throw new Error(`${e} cannot contain ${_e(u?.[0])}`)},"B"),ur=a(t=>{const e={},u=a((s,n)=>{if(Ae(e,s))throw new Error(`Duplicate flags named ${_e(s)}`);e[s]=n},"r");for(const s in t){if(!Ae(t,s))continue;tr(s);const n=t[s],r=[[],...Au(n),n];u(s,r);const i=qn(s);if(s!==i&&u(i,r),"alias"in n&&typeof n.alias=="string"){const{alias:D}=n,o=`Flag alias ${_e(D)} for flag ${_e(s)}`;if(D.length===0)throw new Error(`${o} cannot be empty`);if(D.length>1)throw new Error(`${o} must be a single character`);u(D,r)}}return e},"K$1"),sr=a((t,e)=>{const u={};for(const s in t){if(!Ae(t,s))continue;const[n,,r,i]=e[s];if(n.length===0&&"default"in i){let{default:D}=i;typeof D=="function"&&(D=D()),u[s]=D}else u[s]=r?n:n.pop()}return u},"_$2"),ke="--",nr=/[.:=]/,rr=/^-{1,2}\w/,ir=a(t=>{if(!rr.test(t))return;const e=!t.startsWith(ke);let u=t.slice(e?1:2),s;const n=u.match(nr);if(n){const{index:r}=n;s=u.slice(r+1),u=u.slice(0,r)}return[u,s,e]},"N"),Dr=a((t,{onFlag:e,onArgument:u})=>{let s;const n=a((r,i)=>{if(typeof s!="function")return!0;s(r,i),s=void 0},"o");for(let r=0;r<t.length;r+=1){const i=t[r];if(i===ke){n();const o=t.slice(r+1);u?.(o,[r],!0);break}const D=ir(i);if(D){if(n(),!e)continue;const[o,c,f]=D;if(f)for(let h=0;h<o.length;h+=1){n();const l=h===o.length-1;s=e(o[h],l?c:void 0,[r,h+1,l])}else s=e(o,c,[r])}else n(i,[r])&&u?.([i],[r])}n()},"$$1"),or=a((t,e)=>{for(const[u,s,n]of e.reverse()){if(s){const r=t[u];let i=r.slice(0,s);if(n||(i+=r.slice(s+1)),i!=="-"){t[u]=i;continue}}t.splice(u,1)}},"E"),yu=a((t,e=process.argv.slice(2),{ignore:u}={})=>{const s=[],n=ur(t),r={},i=[];return i[ke]=[],Dr(e,{onFlag(D,o,c){const f=Ae(n,D);if(!u?.(f?Kn:Vn,D,o)){if(f){const[h,l]=n[D],p=Zn(l,o),C=a((g,y)=>{s.push(c),y&&s.push(y),h.push(Jn(l,g||""))},"p");return p===void 0?C:C(p)}Ae(r,D)||(r[D]=[]),r[D].push(o===void 0?!0:o),s.push(c)}},onArgument(D,o,c){u?.(zn,e[o[0]])||(i.push(...D),c?(i[ke]=D,e.splice(o[0])):s.push(o))}}),or(e,s),{flags:sr(t,n),unknownFlags:r,_:i}},"U$2");var ar=Object.create,Me=Object.defineProperty,lr=Object.defineProperties,cr=Object.getOwnPropertyDescriptor,fr=Object.getOwnPropertyDescriptors,hr=Object.getOwnPropertyNames,wu=Object.getOwnPropertySymbols,dr=Object.getPrototypeOf,Ru=Object.prototype.hasOwnProperty,Er=Object.prototype.propertyIsEnumerable,bu=a((t,e,u)=>e in t?Me(t,e,{enumerable:!0,configurable:!0,writable:!0,value:u}):t[e]=u,"W$1"),Ge=a((t,e)=>{for(var u in e||(e={}))Ru.call(e,u)&&bu(t,u,e[u]);if(wu)for(var u of wu(e))Er.call(e,u)&&bu(t,u,e[u]);return t},"p"),ft=a((t,e)=>lr(t,fr(e)),"c"),pr=a(t=>Me(t,"__esModule",{value:!0}),"nD"),Cr=a((t,e)=>()=>(t&&(e=t(t=0)),e),"rD"),Fr=a((t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),"iD"),gr=a((t,e,u,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of hr(e))!Ru.call(t,n)&&n!=="default"&&Me(t,n,{get:a(()=>e[n],"get"),enumerable:!(s=cr(e,n))||s.enumerable});return t},"oD"),mr=a((t,e)=>gr(pr(Me(t!=null?ar(dr(t)):{},"default",{value:t,enumerable:!0})),t),"BD"),K=Cr(()=>{}),_r=Fr((t,e)=>{K(),e.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}});K(),K(),K();var Ar=a(t=>{var e,u,s;let n=(e=process.stdout.columns)!=null?e:Number.POSITIVE_INFINITY;return typeof t=="function"&&(t=t(n)),t||(t={}),Array.isArray(t)?{columns:t,stdoutColumns:n}:{columns:(u=t.columns)!=null?u:[],stdoutColumns:(s=t.stdoutColumns)!=null?s:n}},"v");K(),K(),K(),K(),K();function yr({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}a(yr,"w$1");function vu(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(yr(),"")}a(vu,"d$1"),K();function wr(t){return Number.isInteger(t)?t>=4352&&(t<=4447||t===9001||t===9002||11904<=t&&t<=12871&&t!==12351||12880<=t&&t<=19903||19968<=t&&t<=42182||43360<=t&&t<=43388||44032<=t&&t<=55203||63744<=t&&t<=64255||65040<=t&&t<=65049||65072<=t&&t<=65131||65281<=t&&t<=65376||65504<=t&&t<=65510||110592<=t&&t<=110593||127488<=t&&t<=127569||131072<=t&&t<=262141):!1}a(wr,"y$1");var Rr=mr(_r());function oe(t){if(typeof t!="string"||t.length===0||(t=vu(t),t.length===0))return 0;t=t.replace((0,Rr.default)(),"  ");let e=0;for(let u=0;u<t.length;u++){let s=t.codePointAt(u);s<=31||s>=127&&s<=159||s>=768&&s<=879||(s>65535&&u++,e+=wr(s)?2:1)}return e}a(oe,"g");var Su=a(t=>Math.max(...t.split(`
`).map(oe)),"b$1"),br=a(t=>{let e=[];for(let u of t){let{length:s}=u,n=s-e.length;for(let r=0;r<n;r+=1)e.push(0);for(let r=0;r<s;r+=1){let i=Su(u[r]);i>e[r]&&(e[r]=i)}}return e},"k$1");K();var Bu=/^\d+%$/,$u={width:"auto",align:"left",contentWidth:0,paddingLeft:0,paddingRight:0,paddingTop:0,paddingBottom:0,horizontalPadding:0,paddingLeftString:"",paddingRightString:""},vr=a((t,e)=>{var u;let s=[];for(let n=0;n<t.length;n+=1){let r=(u=e[n])!=null?u:"auto";if(typeof r=="number"||r==="auto"||r==="content-width"||typeof r=="string"&&Bu.test(r)){s.push(ft(Ge({},$u),{width:r,contentWidth:t[n]}));continue}if(r&&typeof r=="object"){let i=ft(Ge(Ge({},$u),r),{contentWidth:t[n]});i.horizontalPadding=i.paddingLeft+i.paddingRight,s.push(i);continue}throw new Error(`Invalid column width: ${JSON.stringify(r)}`)}return s},"sD");function Sr(t,e){for(let u of t){let{width:s}=u;if(s==="content-width"&&(u.width=u.contentWidth),s==="auto"){let o=Math.min(20,u.contentWidth);u.width=o,u.autoOverflow=u.contentWidth-o}if(typeof s=="string"&&Bu.test(s)){let o=Number.parseFloat(s.slice(0,-1))/100;u.width=Math.floor(e*o)-(u.paddingLeft+u.paddingRight)}let{horizontalPadding:n}=u,r=1,i=r+n;if(i>=e){let o=i-e,c=Math.ceil(u.paddingLeft/n*o),f=o-c;u.paddingLeft-=c,u.paddingRight-=f,u.horizontalPadding=u.paddingLeft+u.paddingRight}u.paddingLeftString=u.paddingLeft?" ".repeat(u.paddingLeft):"",u.paddingRightString=u.paddingRight?" ".repeat(u.paddingRight):"";let D=e-u.horizontalPadding;u.width=Math.max(Math.min(u.width,D),r)}}a(Sr,"aD");var Tu=a(()=>Object.assign([],{columns:0}),"G$1");function Br(t,e){let u=[Tu()],[s]=u;for(let n of t){let r=n.width+n.horizontalPadding;s.columns+r>e&&(s=Tu(),u.push(s)),s.push(n),s.columns+=r}for(let n of u){let r=n.reduce((l,p)=>l+p.width+p.horizontalPadding,0),i=e-r;if(i===0)continue;let D=n.filter(l=>"autoOverflow"in l),o=D.filter(l=>l.autoOverflow>0),c=o.reduce((l,p)=>l+p.autoOverflow,0),f=Math.min(c,i);for(let l of o){let p=Math.floor(l.autoOverflow/c*f);l.width+=p,i-=p}let h=Math.floor(i/D.length);for(let l=0;l<D.length;l+=1){let p=D[l];l===D.length-1?p.width+=i:p.width+=h,i-=h}}return u}a(Br,"lD");function $r(t,e,u){let s=vr(u,e);return Sr(s,t),Br(s,t)}a($r,"Z$1"),K(),K(),K();var ht=10,xu=a((t=0)=>e=>`\x1B[${e+t}m`,"U$1"),Ou=a((t=0)=>e=>`\x1B[${38+t};5;${e}m`,"V$1"),Nu=a((t=0)=>(e,u,s)=>`\x1B[${38+t};2;${e};${u};${s}m`,"Y");function Tr(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.gray=e.color.blackBright,e.bgColor.bgGray=e.bgColor.bgBlackBright,e.color.grey=e.color.blackBright,e.bgColor.bgGrey=e.bgColor.bgBlackBright;for(let[u,s]of Object.entries(e)){for(let[n,r]of Object.entries(s))e[n]={open:`\x1B[${r[0]}m`,close:`\x1B[${r[1]}m`},s[n]=e[n],t.set(r[0],r[1]);Object.defineProperty(e,u,{value:s,enumerable:!1})}return Object.defineProperty(e,"codes",{value:t,enumerable:!1}),e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi=xu(),e.color.ansi256=Ou(),e.color.ansi16m=Nu(),e.bgColor.ansi=xu(ht),e.bgColor.ansi256=Ou(ht),e.bgColor.ansi16m=Nu(ht),Object.defineProperties(e,{rgbToAnsi256:{value:a((u,s,n)=>u===s&&s===n?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(s/255*5)+Math.round(n/255*5),"value"),enumerable:!1},hexToRgb:{value:a(u=>{let s=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!s)return[0,0,0];let{colorString:n}=s.groups;n.length===3&&(n=n.split("").map(i=>i+i).join(""));let r=Number.parseInt(n,16);return[r>>16&255,r>>8&255,r&255]},"value"),enumerable:!1},hexToAnsi256:{value:a(u=>e.rgbToAnsi256(...e.hexToRgb(u)),"value"),enumerable:!1},ansi256ToAnsi:{value:a(u=>{if(u<8)return 30+u;if(u<16)return 90+(u-8);let s,n,r;if(u>=232)s=((u-232)*10+8)/255,n=s,r=s;else{u-=16;let o=u%36;s=Math.floor(u/36)/5,n=Math.floor(o/6)/5,r=o%6/5}let i=Math.max(s,n,r)*2;if(i===0)return 30;let D=30+(Math.round(r)<<2|Math.round(n)<<1|Math.round(s));return i===2&&(D+=60),D},"value"),enumerable:!1},rgbToAnsi:{value:a((u,s,n)=>e.ansi256ToAnsi(e.rgbToAnsi256(u,s,n)),"value"),enumerable:!1},hexToAnsi:{value:a(u=>e.ansi256ToAnsi(e.hexToAnsi256(u)),"value"),enumerable:!1}}),e}a(Tr,"AD");var xr=Tr(),Or=xr,We=new Set(["\x1B","\x9B"]),Nr=39,dt="\x07",Hu="[",Hr="]",Pu="m",Et=`${Hr}8;;`,Lu=a(t=>`${We.values().next().value}${Hu}${t}${Pu}`,"J$1"),Iu=a(t=>`${We.values().next().value}${Et}${t}${dt}`,"Q"),Pr=a(t=>t.split(" ").map(e=>oe(e)),"hD"),pt=a((t,e,u)=>{let s=[...e],n=!1,r=!1,i=oe(vu(t[t.length-1]));for(let[D,o]of s.entries()){let c=oe(o);if(i+c<=u?t[t.length-1]+=o:(t.push(o),i=0),We.has(o)&&(n=!0,r=s.slice(D+1).join("").startsWith(Et)),n){r?o===dt&&(n=!1,r=!1):o===Pu&&(n=!1);continue}i+=c,i===u&&D<s.length-1&&(t.push(""),i=0)}!i&&t[t.length-1].length>0&&t.length>1&&(t[t.length-2]+=t.pop())},"S$1"),Lr=a(t=>{let e=t.split(" "),u=e.length;for(;u>0&&!(oe(e[u-1])>0);)u--;return u===e.length?t:e.slice(0,u).join(" ")+e.slice(u).join("")},"cD"),Ir=a((t,e,u={})=>{if(u.trim!==!1&&t.trim()==="")return"";let s="",n,r,i=Pr(t),D=[""];for(let[c,f]of t.split(" ").entries()){u.trim!==!1&&(D[D.length-1]=D[D.length-1].trimStart());let h=oe(D[D.length-1]);if(c!==0&&(h>=e&&(u.wordWrap===!1||u.trim===!1)&&(D.push(""),h=0),(h>0||u.trim===!1)&&(D[D.length-1]+=" ",h++)),u.hard&&i[c]>e){let l=e-h,p=1+Math.floor((i[c]-l-1)/e);Math.floor((i[c]-1)/e)<p&&D.push(""),pt(D,f,e);continue}if(h+i[c]>e&&h>0&&i[c]>0){if(u.wordWrap===!1&&h<e){pt(D,f,e);continue}D.push("")}if(h+i[c]>e&&u.wordWrap===!1){pt(D,f,e);continue}D[D.length-1]+=f}u.trim!==!1&&(D=D.map(c=>Lr(c)));let o=[...D.join(`
`)];for(let[c,f]of o.entries()){if(s+=f,We.has(f)){let{groups:l}=new RegExp(`(?:\\${Hu}(?<code>\\d+)m|\\${Et}(?<uri>.*)${dt})`).exec(o.slice(c).join(""))||{groups:{}};if(l.code!==void 0){let p=Number.parseFloat(l.code);n=p===Nr?void 0:p}else l.uri!==void 0&&(r=l.uri.length===0?void 0:l.uri)}let h=Or.codes.get(Number(n));o[c+1]===`
`?(r&&(s+=Iu("")),n&&h&&(s+=Lu(h))):f===`
`&&(n&&h&&(s+=Lu(n)),r&&(s+=Iu(r)))}return s},"dD");function kr(t,e,u){return String(t).normalize().replace(/\r\n/g,`
`).split(`
`).map(s=>Ir(s,e,u)).join(`
`)}a(kr,"T$1");var ku=a(t=>Array.from({length:t}).fill(""),"X");function Mr(t,e){let u=[],s=0;for(let n of t){let r=0,i=n.map(o=>{var c;let f=(c=e[s])!=null?c:"";s+=1,o.preprocess&&(f=o.preprocess(f)),Su(f)>o.width&&(f=kr(f,o.width,{hard:!0}));let h=f.split(`
`);if(o.postprocess){let{postprocess:l}=o;h=h.map((p,C)=>l.call(o,p,C))}return o.paddingTop&&h.unshift(...ku(o.paddingTop)),o.paddingBottom&&h.push(...ku(o.paddingBottom)),h.length>r&&(r=h.length),ft(Ge({},o),{lines:h})}),D=[];for(let o=0;o<r;o+=1){let c=i.map(f=>{var h;let l=(h=f.lines[o])!=null?h:"",p=Number.isFinite(f.width)?" ".repeat(f.width-oe(l)):"",C=f.paddingLeftString;return f.align==="right"&&(C+=p),C+=l,f.align==="left"&&(C+=p),C+f.paddingRightString}).join("");D.push(c)}u.push(D.join(`
`))}return u.join(`
`)}a(Mr,"P");function Gr(t,e){if(!t||t.length===0)return"";let u=br(t),s=u.length;if(s===0)return"";let{stdoutColumns:n,columns:r}=Ar(e);if(r.length>s)throw new Error(`${r.length} columns defined, but only ${s} columns found`);let i=$r(n,r,u);return t.map(D=>Mr(i,D)).join(`
`)}a(Gr,"mD"),K();var Wr=["<",">","=",">=","<="];function jr(t){if(!Wr.includes(t))throw new TypeError(`Invalid breakpoint operator: ${t}`)}a(jr,"xD");function Ur(t){let e=Object.keys(t).map(u=>{let[s,n]=u.split(" ");jr(s);let r=Number.parseInt(n,10);if(Number.isNaN(r))throw new TypeError(`Invalid breakpoint value: ${n}`);let i=t[u];return{operator:s,breakpoint:r,value:i}}).sort((u,s)=>s.breakpoint-u.breakpoint);return u=>{var s;return(s=e.find(({operator:n,breakpoint:r})=>n==="="&&u===r||n===">"&&u>r||n==="<"&&u<r||n===">="&&u>=r||n==="<="&&u<=r))==null?void 0:s.value}}a(Ur,"wD");const Kr=a(t=>t.replace(/[\W_]([a-z\d])?/gi,(e,u)=>u?u.toUpperCase():""),"S"),Vr=a(t=>t.replace(/\B([A-Z])/g,"-$1").toLowerCase(),"q"),zr={"> 80":[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"auto"}],"> 40":[{width:"auto",paddingLeft:2,paddingRight:8,preprocess:a(t=>t.trim(),"preprocess")},{width:"100%",paddingLeft:2,paddingBottom:1}],"> 0":{stdoutColumns:1e3,columns:[{width:"content-width",paddingLeft:2,paddingRight:8},{width:"content-width"}]}};function Yr(t){let e=!1;return{type:"table",data:{tableData:Object.keys(t).sort((u,s)=>u.localeCompare(s)).map(u=>{const s=t[u],n="alias"in s;return n&&(e=!0),{name:u,flag:s,flagFormatted:`--${Vr(u)}`,aliasesEnabled:e,aliasFormatted:n?`-${s.alias}`:void 0}}).map(u=>(u.aliasesEnabled=e,[{type:"flagName",data:u},{type:"flagDescription",data:u}])),tableBreakpoints:zr}}}a(Yr,"D");const Mu=a(t=>!t||(t.version??(t.help?t.help.version:void 0)),"A"),Gu=a(t=>{const e="parent"in t&&t.parent?.name;return(e?`${e} `:"")+t.name},"C");function qr(t){const e=[];t.name&&e.push(Gu(t));const u=Mu(t)??("parent"in t&&Mu(t.parent));if(u&&e.push(`v${u}`),e.length!==0)return{id:"name",type:"text",data:`${e.join(" ")}
`}}a(qr,"R");function Xr(t){const{help:e}=t;if(!(!e||!e.description))return{id:"description",type:"text",data:`${e.description}
`}}a(Xr,"L");function Qr(t){const e=t.help||{};if("usage"in e)return e.usage?{id:"usage",type:"section",data:{title:"Usage:",body:Array.isArray(e.usage)?e.usage.join(`
`):e.usage}}:void 0;if(t.name){const u=[],s=[Gu(t)];if(t.flags&&Object.keys(t.flags).length>0&&s.push("[flags...]"),t.parameters&&t.parameters.length>0){const{parameters:n}=t,r=n.indexOf("--"),i=r>-1&&n.slice(r+1).some(D=>D.startsWith("<"));s.push(n.map(D=>D!=="--"?D:i?"--":"[--]").join(" "))}if(s.length>1&&u.push(s.join(" ")),"commands"in t&&t.commands?.length&&u.push(`${t.name} <command>`),u.length>0)return{id:"usage",type:"section",data:{title:"Usage:",body:u.join(`
`)}}}}a(Qr,"T");function Zr(t){return!("commands"in t)||!t.commands?.length?void 0:{id:"commands",type:"section",data:{title:"Commands:",body:{type:"table",data:{tableData:t.commands.map(e=>[e.options.name,e.options.help?e.options.help.description:""]),tableOptions:[{width:"content-width",paddingLeft:2,paddingRight:8}]}},indentBody:0}}}a(Zr,"_");function Jr(t){if(!(!t.flags||Object.keys(t.flags).length===0))return{id:"flags",type:"section",data:{title:"Flags:",body:Yr(t.flags),indentBody:0}}}a(Jr,"k");function ei(t){const{help:e}=t;if(!e||!e.examples||e.examples.length===0)return;let{examples:u}=e;if(Array.isArray(u)&&(u=u.join(`
`)),u)return{id:"examples",type:"section",data:{title:"Examples:",body:u}}}a(ei,"F");function ti(t){if(!("alias"in t)||!t.alias)return;const{alias:e}=t;return{id:"aliases",type:"section",data:{title:"Aliases:",body:Array.isArray(e)?e.join(", "):e}}}a(ti,"H");const ui=a(t=>[qr,Xr,Qr,Zr,Jr,ei,ti].map(e=>e(t)).filter(Boolean),"U"),si=bn.WriteStream.prototype.hasColors();class ni{static{a(this,"M")}text(e){return e}bold(e){return si?`\x1B[1m${e}\x1B[22m`:e.toLocaleUpperCase()}indentText({text:e,spaces:u}){return e.replace(/^/gm," ".repeat(u))}heading(e){return this.bold(e)}section({title:e,body:u,indentBody:s=2}){return`${(e?`${this.heading(e)}
`:"")+(u?this.indentText({text:this.render(u),spaces:s}):"")}
`}table({tableData:e,tableOptions:u,tableBreakpoints:s}){return Gr(e.map(n=>n.map(r=>this.render(r))),s?Ur(s):u)}flagParameter(e){return e===Boolean?"":e===String?"<string>":e===Number?"<number>":Array.isArray(e)?this.flagParameter(e[0]):"<value>"}flagOperator(e){return" "}flagName(e){const{flag:u,flagFormatted:s,aliasesEnabled:n,aliasFormatted:r}=e;let i="";if(r?i+=`${r}, `:n&&(i+="    "),i+=s,"placeholder"in u&&typeof u.placeholder=="string")i+=`${this.flagOperator(e)}${u.placeholder}`;else{const D=this.flagParameter("type"in u?u.type:u);D&&(i+=`${this.flagOperator(e)}${D}`)}return i}flagDefault(e){return JSON.stringify(e)}flagDescription({flag:e}){let u="description"in e?e.description??"":"";if("default"in e){let{default:s}=e;typeof s=="function"&&(s=s()),s&&(u+=` (default: ${this.flagDefault(s)})`)}return u}render(e){if(typeof e=="string")return e;if(Array.isArray(e))return e.map(u=>this.render(u)).join(`
`);if("type"in e&&this[e.type]){const u=this[e.type];if(typeof u=="function")return u.call(this,e.data)}throw new Error(`Invalid node type: ${JSON.stringify(e)}`)}}const Ct=/^[\w.-]+$/,{stringify:ee}=JSON,ri=/[|\\{}()[\]^$+*?.]/;function Ft(t){const e=[];let u,s;for(const n of t){if(s)throw new Error(`Invalid parameter: Spread parameter ${ee(s)} must be last`);const r=n[0],i=n[n.length-1];let D;if(r==="<"&&i===">"&&(D=!0,u))throw new Error(`Invalid parameter: Required parameter ${ee(n)} cannot come after optional parameter ${ee(u)}`);if(r==="["&&i==="]"&&(D=!1,u=n),D===void 0)throw new Error(`Invalid parameter: ${ee(n)}. Must be wrapped in <> (required parameter) or [] (optional parameter)`);let o=n.slice(1,-1);const c=o.slice(-3)==="...";c&&(s=n,o=o.slice(0,-3));const f=o.match(ri);if(f)throw new Error(`Invalid parameter: ${ee(n)}. Invalid character found ${ee(f[0])}`);e.push({name:o,required:D,spread:c})}return e}a(Ft,"w");function gt(t,e,u,s){for(let n=0;n<e.length;n+=1){const{name:r,required:i,spread:D}=e[n],o=Kr(r);if(o in t)throw new Error(`Invalid parameter: ${ee(r)} is used more than once.`);const c=D?u.slice(n):u[n];if(D&&(n=e.length),i&&(!c||D&&c.length===0))return console.error(`Error: Missing required parameter ${ee(r)}
`),s(),process.exit(1);t[o]=c}}a(gt,"b");function ii(t){return t===void 0||t!==!1}a(ii,"W");function Wu(t,e,u,s){const n={...e.flags},r=e.version;r&&(n.version={type:Boolean,description:"Show version"});const{help:i}=e,D=ii(i);D&&!("help"in n)&&(n.help={type:Boolean,alias:"h",description:"Show help"});const o=yu(n,s,{ignore:e.ignoreArgv}),c=a(()=>{console.log(e.version)},"f");if(r&&o.flags.version===!0)return c(),process.exit(0);const f=new ni,h=D&&i?.render?i.render:C=>f.render(C),l=a(C=>{const g=ui({...e,...C?{help:C}:{},flags:n});console.log(h(g,f))},"u");if(D&&o.flags.help===!0)return l(),process.exit(0);if(e.parameters){let{parameters:C}=e,g=o._;const y=C.indexOf("--"),B=C.slice(y+1),H=Object.create(null);if(y>-1&&B.length>0){C=C.slice(0,y);const $=o._["--"];g=g.slice(0,-$.length||void 0),gt(H,Ft(C),g,l),gt(H,Ft(B),$,l)}else gt(H,Ft(C),g,l);Object.assign(o._,H)}const p={...o,showVersion:c,showHelp:l};return typeof u=="function"&&u(p),{command:t,...p}}a(Wu,"x");function Di(t,e){const u=new Map;for(const s of e){const n=[s.options.name],{alias:r}=s.options;r&&(Array.isArray(r)?n.push(...r):n.push(r));for(const i of n){if(u.has(i))throw new Error(`Duplicate command name found: ${ee(i)}`);u.set(i,s)}}return u.get(t)}a(Di,"z");function ju(t,e,u=process.argv.slice(2)){if(!t)throw new Error("Options is required");if("name"in t&&(!t.name||!Ct.test(t.name)))throw new Error(`Invalid script name: ${ee(t.name)}`);const s=u[0];if(t.commands&&Ct.test(s)){const n=Di(s,t.commands);if(n)return Wu(n.options.name,{...n.options,parent:t},n.callback,u.slice(1))}return Wu(void 0,t,e,u)}a(ju,"Z");function oi(t,e){if(!t)throw new Error("Command options are required");const{name:u}=t;if(t.name===void 0)throw new Error("Command name is required");if(!Ct.test(u))throw new Error(`Invalid command name ${JSON.stringify(u)}. Command names must be one word.`);return{options:t,callback:e}}a(oi,"G");var ai=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function li(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}a(li,"getDefaultExportFromCjs");var fe={exports:{}},mt,Uu;function ci(){if(Uu)return mt;Uu=1,mt=s,s.sync=n;var t=De;function e(r,i){var D=i.pathExt!==void 0?i.pathExt:process.env.PATHEXT;if(!D||(D=D.split(";"),D.indexOf("")!==-1))return!0;for(var o=0;o<D.length;o++){var c=D[o].toLowerCase();if(c&&r.substr(-c.length).toLowerCase()===c)return!0}return!1}a(e,"checkPathExt");function u(r,i,D){return!r.isSymbolicLink()&&!r.isFile()?!1:e(i,D)}a(u,"checkStat");function s(r,i,D){t.stat(r,function(o,c){D(o,o?!1:u(c,r,i))})}a(s,"isexe");function n(r,i){return u(t.statSync(r),r,i)}return a(n,"sync"),mt}a(ci,"requireWindows");var _t,Ku;function fi(){if(Ku)return _t;Ku=1,_t=e,e.sync=u;var t=De;function e(r,i,D){t.stat(r,function(o,c){D(o,o?!1:s(c,i))})}a(e,"isexe");function u(r,i){return s(t.statSync(r),i)}a(u,"sync");function s(r,i){return r.isFile()&&n(r,i)}a(s,"checkStat");function n(r,i){var D=r.mode,o=r.uid,c=r.gid,f=i.uid!==void 0?i.uid:process.getuid&&process.getuid(),h=i.gid!==void 0?i.gid:process.getgid&&process.getgid(),l=parseInt("100",8),p=parseInt("010",8),C=parseInt("001",8),g=l|p,y=D&C||D&p&&c===h||D&l&&o===f||D&g&&f===0;return y}return a(n,"checkMode"),_t}a(fi,"requireMode");var je;process.platform==="win32"||ai.TESTING_WINDOWS?je=ci():je=fi();var hi=At;At.sync=di;function At(t,e,u){if(typeof e=="function"&&(u=e,e={}),!u){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(s,n){At(t,e||{},function(r,i){r?n(r):s(i)})})}je(t,e||{},function(s,n){s&&(s.code==="EACCES"||e&&e.ignoreErrors)&&(s=null,n=!1),u(s,n)})}a(At,"isexe$1");function di(t,e){try{return je.sync(t,e||{})}catch(u){if(e&&e.ignoreErrors||u.code==="EACCES")return!1;throw u}}a(di,"sync");const he=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",Vu=z,Ei=he?";":":",zu=hi,Yu=a(t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),"getNotFoundError"),qu=a((t,e)=>{const u=e.colon||Ei,s=t.match(/\//)||he&&t.match(/\\/)?[""]:[...he?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(u)],n=he?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",r=he?n.split(u):[""];return he&&t.indexOf(".")!==-1&&r[0]!==""&&r.unshift(""),{pathEnv:s,pathExt:r,pathExtExe:n}},"getPathInfo"),Xu=a((t,e,u)=>{typeof e=="function"&&(u=e,e={}),e||(e={});const{pathEnv:s,pathExt:n,pathExtExe:r}=qu(t,e),i=[],D=a(c=>new Promise((f,h)=>{if(c===s.length)return e.all&&i.length?f(i):h(Yu(t));const l=s[c],p=/^".*"$/.test(l)?l.slice(1,-1):l,C=Vu.join(p,t),g=!p&&/^\.[\\\/]/.test(t)?t.slice(0,2)+C:C;f(o(g,c,0))}),"step"),o=a((c,f,h)=>new Promise((l,p)=>{if(h===n.length)return l(D(f+1));const C=n[h];zu(c+C,{pathExt:r},(g,y)=>{if(!g&&y)if(e.all)i.push(c+C);else return l(c+C);return l(o(c,f,h+1))})}),"subStep");return u?D(0).then(c=>u(null,c),u):D(0)},"which$1"),pi=a((t,e)=>{e=e||{};const{pathEnv:u,pathExt:s,pathExtExe:n}=qu(t,e),r=[];for(let i=0;i<u.length;i++){const D=u[i],o=/^".*"$/.test(D)?D.slice(1,-1):D,c=Vu.join(o,t),f=!o&&/^\.[\\\/]/.test(t)?t.slice(0,2)+c:c;for(let h=0;h<s.length;h++){const l=f+s[h];try{if(zu.sync(l,{pathExt:n}))if(e.all)r.push(l);else return l}catch{}}}if(e.all&&r.length)return r;if(e.nothrow)return null;throw Yu(t)},"whichSync");var Ci=Xu;Xu.sync=pi;var yt={exports:{}};const Qu=a((t={})=>{const e=t.env||process.env;return(t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(s=>s.toUpperCase()==="PATH")||"Path"},"pathKey");yt.exports=Qu,yt.exports.default=Qu;var Fi=yt.exports;const Zu=z,gi=Ci,mi=Fi;function Ju(t,e){const u=t.options.env||process.env,s=process.cwd(),n=t.options.cwd!=null,r=n&&process.chdir!==void 0&&!process.chdir.disabled;if(r)try{process.chdir(t.options.cwd)}catch{}let i;try{i=gi.sync(t.command,{path:u[mi({env:u})],pathExt:e?Zu.delimiter:void 0})}catch{}finally{r&&process.chdir(s)}return i&&(i=Zu.resolve(n?t.options.cwd:"",i)),i}a(Ju,"resolveCommandAttempt");function _i(t){return Ju(t)||Ju(t,!0)}a(_i,"resolveCommand$1");var Ai=_i,wt={};const Rt=/([()\][%!^"`<>&|;, *?])/g;function yi(t){return t=t.replace(Rt,"^$1"),t}a(yi,"escapeCommand");function wi(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(Rt,"^$1"),e&&(t=t.replace(Rt,"^$1")),t}a(wi,"escapeArgument"),wt.command=yi,wt.argument=wi;var Ri=/^#!(.*)/;const bi=Ri;var vi=a((t="")=>{const e=t.match(bi);if(!e)return null;const[u,s]=e[0].replace(/#! ?/,"").split(" "),n=u.split("/").pop();return n==="env"?s:s?`${n} ${s}`:n},"shebangCommand$1");const bt=De,Si=vi;function Bi(t){const u=Buffer.alloc(150);let s;try{s=bt.openSync(t,"r"),bt.readSync(s,u,0,150,0),bt.closeSync(s)}catch{}return Si(u.toString())}a(Bi,"readShebang$1");var $i=Bi;const Ti=z,es=Ai,ts=wt,xi=$i,Oi=process.platform==="win32",Ni=/\.(?:com|exe)$/i,Hi=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Pi(t){t.file=es(t);const e=t.file&&xi(t.file);return e?(t.args.unshift(t.file),t.command=e,es(t)):t.file}a(Pi,"detectShebang");function Li(t){if(!Oi)return t;const e=Pi(t),u=!Ni.test(e);if(t.options.forceShell||u){const s=Hi.test(e);t.command=Ti.normalize(t.command),t.command=ts.command(t.command),t.args=t.args.map(r=>ts.argument(r,s));const n=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${n}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0}return t}a(Li,"parseNonShell");function Ii(t,e,u){e&&!Array.isArray(e)&&(u=e,e=null),e=e?e.slice(0):[],u=Object.assign({},u);const s={command:t,args:e,options:u,file:void 0,original:{command:t,args:e}};return u.shell?s:Li(s)}a(Ii,"parse$5");var ki=Ii;const vt=process.platform==="win32";function St(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}a(St,"notFoundError");function Mi(t,e){if(!vt)return;const u=t.emit;t.emit=function(s,n){if(s==="exit"){const r=us(n,e);if(r)return u.call(t,"error",r)}return u.apply(t,arguments)}}a(Mi,"hookChildProcess");function us(t,e){return vt&&t===1&&!e.file?St(e.original,"spawn"):null}a(us,"verifyENOENT");function Gi(t,e){return vt&&t===1&&!e.file?St(e.original,"spawnSync"):null}a(Gi,"verifyENOENTSync");var Wi={hookChildProcess:Mi,verifyENOENT:us,verifyENOENTSync:Gi,notFoundError:St};const ss=On,Bt=ki,$t=Wi;function ns(t,e,u){const s=Bt(t,e,u),n=ss.spawn(s.command,s.args,s.options);return $t.hookChildProcess(n,s),n}a(ns,"spawn");function ji(t,e,u){const s=Bt(t,e,u),n=ss.spawnSync(s.command,s.args,s.options);return n.error=n.error||$t.verifyENOENTSync(n.status,s),n}a(ji,"spawnSync"),fe.exports=ns,fe.exports.spawn=ns,fe.exports.sync=ji,fe.exports._parse=Bt,fe.exports._enoent=$t;var Ui=fe.exports,Ki=li(Ui);const rs=a((t,e)=>{const u={...process.env},s=["inherit","inherit","inherit"];process.send&&s.push("ipc"),e&&(e.noCache&&(u.TSX_DISABLE_CACHE="1"),e.tsconfigPath&&(u.TSX_TSCONFIG_PATH=e.tsconfigPath));const n=t.filter(r=>r!=="-i"&&r!=="--interactive").length===0;return Ki(process.execPath,["--require",Ie.resolve("./preflight.cjs"),...n?["--require",Ie.resolve("./patch-repl.cjs")]:[],mu(Nn)?"--import":"--loader",Tn(Ie.resolve("./loader.mjs")).toString(),...t],{stdio:s,env:u})},"run");var Ue={};const Vi=z,te="\\\\/",is=`[^${te}]`,ue="\\.",zi="\\+",Yi="\\?",Ke="\\/",qi="(?=.)",Ds="[^/]",Tt=`(?:${Ke}|$)`,os=`(?:^|${Ke})`,xt=`${ue}{1,2}${Tt}`,Xi=`(?!${ue})`,Qi=`(?!${os}${xt})`,Zi=`(?!${ue}{0,1}${Tt})`,Ji=`(?!${xt})`,eD=`[^.${Ke}]`,tD=`${Ds}*?`,as={DOT_LITERAL:ue,PLUS_LITERAL:zi,QMARK_LITERAL:Yi,SLASH_LITERAL:Ke,ONE_CHAR:qi,QMARK:Ds,END_ANCHOR:Tt,DOTS_SLASH:xt,NO_DOT:Xi,NO_DOTS:Qi,NO_DOT_SLASH:Zi,NO_DOTS_SLASH:Ji,QMARK_NO_DOT:eD,STAR:tD,START_ANCHOR:os},uD={...as,SLASH_LITERAL:`[${te}]`,QMARK:is,STAR:`${is}*?`,DOTS_SLASH:`${ue}{1,2}(?:[${te}]|$)`,NO_DOT:`(?!${ue})`,NO_DOTS:`(?!(?:^|[${te}])${ue}{1,2}(?:[${te}]|$))`,NO_DOT_SLASH:`(?!${ue}{0,1}(?:[${te}]|$))`,NO_DOTS_SLASH:`(?!${ue}{1,2}(?:[${te}]|$))`,QMARK_NO_DOT:`[^.${te}]`,START_ANCHOR:`(?:^|[${te}])`,END_ANCHOR:`(?:[${te}]|$)`},sD={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var Ve={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:sD,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHARS:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:Vi.sep,extglobChars(t){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${t.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(t){return t===!0?uD:as}};(function(t){const e=z,u=process.platform==="win32",{REGEX_BACKSLASH:s,REGEX_REMOVE_BACKSLASH:n,REGEX_SPECIAL_CHARS:r,REGEX_SPECIAL_CHARS_GLOBAL:i}=Ve;t.isObject=D=>D!==null&&typeof D=="object"&&!Array.isArray(D),t.hasRegexChars=D=>r.test(D),t.isRegexChar=D=>D.length===1&&t.hasRegexChars(D),t.escapeRegex=D=>D.replace(i,"\\$1"),t.toPosixSlashes=D=>D.replace(s,"/"),t.removeBackslashes=D=>D.replace(n,o=>o==="\\"?"":o),t.supportsLookbehinds=()=>{const D=process.version.slice(1).split(".").map(Number);return D.length===3&&D[0]>=9||D[0]===8&&D[1]>=10},t.isWindows=D=>D&&typeof D.windows=="boolean"?D.windows:u===!0||e.sep==="\\",t.escapeLast=(D,o,c)=>{const f=D.lastIndexOf(o,c);return f===-1?D:D[f-1]==="\\"?t.escapeLast(D,o,f-1):`${D.slice(0,f)}\\${D.slice(f)}`},t.removePrefix=(D,o={})=>{let c=D;return c.startsWith("./")&&(c=c.slice(2),o.prefix="./"),c},t.wrapOutput=(D,o={},c={})=>{const f=c.contains?"":"^",h=c.contains?"":"$";let l=`${f}(?:${D})${h}`;return o.negated===!0&&(l=`(?:^(?!${l}).*$)`),l}})(Ue);const ls=Ue,{CHAR_ASTERISK:Ot,CHAR_AT:nD,CHAR_BACKWARD_SLASH:ye,CHAR_COMMA:rD,CHAR_DOT:Nt,CHAR_EXCLAMATION_MARK:Ht,CHAR_FORWARD_SLASH:cs,CHAR_LEFT_CURLY_BRACE:Pt,CHAR_LEFT_PARENTHESES:Lt,CHAR_LEFT_SQUARE_BRACKET:iD,CHAR_PLUS:DD,CHAR_QUESTION_MARK:fs,CHAR_RIGHT_CURLY_BRACE:oD,CHAR_RIGHT_PARENTHESES:hs,CHAR_RIGHT_SQUARE_BRACKET:aD}=Ve,ds=a(t=>t===cs||t===ye,"isPathSeparator"),Es=a(t=>{t.isPrefix!==!0&&(t.depth=t.isGlobstar?1/0:1)},"depth"),lD=a((t,e)=>{const u=e||{},s=t.length-1,n=u.parts===!0||u.scanToEnd===!0,r=[],i=[],D=[];let o=t,c=-1,f=0,h=0,l=!1,p=!1,C=!1,g=!1,y=!1,B=!1,H=!1,$=!1,Q=!1,G=!1,ne=0,W,A,v={value:"",depth:0,isGlob:!1};const M=a(()=>c>=s,"eos"),F=a(()=>o.charCodeAt(c+1),"peek"),O=a(()=>(W=A,o.charCodeAt(++c)),"advance");for(;c<s;){A=O();let j;if(A===ye){H=v.backslashes=!0,A=O(),A===Pt&&(B=!0);continue}if(B===!0||A===Pt){for(ne++;M()!==!0&&(A=O());){if(A===ye){H=v.backslashes=!0,O();continue}if(A===Pt){ne++;continue}if(B!==!0&&A===Nt&&(A=O())===Nt){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,n===!0)continue;break}if(B!==!0&&A===rD){if(l=v.isBrace=!0,C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===oD&&(ne--,ne===0)){B=!1,l=v.isBrace=!0,G=!0;break}}if(n===!0)continue;break}if(A===cs){if(r.push(c),i.push(v),v={value:"",depth:0,isGlob:!1},G===!0)continue;if(W===Nt&&c===f+1){f+=2;continue}h=c+1;continue}if(u.noext!==!0&&(A===DD||A===nD||A===Ot||A===fs||A===Ht)===!0&&F()===Lt){if(C=v.isGlob=!0,g=v.isExtglob=!0,G=!0,A===Ht&&c===f&&(Q=!0),n===!0){for(;M()!==!0&&(A=O());){if(A===ye){H=v.backslashes=!0,A=O();continue}if(A===hs){C=v.isGlob=!0,G=!0;break}}continue}break}if(A===Ot){if(W===Ot&&(y=v.isGlobstar=!0),C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===fs){if(C=v.isGlob=!0,G=!0,n===!0)continue;break}if(A===iD){for(;M()!==!0&&(j=O());){if(j===ye){H=v.backslashes=!0,O();continue}if(j===aD){p=v.isBracket=!0,C=v.isGlob=!0,G=!0;break}}if(n===!0)continue;break}if(u.nonegate!==!0&&A===Ht&&c===f){$=v.negated=!0,f++;continue}if(u.noparen!==!0&&A===Lt){if(C=v.isGlob=!0,n===!0){for(;M()!==!0&&(A=O());){if(A===Lt){H=v.backslashes=!0,A=O();continue}if(A===hs){G=!0;break}}continue}break}if(C===!0){if(G=!0,n===!0)continue;break}}u.noext===!0&&(g=!1,C=!1);let T=o,re="",d="";f>0&&(re=o.slice(0,f),o=o.slice(f),h-=f),T&&C===!0&&h>0?(T=o.slice(0,h),d=o.slice(h)):C===!0?(T="",d=o):T=o,T&&T!==""&&T!=="/"&&T!==o&&ds(T.charCodeAt(T.length-1))&&(T=T.slice(0,-1)),u.unescape===!0&&(d&&(d=ls.removeBackslashes(d)),T&&H===!0&&(T=ls.removeBackslashes(T)));const E={prefix:re,input:t,start:f,base:T,glob:d,isBrace:l,isBracket:p,isGlob:C,isExtglob:g,isGlobstar:y,negated:$,negatedExtglob:Q};if(u.tokens===!0&&(E.maxDepth=0,ds(A)||i.push(v),E.tokens=i),u.parts===!0||u.tokens===!0){let j;for(let b=0;b<r.length;b++){const Z=j?j+1:f,J=r[b],V=t.slice(Z,J);u.tokens&&(b===0&&f!==0?(i[b].isPrefix=!0,i[b].value=re):i[b].value=V,Es(i[b]),E.maxDepth+=i[b].depth),(b!==0||V!=="")&&D.push(V),j=J}if(j&&j+1<t.length){const b=t.slice(j+1);D.push(b),u.tokens&&(i[i.length-1].value=b,Es(i[i.length-1]),E.maxDepth+=i[i.length-1].depth)}E.slashes=r,E.parts=D}return E},"scan$1");var cD=lD;const ze=Ve,Y=Ue,{MAX_LENGTH:Ye,POSIX_REGEX_SOURCE:fD,REGEX_NON_SPECIAL_CHARS:hD,REGEX_SPECIAL_CHARS_BACKREF:dD,REPLACEMENTS:ps}=ze,ED=a((t,e)=>{if(typeof e.expandRange=="function")return e.expandRange(...t,e);t.sort();const u=`[${t.join("-")}]`;try{new RegExp(u)}catch{return t.map(n=>Y.escapeRegex(n)).join("..")}return u},"expandRange"),de=a((t,e)=>`Missing ${t}: "${e}" - use "\\\\${e}" to match literal characters`,"syntaxError"),It=a((t,e)=>{if(typeof t!="string")throw new TypeError("Expected a string");t=ps[t]||t;const u={...e},s=typeof u.maxLength=="number"?Math.min(Ye,u.maxLength):Ye;let n=t.length;if(n>s)throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${s}`);const r={type:"bos",value:"",output:u.prepend||""},i=[r],D=u.capture?"":"?:",o=Y.isWindows(e),c=ze.globChars(o),f=ze.extglobChars(c),{DOT_LITERAL:h,PLUS_LITERAL:l,SLASH_LITERAL:p,ONE_CHAR:C,DOTS_SLASH:g,NO_DOT:y,NO_DOT_SLASH:B,NO_DOTS_SLASH:H,QMARK:$,QMARK_NO_DOT:Q,STAR:G,START_ANCHOR:ne}=c,W=a(_=>`(${D}(?:(?!${ne}${_.dot?g:h}).)*?)`,"globstar"),A=u.dot?"":y,v=u.dot?$:Q;let M=u.bash===!0?W(u):G;u.capture&&(M=`(${M})`),typeof u.noext=="boolean"&&(u.noextglob=u.noext);const F={input:t,index:-1,start:0,dot:u.dot===!0,consumed:"",output:"",prefix:"",backtrack:!1,negated:!1,brackets:0,braces:0,parens:0,quotes:0,globstar:!1,tokens:i};t=Y.removePrefix(t,F),n=t.length;const O=[],T=[],re=[];let d=r,E;const j=a(()=>F.index===n-1,"eos"),b=F.peek=(_=1)=>t[F.index+_],Z=F.advance=()=>t[++F.index]||"",J=a(()=>t.slice(F.index+1),"remaining"),V=a((_="",x=0)=>{F.consumed+=_,F.index+=x},"consume"),Ne=a(_=>{F.output+=_.output!=null?_.output:_.value,V(_.value)},"append"),yn=a(()=>{let _=1;for(;b()==="!"&&(b(2)!=="("||b(3)==="?");)Z(),F.start++,_++;return _%2===0?!1:(F.negated=!0,F.start++,!0)},"negate"),He=a(_=>{F[_]++,re.push(_)},"increment"),ie=a(_=>{F[_]--,re.pop()},"decrement"),R=a(_=>{if(d.type==="globstar"){const x=F.braces>0&&(_.type==="comma"||_.type==="brace"),m=_.extglob===!0||O.length&&(_.type==="pipe"||_.type==="paren");_.type!=="slash"&&_.type!=="paren"&&!x&&!m&&(F.output=F.output.slice(0,-d.output.length),d.type="star",d.value="*",d.output=M,F.output+=d.output)}if(O.length&&_.type!=="paren"&&(O[O.length-1].inner+=_.value),(_.value||_.output)&&Ne(_),d&&d.type==="text"&&_.type==="text"){d.value+=_.value,d.output=(d.output||"")+_.value;return}_.prev=d,i.push(_),d=_},"push"),Pe=a((_,x)=>{const m={...f[x],conditions:1,inner:""};m.prev=d,m.parens=F.parens,m.output=F.output;const w=(u.capture?"(":"")+m.open;He("parens"),R({type:_,value:x,output:F.output?"":C}),R({type:"paren",extglob:!0,value:Z(),output:w}),O.push(m)},"extglobOpen"),wn=a(_=>{let x=_.close+(u.capture?")":""),m;if(_.type==="negate"){let w=M;if(_.inner&&_.inner.length>1&&_.inner.includes("/")&&(w=W(u)),(w!==M||j()||/^\)+$/.test(J()))&&(x=_.close=`)$))${w}`),_.inner.includes("*")&&(m=J())&&/^\.[^\\/.]+$/.test(m)){const N=It(m,{...e,fastpaths:!1}).output;x=_.close=`)${N})${w})`}_.prev.type==="bos"&&(F.negatedExtglob=!0)}R({type:"paren",extglob:!0,value:E,output:x}),ie("parens")},"extglobClose");if(u.fastpaths!==!1&&!/(^[*!]|[/()[\]{}"])/.test(t)){let _=!1,x=t.replace(dD,(m,w,N,U,L,at)=>U==="\\"?(_=!0,m):U==="?"?w?w+U+(L?$.repeat(L.length):""):at===0?v+(L?$.repeat(L.length):""):$.repeat(N.length):U==="."?h.repeat(N.length):U==="*"?w?w+U+(L?M:""):M:w?m:`\\${m}`);return _===!0&&(u.unescape===!0?x=x.replace(/\\/g,""):x=x.replace(/\\+/g,m=>m.length%2===0?"\\\\":m?"\\":"")),x===t&&u.contains===!0?(F.output=t,F):(F.output=Y.wrapOutput(x,F,e),F)}for(;!j();){if(E=Z(),E==="\0")continue;if(E==="\\"){const m=b();if(m==="/"&&u.bash!==!0||m==="."||m===";")continue;if(!m){E+="\\",R({type:"text",value:E});continue}const w=/^\\+/.exec(J());let N=0;if(w&&w[0].length>2&&(N=w[0].length,F.index+=N,N%2!==0&&(E+="\\")),u.unescape===!0?E=Z():E+=Z(),F.brackets===0){R({type:"text",value:E});continue}}if(F.brackets>0&&(E!=="]"||d.value==="["||d.value==="[^")){if(u.posix!==!1&&E===":"){const m=d.value.slice(1);if(m.includes("[")&&(d.posix=!0,m.includes(":"))){const w=d.value.lastIndexOf("["),N=d.value.slice(0,w),U=d.value.slice(w+2),L=fD[U];if(L){d.value=N+L,F.backtrack=!0,Z(),!r.output&&i.indexOf(d)===1&&(r.output=C);continue}}}(E==="["&&b()!==":"||E==="-"&&b()==="]")&&(E=`\\${E}`),E==="]"&&(d.value==="["||d.value==="[^")&&(E=`\\${E}`),u.posix===!0&&E==="!"&&d.value==="["&&(E="^"),d.value+=E,Ne({value:E});continue}if(F.quotes===1&&E!=='"'){E=Y.escapeRegex(E),d.value+=E,Ne({value:E});continue}if(E==='"'){F.quotes=F.quotes===1?0:1,u.keepQuotes===!0&&R({type:"text",value:E});continue}if(E==="("){He("parens"),R({type:"paren",value:E});continue}if(E===")"){if(F.parens===0&&u.strictBrackets===!0)throw new SyntaxError(de("opening","("));const m=O[O.length-1];if(m&&F.parens===m.parens+1){wn(O.pop());continue}R({type:"paren",value:E,output:F.parens?")":"\\)"}),ie("parens");continue}if(E==="["){if(u.nobracket===!0||!J().includes("]")){if(u.nobracket!==!0&&u.strictBrackets===!0)throw new SyntaxError(de("closing","]"));E=`\\${E}`}else He("brackets");R({type:"bracket",value:E});continue}if(E==="]"){if(u.nobracket===!0||d&&d.type==="bracket"&&d.value.length===1){R({type:"text",value:E,output:`\\${E}`});continue}if(F.brackets===0){if(u.strictBrackets===!0)throw new SyntaxError(de("opening","["));R({type:"text",value:E,output:`\\${E}`});continue}ie("brackets");const m=d.value.slice(1);if(d.posix!==!0&&m[0]==="^"&&!m.includes("/")&&(E=`/${E}`),d.value+=E,Ne({value:E}),u.literalBrackets===!1||Y.hasRegexChars(m))continue;const w=Y.escapeRegex(d.value);if(F.output=F.output.slice(0,-d.value.length),u.literalBrackets===!0){F.output+=w,d.value=w;continue}d.value=`(${D}${w}|${d.value})`,F.output+=d.value;continue}if(E==="{"&&u.nobrace!==!0){He("braces");const m={type:"brace",value:E,output:"(",outputIndex:F.output.length,tokensIndex:F.tokens.length};T.push(m),R(m);continue}if(E==="}"){const m=T[T.length-1];if(u.nobrace===!0||!m){R({type:"text",value:E,output:E});continue}let w=")";if(m.dots===!0){const N=i.slice(),U=[];for(let L=N.length-1;L>=0&&(i.pop(),N[L].type!=="brace");L--)N[L].type!=="dots"&&U.unshift(N[L].value);w=ED(U,u),F.backtrack=!0}if(m.comma!==!0&&m.dots!==!0){const N=F.output.slice(0,m.outputIndex),U=F.tokens.slice(m.tokensIndex);m.value=m.output="\\{",E=w="\\}",F.output=N;for(const L of U)F.output+=L.output||L.value}R({type:"brace",value:E,output:w}),ie("braces"),T.pop();continue}if(E==="|"){O.length>0&&O[O.length-1].conditions++,R({type:"text",value:E});continue}if(E===","){let m=E;const w=T[T.length-1];w&&re[re.length-1]==="braces"&&(w.comma=!0,m="|"),R({type:"comma",value:E,output:m});continue}if(E==="/"){if(d.type==="dot"&&F.index===F.start+1){F.start=F.index+1,F.consumed="",F.output="",i.pop(),d=r;continue}R({type:"slash",value:E,output:p});continue}if(E==="."){if(F.braces>0&&d.type==="dot"){d.value==="."&&(d.output=h);const m=T[T.length-1];d.type="dots",d.output+=E,d.value+=E,m.dots=!0;continue}if(F.braces+F.parens===0&&d.type!=="bos"&&d.type!=="slash"){R({type:"text",value:E,output:h});continue}R({type:"dot",value:E,output:h});continue}if(E==="?"){if(!(d&&d.value==="(")&&u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Pe("qmark",E);continue}if(d&&d.type==="paren"){const w=b();let N=E;if(w==="<"&&!Y.supportsLookbehinds())throw new Error("Node.js v10 or higher is required for regex lookbehinds");(d.value==="("&&!/[!=<:]/.test(w)||w==="<"&&!/<([!=]|\w+>)/.test(J()))&&(N=`\\${E}`),R({type:"text",value:E,output:N});continue}if(u.dot!==!0&&(d.type==="slash"||d.type==="bos")){R({type:"qmark",value:E,output:Q});continue}R({type:"qmark",value:E,output:$});continue}if(E==="!"){if(u.noextglob!==!0&&b()==="("&&(b(2)!=="?"||!/[!=<:]/.test(b(3)))){Pe("negate",E);continue}if(u.nonegate!==!0&&F.index===0){yn();continue}}if(E==="+"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){Pe("plus",E);continue}if(d&&d.value==="("||u.regex===!1){R({type:"plus",value:E,output:l});continue}if(d&&(d.type==="bracket"||d.type==="paren"||d.type==="brace")||F.parens>0){R({type:"plus",value:E});continue}R({type:"plus",value:l});continue}if(E==="@"){if(u.noextglob!==!0&&b()==="("&&b(2)!=="?"){R({type:"at",extglob:!0,value:E,output:""});continue}R({type:"text",value:E});continue}if(E!=="*"){(E==="$"||E==="^")&&(E=`\\${E}`);const m=hD.exec(J());m&&(E+=m[0],F.index+=m[0].length),R({type:"text",value:E});continue}if(d&&(d.type==="globstar"||d.star===!0)){d.type="star",d.star=!0,d.value+=E,d.output=M,F.backtrack=!0,F.globstar=!0,V(E);continue}let _=J();if(u.noextglob!==!0&&/^\([^?]/.test(_)){Pe("star",E);continue}if(d.type==="star"){if(u.noglobstar===!0){V(E);continue}const m=d.prev,w=m.prev,N=m.type==="slash"||m.type==="bos",U=w&&(w.type==="star"||w.type==="globstar");if(u.bash===!0&&(!N||_[0]&&_[0]!=="/")){R({type:"star",value:E,output:""});continue}const L=F.braces>0&&(m.type==="comma"||m.type==="brace"),at=O.length&&(m.type==="pipe"||m.type==="paren");if(!N&&m.type!=="paren"&&!L&&!at){R({type:"star",value:E,output:""});continue}for(;_.slice(0,3)==="/**";){const Le=t[F.index+4];if(Le&&Le!=="/")break;_=_.slice(3),V("/**",3)}if(m.type==="bos"&&j()){d.type="globstar",d.value+=E,d.output=W(u),F.output=d.output,F.globstar=!0,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&!U&&j()){F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=W(u)+(u.strictSlashes?")":"|$)"),d.value+=E,F.globstar=!0,F.output+=m.output+d.output,V(E);continue}if(m.type==="slash"&&m.prev.type!=="bos"&&_[0]==="/"){const Le=_[1]!==void 0?"|$":"";F.output=F.output.slice(0,-(m.output+d.output).length),m.output=`(?:${m.output}`,d.type="globstar",d.output=`${W(u)}${p}|${p}${Le})`,d.value+=E,F.output+=m.output+d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}if(m.type==="bos"&&_[0]==="/"){d.type="globstar",d.value+=E,d.output=`(?:^|${p}|${W(u)}${p})`,F.output=d.output,F.globstar=!0,V(E+Z()),R({type:"slash",value:"/",output:""});continue}F.output=F.output.slice(0,-d.output.length),d.type="globstar",d.output=W(u),d.value+=E,F.output+=d.output,F.globstar=!0,V(E);continue}const x={type:"star",value:E,output:M};if(u.bash===!0){x.output=".*?",(d.type==="bos"||d.type==="slash")&&(x.output=A+x.output),R(x);continue}if(d&&(d.type==="bracket"||d.type==="paren")&&u.regex===!0){x.output=E,R(x);continue}(F.index===F.start||d.type==="slash"||d.type==="dot")&&(d.type==="dot"?(F.output+=B,d.output+=B):u.dot===!0?(F.output+=H,d.output+=H):(F.output+=A,d.output+=A),b()!=="*"&&(F.output+=C,d.output+=C)),R(x)}for(;F.brackets>0;){if(u.strictBrackets===!0)throw new SyntaxError(de("closing","]"));F.output=Y.escapeLast(F.output,"["),ie("brackets")}for(;F.parens>0;){if(u.strictBrackets===!0)throw new SyntaxError(de("closing",")"));F.output=Y.escapeLast(F.output,"("),ie("parens")}for(;F.braces>0;){if(u.strictBrackets===!0)throw new SyntaxError(de("closing","}"));F.output=Y.escapeLast(F.output,"{"),ie("braces")}if(u.strictSlashes!==!0&&(d.type==="star"||d.type==="bracket")&&R({type:"maybe_slash",value:"",output:`${p}?`}),F.backtrack===!0){F.output="";for(const _ of F.tokens)F.output+=_.output!=null?_.output:_.value,_.suffix&&(F.output+=_.suffix)}return F},"parse$3");It.fastpaths=(t,e)=>{const u={...e},s=typeof u.maxLength=="number"?Math.min(Ye,u.maxLength):Ye,n=t.length;if(n>s)throw new SyntaxError(`Input length: ${n}, exceeds maximum allowed length: ${s}`);t=ps[t]||t;const r=Y.isWindows(e),{DOT_LITERAL:i,SLASH_LITERAL:D,ONE_CHAR:o,DOTS_SLASH:c,NO_DOT:f,NO_DOTS:h,NO_DOTS_SLASH:l,STAR:p,START_ANCHOR:C}=ze.globChars(r),g=u.dot?h:f,y=u.dot?l:f,B=u.capture?"":"?:",H={negated:!1,prefix:""};let $=u.bash===!0?".*?":p;u.capture&&($=`(${$})`);const Q=a(A=>A.noglobstar===!0?$:`(${B}(?:(?!${C}${A.dot?c:i}).)*?)`,"globstar"),G=a(A=>{switch(A){case"*":return`${g}${o}${$}`;case".*":return`${i}${o}${$}`;case"*.*":return`${g}${$}${i}${o}${$}`;case"*/*":return`${g}${$}${D}${o}${y}${$}`;case"**":return g+Q(u);case"**/*":return`(?:${g}${Q(u)}${D})?${y}${o}${$}`;case"**/*.*":return`(?:${g}${Q(u)}${D})?${y}${$}${i}${o}${$}`;case"**/.*":return`(?:${g}${Q(u)}${D})?${i}${o}${$}`;default:{const v=/^(.*?)\.(\w+)$/.exec(A);if(!v)return;const M=G(v[1]);return M?M+i+v[2]:void 0}}},"create"),ne=Y.removePrefix(t,H);let W=G(ne);return W&&u.strictSlashes!==!0&&(W+=`${D}?`),W};var pD=It;const CD=z,FD=cD,kt=pD,Mt=Ue,gD=Ve,mD=a(t=>t&&typeof t=="object"&&!Array.isArray(t),"isObject$1"),P=a((t,e,u=!1)=>{if(Array.isArray(t)){const f=t.map(l=>P(l,e,u));return a(l=>{for(const p of f){const C=p(l);if(C)return C}return!1},"arrayMatcher")}const s=mD(t)&&t.tokens&&t.input;if(t===""||typeof t!="string"&&!s)throw new TypeError("Expected pattern to be a non-empty string");const n=e||{},r=Mt.isWindows(e),i=s?P.compileRe(t,e):P.makeRe(t,e,!1,!0),D=i.state;delete i.state;let o=a(()=>!1,"isIgnored");if(n.ignore){const f={...e,ignore:null,onMatch:null,onResult:null};o=P(n.ignore,f,u)}const c=a((f,h=!1)=>{const{isMatch:l,match:p,output:C}=P.test(f,i,e,{glob:t,posix:r}),g={glob:t,state:D,regex:i,posix:r,input:f,output:C,match:p,isMatch:l};return typeof n.onResult=="function"&&n.onResult(g),l===!1?(g.isMatch=!1,h?g:!1):o(f)?(typeof n.onIgnore=="function"&&n.onIgnore(g),g.isMatch=!1,h?g:!1):(typeof n.onMatch=="function"&&n.onMatch(g),h?g:!0)},"matcher");return u&&(c.state=D),c},"picomatch$3");P.test=(t,e,u,{glob:s,posix:n}={})=>{if(typeof t!="string")throw new TypeError("Expected input to be a string");if(t==="")return{isMatch:!1,output:""};const r=u||{},i=r.format||(n?Mt.toPosixSlashes:null);let D=t===s,o=D&&i?i(t):t;return D===!1&&(o=i?i(t):t,D=o===s),(D===!1||r.capture===!0)&&(r.matchBase===!0||r.basename===!0?D=P.matchBase(t,e,u,n):D=e.exec(o)),{isMatch:!!D,match:D,output:o}},P.matchBase=(t,e,u,s=Mt.isWindows(u))=>(e instanceof RegExp?e:P.makeRe(e,u)).test(CD.basename(t)),P.isMatch=(t,e,u)=>P(e,u)(t),P.parse=(t,e)=>Array.isArray(t)?t.map(u=>P.parse(u,e)):kt(t,{...e,fastpaths:!1}),P.scan=(t,e)=>FD(t,e),P.compileRe=(t,e,u=!1,s=!1)=>{if(u===!0)return t.output;const n=e||{},r=n.contains?"":"^",i=n.contains?"":"$";let D=`${r}(?:${t.output})${i}`;t&&t.negated===!0&&(D=`^(?!${D}).*$`);const o=P.toRegex(D,e);return s===!0&&(o.state=t),o},P.makeRe=(t,e={},u=!1,s=!1)=>{if(!t||typeof t!="string")throw new TypeError("Expected a non-empty string");let n={negated:!1,fastpaths:!0};return e.fastpaths!==!1&&(t[0]==="."||t[0]==="*")&&(n.output=kt.fastpaths(t,e)),n.output||(n=kt(t,e)),P.compileRe(n,e,u,s)},P.toRegex=(t,e)=>{try{const u=e||{};return new RegExp(t,u.flags||(u.nocase?"i":""))}catch(u){if(e&&e.debug===!0)throw u;return/$^/}},P.constants=gD;var _D=P,Cs=_D;const we=De,{Readable:AD}=In,Re=z,{promisify:qe}=ge,Gt=Cs,yD=qe(we.readdir),wD=qe(we.stat),Fs=qe(we.lstat),RD=qe(we.realpath),bD="!",gs="READDIRP_RECURSIVE_ERROR",vD=new Set(["ENOENT","EPERM","EACCES","ELOOP",gs]),Wt="files",ms="directories",Xe="files_directories",Qe="all",_s=[Wt,ms,Xe,Qe],SD=a(t=>vD.has(t.code),"isNormalFlowError"),[As,BD]=process.versions.node.split(".").slice(0,2).map(t=>Number.parseInt(t,10)),$D=process.platform==="win32"&&(As>10||As===10&&BD>=5),ys=a(t=>{if(t!==void 0){if(typeof t=="function")return t;if(typeof t=="string"){const e=Gt(t.trim());return u=>e(u.basename)}if(Array.isArray(t)){const e=[],u=[];for(const s of t){const n=s.trim();n.charAt(0)===bD?u.push(Gt(n.slice(1))):e.push(Gt(n))}return u.length>0?e.length>0?s=>e.some(n=>n(s.basename))&&!u.some(n=>n(s.basename)):s=>!u.some(n=>n(s.basename)):s=>e.some(n=>n(s.basename))}}},"normalizeFilter");class ot extends AD{static{a(this,"ReaddirpStream")}static get defaultOptions(){return{root:".",fileFilter:a(e=>!0,"fileFilter"),directoryFilter:a(e=>!0,"directoryFilter"),type:Wt,lstat:!1,depth:2147483648,alwaysStat:!1}}constructor(e={}){super({objectMode:!0,autoDestroy:!0,highWaterMark:e.highWaterMark||4096});const u={...ot.defaultOptions,...e},{root:s,type:n}=u;this._fileFilter=ys(u.fileFilter),this._directoryFilter=ys(u.directoryFilter);const r=u.lstat?Fs:wD;$D?this._stat=i=>r(i,{bigint:!0}):this._stat=r,this._maxDepth=u.depth,this._wantsDir=[ms,Xe,Qe].includes(n),this._wantsFile=[Wt,Xe,Qe].includes(n),this._wantsEverything=n===Qe,this._root=Re.resolve(s),this._isDirent="Dirent"in we&&!u.alwaysStat,this._statsProp=this._isDirent?"dirent":"stats",this._rdOptions={encoding:"utf8",withFileTypes:this._isDirent},this.parents=[this._exploreDir(s,1)],this.reading=!1,this.parent=void 0}async _read(e){if(!this.reading){this.reading=!0;try{for(;!this.destroyed&&e>0;){const{path:u,depth:s,files:n=[]}=this.parent||{};if(n.length>0){const r=n.splice(0,e).map(i=>this._formatEntry(i,u));for(const i of await Promise.all(r)){if(this.destroyed)return;const D=await this._getEntryType(i);D==="directory"&&this._directoryFilter(i)?(s<=this._maxDepth&&this.parents.push(this._exploreDir(i.fullPath,s+1)),this._wantsDir&&(this.push(i),e--)):(D==="file"||this._includeAsFile(i))&&this._fileFilter(i)&&this._wantsFile&&(this.push(i),e--)}}else{const r=this.parents.pop();if(!r){this.push(null);break}if(this.parent=await r,this.destroyed)return}}}catch(u){this.destroy(u)}finally{this.reading=!1}}}async _exploreDir(e,u){let s;try{s=await yD(e,this._rdOptions)}catch(n){this._onError(n)}return{files:s,depth:u,path:e}}async _formatEntry(e,u){let s;try{const n=this._isDirent?e.name:e,r=Re.resolve(Re.join(u,n));s={path:Re.relative(this._root,r),fullPath:r,basename:n},s[this._statsProp]=this._isDirent?e:await this._stat(r)}catch(n){this._onError(n)}return s}_onError(e){SD(e)&&!this.destroyed?this.emit("warn",e):this.destroy(e)}async _getEntryType(e){const u=e&&e[this._statsProp];if(u){if(u.isFile())return"file";if(u.isDirectory())return"directory";if(u&&u.isSymbolicLink()){const s=e.fullPath;try{const n=await RD(s),r=await Fs(n);if(r.isFile())return"file";if(r.isDirectory()){const i=n.length;if(s.startsWith(n)&&s.substr(i,1)===Re.sep){const D=new Error(`Circular symlink detected: "${s}" points to "${n}"`);return D.code=gs,this._onError(D)}return"directory"}}catch(n){this._onError(n)}}}}_includeAsFile(e){const u=e&&e[this._statsProp];return u&&this._wantsEverything&&!u.isDirectory()}}const Ee=a((t,e={})=>{let u=e.entryType||e.type;if(u==="both"&&(u=Xe),u&&(e.type=u),t){if(typeof t!="string")throw new TypeError("readdirp: root argument must be a string. Usage: readdirp(root, options)");if(u&&!_s.includes(u))throw new Error(`readdirp: Invalid type passed. Use one of ${_s.join(", ")}`)}else throw new Error("readdirp: root argument is required. Usage: readdirp(root, options)");return e.root=t,new ot(e)},"readdirp$1"),TD=a((t,e={})=>new Promise((u,s)=>{const n=[];Ee(t,e).on("data",r=>n.push(r)).on("end",()=>u(n)).on("error",r=>s(r))}),"readdirpPromise");Ee.promise=TD,Ee.ReaddirpStream=ot,Ee.default=Ee;var xD=Ee,jt={exports:{}};/*!
 * normalize-path <https://github.com/jonschlinkert/normalize-path>
 *
 * Copyright (c) 2014-2018, Jon Schlinkert.
 * Released under the MIT License.
 */var ws=a(function(t,e){if(typeof t!="string")throw new TypeError("expected path to be a string");if(t==="\\"||t==="/")return"/";var u=t.length;if(u<=1)return t;var s="";if(u>4&&t[3]==="\\"){var n=t[2];(n==="?"||n===".")&&t.slice(0,2)==="\\\\"&&(t=t.slice(2),s="//")}var r=t.split(/[/\\]+/);return e!==!1&&r[r.length-1]===""&&r.pop(),s+r.join("/")},"normalizePath$2"),OD=jt.exports;Object.defineProperty(OD,"__esModule",{value:!0});const Rs=Cs,ND=ws,bs="!",HD={returnIndex:!1},PD=a(t=>Array.isArray(t)?t:[t],"arrify$1"),LD=a((t,e)=>{if(typeof t=="function")return t;if(typeof t=="string"){const u=Rs(t,e);return s=>t===s||u(s)}return t instanceof RegExp?u=>t.test(u):u=>!1},"createPattern"),vs=a((t,e,u,s)=>{const n=Array.isArray(u),r=n?u[0]:u;if(!n&&typeof r!="string")throw new TypeError("anymatch: second argument must be a string: got "+Object.prototype.toString.call(r));const i=ND(r,!1);for(let o=0;o<e.length;o++){const c=e[o];if(c(i))return s?-1:!1}const D=n&&[i].concat(u.slice(1));for(let o=0;o<t.length;o++){const c=t[o];if(n?c(...D):c(i))return s?o:!0}return s?-1:!1},"matchPatterns"),Ut=a((t,e,u=HD)=>{if(t==null)throw new TypeError("anymatch: specify first argument");const s=typeof u=="boolean"?{returnIndex:u}:u,n=s.returnIndex||!1,r=PD(t),i=r.filter(o=>typeof o=="string"&&o.charAt(0)===bs).map(o=>o.slice(1)).map(o=>Rs(o,s)),D=r.filter(o=>typeof o!="string"||typeof o=="string"&&o.charAt(0)!==bs).map(o=>LD(o,s));return e==null?(o,c=!1)=>vs(D,i,o,typeof c=="boolean"?c:!1):vs(D,i,e,n)},"anymatch$1");Ut.default=Ut,jt.exports=Ut;var ID=jt.exports;/*!
 * is-extglob <https://github.com/jonschlinkert/is-extglob>
 *
 * Copyright (c) 2014-2016, Jon Schlinkert.
 * Licensed under the MIT License.
 */var kD=a(function(e){if(typeof e!="string"||e==="")return!1;for(var u;u=/(\\).|([@?!+*]\(.*\))/g.exec(e);){if(u[2])return!0;e=e.slice(u.index+u[0].length)}return!1},"isExtglob");/*!
 * is-glob <https://github.com/jonschlinkert/is-glob>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */var MD=kD,Ss={"{":"}","(":")","[":"]"},GD=a(function(t){if(t[0]==="!")return!0;for(var e=0,u=-2,s=-2,n=-2,r=-2,i=-2;e<t.length;){if(t[e]==="*"||t[e+1]==="?"&&/[\].+)]/.test(t[e])||s!==-1&&t[e]==="["&&t[e+1]!=="]"&&(s<e&&(s=t.indexOf("]",e)),s>e&&(i===-1||i>s||(i=t.indexOf("\\",e),i===-1||i>s)))||n!==-1&&t[e]==="{"&&t[e+1]!=="}"&&(n=t.indexOf("}",e),n>e&&(i=t.indexOf("\\",e),i===-1||i>n))||r!==-1&&t[e]==="("&&t[e+1]==="?"&&/[:!=]/.test(t[e+2])&&t[e+3]!==")"&&(r=t.indexOf(")",e),r>e&&(i=t.indexOf("\\",e),i===-1||i>r))||u!==-1&&t[e]==="("&&t[e+1]!=="|"&&(u<e&&(u=t.indexOf("|",e)),u!==-1&&t[u+1]!==")"&&(r=t.indexOf(")",u),r>u&&(i=t.indexOf("\\",u),i===-1||i>r))))return!0;if(t[e]==="\\"){var D=t[e+1];e+=2;var o=Ss[D];if(o){var c=t.indexOf(o,e);c!==-1&&(e=c+1)}if(t[e]==="!")return!0}else e++}return!1},"strictCheck"),WD=a(function(t){if(t[0]==="!")return!0;for(var e=0;e<t.length;){if(/[*?{}()[\]]/.test(t[e]))return!0;if(t[e]==="\\"){var u=t[e+1];e+=2;var s=Ss[u];if(s){var n=t.indexOf(s,e);n!==-1&&(e=n+1)}if(t[e]==="!")return!0}else e++}return!1},"relaxedCheck"),Bs=a(function(e,u){if(typeof e!="string"||e==="")return!1;if(MD(e))return!0;var s=GD;return u&&u.strict===!1&&(s=WD),s(e)},"isGlob"),jD=Bs,UD=z.posix.dirname,KD=_u.platform()==="win32",Kt="/",VD=/\\/g,zD=/[\{\[].*[\}\]]$/,YD=/(^|[^\\])([\{\[]|\([^\)]+$)/,qD=/\\([\!\*\?\|\[\]\(\)\{\}])/g,XD=a(function(e,u){var s=Object.assign({flipBackslashes:!0},u);s.flipBackslashes&&KD&&e.indexOf(Kt)<0&&(e=e.replace(VD,Kt)),zD.test(e)&&(e+=Kt),e+="a";do e=UD(e);while(jD(e)||YD.test(e));return e.replace(qD,"$1")},"globParent"),Ze={};(function(t){t.isInteger=e=>typeof e=="number"?Number.isInteger(e):typeof e=="string"&&e.trim()!==""?Number.isInteger(Number(e)):!1,t.find=(e,u)=>e.nodes.find(s=>s.type===u),t.exceedsLimit=(e,u,s=1,n)=>n===!1||!t.isInteger(e)||!t.isInteger(u)?!1:(Number(u)-Number(e))/Number(s)>=n,t.escapeNode=(e,u=0,s)=>{let n=e.nodes[u];n&&(s&&n.type===s||n.type==="open"||n.type==="close")&&n.escaped!==!0&&(n.value="\\"+n.value,n.escaped=!0)},t.encloseBrace=e=>e.type!=="brace"||e.commas>>0+e.ranges>>0?!1:(e.invalid=!0,!0),t.isInvalidBrace=e=>e.type!=="brace"?!1:e.invalid===!0||e.dollar?!0:!(e.commas>>0+e.ranges>>0)||e.open!==!0||e.close!==!0?(e.invalid=!0,!0):!1,t.isOpenOrClose=e=>e.type==="open"||e.type==="close"?!0:e.open===!0||e.close===!0,t.reduce=e=>e.reduce((u,s)=>(s.type==="text"&&u.push(s.value),s.type==="range"&&(s.type="text"),u),[]),t.flatten=(...e)=>{const u=[],s=a(n=>{for(let r=0;r<n.length;r++){let i=n[r];Array.isArray(i)?s(i):i!==void 0&&u.push(i)}return u},"flat");return s(e),u}})(Ze);const $s=Ze;var Vt=a((t,e={})=>{let u=a((s,n={})=>{let r=e.escapeInvalid&&$s.isInvalidBrace(n),i=s.invalid===!0&&e.escapeInvalid===!0,D="";if(s.value)return(r||i)&&$s.isOpenOrClose(s)?"\\"+s.value:s.value;if(s.value)return s.value;if(s.nodes)for(let o of s.nodes)D+=u(o);return D},"stringify");return u(t)},"stringify$4");/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var QD=a(function(t){return typeof t=="number"?t-t===0:typeof t=="string"&&t.trim()!==""?Number.isFinite?Number.isFinite(+t):isFinite(+t):!1},"isNumber$2");/*!
 * to-regex-range <https://github.com/micromatch/to-regex-range>
 *
 * Copyright (c) 2015-present, Jon Schlinkert.
 * Released under the MIT License.
 */const Ts=QD,ae=a((t,e,u)=>{if(Ts(t)===!1)throw new TypeError("toRegexRange: expected the first argument to be a number");if(e===void 0||t===e)return String(t);if(Ts(e)===!1)throw new TypeError("toRegexRange: expected the second argument to be a number.");let s={relaxZeros:!0,...u};typeof s.strictZeros=="boolean"&&(s.relaxZeros=s.strictZeros===!1);let n=String(s.relaxZeros),r=String(s.shorthand),i=String(s.capture),D=String(s.wrap),o=t+":"+e+"="+n+r+i+D;if(ae.cache.hasOwnProperty(o))return ae.cache[o].result;let c=Math.min(t,e),f=Math.max(t,e);if(Math.abs(c-f)===1){let g=t+"|"+e;return s.capture?`(${g})`:s.wrap===!1?g:`(?:${g})`}let h=Ls(t)||Ls(e),l={min:t,max:e,a:c,b:f},p=[],C=[];if(h&&(l.isPadded=h,l.maxLen=String(l.max).length),c<0){let g=f<0?Math.abs(f):1;C=xs(g,Math.abs(c),l,s),c=l.a=0}return f>=0&&(p=xs(c,f,l,s)),l.negatives=C,l.positives=p,l.result=ZD(C,p),s.capture===!0?l.result=`(${l.result})`:s.wrap!==!1&&p.length+C.length>1&&(l.result=`(?:${l.result})`),ae.cache[o]=l,l.result},"toRegexRange$1");function ZD(t,e,u){let s=zt(t,e,"-",!1)||[],n=zt(e,t,"",!1)||[],r=zt(t,e,"-?",!0)||[];return s.concat(r).concat(n).join("|")}a(ZD,"collatePatterns");function JD(t,e){let u=1,s=1,n=Ns(t,u),r=new Set([e]);for(;t<=n&&n<=e;)r.add(n),u+=1,n=Ns(t,u);for(n=Hs(e+1,s)-1;t<n&&n<=e;)r.add(n),s+=1,n=Hs(e+1,s)-1;return r=[...r],r.sort(uo),r}a(JD,"splitToRanges");function eo(t,e,u){if(t===e)return{pattern:t,count:[],digits:0};let s=to(t,e),n=s.length,r="",i=0;for(let D=0;D<n;D++){let[o,c]=s[D];o===c?r+=o:o!=="0"||c!=="9"?r+=so(o,c):i++}return i&&(r+=u.shorthand===!0?"\\d":"[0-9]"),{pattern:r,count:[i],digits:n}}a(eo,"rangeToPattern");function xs(t,e,u,s){let n=JD(t,e),r=[],i=t,D;for(let o=0;o<n.length;o++){let c=n[o],f=eo(String(i),String(c),s),h="";if(!u.isPadded&&D&&D.pattern===f.pattern){D.count.length>1&&D.count.pop(),D.count.push(f.count[0]),D.string=D.pattern+Ps(D.count),i=c+1;continue}u.isPadded&&(h=no(c,u,s)),f.string=h+f.pattern+Ps(f.count),r.push(f),i=c+1,D=f}return r}a(xs,"splitToPatterns");function zt(t,e,u,s,n){let r=[];for(let i of t){let{string:D}=i;!s&&!Os(e,"string",D)&&r.push(u+D),s&&Os(e,"string",D)&&r.push(u+D)}return r}a(zt,"filterPatterns");function to(t,e){let u=[];for(let s=0;s<t.length;s++)u.push([t[s],e[s]]);return u}a(to,"zip");function uo(t,e){return t>e?1:e>t?-1:0}a(uo,"compare");function Os(t,e,u){return t.some(s=>s[e]===u)}a(Os,"contains");function Ns(t,e){return Number(String(t).slice(0,-e)+"9".repeat(e))}a(Ns,"countNines");function Hs(t,e){return t-t%Math.pow(10,e)}a(Hs,"countZeros");function Ps(t){let[e=0,u=""]=t;return u||e>1?`{${e+(u?","+u:"")}}`:""}a(Ps,"toQuantifier");function so(t,e,u){return`[${t}${e-t===1?"":"-"}${e}]`}a(so,"toCharacterClass");function Ls(t){return/^-?(0+)\d/.test(t)}a(Ls,"hasPadding");function no(t,e,u){if(!e.isPadded)return t;let s=Math.abs(e.maxLen-String(t).length),n=u.relaxZeros!==!1;switch(s){case 0:return"";case 1:return n?"0?":"0";case 2:return n?"0{0,2}":"00";default:return n?`0{0,${s}}`:`0{${s}}`}}a(no,"padZeros"),ae.cache={},ae.clearCache=()=>ae.cache={};var ro=ae;/*!
 * fill-range <https://github.com/jonschlinkert/fill-range>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Licensed under the MIT License.
 */const io=ge,Is=ro,ks=a(t=>t!==null&&typeof t=="object"&&!Array.isArray(t),"isObject"),Do=a(t=>e=>t===!0?Number(e):String(e),"transform"),Yt=a(t=>typeof t=="number"||typeof t=="string"&&t!=="","isValidValue"),be=a(t=>Number.isInteger(+t),"isNumber"),qt=a(t=>{let e=`${t}`,u=-1;if(e[0]==="-"&&(e=e.slice(1)),e==="0")return!1;for(;e[++u]==="0";);return u>0},"zeros"),oo=a((t,e,u)=>typeof t=="string"||typeof e=="string"?!0:u.stringify===!0,"stringify$3"),ao=a((t,e,u)=>{if(e>0){let s=t[0]==="-"?"-":"";s&&(t=t.slice(1)),t=s+t.padStart(s?e-1:e,"0")}return u===!1?String(t):t},"pad"),Ms=a((t,e)=>{let u=t[0]==="-"?"-":"";for(u&&(t=t.slice(1),e--);t.length<e;)t="0"+t;return u?"-"+t:t},"toMaxLen"),lo=a((t,e)=>{t.negatives.sort((i,D)=>i<D?-1:i>D?1:0),t.positives.sort((i,D)=>i<D?-1:i>D?1:0);let u=e.capture?"":"?:",s="",n="",r;return t.positives.length&&(s=t.positives.join("|")),t.negatives.length&&(n=`-(${u}${t.negatives.join("|")})`),s&&n?r=`${s}|${n}`:r=s||n,e.wrap?`(${u}${r})`:r},"toSequence"),Gs=a((t,e,u,s)=>{if(u)return Is(t,e,{wrap:!1,...s});let n=String.fromCharCode(t);if(t===e)return n;let r=String.fromCharCode(e);return`[${n}-${r}]`},"toRange"),Ws=a((t,e,u)=>{if(Array.isArray(t)){let s=u.wrap===!0,n=u.capture?"":"?:";return s?`(${n}${t.join("|")})`:t.join("|")}return Is(t,e,u)},"toRegex"),js=a((...t)=>new RangeError("Invalid range arguments: "+io.inspect(...t)),"rangeError"),Us=a((t,e,u)=>{if(u.strictRanges===!0)throw js([t,e]);return[]},"invalidRange"),co=a((t,e)=>{if(e.strictRanges===!0)throw new TypeError(`Expected step "${t}" to be a number`);return[]},"invalidStep"),fo=a((t,e,u=1,s={})=>{let n=Number(t),r=Number(e);if(!Number.isInteger(n)||!Number.isInteger(r)){if(s.strictRanges===!0)throw js([t,e]);return[]}n===0&&(n=0),r===0&&(r=0);let i=n>r,D=String(t),o=String(e),c=String(u);u=Math.max(Math.abs(u),1);let f=qt(D)||qt(o)||qt(c),h=f?Math.max(D.length,o.length,c.length):0,l=f===!1&&oo(t,e,s)===!1,p=s.transform||Do(l);if(s.toRegex&&u===1)return Gs(Ms(t,h),Ms(e,h),!0,s);let C={negatives:[],positives:[]},g=a(H=>C[H<0?"negatives":"positives"].push(Math.abs(H)),"push"),y=[],B=0;for(;i?n>=r:n<=r;)s.toRegex===!0&&u>1?g(n):y.push(ao(p(n,B),h,l)),n=i?n-u:n+u,B++;return s.toRegex===!0?u>1?lo(C,s):Ws(y,null,{wrap:!1,...s}):y},"fillNumbers"),ho=a((t,e,u=1,s={})=>{if(!be(t)&&t.length>1||!be(e)&&e.length>1)return Us(t,e,s);let n=s.transform||(l=>String.fromCharCode(l)),r=`${t}`.charCodeAt(0),i=`${e}`.charCodeAt(0),D=r>i,o=Math.min(r,i),c=Math.max(r,i);if(s.toRegex&&u===1)return Gs(o,c,!1,s);let f=[],h=0;for(;D?r>=i:r<=i;)f.push(n(r,h)),r=D?r-u:r+u,h++;return s.toRegex===!0?Ws(f,null,{wrap:!1,options:s}):f},"fillLetters"),Je=a((t,e,u,s={})=>{if(e==null&&Yt(t))return[t];if(!Yt(t)||!Yt(e))return Us(t,e,s);if(typeof u=="function")return Je(t,e,1,{transform:u});if(ks(u))return Je(t,e,0,u);let n={...s};return n.capture===!0&&(n.wrap=!0),u=u||n.step||1,be(u)?be(t)&&be(e)?fo(t,e,u,n):ho(t,e,Math.max(Math.abs(u),1),n):u!=null&&!ks(u)?co(u,n):Je(t,e,1,u)},"fill$2");var Ks=Je;const Eo=Ks,Vs=Ze,po=a((t,e={})=>{let u=a((s,n={})=>{let r=Vs.isInvalidBrace(n),i=s.invalid===!0&&e.escapeInvalid===!0,D=r===!0||i===!0,o=e.escapeInvalid===!0?"\\":"",c="";if(s.isOpen===!0||s.isClose===!0)return o+s.value;if(s.type==="open")return D?o+s.value:"(";if(s.type==="close")return D?o+s.value:")";if(s.type==="comma")return s.prev.type==="comma"?"":D?s.value:"|";if(s.value)return s.value;if(s.nodes&&s.ranges>0){let f=Vs.reduce(s.nodes),h=Eo(...f,{...e,wrap:!1,toRegex:!0});if(h.length!==0)return f.length>1&&h.length>1?`(${h})`:h}if(s.nodes)for(let f of s.nodes)c+=u(f,s);return c},"walk");return u(t)},"compile$1");var Co=po;const Fo=Ks,zs=Vt,pe=Ze,le=a((t="",e="",u=!1)=>{let s=[];if(t=[].concat(t),e=[].concat(e),!e.length)return t;if(!t.length)return u?pe.flatten(e).map(n=>`{${n}}`):e;for(let n of t)if(Array.isArray(n))for(let r of n)s.push(le(r,e,u));else for(let r of e)u===!0&&typeof r=="string"&&(r=`{${r}}`),s.push(Array.isArray(r)?le(n,r,u):n+r);return pe.flatten(s)},"append"),go=a((t,e={})=>{let u=e.rangeLimit===void 0?1e3:e.rangeLimit,s=a((n,r={})=>{n.queue=[];let i=r,D=r.queue;for(;i.type!=="brace"&&i.type!=="root"&&i.parent;)i=i.parent,D=i.queue;if(n.invalid||n.dollar){D.push(le(D.pop(),zs(n,e)));return}if(n.type==="brace"&&n.invalid!==!0&&n.nodes.length===2){D.push(le(D.pop(),["{}"]));return}if(n.nodes&&n.ranges>0){let h=pe.reduce(n.nodes);if(pe.exceedsLimit(...h,e.step,u))throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.");let l=Fo(...h,e);l.length===0&&(l=zs(n,e)),D.push(le(D.pop(),l)),n.nodes=[];return}let o=pe.encloseBrace(n),c=n.queue,f=n;for(;f.type!=="brace"&&f.type!=="root"&&f.parent;)f=f.parent,c=f.queue;for(let h=0;h<n.nodes.length;h++){let l=n.nodes[h];if(l.type==="comma"&&n.type==="brace"){h===1&&c.push(""),c.push("");continue}if(l.type==="close"){D.push(le(D.pop(),c,o));continue}if(l.value&&l.type!=="open"){c.push(le(c.pop(),l.value));continue}l.nodes&&s(l,n)}return c},"walk");return pe.flatten(s(t))},"expand$1");var mo=go,_o={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:`
`,CHAR_NO_BREAK_SPACE:"\xA0",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"	",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\uFEFF"};const Ao=Vt,{MAX_LENGTH:Ys,CHAR_BACKSLASH:Xt,CHAR_BACKTICK:yo,CHAR_COMMA:wo,CHAR_DOT:Ro,CHAR_LEFT_PARENTHESES:bo,CHAR_RIGHT_PARENTHESES:vo,CHAR_LEFT_CURLY_BRACE:So,CHAR_RIGHT_CURLY_BRACE:Bo,CHAR_LEFT_SQUARE_BRACKET:qs,CHAR_RIGHT_SQUARE_BRACKET:Xs,CHAR_DOUBLE_QUOTE:$o,CHAR_SINGLE_QUOTE:To,CHAR_NO_BREAK_SPACE:xo,CHAR_ZERO_WIDTH_NOBREAK_SPACE:Oo}=_o,No=a((t,e={})=>{if(typeof t!="string")throw new TypeError("Expected a string");let u=e||{},s=typeof u.maxLength=="number"?Math.min(Ys,u.maxLength):Ys;if(t.length>s)throw new SyntaxError(`Input length (${t.length}), exceeds max characters (${s})`);let n={type:"root",input:t,nodes:[]},r=[n],i=n,D=n,o=0,c=t.length,f=0,h=0,l;const p=a(()=>t[f++],"advance"),C=a(g=>{if(g.type==="text"&&D.type==="dot"&&(D.type="text"),D&&D.type==="text"&&g.type==="text"){D.value+=g.value;return}return i.nodes.push(g),g.parent=i,g.prev=D,D=g,g},"push");for(C({type:"bos"});f<c;)if(i=r[r.length-1],l=p(),!(l===Oo||l===xo)){if(l===Xt){C({type:"text",value:(e.keepEscaping?l:"")+p()});continue}if(l===Xs){C({type:"text",value:"\\"+l});continue}if(l===qs){o++;let g;for(;f<c&&(g=p());){if(l+=g,g===qs){o++;continue}if(g===Xt){l+=p();continue}if(g===Xs&&(o--,o===0))break}C({type:"text",value:l});continue}if(l===bo){i=C({type:"paren",nodes:[]}),r.push(i),C({type:"text",value:l});continue}if(l===vo){if(i.type!=="paren"){C({type:"text",value:l});continue}i=r.pop(),C({type:"text",value:l}),i=r[r.length-1];continue}if(l===$o||l===To||l===yo){let g=l,y;for(e.keepQuotes!==!0&&(l="");f<c&&(y=p());){if(y===Xt){l+=y+p();continue}if(y===g){e.keepQuotes===!0&&(l+=y);break}l+=y}C({type:"text",value:l});continue}if(l===So){h++;let y={type:"brace",open:!0,close:!1,dollar:D.value&&D.value.slice(-1)==="$"||i.dollar===!0,depth:h,commas:0,ranges:0,nodes:[]};i=C(y),r.push(i),C({type:"open",value:l});continue}if(l===Bo){if(i.type!=="brace"){C({type:"text",value:l});continue}let g="close";i=r.pop(),i.close=!0,C({type:g,value:l}),h--,i=r[r.length-1];continue}if(l===wo&&h>0){if(i.ranges>0){i.ranges=0;let g=i.nodes.shift();i.nodes=[g,{type:"text",value:Ao(i)}]}C({type:"comma",value:l}),i.commas++;continue}if(l===Ro&&h>0&&i.commas===0){let g=i.nodes;if(h===0||g.length===0){C({type:"text",value:l});continue}if(D.type==="dot"){if(i.range=[],D.value+=l,D.type="range",i.nodes.length!==3&&i.nodes.length!==5){i.invalid=!0,i.ranges=0,D.type="text";continue}i.ranges++,i.args=[];continue}if(D.type==="range"){g.pop();let y=g[g.length-1];y.value+=D.value+l,D=y,i.ranges--;continue}C({type:"dot",value:l});continue}C({type:"text",value:l})}do if(i=r.pop(),i.type!=="root"){i.nodes.forEach(B=>{B.nodes||(B.type==="open"&&(B.isOpen=!0),B.type==="close"&&(B.isClose=!0),B.nodes||(B.type="text"),B.invalid=!0)});let g=r[r.length-1],y=g.nodes.indexOf(i);g.nodes.splice(y,1,...i.nodes)}while(r.length>0);return C({type:"eos"}),n},"parse$1");var Ho=No;const Qs=Vt,Po=Co,Lo=mo,Io=Ho,q=a((t,e={})=>{let u=[];if(Array.isArray(t))for(let s of t){let n=q.create(s,e);Array.isArray(n)?u.push(...n):u.push(n)}else u=[].concat(q.create(t,e));return e&&e.expand===!0&&e.nodupes===!0&&(u=[...new Set(u)]),u},"braces$1");q.parse=(t,e={})=>Io(t,e),q.stringify=(t,e={})=>Qs(typeof t=="string"?q.parse(t,e):t,e),q.compile=(t,e={})=>(typeof t=="string"&&(t=q.parse(t,e)),Po(t,e)),q.expand=(t,e={})=>{typeof t=="string"&&(t=q.parse(t,e));let u=Lo(t,e);return e.noempty===!0&&(u=u.filter(Boolean)),e.nodupes===!0&&(u=[...new Set(u)]),u},q.create=(t,e={})=>t===""||t.length<3?[t]:e.expand!==!0?q.compile(t,e):q.expand(t,e);var ko=q,Mo=["3dm","3ds","3g2","3gp","7z","a","aac","adp","afdesign","afphoto","afpub","ai","aif","aiff","alz","ape","apk","appimage","ar","arj","asf","au","avi","bak","baml","bh","bin","bk","bmp","btif","bz2","bzip2","cab","caf","cgm","class","cmx","cpio","cr2","cur","dat","dcm","deb","dex","djvu","dll","dmg","dng","doc","docm","docx","dot","dotm","dra","DS_Store","dsk","dts","dtshd","dvb","dwg","dxf","ecelp4800","ecelp7470","ecelp9600","egg","eol","eot","epub","exe","f4v","fbs","fh","fla","flac","flatpak","fli","flv","fpx","fst","fvt","g3","gh","gif","graffle","gz","gzip","h261","h263","h264","icns","ico","ief","img","ipa","iso","jar","jpeg","jpg","jpgv","jpm","jxr","key","ktx","lha","lib","lvp","lz","lzh","lzma","lzo","m3u","m4a","m4v","mar","mdi","mht","mid","midi","mj2","mka","mkv","mmr","mng","mobi","mov","movie","mp3","mp4","mp4a","mpeg","mpg","mpga","mxu","nef","npx","numbers","nupkg","o","odp","ods","odt","oga","ogg","ogv","otf","ott","pages","pbm","pcx","pdb","pdf","pea","pgm","pic","png","pnm","pot","potm","potx","ppa","ppam","ppm","pps","ppsm","ppsx","ppt","pptm","pptx","psd","pya","pyc","pyo","pyv","qt","rar","ras","raw","resources","rgb","rip","rlc","rmf","rmvb","rpm","rtf","rz","s3m","s7z","scpt","sgi","shar","snap","sil","sketch","slk","smv","snk","so","stl","suo","sub","swf","tar","tbz","tbz2","tga","tgz","thmx","tif","tiff","tlz","ttc","ttf","txz","udf","uvh","uvi","uvm","uvp","uvs","uvu","viv","vob","war","wav","wax","wbmp","wdp","weba","webm","webp","whl","wim","wm","wma","wmv","wmx","woff","woff2","wrm","wvx","xbm","xif","xla","xlam","xls","xlsb","xlsm","xlsx","xlt","xltm","xltx","xm","xmind","xpi","xpm","xwd","xz","z","zip","zipx"],Go=Mo;const Wo=z,jo=Go,Uo=new Set(jo);var Ko=a(t=>Uo.has(Wo.extname(t).slice(1).toLowerCase()),"isBinaryPath$1"),et={};(function(t){const{sep:e}=z,{platform:u}=process,s=_u;t.EV_ALL="all",t.EV_READY="ready",t.EV_ADD="add",t.EV_CHANGE="change",t.EV_ADD_DIR="addDir",t.EV_UNLINK="unlink",t.EV_UNLINK_DIR="unlinkDir",t.EV_RAW="raw",t.EV_ERROR="error",t.STR_DATA="data",t.STR_END="end",t.STR_CLOSE="close",t.FSEVENT_CREATED="created",t.FSEVENT_MODIFIED="modified",t.FSEVENT_DELETED="deleted",t.FSEVENT_MOVED="moved",t.FSEVENT_CLONED="cloned",t.FSEVENT_UNKNOWN="unknown",t.FSEVENT_FLAG_MUST_SCAN_SUBDIRS=1,t.FSEVENT_TYPE_FILE="file",t.FSEVENT_TYPE_DIRECTORY="directory",t.FSEVENT_TYPE_SYMLINK="symlink",t.KEY_LISTENERS="listeners",t.KEY_ERR="errHandlers",t.KEY_RAW="rawEmitters",t.HANDLER_KEYS=[t.KEY_LISTENERS,t.KEY_ERR,t.KEY_RAW],t.DOT_SLASH=`.${e}`,t.BACK_SLASH_RE=/\\/g,t.DOUBLE_SLASH_RE=/\/\//,t.SLASH_OR_BACK_SLASH_RE=/[/\\]/,t.DOT_RE=/\..*\.(sw[px])$|~$|\.subl.*\.tmp/,t.REPLACER_RE=/^\.[/\\]/,t.SLASH="/",t.SLASH_SLASH="//",t.BRACE_START="{",t.BANG="!",t.ONE_DOT=".",t.TWO_DOTS="..",t.STAR="*",t.GLOBSTAR="**",t.ROOT_GLOBSTAR="/**/*",t.SLASH_GLOBSTAR="/**",t.DIR_SUFFIX="Dir",t.ANYMATCH_OPTS={dot:!0},t.STRING_TYPE="string",t.FUNCTION_TYPE="function",t.EMPTY_STR="",t.EMPTY_FN=()=>{},t.IDENTITY_FN=n=>n,t.isWindows=u==="win32",t.isMacos=u==="darwin",t.isLinux=u==="linux",t.isIBMi=s.type()==="OS400"})(et);const se=De,I=z,{promisify:ve}=ge,Vo=Ko,{isWindows:zo,isLinux:Yo,EMPTY_FN:qo,EMPTY_STR:Xo,KEY_LISTENERS:Ce,KEY_ERR:Qt,KEY_RAW:Se,HANDLER_KEYS:Qo,EV_CHANGE:tt,EV_ADD:ut,EV_ADD_DIR:Zo,EV_ERROR:Zs,STR_DATA:Jo,STR_END:ea,BRACE_START:ta,STAR:ua}=et,sa="watch",na=ve(se.open),Js=ve(se.stat),ra=ve(se.lstat),ia=ve(se.close),Zt=ve(se.realpath),Da={lstat:ra,stat:Js},Jt=a((t,e)=>{t instanceof Set?t.forEach(e):e(t)},"foreach"),Be=a((t,e,u)=>{let s=t[e];s instanceof Set||(t[e]=s=new Set([s])),s.add(u)},"addAndConvert"),oa=a(t=>e=>{const u=t[e];u instanceof Set?u.clear():delete t[e]},"clearItem"),$e=a((t,e,u)=>{const s=t[e];s instanceof Set?s.delete(u):s===u&&delete t[e]},"delFromSet"),en=a(t=>t instanceof Set?t.size===0:!t,"isEmptySet"),st=new Map;function tn(t,e,u,s,n){const r=a((i,D)=>{u(t),n(i,D,{watchedPath:t}),D&&t!==D&&nt(I.resolve(t,D),Ce,I.join(t,D))},"handleEvent");try{return se.watch(t,e,r)}catch(i){s(i)}}a(tn,"createFsWatchInstance");const nt=a((t,e,u,s,n)=>{const r=st.get(t);r&&Jt(r[e],i=>{i(u,s,n)})},"fsWatchBroadcast"),aa=a((t,e,u,s)=>{const{listener:n,errHandler:r,rawEmitter:i}=s;let D=st.get(e),o;if(!u.persistent)return o=tn(t,u,n,r,i),o.close.bind(o);if(D)Be(D,Ce,n),Be(D,Qt,r),Be(D,Se,i);else{if(o=tn(t,u,nt.bind(null,e,Ce),r,nt.bind(null,e,Se)),!o)return;o.on(Zs,async c=>{const f=nt.bind(null,e,Qt);if(D.watcherUnusable=!0,zo&&c.code==="EPERM")try{const h=await na(t,"r");await ia(h),f(c)}catch{}else f(c)}),D={listeners:n,errHandlers:r,rawEmitters:i,watcher:o},st.set(e,D)}return()=>{$e(D,Ce,n),$e(D,Qt,r),$e(D,Se,i),en(D.listeners)&&(D.watcher.close(),st.delete(e),Qo.forEach(oa(D)),D.watcher=void 0,Object.freeze(D))}},"setFsWatchListener"),eu=new Map,la=a((t,e,u,s)=>{const{listener:n,rawEmitter:r}=s;let i=eu.get(e);const D=i&&i.options;return D&&(D.persistent<u.persistent||D.interval>u.interval)&&(i.listeners,i.rawEmitters,se.unwatchFile(e),i=void 0),i?(Be(i,Ce,n),Be(i,Se,r)):(i={listeners:n,rawEmitters:r,options:u,watcher:se.watchFile(e,u,(o,c)=>{Jt(i.rawEmitters,h=>{h(tt,e,{curr:o,prev:c})});const f=o.mtimeMs;(o.size!==c.size||f>c.mtimeMs||f===0)&&Jt(i.listeners,h=>h(t,o))})},eu.set(e,i)),()=>{$e(i,Ce,n),$e(i,Se,r),en(i.listeners)&&(eu.delete(e),se.unwatchFile(e),i.options=i.watcher=void 0,Object.freeze(i))}},"setFsWatchFileListener");let ca=class{static{a(this,"NodeFsHandler")}constructor(e){this.fsw=e,this._boundHandleError=u=>e._handleError(u)}_watchWithNodeFs(e,u){const s=this.fsw.options,n=I.dirname(e),r=I.basename(e);this.fsw._getWatchedDir(n).add(r);const D=I.resolve(e),o={persistent:s.persistent};u||(u=qo);let c;return s.usePolling?(o.interval=s.enableBinaryInterval&&Vo(r)?s.binaryInterval:s.interval,c=la(e,D,o,{listener:u,rawEmitter:this.fsw._emitRaw})):c=aa(e,D,o,{listener:u,errHandler:this._boundHandleError,rawEmitter:this.fsw._emitRaw}),c}_handleFile(e,u,s){if(this.fsw.closed)return;const n=I.dirname(e),r=I.basename(e),i=this.fsw._getWatchedDir(n);let D=u;if(i.has(r))return;const o=a(async(f,h)=>{if(this.fsw._throttle(sa,e,5)){if(!h||h.mtimeMs===0)try{const l=await Js(e);if(this.fsw.closed)return;const p=l.atimeMs,C=l.mtimeMs;(!p||p<=C||C!==D.mtimeMs)&&this.fsw._emit(tt,e,l),Yo&&D.ino!==l.ino?(this.fsw._closeFile(f),D=l,this.fsw._addPathCloser(f,this._watchWithNodeFs(e,o))):D=l}catch{this.fsw._remove(n,r)}else if(i.has(r)){const l=h.atimeMs,p=h.mtimeMs;(!l||l<=p||p!==D.mtimeMs)&&this.fsw._emit(tt,e,h),D=h}}},"listener"),c=this._watchWithNodeFs(e,o);if(!(s&&this.fsw.options.ignoreInitial)&&this.fsw._isntIgnored(e)){if(!this.fsw._throttle(ut,e,0))return;this.fsw._emit(ut,e,u)}return c}async _handleSymlink(e,u,s,n){if(this.fsw.closed)return;const r=e.fullPath,i=this.fsw._getWatchedDir(u);if(!this.fsw.options.followSymlinks){this.fsw._incrReadyCount();let D;try{D=await Zt(s)}catch{return this.fsw._emitReady(),!0}return this.fsw.closed?void 0:(i.has(n)?this.fsw._symlinkPaths.get(r)!==D&&(this.fsw._symlinkPaths.set(r,D),this.fsw._emit(tt,s,e.stats)):(i.add(n),this.fsw._symlinkPaths.set(r,D),this.fsw._emit(ut,s,e.stats)),this.fsw._emitReady(),!0)}if(this.fsw._symlinkPaths.has(r))return!0;this.fsw._symlinkPaths.set(r,!0)}_handleRead(e,u,s,n,r,i,D){if(e=I.join(e,Xo),!s.hasGlob&&(D=this.fsw._throttle("readdir",e,1e3),!D))return;const o=this.fsw._getWatchedDir(s.path),c=new Set;let f=this.fsw._readdirp(e,{fileFilter:a(h=>s.filterPath(h),"fileFilter"),directoryFilter:a(h=>s.filterDir(h),"directoryFilter"),depth:0}).on(Jo,async h=>{if(this.fsw.closed){f=void 0;return}const l=h.path;let p=I.join(e,l);if(c.add(l),!(h.stats.isSymbolicLink()&&await this._handleSymlink(h,e,p,l))){if(this.fsw.closed){f=void 0;return}(l===n||!n&&!o.has(l))&&(this.fsw._incrReadyCount(),p=I.join(r,I.relative(r,p)),this._addToNodeFs(p,u,s,i+1))}}).on(Zs,this._boundHandleError);return new Promise(h=>f.once(ea,()=>{if(this.fsw.closed){f=void 0;return}const l=D?D.clear():!1;h(),o.getChildren().filter(p=>p!==e&&!c.has(p)&&(!s.hasGlob||s.filterPath({fullPath:I.resolve(e,p)}))).forEach(p=>{this.fsw._remove(e,p)}),f=void 0,l&&this._handleRead(e,!1,s,n,r,i,D)}))}async _handleDir(e,u,s,n,r,i,D){const o=this.fsw._getWatchedDir(I.dirname(e)),c=o.has(I.basename(e));!(s&&this.fsw.options.ignoreInitial)&&!r&&!c&&(!i.hasGlob||i.globFilter(e))&&this.fsw._emit(Zo,e,u),o.add(I.basename(e)),this.fsw._getWatchedDir(e);let f,h;const l=this.fsw.options.depth;if((l==null||n<=l)&&!this.fsw._symlinkPaths.has(D)){if(!r&&(await this._handleRead(e,s,i,r,e,n,f),this.fsw.closed))return;h=this._watchWithNodeFs(e,(p,C)=>{C&&C.mtimeMs===0||this._handleRead(p,!1,i,r,e,n,f)})}return h}async _addToNodeFs(e,u,s,n,r){const i=this.fsw._emitReady;if(this.fsw._isIgnored(e)||this.fsw.closed)return i(),!1;const D=this.fsw._getWatchHelpers(e,n);!D.hasGlob&&s&&(D.hasGlob=s.hasGlob,D.globFilter=s.globFilter,D.filterPath=o=>s.filterPath(o),D.filterDir=o=>s.filterDir(o));try{const o=await Da[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))return i(),!1;const c=this.fsw.options.followSymlinks&&!e.includes(ua)&&!e.includes(ta);let f;if(o.isDirectory()){const h=I.resolve(e),l=c?await Zt(e):e;if(this.fsw.closed||(f=await this._handleDir(D.watchPath,o,u,n,r,D,l),this.fsw.closed))return;h!==l&&l!==void 0&&this.fsw._symlinkPaths.set(h,l)}else if(o.isSymbolicLink()){const h=c?await Zt(e):e;if(this.fsw.closed)return;const l=I.dirname(D.watchPath);if(this.fsw._getWatchedDir(l).add(D.watchPath),this.fsw._emit(ut,D.watchPath,o),f=await this._handleDir(l,o,u,n,e,D,h),this.fsw.closed)return;h!==void 0&&this.fsw._symlinkPaths.set(I.resolve(e),h)}else f=this._handleFile(D.watchPath,o,u);return i(),this.fsw._addPathCloser(e,f),!1}catch(o){if(this.fsw._handleError(o))return i(),e}}};var fa=ca,tu={exports:{}};const uu=De,k=z,{promisify:su}=ge;let Fe;try{Fe=Ie("fsevents")}catch(t){process.env.CHOKIDAR_PRINT_FSEVENTS_REQUIRE_ERROR&&console.error(t)}if(Fe){const t=process.version.match(/v(\d+)\.(\d+)/);if(t&&t[1]&&t[2]){const e=Number.parseInt(t[1],10),u=Number.parseInt(t[2],10);e===8&&u<16&&(Fe=void 0)}}const{EV_ADD:nu,EV_CHANGE:ha,EV_ADD_DIR:un,EV_UNLINK:rt,EV_ERROR:da,STR_DATA:Ea,STR_END:pa,FSEVENT_CREATED:Ca,FSEVENT_MODIFIED:Fa,FSEVENT_DELETED:ga,FSEVENT_MOVED:ma,FSEVENT_UNKNOWN:_a,FSEVENT_FLAG_MUST_SCAN_SUBDIRS:Aa,FSEVENT_TYPE_FILE:ya,FSEVENT_TYPE_DIRECTORY:Te,FSEVENT_TYPE_SYMLINK:sn,ROOT_GLOBSTAR:nn,DIR_SUFFIX:wa,DOT_SLASH:rn,FUNCTION_TYPE:ru,EMPTY_FN:Ra,IDENTITY_FN:ba}=et,va=a(t=>isNaN(t)?{}:{depth:t},"Depth"),iu=su(uu.stat),Sa=su(uu.lstat),Dn=su(uu.realpath),Ba={stat:iu,lstat:Sa},ce=new Map,$a=10,Ta=new Set([69888,70400,71424,72704,73472,131328,131840,262912]),xa=a((t,e)=>({stop:Fe.watch(t,e)}),"createFSEventsInstance");function Oa(t,e,u,s){let n=k.extname(e)?k.dirname(e):e;const r=k.dirname(n);let i=ce.get(n);Na(r)&&(n=r);const D=k.resolve(t),o=D!==e,c=a((h,l,p)=>{o&&(h=h.replace(e,D)),(h===D||!h.indexOf(D+k.sep))&&u(h,l,p)},"filteredListener");let f=!1;for(const h of ce.keys())if(e.indexOf(k.resolve(h)+k.sep)===0){n=h,i=ce.get(n),f=!0;break}return i||f?i.listeners.add(c):(i={listeners:new Set([c]),rawEmitter:s,watcher:xa(n,(h,l)=>{if(!i.listeners.size||l&Aa)return;const p=Fe.getInfo(h,l);i.listeners.forEach(C=>{C(h,l,p)}),i.rawEmitter(p.event,h,p)})},ce.set(n,i)),()=>{const h=i.listeners;if(h.delete(c),!h.size&&(ce.delete(n),i.watcher))return i.watcher.stop().then(()=>{i.rawEmitter=i.watcher=void 0,Object.freeze(i)})}}a(Oa,"setFSEventsListener");const Na=a(t=>{let e=0;for(const u of ce.keys())if(u.indexOf(t)===0&&(e++,e>=$a))return!0;return!1},"couldConsolidate"),Ha=a(()=>Fe&&ce.size<128,"canUse"),Du=a((t,e)=>{let u=0;for(;!t.indexOf(e)&&(t=k.dirname(t))!==e;)u++;return u},"calcDepth"),on=a((t,e)=>t.type===Te&&e.isDirectory()||t.type===sn&&e.isSymbolicLink()||t.type===ya&&e.isFile(),"sameTypes");let Pa=class{static{a(this,"FsEventsHandler")}constructor(e){this.fsw=e}checkIgnored(e,u){const s=this.fsw._ignoredPaths;if(this.fsw._isIgnored(e,u))return s.add(e),u&&u.isDirectory()&&s.add(e+nn),!0;s.delete(e),s.delete(e+nn)}addOrChange(e,u,s,n,r,i,D,o){const c=r.has(i)?ha:nu;this.handleEvent(c,e,u,s,n,r,i,D,o)}async checkExists(e,u,s,n,r,i,D,o){try{const c=await iu(e);if(this.fsw.closed)return;on(D,c)?this.addOrChange(e,u,s,n,r,i,D,o):this.handleEvent(rt,e,u,s,n,r,i,D,o)}catch(c){c.code==="EACCES"?this.addOrChange(e,u,s,n,r,i,D,o):this.handleEvent(rt,e,u,s,n,r,i,D,o)}}handleEvent(e,u,s,n,r,i,D,o,c){if(!(this.fsw.closed||this.checkIgnored(u)))if(e===rt){const f=o.type===Te;(f||i.has(D))&&this.fsw._remove(r,D,f)}else{if(e===nu){if(o.type===Te&&this.fsw._getWatchedDir(u),o.type===sn&&c.followSymlinks){const h=c.depth===void 0?void 0:Du(s,n)+1;return this._addToFsEvents(u,!1,!0,h)}this.fsw._getWatchedDir(r).add(D)}const f=o.type===Te?e+wa:e;this.fsw._emit(f,u),f===un&&this._addToFsEvents(u,!1,!0)}}_watchWithFsEvents(e,u,s,n){if(this.fsw.closed||this.fsw._isIgnored(e))return;const r=this.fsw.options,D=Oa(e,u,a(async(o,c,f)=>{if(this.fsw.closed||r.depth!==void 0&&Du(o,u)>r.depth)return;const h=s(k.join(e,k.relative(e,o)));if(n&&!n(h))return;const l=k.dirname(h),p=k.basename(h),C=this.fsw._getWatchedDir(f.type===Te?h:l);if(Ta.has(c)||f.event===_a)if(typeof r.ignored===ru){let g;try{g=await iu(h)}catch{}if(this.fsw.closed||this.checkIgnored(h,g))return;on(f,g)?this.addOrChange(h,o,u,l,C,p,f,r):this.handleEvent(rt,h,o,u,l,C,p,f,r)}else this.checkExists(h,o,u,l,C,p,f,r);else switch(f.event){case Ca:case Fa:return this.addOrChange(h,o,u,l,C,p,f,r);case ga:case ma:return this.checkExists(h,o,u,l,C,p,f,r)}},"watchCallback"),this.fsw._emitRaw);return this.fsw._emitReady(),D}async _handleFsEventsSymlink(e,u,s,n){if(!(this.fsw.closed||this.fsw._symlinkPaths.has(u))){this.fsw._symlinkPaths.set(u,!0),this.fsw._incrReadyCount();try{const r=await Dn(e);if(this.fsw.closed)return;if(this.fsw._isIgnored(r))return this.fsw._emitReady();this.fsw._incrReadyCount(),this._addToFsEvents(r||e,i=>{let D=e;return r&&r!==rn?D=i.replace(r,e):i!==rn&&(D=k.join(e,i)),s(D)},!1,n)}catch(r){if(this.fsw._handleError(r))return this.fsw._emitReady()}}}emitAdd(e,u,s,n,r){const i=s(e),D=u.isDirectory(),o=this.fsw._getWatchedDir(k.dirname(i)),c=k.basename(i);D&&this.fsw._getWatchedDir(i),!o.has(c)&&(o.add(c),(!n.ignoreInitial||r===!0)&&this.fsw._emit(D?un:nu,i,u))}initWatch(e,u,s,n){if(this.fsw.closed)return;const r=this._watchWithFsEvents(s.watchPath,k.resolve(e||s.watchPath),n,s.globFilter);this.fsw._addPathCloser(u,r)}async _addToFsEvents(e,u,s,n){if(this.fsw.closed)return;const r=this.fsw.options,i=typeof u===ru?u:ba,D=this.fsw._getWatchHelpers(e);try{const o=await Ba[D.statMethod](D.watchPath);if(this.fsw.closed)return;if(this.fsw._isIgnored(D.watchPath,o))throw null;if(o.isDirectory()){if(D.globFilter||this.emitAdd(i(e),o,i,r,s),n&&n>r.depth)return;this.fsw._readdirp(D.watchPath,{fileFilter:a(c=>D.filterPath(c),"fileFilter"),directoryFilter:a(c=>D.filterDir(c),"directoryFilter"),...va(r.depth-(n||0))}).on(Ea,c=>{if(this.fsw.closed||c.stats.isDirectory()&&!D.filterPath(c))return;const f=k.join(D.watchPath,c.path),{fullPath:h}=c;if(D.followSymlinks&&c.stats.isSymbolicLink()){const l=r.depth===void 0?void 0:Du(f,k.resolve(D.watchPath))+1;this._handleFsEventsSymlink(f,h,i,l)}else this.emitAdd(f,c.stats,i,r,s)}).on(da,Ra).on(pa,()=>{this.fsw._emitReady()})}else this.emitAdd(D.watchPath,o,i,r,s),this.fsw._emitReady()}catch(o){(!o||this.fsw._handleError(o))&&(this.fsw._emitReady(),this.fsw._emitReady())}if(r.persistent&&s!==!0)if(typeof u===ru)this.initWatch(void 0,e,D,i);else{let o;try{o=await Dn(D.watchPath)}catch{}this.initWatch(o,e,D,i)}}};tu.exports=Pa,tu.exports.canUse=Ha;var La=tu.exports;const{EventEmitter:Ia}=Ln,ou=De,S=z,{promisify:an}=ge,ka=xD,au=ID.default,Ma=XD,lu=Bs,Ga=ko,Wa=ws,ja=fa,ln=La,{EV_ALL:cu,EV_READY:Ua,EV_ADD:it,EV_CHANGE:xe,EV_UNLINK:cn,EV_ADD_DIR:Ka,EV_UNLINK_DIR:Va,EV_RAW:za,EV_ERROR:fu,STR_CLOSE:Ya,STR_END:qa,BACK_SLASH_RE:Xa,DOUBLE_SLASH_RE:fn,SLASH_OR_BACK_SLASH_RE:Qa,DOT_RE:Za,REPLACER_RE:Ja,SLASH:hu,SLASH_SLASH:el,BRACE_START:tl,BANG:du,ONE_DOT:hn,TWO_DOTS:ul,GLOBSTAR:sl,SLASH_GLOBSTAR:Eu,ANYMATCH_OPTS:pu,STRING_TYPE:Cu,FUNCTION_TYPE:nl,EMPTY_STR:Fu,EMPTY_FN:rl,isWindows:il,isMacos:Dl,isIBMi:ol}=et,al=an(ou.stat),ll=an(ou.readdir),gu=a((t=[])=>Array.isArray(t)?t:[t],"arrify"),dn=a((t,e=[])=>(t.forEach(u=>{Array.isArray(u)?dn(u,e):e.push(u)}),e),"flatten"),En=a(t=>{const e=dn(gu(t));if(!e.every(u=>typeof u===Cu))throw new TypeError(`Non-string provided as watch path: ${e}`);return e.map(Cn)},"unifyPaths"),pn=a(t=>{let e=t.replace(Xa,hu),u=!1;for(e.startsWith(el)&&(u=!0);e.match(fn);)e=e.replace(fn,hu);return u&&(e=hu+e),e},"toUnix"),Cn=a(t=>pn(S.normalize(pn(t))),"normalizePathToUnix"),Fn=a((t=Fu)=>e=>typeof e!==Cu?e:Cn(S.isAbsolute(e)?e:S.join(t,e)),"normalizeIgnored"),cl=a((t,e)=>S.isAbsolute(t)?t:t.startsWith(du)?du+S.join(e,t.slice(1)):S.join(e,t),"getAbsolutePath"),X=a((t,e)=>t[e]===void 0,"undef");class fl{static{a(this,"DirEntry")}constructor(e,u){this.path=e,this._removeWatcher=u,this.items=new Set}add(e){const{items:u}=this;u&&e!==hn&&e!==ul&&u.add(e)}async remove(e){const{items:u}=this;if(!u||(u.delete(e),u.size>0))return;const s=this.path;try{await ll(s)}catch{this._removeWatcher&&this._removeWatcher(S.dirname(s),S.basename(s))}}has(e){const{items:u}=this;if(u)return u.has(e)}getChildren(){const{items:e}=this;if(e)return[...e.values()]}dispose(){this.items.clear(),delete this.path,delete this._removeWatcher,delete this.items,Object.freeze(this)}}const hl="stat",dl="lstat";class El{static{a(this,"WatchHelper")}constructor(e,u,s,n){this.fsw=n,this.path=e=e.replace(Ja,Fu),this.watchPath=u,this.fullWatchPath=S.resolve(u),this.hasGlob=u!==e,e===Fu&&(this.hasGlob=!1),this.globSymlink=this.hasGlob&&s?void 0:!1,this.globFilter=this.hasGlob?au(e,void 0,pu):!1,this.dirParts=this.getDirParts(e),this.dirParts.forEach(r=>{r.length>1&&r.pop()}),this.followSymlinks=s,this.statMethod=s?hl:dl}checkGlobSymlink(e){return this.globSymlink===void 0&&(this.globSymlink=e.fullParentDir===this.fullWatchPath?!1:{realPath:e.fullParentDir,linkPath:this.fullWatchPath}),this.globSymlink?e.fullPath.replace(this.globSymlink.realPath,this.globSymlink.linkPath):e.fullPath}entryPath(e){return S.join(this.watchPath,S.relative(this.watchPath,this.checkGlobSymlink(e)))}filterPath(e){const{stats:u}=e;if(u&&u.isSymbolicLink())return this.filterDir(e);const s=this.entryPath(e);return(this.hasGlob&&typeof this.globFilter===nl?this.globFilter(s):!0)&&this.fsw._isntIgnored(s,u)&&this.fsw._hasReadPermissions(u)}getDirParts(e){if(!this.hasGlob)return[];const u=[];return(e.includes(tl)?Ga.expand(e):[e]).forEach(n=>{u.push(S.relative(this.watchPath,n).split(Qa))}),u}filterDir(e){if(this.hasGlob){const u=this.getDirParts(this.checkGlobSymlink(e));let s=!1;this.unmatchedGlob=!this.dirParts.some(n=>n.every((r,i)=>(r===sl&&(s=!0),s||!u[0][i]||au(r,u[0][i],pu))))}return!this.unmatchedGlob&&this.fsw._isntIgnored(this.entryPath(e),e.stats)}}class pl extends Ia{static{a(this,"FSWatcher")}constructor(e){super();const u={};e&&Object.assign(u,e),this._watched=new Map,this._closers=new Map,this._ignoredPaths=new Set,this._throttled=new Map,this._symlinkPaths=new Map,this._streams=new Set,this.closed=!1,X(u,"persistent")&&(u.persistent=!0),X(u,"ignoreInitial")&&(u.ignoreInitial=!1),X(u,"ignorePermissionErrors")&&(u.ignorePermissionErrors=!1),X(u,"interval")&&(u.interval=100),X(u,"binaryInterval")&&(u.binaryInterval=300),X(u,"disableGlobbing")&&(u.disableGlobbing=!1),u.enableBinaryInterval=u.binaryInterval!==u.interval,X(u,"useFsEvents")&&(u.useFsEvents=!u.usePolling),ln.canUse()||(u.useFsEvents=!1),X(u,"usePolling")&&!u.useFsEvents&&(u.usePolling=Dl),ol&&(u.usePolling=!0);const n=process.env.CHOKIDAR_USEPOLLING;if(n!==void 0){const o=n.toLowerCase();o==="false"||o==="0"?u.usePolling=!1:o==="true"||o==="1"?u.usePolling=!0:u.usePolling=!!o}const r=process.env.CHOKIDAR_INTERVAL;r&&(u.interval=Number.parseInt(r,10)),X(u,"atomic")&&(u.atomic=!u.usePolling&&!u.useFsEvents),u.atomic&&(this._pendingUnlinks=new Map),X(u,"followSymlinks")&&(u.followSymlinks=!0),X(u,"awaitWriteFinish")&&(u.awaitWriteFinish=!1),u.awaitWriteFinish===!0&&(u.awaitWriteFinish={});const i=u.awaitWriteFinish;i&&(i.stabilityThreshold||(i.stabilityThreshold=2e3),i.pollInterval||(i.pollInterval=100),this._pendingWrites=new Map),u.ignored&&(u.ignored=gu(u.ignored));let D=0;this._emitReady=()=>{D++,D>=this._readyCount&&(this._emitReady=rl,this._readyEmitted=!0,process.nextTick(()=>this.emit(Ua)))},this._emitRaw=(...o)=>this.emit(za,...o),this._readyEmitted=!1,this.options=u,u.useFsEvents?this._fsEventsHandler=new ln(this):this._nodeFsHandler=new ja(this),Object.freeze(u)}add(e,u,s){const{cwd:n,disableGlobbing:r}=this.options;this.closed=!1;let i=En(e);return n&&(i=i.map(D=>{const o=cl(D,n);return r||!lu(D)?o:Wa(o)})),i=i.filter(D=>D.startsWith(du)?(this._ignoredPaths.add(D.slice(1)),!1):(this._ignoredPaths.delete(D),this._ignoredPaths.delete(D+Eu),this._userIgnored=void 0,!0)),this.options.useFsEvents&&this._fsEventsHandler?(this._readyCount||(this._readyCount=i.length),this.options.persistent&&(this._readyCount+=i.length),i.forEach(D=>this._fsEventsHandler._addToFsEvents(D))):(this._readyCount||(this._readyCount=0),this._readyCount+=i.length,Promise.all(i.map(async D=>{const o=await this._nodeFsHandler._addToNodeFs(D,!s,0,0,u);return o&&this._emitReady(),o})).then(D=>{this.closed||D.filter(o=>o).forEach(o=>{this.add(S.dirname(o),S.basename(u||o))})})),this}unwatch(e){if(this.closed)return this;const u=En(e),{cwd:s}=this.options;return u.forEach(n=>{!S.isAbsolute(n)&&!this._closers.has(n)&&(s&&(n=S.join(s,n)),n=S.resolve(n)),this._closePath(n),this._ignoredPaths.add(n),this._watched.has(n)&&this._ignoredPaths.add(n+Eu),this._userIgnored=void 0}),this}close(){if(this.closed)return this._closePromise;this.closed=!0,this.removeAllListeners();const e=[];return this._closers.forEach(u=>u.forEach(s=>{const n=s();n instanceof Promise&&e.push(n)})),this._streams.forEach(u=>u.destroy()),this._userIgnored=void 0,this._readyCount=0,this._readyEmitted=!1,this._watched.forEach(u=>u.dispose()),["closers","watched","streams","symlinkPaths","throttled"].forEach(u=>{this[`_${u}`].clear()}),this._closePromise=e.length?Promise.all(e).then(()=>{}):Promise.resolve(),this._closePromise}getWatched(){const e={};return this._watched.forEach((u,s)=>{const n=this.options.cwd?S.relative(this.options.cwd,s):s;e[n||hn]=u.getChildren().sort()}),e}emitWithAll(e,u){this.emit(...u),e!==fu&&this.emit(cu,...u)}async _emit(e,u,s,n,r){if(this.closed)return;const i=this.options;il&&(u=S.normalize(u)),i.cwd&&(u=S.relative(i.cwd,u));const D=[e,u];r!==void 0?D.push(s,n,r):n!==void 0?D.push(s,n):s!==void 0&&D.push(s);const o=i.awaitWriteFinish;let c;if(o&&(c=this._pendingWrites.get(u)))return c.lastChange=new Date,this;if(i.atomic){if(e===cn)return this._pendingUnlinks.set(u,D),setTimeout(()=>{this._pendingUnlinks.forEach((f,h)=>{this.emit(...f),this.emit(cu,...f),this._pendingUnlinks.delete(h)})},typeof i.atomic=="number"?i.atomic:100),this;e===it&&this._pendingUnlinks.has(u)&&(e=D[0]=xe,this._pendingUnlinks.delete(u))}if(o&&(e===it||e===xe)&&this._readyEmitted){const f=a((h,l)=>{h?(e=D[0]=fu,D[1]=h,this.emitWithAll(e,D)):l&&(D.length>2?D[2]=l:D.push(l),this.emitWithAll(e,D))},"awfEmit");return this._awaitWriteFinish(u,o.stabilityThreshold,e,f),this}if(e===xe&&!this._throttle(xe,u,50))return this;if(i.alwaysStat&&s===void 0&&(e===it||e===Ka||e===xe)){const f=i.cwd?S.join(i.cwd,u):u;let h;try{h=await al(f)}catch{}if(!h||this.closed)return;D.push(h)}return this.emitWithAll(e,D),this}_handleError(e){const u=e&&e.code;return e&&u!=="ENOENT"&&u!=="ENOTDIR"&&(!this.options.ignorePermissionErrors||u!=="EPERM"&&u!=="EACCES")&&this.emit(fu,e),e||this.closed}_throttle(e,u,s){this._throttled.has(e)||this._throttled.set(e,new Map);const n=this._throttled.get(e),r=n.get(u);if(r)return r.count++,!1;let i;const D=a(()=>{const c=n.get(u),f=c?c.count:0;return n.delete(u),clearTimeout(i),c&&clearTimeout(c.timeoutObject),f},"clear");i=setTimeout(D,s);const o={timeoutObject:i,clear:D,count:0};return n.set(u,o),o}_incrReadyCount(){return this._readyCount++}_awaitWriteFinish(e,u,s,n){let r,i=e;this.options.cwd&&!S.isAbsolute(e)&&(i=S.join(this.options.cwd,e));const D=new Date,o=a(c=>{ou.stat(i,(f,h)=>{if(f||!this._pendingWrites.has(e)){f&&f.code!=="ENOENT"&&n(f);return}const l=Number(new Date);c&&h.size!==c.size&&(this._pendingWrites.get(e).lastChange=l);const p=this._pendingWrites.get(e);l-p.lastChange>=u?(this._pendingWrites.delete(e),n(void 0,h)):r=setTimeout(o,this.options.awaitWriteFinish.pollInterval,h)})},"awaitWriteFinish");this._pendingWrites.has(e)||(this._pendingWrites.set(e,{lastChange:D,cancelWait:a(()=>(this._pendingWrites.delete(e),clearTimeout(r),s),"cancelWait")}),r=setTimeout(o,this.options.awaitWriteFinish.pollInterval))}_getGlobIgnored(){return[...this._ignoredPaths.values()]}_isIgnored(e,u){if(this.options.atomic&&Za.test(e))return!0;if(!this._userIgnored){const{cwd:s}=this.options,n=this.options.ignored,r=n&&n.map(Fn(s)),i=gu(r).filter(o=>typeof o===Cu&&!lu(o)).map(o=>o+Eu),D=this._getGlobIgnored().map(Fn(s)).concat(r,i);this._userIgnored=au(D,void 0,pu)}return this._userIgnored([e,u])}_isntIgnored(e,u){return!this._isIgnored(e,u)}_getWatchHelpers(e,u){const s=u||this.options.disableGlobbing||!lu(e)?e:Ma(e),n=this.options.followSymlinks;return new El(e,s,n,this)}_getWatchedDir(e){this._boundRemove||(this._boundRemove=this._remove.bind(this));const u=S.resolve(e);return this._watched.has(u)||this._watched.set(u,new fl(u,this._boundRemove)),this._watched.get(u)}_hasReadPermissions(e){if(this.options.ignorePermissionErrors)return!0;const s=(e&&Number.parseInt(e.mode,10))&511;return!!(4&Number.parseInt(s.toString(8)[0],10))}_remove(e,u,s){const n=S.join(e,u),r=S.resolve(n);if(s=s??(this._watched.has(n)||this._watched.has(r)),!this._throttle("remove",n,100))return;!s&&!this.options.useFsEvents&&this._watched.size===1&&this.add(e,u,!0),this._getWatchedDir(n).getChildren().forEach(l=>this._remove(n,l));const o=this._getWatchedDir(e),c=o.has(u);o.remove(u),this._symlinkPaths.has(r)&&this._symlinkPaths.delete(r);let f=n;if(this.options.cwd&&(f=S.relative(this.options.cwd,n)),this.options.awaitWriteFinish&&this._pendingWrites.has(f)&&this._pendingWrites.get(f).cancelWait()===it)return;this._watched.delete(n),this._watched.delete(r);const h=s?Va:cn;c&&!this._isIgnored(n)&&this._emit(h,n),this.options.useFsEvents||this._closePath(n)}_closePath(e){this._closeFile(e);const u=S.dirname(e);this._getWatchedDir(u).remove(S.basename(e))}_closeFile(e){const u=this._closers.get(e);u&&(u.forEach(s=>s()),this._closers.delete(e))}_addPathCloser(e,u){if(!u)return;let s=this._closers.get(e);s||(s=[],this._closers.set(e,s)),s.push(u)}_readdirp(e,u){if(this.closed)return;const s={type:cu,alwaysStat:!0,lstat:!0,...u};let n=ka(e,s);return this._streams.add(n),n.once(Ya,()=>{n=void 0}),n.once(qa,()=>{n&&(this._streams.delete(n),n=void 0)}),n}}const Cl=a((t,e)=>{const u=new pl(e);return u.add(t),u},"watch");var Fl=Cl;const Dt=a((t=!0)=>{let e=!1;return u=>{if(e||u==="unknown-flag")return!0;if(u==="argument")return e=!0,t}},"ignoreAfterArgument"),gn=a((t,e=process.argv.slice(2))=>(yu(t,e,{ignore:Dt()}),e),"removeArgvFlags"),gl=a(t=>{let e=Buffer.alloc(0);return u=>{for(e=Buffer.concat([e,u]);e.length>4;){const s=e.readInt32BE(0);if(e.length>=4+s){const n=e.slice(4,4+s);t(n),e=e.slice(4+s)}else break}}},"bufferData"),mn=a(async()=>{const t=jn.createServer(u=>{u.on("data",gl(s=>{const n=JSON.parse(s.toString());t.emit("data",n)}))}),e=Bn(process.pid);return await ct.promises.mkdir(Un,{recursive:!0}),await ct.promises.rm(e,{force:!0}),await new Promise((u,s)=>{t.listen(e,u),t.on("error",s)}),t.unref(),process.on("exit",()=>{if(t.close(),!$n)try{ct.rmSync(e)}catch{}}),t},"createIpcServer"),ml=a(()=>new Date().toLocaleTimeString(),"currentTime"),Oe=a((...t)=>console.log(kn(ml()),Mn("[tsx]"),...t),"log"),_l="\x1Bc",Al=a((t,e)=>{let u;return function(){u&&clearTimeout(u),u=setTimeout(()=>Reflect.apply(t,this,arguments),e)}},"debounce"),_n={noCache:{type:Boolean,description:"Disable caching",default:!1},tsconfig:{type:String,description:"Custom tsconfig.json path"},clearScreen:{type:Boolean,description:"Clearing the screen on rerun",default:!0},ignore:{type:[String],description:"Paths & globs to exclude from being watched (Deprecated: use --exclude)"},include:{type:[String],description:"Additional paths & globs to watch"},exclude:{type:[String],description:"Paths & globs to exclude from being watched"}},yl=oi({name:"watch",parameters:["<script path>"],flags:_n,help:{description:"Run the script and watch for changes"},ignoreArgv:Dt(!1)},async t=>{const e=gn(_n,process.argv.slice(3)),u={noCache:t.flags.noCache,tsconfigPath:t.flags.tsconfig,clearScreen:t.flags.clearScreen,include:t.flags.include,exclude:[...t.flags.ignore,...t.flags.exclude],ipc:!0};let s,n=!1;(await mn()).on("data",l=>{if(l&&typeof l=="object"&&"type"in l&&l.type==="dependency"&&"path"in l&&typeof l.path=="string"){const p=l.path.startsWith("file:")?xn(l.path):l.path;Pn.isAbsolute(p)&&h.add(p)}});const i=a(()=>{if(!n)return rs(e,u)},"spawnProcess");let D=!1;const o=a(async(l,p="SIGTERM",C=5e3)=>{let g=!1;const y=new Promise(B=>{l.on("exit",H=>{g=!0,D=!1,B(H)})});return D=!0,l.kill(p),setTimeout(()=>{g||(Oe(me(`Process didn't exit in ${Math.floor(C/1e3)}s. Force killing...`)),l.kill("SIGKILL"))},C),await y},"killProcess"),c=Al(async(l,p)=>{const C=l?`${l?Gn(l):""}${p?` in ${Wn(`./${p}`)}`:""}`:"";if(D){Oe(C,me("Process hasn't exited. Killing process...")),s.kill("SIGKILL");return}s&&(s.exitCode===null?(Oe(C,me("Restarting...")),await o(s)):Oe(C,me("Rerunning...")),u.clearScreen&&process.stdout.write(_l)),s=i()},100);c();const f=a(l=>{n=!0,s?.exitCode===null?(D&&Oe(me("Previous process hasn't exited yet. Force killing...")),o(s,D?"SIGKILL":l).then(p=>{process.exit(p??0)},()=>{})):process.exit(lt.signals[l])},"relaySignal");process.on("SIGINT",f),process.on("SIGTERM",f);const h=Fl([...t._,...u.include],{cwd:process.cwd(),ignoreInitial:!0,ignored:["**/.*/**","**/.*","**/{node_modules,bower_components,vendor}/**",...u.exclude],ignorePermissionErrors:!0}).on("all",c);process.stdin.on("data",()=>c("Return key"))}),wl=a((t,e)=>{let u;e.on("data",r=>{r&&r.type==="signal"&&u&&u(r.signal)});const s=a(()=>{const r=new Promise(i=>{setTimeout(()=>i(void 0),30),u=i});return r.then(()=>{u=void 0},()=>{}),r},"waitForSignalFromChild"),n=a(async r=>{await s()!==r&&(t.kill(r),await s()!==r&&(t.on("exit",()=>{const o=lt.signals[r];process.exit(128+o)}),t.kill("SIGKILL")))},"relaySignalToChild");process.on("SIGINT",n),process.on("SIGTERM",n)},"relaySignals"),An={noCache:{type:Boolean,description:"Disable caching"},tsconfig:{type:String,description:"Custom tsconfig.json path"}};ju({name:"tsx",parameters:["[script path]"],commands:[yl],flags:{...An,version:{type:Boolean,alias:"v",description:"Show version"},help:{type:Boolean,alias:"h",description:"Show help"}},help:!1,ignoreArgv:Dt()},async t=>{t.flags.version?process.stdout.write(`tsx v${Sn}
node `):t.flags.help&&(t.showHelp({description:"Node.js runtime enhanced with esbuild for loading TypeScript & ESM"}),console.log(`${"-".repeat(45)}
`));const e={eval:{type:String,alias:"e"},print:{type:String,alias:"p"}},{_:u,flags:s}=ju({flags:{...e,inputType:String,test:Boolean},help:!1,ignoreArgv:Dt(!1)}),n=gn({...An,...e}),i=["print","eval"].find(c=>!!s[c]);if(i){const{inputType:c}=s,f=s[i],h=vn(f,{loader:"default",sourcefile:"/eval.ts",format:c==="module"?"esm":"cjs"});n.unshift(`--${i}`,h.code)}mu(Hn)&&s.test&&u.length===0&&n.push("**/{test,test/**/*,test-*,*[.-_]test}.?(c|m)@(t|j)s");const D=await mn(),o=rs(n,{noCache:!!t.flags.noCache,tsconfigPath:t.flags.tsconfig});wl(o,D),process.send&&o.on("message",c=>{process.send(c)}),o.send&&process.on("message",c=>{o.send(c)}),o.on("close",c=>{c===null&&(c=lt.signals[o.signalCode]+128),process.exit(c)})});
