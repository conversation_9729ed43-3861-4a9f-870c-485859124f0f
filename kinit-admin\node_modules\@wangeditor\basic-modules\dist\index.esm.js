import{i18nAddResources as t,DomEditor as e,t as n,genModalInputElems as r,genModalButtonElems as o}from"@wangeditor/core";import{jsx as i,h as u}from"snabbdom";import{Text as a,Editor as c,Element as l,Node as s,Transforms as f,Range as p,Point as d}from"slate";import h,{css as v,append as g,prepend as y,addClass as m,removeClass as b,hasClass as w,on as x,off as S,focus as k,attr as E,removeAttr as T,hide as M,show as H,parents as O,dataset as I,val as N,text as L,html as z,children as j,remove as A,find as P,width as V,height as C,filter as D,empty as B}from"dom7";import{nanoid as R}from"nanoid";import F from"lodash.throttle";t("en",{common:{ok:"OK",delete:"Delete",enter:"Enter"},blockQuote:{title:"Quote"},codeBlock:{title:"Code block"},color:{color:"Font color",bgColor:"Back color",default:"Default color",clear:"Clear back color"},divider:{title:"Divider"},emotion:{title:"Emotion"},fontSize:{title:"Font size",default:"Default"},fontFamily:{title:"Font family",default:"Default"},fullScreen:{title:"Full screen"},header:{title:"Header",text:"Text"},image:{netImage:"Net image",delete:"Delete image",edit:"Edit image",viewLink:"View link",src:"Image src",desc:"Description",link:"Image link"},indent:{decrease:"Decrease",increase:"Increase"},justify:{left:"Left",right:"Right",center:"Center",justify:"Justify"},lineHeight:{title:"Line height",default:"Default"},link:{insert:"Insert link",text:"Link text",url:"Link source",unLink:"Unlink",edit:"Edit link",view:"View link"},textStyle:{bold:"Bold",clear:"Clear styles",code:"Inline code",italic:"Italic",sub:"Sub",sup:"Sup",through:"Through",underline:"Underline"},undo:{undo:"undo",redo:"Redo"},todo:{todo:"Todo"}}),t("zh-CN",{common:{ok:"确定",delete:"删除",enter:"回车"},blockQuote:{title:"引用"},codeBlock:{title:"代码块"},color:{color:"文字颜色",bgColor:"背景色",default:"默认颜色",clear:"清除背景色"},divider:{title:"分割线"},emotion:{title:"表情"},fontSize:{title:"字号",default:"默认字号"},fontFamily:{title:"字体",default:"默认字体"},fullScreen:{title:"全屏"},header:{title:"标题",text:"正文"},image:{netImage:"网络图片",delete:"删除图片",edit:"编辑图片",viewLink:"查看链接",src:"图片地址",desc:"图片描述",link:"图片链接"},indent:{decrease:"减少缩进",increase:"增加缩进"},justify:{left:"左对齐",right:"右对齐",center:"居中对齐",justify:"两端对齐"},lineHeight:{title:"行高",default:"默认行高"},link:{insert:"插入链接",text:"链接文本",url:"链接地址",unLink:"取消链接",edit:"修改链接",view:"查看链接"},textStyle:{bold:"粗体",clear:"清除格式",code:"行内代码",italic:"斜体",sub:"下标",sup:"上标",through:"删除线",underline:"下划线"},undo:{undo:"撤销",redo:"重做"},todo:{todo:"待办"}});var _={type:"paragraph",renderElem:function(t,e,n){return i("p",null,e)}};var $={type:"paragraph",elemToHtml:function(t,e){return""===e?"<p><br></p>":"<p>"+e+"</p>"}},q="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function W(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function G(t){var e={exports:{}};return t(e,e.exports),e.exports}var U,X,Y=function(t){return t&&t.Math==Math&&t},J=Y("object"==typeof globalThis&&globalThis)||Y("object"==typeof window&&window)||Y("object"==typeof self&&self)||Y("object"==typeof q&&q)||function(){return this}()||Function("return this")(),K=function(t){try{return!!t()}catch(t){return!0}},Q=!K((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),Z=Function.prototype.call,tt=Z.bind?Z.bind(Z):function(){return Z.apply(Z,arguments)},et={}.propertyIsEnumerable,nt=Object.getOwnPropertyDescriptor,rt=nt&&!et.call({1:2},1)?function(t){var e=nt(this,t);return!!e&&e.enumerable}:et,ot={f:rt},it=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},ut=Function.prototype,at=ut.bind,ct=ut.call,lt=at&&at.bind(ct),st=at?function(t){return t&&lt(ct,t)}:function(t){return t&&function(){return ct.apply(t,arguments)}},ft=st({}.toString),pt=st("".slice),dt=function(t){return pt(ft(t),8,-1)},ht=J.Object,vt=st("".split),gt=K((function(){return!ht("z").propertyIsEnumerable(0)}))?function(t){return"String"==dt(t)?vt(t,""):ht(t)}:ht,yt=J.TypeError,mt=function(t){if(null==t)throw yt("Can't call method on "+t);return t},bt=function(t){return gt(mt(t))},wt=function(t){return"function"==typeof t},xt=function(t){return"object"==typeof t?null!==t:wt(t)},St=function(t){return wt(t)?t:void 0},kt=function(t,e){return arguments.length<2?St(J[t]):J[t]&&J[t][e]},Et=st({}.isPrototypeOf),Tt=kt("navigator","userAgent")||"",Mt=J.process,Ht=J.Deno,Ot=Mt&&Mt.versions||Ht&&Ht.version,It=Ot&&Ot.v8;It&&(X=(U=It.split("."))[0]>0&&U[0]<4?1:+(U[0]+U[1])),!X&&Tt&&(!(U=Tt.match(/Edge\/(\d+)/))||U[1]>=74)&&(U=Tt.match(/Chrome\/(\d+)/))&&(X=+U[1]);var Nt=X,Lt=!!Object.getOwnPropertySymbols&&!K((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Nt&&Nt<41})),zt=Lt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,jt=J.Object,At=zt?function(t){return"symbol"==typeof t}:function(t){var e=kt("Symbol");return wt(e)&&Et(e.prototype,jt(t))},Pt=J.String,Vt=function(t){try{return Pt(t)}catch(t){return"Object"}},Ct=J.TypeError,Dt=function(t){if(wt(t))return t;throw Ct(Vt(t)+" is not a function")},Bt=function(t,e){var n=t[e];return null==n?void 0:Dt(n)},Rt=J.TypeError,Ft=Object.defineProperty,_t=function(t,e){try{Ft(J,t,{value:e,configurable:!0,writable:!0})}catch(n){J[t]=e}return e},$t=J["__core-js_shared__"]||_t("__core-js_shared__",{}),qt=G((function(t){(t.exports=function(t,e){return $t[t]||($t[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),Wt=J.Object,Gt=function(t){return Wt(mt(t))},Ut=st({}.hasOwnProperty),Xt=Object.hasOwn||function(t,e){return Ut(Gt(t),e)},Yt=0,Jt=Math.random(),Kt=st(1..toString),Qt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Kt(++Yt+Jt,36)},Zt=qt("wks"),te=J.Symbol,ee=te&&te.for,ne=zt?te:te&&te.withoutSetter||Qt,re=function(t){if(!Xt(Zt,t)||!Lt&&"string"!=typeof Zt[t]){var e="Symbol."+t;Lt&&Xt(te,t)?Zt[t]=te[t]:Zt[t]=zt&&ee?ee(e):ne(e)}return Zt[t]},oe=J.TypeError,ie=re("toPrimitive"),ue=function(t,e){if(!xt(t)||At(t))return t;var n,r=Bt(t,ie);if(r){if(void 0===e&&(e="default"),n=tt(r,t,e),!xt(n)||At(n))return n;throw oe("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&wt(n=t.toString)&&!xt(r=tt(n,t)))return r;if(wt(n=t.valueOf)&&!xt(r=tt(n,t)))return r;if("string"!==e&&wt(n=t.toString)&&!xt(r=tt(n,t)))return r;throw Rt("Can't convert object to primitive value")}(t,e)},ae=function(t){var e=ue(t,"string");return At(e)?e:e+""},ce=J.document,le=xt(ce)&&xt(ce.createElement),se=function(t){return le?ce.createElement(t):{}},fe=!Q&&!K((function(){return 7!=Object.defineProperty(se("div"),"a",{get:function(){return 7}}).a})),pe=Object.getOwnPropertyDescriptor,de={f:Q?pe:function(t,e){if(t=bt(t),e=ae(e),fe)try{return pe(t,e)}catch(t){}if(Xt(t,e))return it(!tt(ot.f,t,e),t[e])}},he=J.String,ve=J.TypeError,ge=function(t){if(xt(t))return t;throw ve(he(t)+" is not an object")},ye=J.TypeError,me=Object.defineProperty,be={f:Q?me:function(t,e,n){if(ge(t),e=ae(e),ge(n),fe)try{return me(t,e,n)}catch(t){}if("get"in n||"set"in n)throw ye("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},we=Q?function(t,e,n){return be.f(t,e,it(1,n))}:function(t,e,n){return t[e]=n,t},xe=st(Function.toString);wt($t.inspectSource)||($t.inspectSource=function(t){return xe(t)});var Se,ke,Ee,Te=$t.inspectSource,Me=J.WeakMap,He=wt(Me)&&/native code/.test(Te(Me)),Oe=qt("keys"),Ie=function(t){return Oe[t]||(Oe[t]=Qt(t))},Ne={},Le=J.TypeError,ze=J.WeakMap;if(He||$t.state){var je=$t.state||($t.state=new ze),Ae=st(je.get),Pe=st(je.has),Ve=st(je.set);Se=function(t,e){if(Pe(je,t))throw new Le("Object already initialized");return e.facade=t,Ve(je,t,e),e},ke=function(t){return Ae(je,t)||{}},Ee=function(t){return Pe(je,t)}}else{var Ce=Ie("state");Ne[Ce]=!0,Se=function(t,e){if(Xt(t,Ce))throw new Le("Object already initialized");return e.facade=t,we(t,Ce,e),e},ke=function(t){return Xt(t,Ce)?t[Ce]:{}},Ee=function(t){return Xt(t,Ce)}}var De={set:Se,get:ke,has:Ee,enforce:function(t){return Ee(t)?ke(t):Se(t,{})},getterFor:function(t){return function(e){var n;if(!xt(e)||(n=ke(e)).type!==t)throw Le("Incompatible receiver, "+t+" required");return n}}},Be=Function.prototype,Re=Q&&Object.getOwnPropertyDescriptor,Fe=Xt(Be,"name"),_e={EXISTS:Fe,PROPER:Fe&&"something"===function(){}.name,CONFIGURABLE:Fe&&(!Q||Q&&Re(Be,"name").configurable)},$e=G((function(t){var e=_e.CONFIGURABLE,n=De.get,r=De.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var a,c=!!u&&!!u.unsafe,l=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,f=u&&void 0!==u.name?u.name:n;wt(i)&&("Symbol("===String(f).slice(0,7)&&(f="["+String(f).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Xt(i,"name")||e&&i.name!==f)&&we(i,"name",f),(a=r(i)).source||(a.source=o.join("string"==typeof f?f:""))),t!==J?(c?!s&&t[n]&&(l=!0):delete t[n],l?t[n]=i:we(t,n,i)):l?t[n]=i:_t(n,i)})(Function.prototype,"toString",(function(){return wt(this)&&n(this).source||Te(this)}))})),qe=Math.ceil,We=Math.floor,Ge=function(t){var e=+t;return e!=e||0===e?0:(e>0?We:qe)(e)},Ue=Math.max,Xe=Math.min,Ye=function(t,e){var n=Ge(t);return n<0?Ue(n+e,0):Xe(n,e)},Je=Math.min,Ke=function(t){return t>0?Je(Ge(t),9007199254740991):0},Qe=function(t){return Ke(t.length)},Ze=function(t){return function(e,n,r){var o,i=bt(e),u=Qe(i),a=Ye(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},tn={includes:Ze(!0),indexOf:Ze(!1)},en=tn.indexOf,nn=st([].push),rn=function(t,e){var n,r=bt(t),o=0,i=[];for(n in r)!Xt(Ne,n)&&Xt(r,n)&&nn(i,n);for(;e.length>o;)Xt(r,n=e[o++])&&(~en(i,n)||nn(i,n));return i},on=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],un=on.concat("length","prototype"),an={f:Object.getOwnPropertyNames||function(t){return rn(t,un)}},cn={f:Object.getOwnPropertySymbols},ln=st([].concat),sn=kt("Reflect","ownKeys")||function(t){var e=an.f(ge(t)),n=cn.f;return n?ln(e,n(t)):e},fn=function(t,e){for(var n=sn(e),r=be.f,o=de.f,i=0;i<n.length;i++){var u=n[i];Xt(t,u)||r(t,u,o(e,u))}},pn=/#|\.prototype\./,dn=function(t,e){var n=vn[hn(t)];return n==yn||n!=gn&&(wt(e)?K(e):!!e)},hn=dn.normalize=function(t){return String(t).replace(pn,".").toLowerCase()},vn=dn.data={},gn=dn.NATIVE="N",yn=dn.POLYFILL="P",mn=dn,bn=de.f,wn=function(t,e){var n,r,o,i,u,a=t.target,c=t.global,l=t.stat;if(n=c?J:l?J[a]||_t(a,{}):(J[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=bn(n,r))&&u.value:n[r],!mn(c?r:a+(l?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;fn(i,o)}(t.sham||o&&o.sham)&&we(i,"sham",!0),$e(n,r,i,t)}},xn=st(st.bind),Sn=Array.isArray||function(t){return"Array"==dt(t)},kn={};kn[re("toStringTag")]="z";var En="[object z]"===String(kn),Tn=re("toStringTag"),Mn=J.Object,Hn="Arguments"==dt(function(){return arguments}()),On=En?dt:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Mn(t),Tn))?n:Hn?dt(e):"Object"==(r=dt(e))&&wt(e.callee)?"Arguments":r},In=function(){},Nn=[],Ln=kt("Reflect","construct"),zn=/^\s*(?:class|function)\b/,jn=st(zn.exec),An=!zn.exec(In),Pn=function(t){if(!wt(t))return!1;try{return Ln(In,Nn,t),!0}catch(t){return!1}},Vn=!Ln||K((function(){var t;return Pn(Pn.call)||!Pn(Object)||!Pn((function(){t=!0}))||t}))?function(t){if(!wt(t))return!1;switch(On(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return An||!!jn(zn,Te(t))}:Pn,Cn=re("species"),Dn=J.Array,Bn=function(t,e){return new(function(t){var e;return Sn(t)&&(e=t.constructor,(Vn(e)&&(e===Dn||Sn(e.prototype))||xt(e)&&null===(e=e[Cn]))&&(e=void 0)),void 0===e?Dn:e}(t))(0===e?0:e)},Rn=st([].push),Fn=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,l,s,f){for(var p,d,h=Gt(c),v=gt(h),g=function(t,e){return Dt(t),void 0===e?t:xn?xn(t,e):function(){return t.apply(e,arguments)}}(l,s),y=Qe(v),m=0,b=f||Bn,w=e?b(c,y):n||u?b(c,0):void 0;y>m;m++)if((a||m in v)&&(d=g(p=v[m],m,h),t))if(e)w[m]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return m;case 2:Rn(w,p)}else switch(t){case 4:return!1;case 7:Rn(w,p)}return i?-1:r||o?o:w}},_n={forEach:Fn(0),map:Fn(1),filter:Fn(2),some:Fn(3),every:Fn(4),find:Fn(5),findIndex:Fn(6),filterReject:Fn(7)},$n=re("species"),qn=function(t){return Nt>=51||!K((function(){var e=[];return(e.constructor={})[$n]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Wn=_n.filter;wn({target:"Array",proto:!0,forced:!qn("filter")},{filter:function(t){return Wn(this,t,arguments.length>1?arguments[1]:void 0)}});var Gn=En?{}.toString:function(){return"[object "+On(this)+"]"};En||$e(Object.prototype,"toString",Gn,{unsafe:!0});var Un,Xn=J.String,Yn=function(t){if("Symbol"===On(t))throw TypeError("Cannot convert a Symbol value to a string");return Xn(t)},Jn=function(){var t=ge(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Kn=J.RegExp,Qn=K((function(){var t=Kn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Zn=Qn||K((function(){return!Kn("a","y").sticky})),tr={BROKEN_CARET:Qn||K((function(){var t=Kn("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Zn,UNSUPPORTED_Y:Qn},er=Object.keys||function(t){return rn(t,on)},nr=Q?Object.defineProperties:function(t,e){ge(t);for(var n,r=bt(e),o=er(e),i=o.length,u=0;i>u;)be.f(t,n=o[u++],r[n]);return t},rr=kt("document","documentElement"),or=Ie("IE_PROTO"),ir=function(){},ur=function(t){return"<script>"+t+"<\/script>"},ar=function(t){t.write(ur("")),t.close();var e=t.parentWindow.Object;return t=null,e},cr=function(){try{Un=new ActiveXObject("htmlfile")}catch(t){}var t,e;cr="undefined"!=typeof document?document.domain&&Un?ar(Un):((e=se("iframe")).style.display="none",rr.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ur("document.F=Object")),t.close(),t.F):ar(Un);for(var n=on.length;n--;)delete cr.prototype[on[n]];return cr()};Ne[or]=!0;var lr,sr,fr=Object.create||function(t,e){var n;return null!==t?(ir.prototype=ge(t),n=new ir,ir.prototype=null,n[or]=t):n=cr(),void 0===e?n:nr(n,e)},pr=J.RegExp,dr=K((function(){var t=pr(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),hr=J.RegExp,vr=K((function(){var t=hr("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),gr=De.get,yr=qt("native-string-replace",String.prototype.replace),mr=RegExp.prototype.exec,br=mr,wr=st("".charAt),xr=st("".indexOf),Sr=st("".replace),kr=st("".slice),Er=(sr=/b*/g,tt(mr,lr=/a/,"a"),tt(mr,sr,"a"),0!==lr.lastIndex||0!==sr.lastIndex),Tr=tr.BROKEN_CARET,Mr=void 0!==/()??/.exec("")[1];(Er||Mr||Tr||dr||vr)&&(br=function(t){var e,n,r,o,i,u,a,c=this,l=gr(c),s=Yn(t),f=l.raw;if(f)return f.lastIndex=c.lastIndex,e=tt(br,f,s),c.lastIndex=f.lastIndex,e;var p=l.groups,d=Tr&&c.sticky,h=tt(Jn,c),v=c.source,g=0,y=s;if(d&&(h=Sr(h,"y",""),-1===xr(h,"g")&&(h+="g"),y=kr(s,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==wr(s,c.lastIndex-1))&&(v="(?: "+v+")",y=" "+y,g++),n=new RegExp("^(?:"+v+")",h)),Mr&&(n=new RegExp("^"+v+"$(?!\\s)",h)),Er&&(r=c.lastIndex),o=tt(mr,d?n:c,y),d?o?(o.input=kr(o.input,g),o[0]=kr(o[0],g),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Er&&o&&(c.lastIndex=c.global?o.index+o[0].length:r),Mr&&o&&o.length>1&&tt(yr,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&p)for(o.groups=u=fr(null),i=0;i<p.length;i++)u[(a=p[i])[0]]=o[a[1]];return o});var Hr=br;wn({target:"RegExp",proto:!0,forced:/./.exec!==Hr},{exec:Hr});var Or=Function.prototype,Ir=Or.apply,Nr=Or.bind,Lr=Or.call,zr="object"==typeof Reflect&&Reflect.apply||(Nr?Lr.bind(Ir):function(){return Lr.apply(Ir,arguments)}),jr=re("species"),Ar=RegExp.prototype,Pr=function(t,e,n,r){var o=re(t),i=!K((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!K((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[jr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!u||n){var a=st(/./[o]),c=e(o,""[t],(function(t,e,n,r,o){var u=st(t),c=e.exec;return c===Hr||c===Ar.exec?i&&!o?{done:!0,value:a(e,n,r)}:{done:!0,value:u(n,e,r)}:{done:!1}}));$e(String.prototype,t,c[0]),$e(Ar,o,c[1])}r&&we(Ar[o],"sham",!0)},Vr=st("".charAt),Cr=st("".charCodeAt),Dr=st("".slice),Br=function(t){return function(e,n){var r,o,i=Yn(mt(e)),u=Ge(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=Cr(i,u))<55296||r>56319||u+1===a||(o=Cr(i,u+1))<56320||o>57343?t?Vr(i,u):r:t?Dr(i,u,u+2):o-56320+(r-55296<<10)+65536}},Rr={codeAt:Br(!1),charAt:Br(!0)}.charAt,Fr=function(t,e,n){return e+(n?Rr(t,e).length:1)},_r=Math.floor,$r=st("".charAt),qr=st("".replace),Wr=st("".slice),Gr=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ur=/\$([$&'`]|\d{1,2})/g,Xr=function(t,e,n,r,o,i){var u=n+t.length,a=r.length,c=Ur;return void 0!==o&&(o=Gt(o),c=Gr),qr(i,c,(function(i,c){var l;switch($r(c,0)){case"$":return"$";case"&":return t;case"`":return Wr(e,0,n);case"'":return Wr(e,u);case"<":l=o[Wr(c,1,-1)];break;default:var s=+c;if(0===s)return i;if(s>a){var f=_r(s/10);return 0===f?i:f<=a?void 0===r[f-1]?$r(c,1):r[f-1]+$r(c,1):i}l=r[s-1]}return void 0===l?"":l}))},Yr=J.TypeError,Jr=function(t,e){var n=t.exec;if(wt(n)){var r=tt(n,t,e);return null!==r&&ge(r),r}if("RegExp"===dt(t))return tt(Hr,t,e);throw Yr("RegExp#exec called on incompatible receiver")},Kr=re("replace"),Qr=Math.max,Zr=Math.min,to=st([].concat),eo=st([].push),no=st("".indexOf),ro=st("".slice),oo="$0"==="a".replace(/./,"$0"),io=!!/./[Kr]&&""===/./[Kr]("a","$0");Pr("replace",(function(t,e,n){var r=io?"$":"$0";return[function(t,n){var r=mt(this),o=null==t?void 0:Bt(t,Kr);return o?tt(o,t,r,n):tt(e,Yn(r),t,n)},function(t,o){var i=ge(this),u=Yn(t);if("string"==typeof o&&-1===no(o,r)&&-1===no(o,"$<")){var a=n(e,i,u,o);if(a.done)return a.value}var c=wt(o);c||(o=Yn(o));var l=i.global;if(l){var s=i.unicode;i.lastIndex=0}for(var f=[];;){var p=Jr(i,u);if(null===p)break;if(eo(f,p),!l)break;""===Yn(p[0])&&(i.lastIndex=Fr(u,Ke(i.lastIndex),s))}for(var d,h="",v=0,g=0;g<f.length;g++){for(var y=Yn((p=f[g])[0]),m=Qr(Zr(Ge(p.index),u.length),0),b=[],w=1;w<p.length;w++)eo(b,void 0===(d=p[w])?d:String(d));var x=p.groups;if(c){var S=to([y],b,m,u);void 0!==x&&eo(S,x);var k=Yn(zr(o,void 0,S))}else k=Xr(y,u,m,b,x,o);m>=v&&(h+=ro(u,v,m)+k,v=m+y.length)}return h+ro(u,v)}]}),!!K((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!oo||io);var uo=re("unscopables"),ao=Array.prototype;null==ao[uo]&&be.f(ao,uo,{configurable:!0,value:fr(null)});var co=function(t){ao[uo][t]=!0},lo=_n.find,so=!0;"find"in[]&&Array(1).find((function(){so=!1})),wn({target:"Array",proto:!0,forced:so},{find:function(t){return lo(this,t,arguments.length>1?arguments[1]:void 0)}}),co("find");var fo=re("match"),po=function(t){var e;return xt(t)&&(void 0!==(e=t[fo])?!!e:"RegExp"==dt(t))},ho=J.TypeError,vo=re("species"),go=function(t,e){var n,r=ge(t).constructor;return void 0===r||null==(n=ge(r)[vo])?e:function(t){if(Vn(t))return t;throw ho(Vt(t)+" is not a constructor")}(n)},yo=function(t,e,n){var r=ae(e);r in t?be.f(t,r,it(0,n)):t[r]=n},mo=J.Array,bo=Math.max,wo=function(t,e,n){for(var r=Qe(t),o=Ye(e,r),i=Ye(void 0===n?r:n,r),u=mo(bo(i-o,0)),a=0;o<i;o++,a++)yo(u,a,t[o]);return u.length=a,u},xo=tr.UNSUPPORTED_Y,So=Math.min,ko=[].push,Eo=st(/./.exec),To=st(ko),Mo=st("".slice);Pr("split",(function(t,e,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var r=Yn(mt(this)),o=void 0===n?4294967295:n>>>0;if(0===o)return[];if(void 0===t)return[r];if(!po(t))return tt(e,r,t,o);for(var i,u,a,c=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),s=0,f=new RegExp(t.source,l+"g");(i=tt(Hr,f,r))&&!((u=f.lastIndex)>s&&(To(c,Mo(r,s,i.index)),i.length>1&&i.index<r.length&&zr(ko,c,wo(i,1)),a=i[0].length,s=u,c.length>=o));)f.lastIndex===i.index&&f.lastIndex++;return s===r.length?!a&&Eo(f,"")||To(c,""):To(c,Mo(r,s)),c.length>o?wo(c,0,o):c}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:tt(e,this,t,n)}:e,[function(e,n){var o=mt(this),i=null==e?void 0:Bt(e,t);return i?tt(i,e,o,n):tt(r,Yn(o),e,n)},function(t,o){var i=ge(this),u=Yn(t),a=n(r,i,u,o,r!==e);if(a.done)return a.value;var c=go(i,RegExp),l=i.unicode,s=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(xo?"g":"y"),f=new c(xo?"^(?:"+i.source+")":i,s),p=void 0===o?4294967295:o>>>0;if(0===p)return[];if(0===u.length)return null===Jr(f,u)?[u]:[];for(var d=0,h=0,v=[];h<u.length;){f.lastIndex=xo?0:h;var g,y=Jr(f,xo?Mo(u,h):u);if(null===y||(g=So(Ke(f.lastIndex+(xo?h:0)),u.length))===d)h=Fr(u,h,l);else{if(To(v,Mo(u,d,h)),v.length===p)return v;for(var m=1;m<=y.length-1;m++)if(To(v,y[m]),v.length===p)return v;h=d=g}}return To(v,Mo(u,d)),v}]}),!!K((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),xo);var Ho,Oo="\t\n\v\f\r                　\u2028\u2029\ufeff",Io=st("".replace),No="["+Oo+"]",Lo=RegExp("^"+No+No+"*"),zo=RegExp(No+No+"*$"),jo=function(t){return function(e){var n=Yn(mt(e));return 1&t&&(n=Io(n,Lo,"")),2&t&&(n=Io(n,zo,"")),n}},Ao={start:jo(1),end:jo(2),trim:jo(3)},Po=_e.PROPER,Vo=Ao.trim;function Co(t){return 0===h("<div>"+t+"</div>").children().filter((function(t){return"BR"!==t.tagName})).length}function Do(t){return 0===t.length?"":t[0].outerHTML}function Bo(t){return t.length?t[0].tagName.toLowerCase():""}function Ro(t,e){for(var n="",r=(t.attr("style")||"").split(";"),o=r.length,i=0;i<o;i++){var u=r[i];if(u){var a=u.split(":");a[0].trim()===e&&(n=a[1].trim())}}return n}wn({target:"String",proto:!0,forced:(Ho="trim",K((function(){return!!Oo[Ho]()||"​᠎"!=="​᠎"[Ho]()||Po&&Oo[Ho].name!==Ho})))},{trim:function(){return Vo(this)}}),wn({global:!0},{globalThis:J}),v&&(h.fn.css=v),g&&(h.fn.append=g),y&&(h.fn.prepend=y),m&&(h.fn.addClass=m),b&&(h.fn.removeClass=b),w&&(h.fn.hasClass=w),x&&(h.fn.on=x),S&&(h.fn.off=S),k&&(h.fn.focus=k),E&&(h.fn.attr=E),T&&(h.fn.removeAttr=T),M&&(h.fn.hide=M),H&&(h.fn.show=H),O&&(h.fn.parents=O),I&&(h.fn.dataset=I),N&&(h.fn.val=N),L&&(h.fn.text=L),z&&(h.fn.html=z),j&&(h.fn.children=j),A&&(h.fn.remove=A),P&&(h.fn.find=P),V&&(h.fn.width=V),C&&(h.fn.height=C),D&&(h.fn.filter=D),B&&(h.fn.empty=B);var Fo={selector:"p:not([data-w-e-type])",parseElemHtml:function(t,e,n){var r=h(t);return 0===(e=e.filter((function(t){return!!a.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:r.text().replace(/\s+/gm," ")}]),{type:"paragraph",children:e}}},_o=function(t,e){return _o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},_o(t,e)};
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function $o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}_o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var qo=function(){return qo=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},qo.apply(this,arguments)};function Wo(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}c((r=r.apply(t,e||[])).next())}))}function Go(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function Uo(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Xo(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function Yo(t){var e=Xo(c.nodes(t,{match:function(e){return t.children[0]===e},mode:"highest"}),1),n=e[0];if(null==n)return!1;var r=n[0];if(!l.isElement(r))return!1;if("paragraph"===r.type)return!1;if(""!==s.string(r))return!1;var o=r.children,i=void 0===o?[]:o;return!!a.isText(i[0])&&(f.setNodes(t,{type:"paragraph"}),!0)}var Jo={renderElems:[_],elemsToHtml:[$],parseElemsHtml:[Fo],editorPlugin:function(t){var e=t.deleteBackward,n=t.deleteForward;t.insertText,t.insertBreak;var r=t;return r.deleteBackward=function(t){Yo(r)||e(t)},r.deleteForward=function(t){Yo(r)||n(t)},r}},Ko=/"/g,Qo=st("".replace),Zo=function(t,e,n,r){var o=Yn(mt(t)),i="<"+e;return""!==n&&(i+=" "+n+'="'+Qo(Yn(r),Ko,"&quot;")+'"'),i+">"+o+"</"+e+">"},ti=function(t){return K((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))};function ei(t,e){var n=e,r=t,o=r.bold,i=r.italic,u=r.underline;return o&&(n="<strong>"+n+"</strong>"),r.code&&(n="<code>"+n+"</code>"),i&&(n="<em>"+n+"</em>"),u&&(n="<u>"+n+"</u>"),r.through&&(n="<s>"+n+"</s>"),r.sub&&(n="<sub>"+n+"</sub>"),r.sup&&(n="<sup>"+n+"</sup>"),n}function ni(t,e){return 0!==t.length&&(!!t[0].matches(e)||t.find(e).length>0)}wn({target:"String",proto:!0,forced:ti("bold")},{bold:function(){return Zo(this,"b","","")}}),wn({target:"String",proto:!0,forced:ti("italics")},{italics:function(){return Zo(this,"i","","")}}),wn({target:"String",proto:!0,forced:ti("sub")},{sub:function(){return Zo(this,"sub","","")}}),wn({target:"String",proto:!0,forced:ti("sup")},{sup:function(){return Zo(this,"sup","","")}});var ri={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},oi=se("span").classList,ii=oi&&oi.constructor&&oi.constructor.prototype,ui=ii===Object.prototype?void 0:ii,ai=function(t,e){var n=[][t];return!!n&&K((function(){n.call(null,e||function(){throw 1},1)}))},ci=_n.forEach,li=ai("forEach")?[].forEach:function(t){return ci(this,t,arguments.length>1?arguments[1]:void 0)},si=function(t){if(t&&t.forEach!==li)try{we(t,"forEach",li)}catch(e){t.forEach=li}};for(var fi in ri)ri[fi]&&si(J[fi]&&J[fi].prototype);function pi(t,n){return null==t.selection||!!Xo(c.nodes(t,{match:function(n){return"pre"===e.getNodeType(n)||!!c.isVoid(t,n)},universal:!0}),1)[0]}function di(t,e){Object.keys(e).forEach((function(e){"text"!==e&&c.removeMark(t,e)}))}si(ui),wn({target:"Object",stat:!0,forced:K((function(){er(1)}))},{keys:function(t){return er(Gt(t))}});var hi=function(){function t(){this.marksNeedToRemove=[],this.tag="button"}return t.prototype.getValue=function(t){var e=this.mark,n=c.marks(t);return n?n[e]:!!Xo(c.nodes(t,{match:function(t){return!0===t[e]}}),1)[0]},t.prototype.isActive=function(t){return!!this.getValue(t)},t.prototype.isDisabled=function(t){return pi(t,this.mark)},t.prototype.exec=function(t,e){var n=this.mark,r=this.marksNeedToRemove;e?t.removeMark(n):(t.addMark(n,!0),r&&r.forEach((function(e){return t.removeMark(e)})))},t}(),vi='<svg viewBox="0 0 1024 1024"><path d="M707.872 484.64A254.88 254.88 0 0 0 768 320c0-141.152-114.848-256-256-256H192v896h384c141.152 0 256-114.848 256-256a256.096 256.096 0 0 0-124.128-219.36zM384 192h101.504c55.968 0 101.504 57.408 101.504 128s-45.536 128-101.504 128H384V192z m159.008 640H384v-256h159.008c58.464 0 106.016 57.408 106.016 128s-47.552 128-106.016 128z"></path></svg>',gi='<svg viewBox="0 0 1024 1024"><path d="M704 64l128 0 0 416c0 159.072-143.264 288-320 288s-320-128.928-320-288l0-416 128 0 0 416c0 40.16 18.24 78.688 51.36 108.512 36.896 33.216 86.848 51.488 140.64 51.488s103.744-18.304 140.64-51.488c33.12-29.792 51.36-68.352 51.36-108.512l0-416zM192 832l640 0 0 128-640 0z"></path></svg>',yi='<svg viewBox="0 0 1024 1024"><path d="M896 64v64h-128L448 896h128v64H128v-64h128L576 128h-128V64z"></path></svg>',mi='<svg viewBox="0 0 1024 1024"><path d="M1024 512v64h-234.496c27.52 38.496 42.496 82.688 42.496 128 0 70.88-36.672 139.04-100.576 186.976C672.064 935.488 594.144 960 512 960s-160.064-24.512-219.424-69.024C228.64 843.04 192 774.88 192 704h128c0 69.376 87.936 128 192 128s192-58.624 192-128-87.936-128-192-128H0v-64h299.52a385.984 385.984 0 0 1-6.944-5.024C228.64 459.04 192 390.88 192 320s36.672-139.04 100.576-186.976C351.936 88.512 429.856 64 512 64s160.064 24.512 219.424 69.024C795.328 180.96 832 249.12 832 320h-128c0-69.376-87.936-128-192-128s-192 58.624-192 128 87.936 128 192 128c78.976 0 154.048 22.688 212.48 64H1024z"></path></svg>',bi='<svg viewBox="0 0 1024 1024"><path d="M576 736l96 96 320-320L672 192l-96 96 224 224zM448 288l-96-96L32 512l320 320 96-96-224-224z"></path></svg>',wi='<svg viewBox="0 0 1024 1024"><path d="M864 0a160 160 0 0 1 128 256l-64 64-224-224 64-64c26.752-20.096 59.968-32 96-32zM64 736l-64 288 288-64 592-592-224-224L64 736z m651.584-372.416l-448 448-55.168-55.168 448-448 55.168 55.168z"></path></svg>',xi='<svg viewBox="0 0 1024 1024"><path d="M924.402464 1023.068211H0.679665V99.345412h461.861399v98.909208H99.596867v725.896389h725.896389V561.206811h98.909208z" p-id="10909"></path><path d="M930.805104 22.977336l69.965436 69.965436-453.492405 453.492404-69.965435-69.901489z" p-id="10910"></path><path d="M1022.464381 304.030081h-98.917201V99.345412H709.230573V0.428211h313.233808z"></path></svg>',Si='<svg viewBox="0 0 1024 1024"><path d="M64 864h896v96H64zM360.58 576h302.85l81.53 224h102.16L579.24 64H444.77L176.89 800h102.16l81.53-224zM512 159.96L628.49 480H395.52L512 159.96z"></path></svg>',ki='<svg viewBox="0 0 1024 1024"><path d="M510.030769 315.076923l84.676923 196.923077h-177.230769l76.8-196.923077h15.753846zM945.230769 157.538462v708.923076c0 43.323077-35.446154 78.769231-78.769231 78.769231H157.538462c-43.323077 0-78.769231-35.446154-78.769231-78.769231V157.538462c0-43.323077 35.446154-78.769231 78.769231-78.769231h708.923076c43.323077 0 78.769231 35.446154 78.769231 78.769231z m-108.307692 643.938461L600.615385 216.615385c-5.907692-11.815385-15.753846-19.692308-29.538462-19.692308h-139.815385c-11.815385 0-23.630769 7.876923-27.56923 19.692308l-216.615385 584.861538c-3.938462 11.815385 3.938462 25.6 17.723077 25.6h80.738462c11.815385 0 23.630769-9.846154 27.56923-21.661538l63.015385-175.261539h263.876923l68.923077 175.261539c3.938462 11.815385 15.753846 21.661538 27.569231 21.661538h80.738461c13.784615 0 23.630769-13.784615 19.692308-25.6z"></path></svg>',Ei='<svg viewBox="0 0 1024 1024"><path d="M64 512h384v128h-128V1024h-128V640h-128z m896-256H708.2496v768h-136.4992V256H320V128h640z"></path></svg>',Ti='<svg viewBox="0 0 1024 1024"><path d="M956.788364 152.110545h-24.110546l23.924364 9.029819 0.186182 121.018181h-65.070546l-86.574545-130.048H566.551273v650.14691l130.048 64.977454v65.163636h-390.050909v-65.163636l129.954909-64.977454V152.110545H198.283636L111.429818 282.065455H46.545455V69.259636C46.545455 33.792 82.664727 22.062545 98.955636 22.062545h812.683637c23.738182 0 45.056 15.173818 45.056 41.053091V169.425455v-17.221819z"></path></svg>',Mi='<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m256-512v384l-256-192z"></path></svg>',Hi='<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',Oi='<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',Ii='<svg viewBox="0 0 1024 1024"><path d="M972.8 793.6v102.4H256v-102.4h716.8z m0-230.4v102.4H51.2v-102.4h921.6z m0-230.4v102.4H256v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',Ni='<svg viewBox="0 0 1024 1024"><path d="M870.4 793.6v102.4H153.6v-102.4h716.8z m102.4-230.4v102.4H51.2v-102.4h921.6z m-102.4-230.4v102.4H153.6v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',Li='<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m0 192h1024v128H0z m0 192h1024v128H0z m0 192h1024v128H0z m0 192h1024v128H0z"></path></svg>',zi='<svg viewBox="0 0 1024 1024"><path d="M768 206.016v50.016h128v64h-192V174.016l128-60V64h-128V0h192v146.016zM676 256h-136L352 444 164 256H28l256 256-256 256h136L352 580 540 768h136l-256-256z"></path></svg>',ji='<svg viewBox="0 0 1024 1024"><path d="M768 910.016v50.016h128v64h-192v-146.016l128-60V768h-128v-64h192v146.016zM676 256h-136L352 444 164 256H28l256 256-256 256h136L352 580 540 768h136l-256-256z"></path></svg>',Ai=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="bold",e.title=n("textStyle.bold"),e.iconSvg=vi,e.hotkey="mod+b",e}return $o(e,t),e}(hi),Pi=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="code",e.title=n("textStyle.code"),e.iconSvg=bi,e.hotkey="mod+e",e}return $o(e,t),e}(hi),Vi=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="italic",e.title=n("textStyle.italic"),e.iconSvg=yi,e.hotkey="mod+i",e}return $o(e,t),e}(hi),Ci=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="through",e.title=n("textStyle.through"),e.iconSvg=mi,e.hotkey="mod+shift+x",e}return $o(e,t),e}(hi),Di=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="underline",e.title=n("textStyle.underline"),e.iconSvg=gi,e.hotkey="mod+u",e}return $o(e,t),e}(hi),Bi=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="sub",e.marksNeedToRemove=["sup"],e.title=n("textStyle.sub"),e.iconSvg=ji,e.hotkey="",e}return $o(e,t),e}(hi),Ri=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.mark="sup",e.marksNeedToRemove=["sub"],e.title=n("textStyle.sup"),e.iconSvg=zi,e.hotkey="",e}return $o(e,t),e}(hi),Fi=function(){function t(){this.title=n("textStyle.clear"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M969.382408 288.738615l-319.401123-270.852152a67.074236 67.074236 0 0 0-96.459139 5.74922l-505.931379 574.922021a68.35184 68.35184 0 0 0-17.886463 47.910169 74.101061 74.101061 0 0 0 24.274486 47.910168l156.50655 132.232065h373.060512L975.131628 383.281347a67.074236 67.074236 0 0 0-5.74922-96.459139z m-440.134747 433.746725H264.144729l-90.071117-78.572676c-5.74922-5.74922-12.137243-12.137243-12.137243-17.886463a36.411728 36.411728 0 0 1 5.749221-24.274485l210.804741-240.828447 265.102932 228.691204z m-439.495945 180.781036h843.218964a60.047411 60.047411 0 1 1 0 120.733624H89.751716a60.047411 60.047411 0 1 1 0-120.733624z m0 0"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return pi(t)},t.prototype.exec=function(t,e){var n,r,o=c.nodes(t,{match:function(t){return a.isText(t)},universal:!0});try{for(var i=Uo(o),u=i.next();!u.done;u=i.next()){di(t,u.value[0])}}catch(t){n={error:t}}finally{try{u&&!u.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}},t}(),_i={renderStyle:function(t,e){var n=t,r=n.bold,o=n.italic,u=n.underline,a=n.code,c=n.through,l=n.sub,s=n.sup,f=e;return r&&(f=i("strong",null,f)),a&&(f=i("code",null,f)),o&&(f=i("em",null,f)),u&&(f=i("u",null,f)),c&&(f=i("s",null,f)),l&&(f=i("sub",null,f)),s&&(f=i("sup",null,f)),f},menus:[{key:"bold",factory:function(){return new Ai}},{key:"underline",factory:function(){return new Di}},{key:"italic",factory:function(){return new Vi}},{key:"through",factory:function(){return new Ci}},{key:"code",factory:function(){return new Pi}},{key:"sub",factory:function(){return new Bi}},{key:"sup",factory:function(){return new Ri}},{key:"clearStyle",factory:function(){return new Fi}}],styleToHtml:function(t,e){if(!a.isText(t))return e;if(Co(e))return ei(t,e);var n=h(e);if("br"===Bo(n))return ei(t,"<br>");var r=n.html();return r=ei(t,r),n.html(r),Do(n)},parseStyleHtml:function(t,e,n){var r=h(t);if(!a.isText(e))return e;var o=e;return ni(r,"b,strong")&&(o.bold=!0),ni(r,"i,em")&&(o.italic=!0),ni(r,"u")&&(o.underline=!0),ni(r,"s,strike")&&(o.through=!0),ni(r,"sub")&&(o.sub=!0),ni(r,"sup")&&(o.sup=!0),ni(r,"code")&&(o.code=!0),o}};function $i(t){return function(e,n,r){return i("h"+t,null,n)}}var qi={type:"header1",renderElem:$i(1)},Wi={type:"header2",renderElem:$i(2)},Gi={type:"header3",renderElem:$i(3)},Ui={type:"header4",renderElem:$i(4)},Xi={type:"header5",renderElem:$i(5)},Yi=_e.PROPER,Ji=RegExp.prototype,Ki=Ji.toString,Qi=st(Jn),Zi=K((function(){return"/a/b"!=Ki.call({source:"a",flags:"b"})})),tu=Yi&&"toString"!=Ki.name;(Zi||tu)&&$e(RegExp.prototype,"toString",(function(){var t=ge(this),e=Yn(t.source),n=t.flags;return"/"+e+"/"+Yn(void 0===n&&Et(Ji,t)&&!("flags"in Ji)?Qi(t):n)}),{unsafe:!0});var eu,nu=J.TypeError,ru=function(t){if(po(t))throw nu("The method doesn't accept regular expressions");return t},ou=re("match"),iu=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[ou]=!1,"/./"[t](e)}catch(t){}}return!1},uu=de.f,au=st("".startsWith),cu=st("".slice),lu=Math.min,su=iu("startsWith");function fu(t){var n=Xo(c.nodes(t,{match:function(t){return e.getNodeType(t).startsWith("header")},universal:!0}),1),r=n[0];if(null==r)return"paragraph";var o=Xo(r,1)[0];return e.getNodeType(o)}function pu(t){return null==t.selection||!Xo(c.nodes(t,{match:function(t){var n=e.getNodeType(t);return"paragraph"===n||!!n.startsWith("header")},universal:!0,mode:"highest"}),1)[0]}function du(t,e){e&&f.setNodes(t,{type:e})}wn({target:"String",proto:!0,forced:!!(su||(eu=uu(String.prototype,"startsWith"),!eu||eu.writable))&&!su},{startsWith:function(t){var e=Yn(mt(this));ru(t);var n=Ke(lu(arguments.length>1?arguments[1]:void 0,e.length)),r=Yn(t);return au?au(e,r,n):cu(e,n,n+r.length)===r}});var hu=function(){function t(){this.title=n("header.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M960 960c-51.2 0-102.4-3.2-153.6-3.2-51.2 0-99.2 3.2-150.4 3.2-19.2 0-28.8-22.4-28.8-38.4 0-51.2 57.6-28.8 86.4-48 19.2-12.8 19.2-60.8 19.2-80v-224-19.2c-9.6-3.2-19.2-3.2-28.8-3.2H320c-9.6 0-19.2 0-28.8 3.2V780.8c0 22.4 0 80 22.4 92.8 28.8 19.2 96-6.4 96 44.8 0 16-9.6 41.6-28.8 41.6-54.4 0-105.6-3.2-160-3.2-48 0-96 3.2-147.2 3.2-19.2 0-28.8-22.4-28.8-38.4 0-51.2 51.2-28.8 80-48 19.2-12.8 19.2-60.8 19.2-83.2V294.4c0-28.8 3.2-115.2-22.4-131.2-25.6-16-86.4 9.6-86.4-41.6 0-16 6.4-41.6 28.8-41.6 51.2 0 105.6 3.2 156.8 3.2 48 0 96-3.2 144-3.2 19.2 0 28.8 22.4 28.8 41.6 0 48-57.6 25.6-83.2 41.6-19.2 12.8-19.2 73.6-19.2 92.8v201.6c6.4 3.2 16 3.2 22.4 3.2h400c6.4 0 12.8 0 22.4-3.2V256c0-22.4 0-80-19.2-92.8-28.8-16-86.4 6.4-86.4-41.6 0-16 9.6-41.6 28.8-41.6 51.2 0 99.2 3.2 150.4 3.2 48 0 99.2-3.2 147.2-3.2 19.2 0 28.8 22.4 28.8 41.6 0 51.2-57.6 25.6-86.4 41.6-19.2 12.8-19.2 70.4-19.2 92.8v537.6c0 19.2 0 67.2 19.2 80 28.8 19.2 89.6-6.4 89.6 44.8 0 19.2-6.4 41.6-28.8 41.6z"></path></svg>',this.tag="select",this.width=60}return t.prototype.getOptions=function(t){var e=[{value:"header1",text:"H1",styleForRenderMenuList:{"font-size":"32px","font-weight":"bold"}},{value:"header2",text:"H2",styleForRenderMenuList:{"font-size":"24px","font-weight":"bold"}},{value:"header3",text:"H3",styleForRenderMenuList:{"font-size":"18px","font-weight":"bold"}},{value:"header4",text:"H4",styleForRenderMenuList:{"font-size":"16px","font-weight":"bold"}},{value:"header5",text:"H5",styleForRenderMenuList:{"font-size":"13px","font-weight":"bold"}},{value:"paragraph",text:n("header.text")}],r=this.getValue(t).toString();return e.forEach((function(t){t.value===r?t.selected=!0:delete t.selected})),e},t.prototype.isActive=function(t){return!1},t.prototype.getValue=function(t){return fu(t)},t.prototype.isDisabled=function(t){return pu(t)},t.prototype.exec=function(t,e){du(t,e.toString())},t}(),vu=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){return fu(t)},t.prototype.isActive=function(t){return this.getValue(t)===this.type},t.prototype.isDisabled=function(t){return pu(t)},t.prototype.exec=function(t,e){var n=this.type;du(t,e===n?"paragraph":n)},t}(),gu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H1",e.type="header1",e}return $o(e,t),e}(vu),yu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H2",e.type="header2",e}return $o(e,t),e}(vu),mu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H3",e.type="header3",e}return $o(e,t),e}(vu),bu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H4",e.type="header4",e}return $o(e,t),e}(vu),wu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="H5",e.type="header5",e}return $o(e,t),e}(vu),xu={key:"headerSelect",factory:function(){return new hu}},Su={key:"header1",factory:function(){return new gu}},ku={key:"header2",factory:function(){return new yu}},Eu={key:"header3",factory:function(){return new mu}},Tu={key:"header4",factory:function(){return new bu}},Mu={key:"header5",factory:function(){return new wu}};function Hu(t){return function(e,n){return"<h"+t+">"+n+"</h"+t+">"}}function Ou(t){return function(e,n,r){var o=h(e);return 0===(n=n.filter((function(t){return!!a.isText(t)||!!r.isInline(t)}))).length&&(n=[{text:o.text().replace(/\s+/gm," ")}]),{type:"header"+t,children:n}}}var Iu={renderElems:[qi,Wi,Gi,Ui,Xi],elemsToHtml:[{type:"header1",elemToHtml:Hu(1)},{type:"header2",elemToHtml:Hu(2)},{type:"header3",elemToHtml:Hu(3)},{type:"header4",elemToHtml:Hu(4)},{type:"header5",elemToHtml:Hu(5)}],parseElemsHtml:[{selector:"h1:not([data-w-e-type])",parseElemHtml:Ou(1)},{selector:"h2:not([data-w-e-type])",parseElemHtml:Ou(2)},{selector:"h3:not([data-w-e-type])",parseElemHtml:Ou(3)},{selector:"h4:not([data-w-e-type])",parseElemHtml:Ou(4)},{selector:"h5:not([data-w-e-type])",parseElemHtml:Ou(5)}],menus:[xu,Su,ku,Eu,Tu,Mu],editorPlugin:function(t){var n=t.insertBreak;t.insertNode;var r=t;return r.insertBreak=function(){var o=Xo(c.nodes(r,{match:function(t){return e.getNodeType(t).startsWith("header")},universal:!0}),1)[0];if(o)if(e.isSelectionAtLineEnd(t,o[1])){f.insertNodes(r,{type:"paragraph",children:[{text:""}]},{mode:"highest"})}else n();else n()},r}},Nu=Object.assign,Lu=Object.defineProperty,zu=st([].concat),ju=!Nu||K((function(){if(Q&&1!==Nu({b:1},Nu(Lu({},"a",{enumerable:!0,get:function(){Lu(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),r="abcdefghijklmnopqrst";return t[n]=7,r.split("").forEach((function(t){e[t]=t})),7!=Nu({},t)[n]||er(Nu({},e)).join("")!=r}))?function(t,e){for(var n=Gt(t),r=arguments.length,o=1,i=cn.f,u=ot.f;r>o;)for(var a,c=gt(arguments[o++]),l=i?zu(er(c),i(c)):er(c),s=l.length,f=0;s>f;)a=l[f++],Q&&!tt(u,c,a)||(n[a]=c[a]);return n}:Nu;function Au(t,e){null==t.data&&(t.data={});var n=t.data;null==n.style&&(n.style={}),Object.assign(n.style,e)}wn({target:"Object",stat:!0,forced:Object.assign!==ju},{assign:ju});var Pu={selector:"font",preParseHtml:function(t){var e=h(t);if("font"!==Bo(e))return t;var n=e.attr("color")||"";return n&&(e.removeAttr("color"),e.css("color",n)),e[0]}};var Vu=function(){function t(){this.tag="button",this.showDropPanel=!0,this.$content=null}return t.prototype.exec=function(t,e){},t.prototype.getValue=function(t){var e=this.mark,n=c.marks(t);return n&&n[e]?n[e]:""},t.prototype.isActive=function(t){return!!this.getValue(t)},t.prototype.isDisabled=function(t){return null==t.selection||!!Xo(c.nodes(t,{match:function(n){return"pre"===e.getNodeType(n)||!!c.isVoid(t,n)},universal:!0}),1)[0]},t.prototype.getPanelContentElem=function(t){var e=this.mark;if(null==this.$content){var r=h('<ul class="w-e-panel-content-color"></ul>');r.on("click","li",(function(n){var r=n.target;if(null!=r&&(n.preventDefault(),null!=t.selection)){var o=h(r).attr("data-value");"0"===o?c.removeMark(t,e):c.addMark(t,e,o)}})),this.$content=r}var o=this.$content;if(null==o)return document.createElement("ul");o.empty();var i=this.getValue(t),u=t.getMenuConfig(e).colors;(void 0===u?[]:u).forEach((function(t){var e=h('<div class="color-block" data-value="'+t+'"></div>');e.css("background-color",t);var n=h('<li data-value="'+t+'"></li>');i===t&&n.addClass("active"),n.append(e),o.append(n)}));var a="";"color"===e&&(a=n("color.default")),"bgColor"===e&&(a=n("color.clear"));var l=h('\n      <li data-value="0" class="clear">\n        <svg viewBox="0 0 1024 1024"><path d="M236.8 128L896 787.2V128H236.8z m614.4 704L192 172.8V832h659.2zM192 64h704c38.4 0 64 25.6 64 64v704c0 38.4-25.6 64-64 64H192c-38.4 0-64-25.6-64-64V128c0-38.4 25.6-64 64-64z"></path></svg>\n        '+a+"\n      </li>\n    ");return o.prepend(l),o[0]},t}(),Cu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("color.color"),e.iconSvg=Si,e.mark="color",e}return $o(e,t),e}(Vu),Du=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("color.bgColor"),e.iconSvg=ki,e.mark="bgColor",e}return $o(e,t),e}(Vu),Bu=["rgb(0, 0, 0)","rgb(38, 38, 38)","rgb(89, 89, 89)","rgb(140, 140, 140)","rgb(191, 191, 191)","rgb(217, 217, 217)","rgb(233, 233, 233)","rgb(245, 245, 245)","rgb(250, 250, 250)","rgb(255, 255, 255)","rgb(225, 60, 57)","rgb(231, 95, 51)","rgb(235, 144, 58)","rgb(245, 219, 77)","rgb(114, 192, 64)","rgb(89, 191, 192)","rgb(66, 144, 247)","rgb(54, 88, 226)","rgb(106, 57, 201)","rgb(216, 68, 147)","rgb(251, 233, 230)","rgb(252, 237, 225)","rgb(252, 239, 212)","rgb(252, 251, 207)","rgb(231, 246, 213)","rgb(218, 244, 240)","rgb(217, 237, 250)","rgb(224, 232, 250)","rgb(237, 225, 248)","rgb(246, 226, 234)","rgb(255, 163, 158)","rgb(255, 187, 150)","rgb(255, 213, 145)","rgb(255, 251, 143)","rgb(183, 235, 143)","rgb(135, 232, 222)","rgb(145, 213, 255)","rgb(173, 198, 255)","rgb(211, 173, 247)","rgb(255, 173, 210)","rgb(255, 77, 79)","rgb(255, 122, 69)","rgb(255, 169, 64)","rgb(255, 236, 61)","rgb(115, 209, 61)","rgb(54, 207, 201)","rgb(64, 169, 255)","rgb(89, 126, 247)","rgb(146, 84, 222)","rgb(247, 89, 171)","rgb(207, 19, 34)","rgb(212, 56, 13)","rgb(212, 107, 8)","rgb(212, 177, 6)","rgb(56, 158, 13)","rgb(8, 151, 156)","rgb(9, 109, 217)","rgb(29, 57, 196)","rgb(83, 29, 171)","rgb(196, 29, 127)","rgb(130, 0, 20)","rgb(135, 20, 0)","rgb(135, 56, 0)","rgb(97, 71, 0)","rgb(19, 82, 0)","rgb(0, 71, 79)","rgb(0, 58, 140)","rgb(6, 17, 120)","rgb(34, 7, 94)","rgb(120, 6, 80)"];var Ru={renderStyle:function(t,e){var n=t,r=n.color,o=n.bgColor,i=e;return r&&Au(i,{color:r}),o&&Au(i,{backgroundColor:o}),i},styleToHtml:function(t,e){if(!a.isText(t))return e;var n,r=t,o=r.color,i=r.bgColor;return o||i?((Co(e)||"span"!==Bo(n=h(e)))&&(n=h("<span>"+e+"</span>")),o&&n.css("color",o),i&&n.css("background-color",i),Do(n)):e},preParseHtml:[Pu],parseStyleHtml:function(t,e,n){var r=h(t);if(!a.isText(e))return e;var o=e,i=Ro(r,"color");i&&(o.color=i);var u=Ro(r,"background-color");return u||(u=Ro(r,"background")),u&&(o.bgColor=u),o},menus:[{key:"color",factory:function(){return new Cu},config:{colors:Bu}},{key:"bgColor",factory:function(){return new Du},config:{colors:Bu}}]},Fu=function(t){if("string"!=typeof t)return!1;var e=t.match(_u);if(!e)return!1;var n=e[1];if(!n)return!1;if($u.test(n)||qu.test(n))return!0;return!1},_u=/^(?:\w+:)?\/\/(\S+)$/,$u=/^localhost[\:?\d]*(?:[^\:?\d]\S*)?$/,qu=/^[^\s\.]+\.\S{2,}$/;var Wu=tn.includes;function Gu(t){return void 0===t&&(t="r"),t+"-"+R()}function Uu(t){return t.replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Xu(t,e,n,r){return Wo(this,void 0,void 0,(function(){var o,i;return Go(this,(function(u){switch(u.label){case 0:return(o=e.getMenuConfig(t).checkLink)?[4,o(n,r)]:[3,2];case 1:if("string"==typeof(i=u.sent()))return e.alert(i,"error"),[2,!1];if(null==i)return[2,!1];u.label=2;case 2:return[2,!0]}}))}))}function Yu(t,e,n){return Wo(this,void 0,void 0,(function(){var r;return Go(this,(function(o){switch(o.label){case 0:return(r=e.getMenuConfig(t).parseLinkUrl)?[4,r(n)]:[3,2];case 1:return[2,o.sent()];case 2:return[2,n]}}))}))}function Ju(t){return null==t.selection||!!e.getSelectedElems(t).some((function(e){var n=e.type;return!!t.isVoid(e)||(!!["pre","code","link"].includes(n)||void 0)}))}function Ku(t,e){return{type:"link",url:Uu(t),children:e?[{text:e}]:[]}}function Qu(t,e,n){return Wo(this,void 0,void 0,(function(){var r,o,i;return Go(this,(function(u){switch(u.label){case 0:return n?(e||(e=n),t.restoreSelection(),Ju(t)?[2]:[4,Xu("insertLink",t,e,n)]):[2];case 1:return u.sent()?[4,Yu("insertLink",t,n)]:[2];case 2:return r=u.sent(),null==(o=t.selection)?[2]:(p.isCollapsed(o)?(t.insertText(" "),i=Ku(r,e),f.insertNodes(t,i),t.insertFragment([{text:" "}])):c.string(t,o)!==e?(t.deleteFragment(),i=Ku(r,e),f.insertNodes(t,i)):(i=Ku(r),f.wrapNodes(t,i,{split:!0}),f.collapse(t,{edge:"end"})),[2])}}))}))}wn({target:"Array",proto:!0},{includes:function(t){return Wu(this,t,arguments.length>1?arguments[1]:void 0)}}),co("includes");var Zu={type:"link",renderElem:function(t,e,n){var r=t,o=r.url,u=r.target;return i("a",{href:o,target:void 0===u?"_blank":u},e)}};var ta={type:"link",elemToHtml:function(t,e){var n=t,r=n.url,o=n.target;return'<a href="'+r+'" target="'+(void 0===o?"_blank":o)+'">'+e+"</a>"}};var ea={selector:"a:not([data-w-e-type])",parseElemHtml:function(t,e,n){var r=h(t);return 0===(e=e.filter((function(t){return!!a.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:r.text().replace(/\s+/gm," ")}]),{type:"link",url:r.attr("href")||"",target:r.attr("target")||"",children:e}}};function na(){return Gu("w-e-insert-link")}var ra=function(){function t(){this.title=n("link.insert"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M440.224 635.776a51.84 51.84 0 0 1-36.768-15.232c-95.136-95.136-95.136-249.92 0-345.056l192-192C641.536 37.408 702.816 12.032 768 12.032s126.432 25.376 172.544 71.456c95.136 95.136 95.136 249.92 0 345.056l-87.776 87.776a51.968 51.968 0 1 1-73.536-73.536l87.776-87.776a140.16 140.16 0 0 0 0-197.984c-26.432-26.432-61.6-40.992-99.008-40.992s-72.544 14.56-99.008 40.992l-192 192a140.16 140.16 0 0 0 0 197.984 51.968 51.968 0 0 1-36.768 88.768z"></path><path d="M256 1012a242.4 242.4 0 0 1-172.544-71.456c-95.136-95.136-95.136-249.92 0-345.056l87.776-87.776a51.968 51.968 0 1 1 73.536 73.536l-87.776 87.776a140.16 140.16 0 0 0 0 197.984c26.432 26.432 61.6 40.992 99.008 40.992s72.544-14.56 99.008-40.992l192-192a140.16 140.16 0 0 0 0-197.984 51.968 51.968 0 1 1 73.536-73.536c95.136 95.136 95.136 249.92 0 345.056l-192 192A242.4 242.4 0 0 1 256 1012z"></path></svg>',this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.textInputId=na(),this.urlInputId=na(),this.buttonId=na()}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return Ju(t)},t.prototype.getModalPositionNode=function(t){return null},t.prototype.getModalContentElem=function(t){var e=t.selection,i=this,u=i.textInputId,a=i.urlInputId,l=i.buttonId,s=Xo(r(n("link.text"),u),2),f=s[0],d=s[1],v=h(d),g=Xo(r(n("link.url"),a),2),y=g[0],m=g[1],b=h(m),w=Xo(o(l,n("common.ok")),1)[0];if(null==this.$content){var x=h("<div></div>");x.on("click","#"+l,(function(e){e.preventDefault();var n=x.find("#"+u).val(),r=x.find("#"+a).val();Qu(t,n,r),t.hidePanelOrModal()})),this.$content=x}var S=this.$content;if(S.empty(),S.append(f),S.append(y),S.append(w),null==e||p.isCollapsed(e))v.val("");else{var k=c.string(t,e);v.val(k)}return b.val(""),setTimeout((function(){v.focus()})),S[0]},t}();function oa(){return Gu("w-e-update-link")}var ia=function(){function t(){this.title=n("link.edit"),this.iconSvg=wi,this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.urlInputId=oa(),this.buttonId=oa()}return t.prototype.getSelectedLinkElem=function(t){var n=e.getSelectedNodeByType(t,"link");return null==n?null:n},t.prototype.getValue=function(t){var e=this.getSelectedLinkElem(t);return e&&e.url||""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedLinkElem(t)},t.prototype.getModalPositionNode=function(t){return e.getSelectedNodeByType(t,"link")},t.prototype.getModalContentElem=function(t){var i=this.urlInputId,u=this.buttonId,a=Xo(r(n("link.url"),i),2),c=a[0],l=a[1],p=h(l),d=Xo(o(u,n("common.ok")),1)[0];if(null==this.$content){var v=h("<div></div>");v.on("click","button",(function(n){n.preventDefault(),t.restoreSelection();var r=e.getSelectedNodeByType(t,"link"),o=r?s.string(r):"",u=v.find("#"+i).val();!function(t,n,r){Wo(this,void 0,void 0,(function(){var o,i;return Go(this,(function(u){switch(u.label){case 0:return r?[4,Xu("editLink",t,n,r)]:[2];case 1:return u.sent()?[4,Yu("editLink",t,r)]:[2];case 2:return o=u.sent(),i={url:Uu(o)},f.setNodes(t,i,{match:function(t){return e.checkNodeType(t,"link")}}),[2]}}))}))}(t,o,u),t.hidePanelOrModal()})),this.$content=v}var g=this.$content;g.empty(),g.append(c),g.append(d);var y=this.getValue(t);return p.val(y),setTimeout((function(){p.focus()})),g[0]},t}(),ua=function(){function t(){this.title=n("link.unLink"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M608.16328 811.815036c9.371954 9.371954 9.371954 24.56788 0 33.941834l-89.347563 89.347564c-118.525421 118.523421-311.38448 118.531421-429.919901 0-118.527421-118.529421-118.527421-311.39048 0-429.917901l89.349564-89.349563c9.371954-9.371954 24.56788-9.371954 33.941834 0l79.195613 79.195613c9.371954 9.371954 9.371954 24.56788 0 33.941834l-89.349563 89.347564c-56.143726 56.145726-56.143726 147.49928 0 203.645005 56.143726 56.143726 147.49928 56.145726 203.647005 0l89.347564-89.347563c9.371954-9.371954 24.56788-9.371954 33.941834 0l79.193613 79.195613z m-113.135447-520.429459c9.371954 9.371954 24.56788 9.371954 33.941834 0l89.347564-89.347564c56.143726-56.149726 147.49928-56.145726 203.647006 0 56.143726 56.145726 56.143726 147.49928 0 203.645006l-89.349564 89.347564c-9.371954 9.371954-9.371954 24.56788 0 33.941834l79.195613 79.195613c9.371954 9.371954 24.56788 9.371954 33.941834 0l89.349564-89.349563c118.529421-118.529421 118.529421-311.38848 0-429.917901-118.531421-118.527421-311.38848-118.527421-429.919901 0l-89.347563 89.347564c-9.371954 9.371954-9.371954 24.56788 0 33.941834l79.193613 79.195613z m469.653707 718.556492l45.253779-45.253779c18.745908-18.745908 18.745908-49.13776 0-67.881669L127.195629 14.062931c-18.745908-18.745908-49.13776-18.745908-67.881669 0L14.058181 59.31871c-18.745908 18.745908-18.745908 49.13776 0 67.881669l882.74169 882.74169c18.745908 18.743908 49.13776 18.743908 67.881669 0z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==e.getSelectedNodeByType(t,"link")},t.prototype.exec=function(t,n){this.isDisabled(t)||f.unwrapNodes(t,{match:function(t){return e.checkNodeType(t,"link")}})},t}(),aa=function(){function t(){this.title=n("link.view"),this.iconSvg=xi,this.tag="button"}return t.prototype.getSelectedLinkElem=function(t){var n=e.getSelectedNodeByType(t,"link");return null==n?null:n},t.prototype.getValue=function(t){var e=this.getSelectedLinkElem(t);return e&&e.url||""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedLinkElem(t)},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){if(!e||"string"!=typeof e)throw new Error("View link failed, link url is '"+e+"'");window.open(e,"_blank")}},t}();var ca={checkLink:function(t,e){return!0},parseLinkUrl:function(t){return t}},la={renderElems:[Zu],elemsToHtml:[ta],parseElemsHtml:[ea],menus:[{key:"insertLink",factory:function(){return new ra},config:ca},{key:"editLink",factory:function(){return new ia},config:ca},{key:"unLink",factory:function(){return new ua}},{key:"viewLink",factory:function(){return new aa}}],editorPlugin:function(t){var n=t.isInline,r=t.insertData,o=t.normalizeNode;t.insertNode,t.insertText;var i=t;return i.isInline=function(t){return"link"===t.type||n(t)},i.insertData=function(t){var e=t.getData("text/plain");if(Fu(e)){if(!Ju(i)){var n=i.selection;if(null!=n){var o=c.string(i,n);Qu(i,o,e)}}}else r(t)},i.normalizeNode=function(t){var n=Xo(t,2),r=n[0],u=n[1];return"link"!==e.getNodeType(r)?o([r,u]):""===s.string(r)?f.removeNodes(i,{at:u}):o([r,u])},i}};var sa=st(1..valueOf),fa=J.RangeError,pa=J.RangeError,da=J.String,ha=Math.floor,va=st((function(t){var e=Yn(mt(this)),n="",r=Ge(t);if(r<0||r==1/0)throw fa("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(e+=e))1&r&&(n+=e);return n})),ga=st("".slice),ya=st(1..toFixed),ma=function(t,e,n){return 0===e?n:e%2==1?ma(t,e-1,n*t):ma(t*t,e/2,n)},ba=function(t,e,n){for(var r=-1,o=n;++r<6;)o+=e*t[r],t[r]=o%1e7,o=ha(o/1e7)},wa=function(t,e){for(var n=6,r=0;--n>=0;)r+=t[n],t[n]=ha(r/e),r=r%e*1e7},xa=function(t){for(var e=6,n="";--e>=0;)if(""!==n||0===e||0!==t[e]){var r=da(t[e]);n=""===n?r:n+va("0",7-r.length)+r}return n};function Sa(t,n){return"w-e-image-container-"+e.findKey(t,n).id}function ka(t,n,r,o){var u=h("body"),a=Sa(t,n),c=o.width,l=o.height,s=0,p=0,d=0,v=!1,g=null;function y(n){g=function(){var t=h("#"+a);if(0===t.length)throw new Error("Cannot find image container elem");return t}(),s=n;var r=g.find("img");if(0===r.length)throw new Error("Cannot find image elem");p=r.width(),d=r.height(),u.on("mousemove",m),u.on("mouseup",b);var o=e.getHoverbar(t);o&&o.hideAndClean()}var m=F((function(t){t.preventDefault();var e=t.clientX,n=p+(v?s-e:e-s),r=d*(n/p);null!=g&&(n<=15||r<=15||(g.css("width",n+"px"),g.css("height",r+"px")))}),100);function b(r){if(u.off("mousemove",m),null!=g){var o=g.width().toFixed(2),i=g.height().toFixed(2),a={style:qo(qo({},n.style),{width:o+"px",height:i+"px"})};f.setNodes(t,a,{at:e.findPath(t,n)}),u.off("mouseup",b)}}var w={};return c&&(w.width=c),l&&(w.height=l),i("div",{id:a,style:w,className:"w-e-image-container w-e-selected-image-container",on:{mousedown:function(t){var e=h(t.target);e.hasClass("w-e-image-dragger")&&(t.preventDefault(),(e.hasClass("left-top")||e.hasClass("left-bottom"))&&(v=!0),y(t.clientX))}}},r,i("div",{className:"w-e-image-dragger left-top"}),i("div",{className:"w-e-image-dragger right-top"}),i("div",{className:"w-e-image-dragger left-bottom"}),i("div",{className:"w-e-image-dragger right-bottom"}))}wn({target:"Number",proto:!0,forced:K((function(){return"0.000"!==ya(8e-5,3)||"1"!==ya(.9,0)||"1.25"!==ya(1.255,2)||"1000000000000000128"!==ya(0xde0b6b3a7640080,0)}))||!K((function(){ya({})}))},{toFixed:function(t){var e,n,r,o,i=sa(this),u=Ge(t),a=[0,0,0,0,0,0],c="",l="0";if(u<0||u>20)throw pa("Incorrect fraction digits");if(i!=i)return"NaN";if(i<=-1e21||i>=1e21)return da(i);if(i<0&&(c="-",i=-i),i>1e-21)if(n=(e=function(t){for(var e=0,n=t;n>=4096;)e+=12,n/=4096;for(;n>=2;)e+=1,n/=2;return e}(i*ma(2,69,1))-69)<0?i*ma(2,-e,1):i/ma(2,e,1),n*=4503599627370496,(e=52-e)>0){for(ba(a,0,n),r=u;r>=7;)ba(a,1e7,0),r-=7;for(ba(a,ma(10,r,1),0),r=e-1;r>=23;)wa(a,1<<23),r-=23;wa(a,1<<r),ba(a,1,1),wa(a,2),l=xa(a)}else ba(a,0,n),ba(a,1<<-e,0),l=xa(a)+va("0",u);return l=u>0?c+((o=l.length)<=u?"0."+va("0",u-o)+l:ga(l,0,o-u)+"."+ga(l,o-u)):c+l}});var Ea={type:"image",renderElem:function(t,n,r){var o=t,u=o.src,a=o.alt,c=void 0===a?"":a,l=o.href,s=void 0===l?"":l,f=o.style,p=void 0===f?{}:f,d=p.width,h=void 0===d?"":d,v=p.height,g=void 0===v?"":v,y=e.isNodeSelected(r,t),m={};h&&(m.width="100%"),g&&(m.height="100%");var b=i("img",{style:m,src:u,alt:c,"data-href":s}),w=r.isDisabled();return y&&!w?ka(r,t,b,{width:h,height:g}):function(t,e,n,r){var o=r.width,u=r.height,a={};o&&(a.width=o),u&&(a.height=u);var c=Sa(t,e);return i("div",{id:c,style:a,className:"w-e-image-container"},n)}(r,t,b,{width:h,height:g})}};var Ta={type:"image",elemToHtml:function(t,e){var n=t,r=n.src,o=n.alt,i=void 0===o?"":o,u=n.href,a=void 0===u?"":u,c=n.style,l=void 0===c?{}:c,s=l.width,f=void 0===s?"":s,p=l.height,d=void 0===p?"":p,h="";return f&&(h+="width: "+f+";"),d&&(h+="height: "+d+";"),'<img src="'+r+'" alt="'+i+'" data-href="'+a+'" style="'+h+'"/>'}};var Ma={selector:"img:not([data-w-e-type])",parseElemHtml:function(t,e,n){var r=h(t),o=r.attr("data-href")||"";return o=decodeURIComponent(o),{type:"image",src:r.attr("src")||"",alt:r.attr("alt")||"",href:o,style:{width:Ro(r,"width"),height:Ro(r,"height")},children:[{text:""}]}}};function Ha(t,e,n,r,o){return void 0===r&&(r=""),void 0===o&&(o=""),Wo(this,void 0,void 0,(function(){var i,u;return Go(this,(function(a){switch(a.label){case 0:return(i=e.getMenuConfig(t).checkImage)?[4,i(n,r,o)]:[3,2];case 1:if("string"==typeof(u=a.sent()))return e.alert(u,"error"),[2,!1];if(null==u)return[2,!1];a.label=2;case 2:return[2,!0]}}))}))}function Oa(t,e,n){return Wo(this,void 0,void 0,(function(){var r;return Go(this,(function(o){switch(o.label){case 0:return(r=e.getMenuConfig(t).parseImageSrc)?[4,r(n)]:[3,2];case 1:return[2,o.sent()];case 2:return[2,n]}}))}))}function Ia(t,n,r,o){return void 0===r&&(r=""),void 0===o&&(o=""),Wo(this,void 0,void 0,(function(){var i,u,a;return Go(this,(function(c){switch(c.label){case 0:return[4,Ha("insertImage",t,n,r,o)];case 1:return c.sent()?[4,Oa("insertImage",t,n)]:[2];case 2:return i=c.sent(),u={type:"image",src:Uu(i),href:o,alt:r,style:{},children:[{text:""}]},null===t.selection&&t.restoreSelection(),e.getSelectedNodeByType(t,"image")&&t.move(1),La(t)?[2]:(f.insertNodes(t,u),(a=t.getMenuConfig("insertImage").onInsertedImage)&&a(u),[2])}}))}))}function Na(t,n,r,o,i){return void 0===r&&(r=""),void 0===o&&(o=""),void 0===i&&(i={}),Wo(this,void 0,void 0,(function(){var u,a,c,l,s,p;return Go(this,(function(d){switch(d.label){case 0:return[4,Ha("editImage",t,n,r,o)];case 1:return d.sent()?[4,Oa("editImage",t,n)]:[2];case 2:return u=d.sent(),null==(a=e.getSelectedNodeByType(t,"image"))?[2]:(c=a.style,l={src:u,alt:r,href:o,style:qo(qo({},void 0===c?{}:c),i)},f.setNodes(t,l,{match:function(t){return e.checkNodeType(t,"image")}}),s=e.getSelectedNodeByType(t,"image"),(p=t.getMenuConfig("editImage").onUpdatedImage)&&p(s),[2])}}))}))}function La(t){var n=t.selection;return null==n||(!p.isCollapsed(n)||!!Xo(c.nodes(t,{match:function(n){var r=e.getNodeType(n);return"code"===r||("pre"===r||("link"===r||("list-item"===r||(!!r.startsWith("header")||("blockquote"===r||!!c.isVoid(t,n))))))},universal:!0}),1)[0])}function za(){return Gu("w-e-insert-image")}var ja=function(){function t(){this.title=n("image.netImage"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.srcInputId=za(),this.altInputId=za(),this.hrefInputId=za(),this.buttonId=za()}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return La(t)},t.prototype.getModalPositionNode=function(t){return null},t.prototype.getModalContentElem=function(t){var e=this,i=this,u=i.srcInputId,a=i.altInputId,c=i.hrefInputId,l=i.buttonId,s=Xo(r(n("image.src"),u),2),f=s[0],p=s[1],d=h(p),v=Xo(r(n("image.desc"),a),2),g=v[0],y=v[1],m=h(y),b=Xo(r(n("image.link"),c),2),w=b[0],x=b[1],S=h(x),k=Xo(o(l,n("common.ok")),1)[0];if(null==this.$content){var E=h("<div></div>");E.on("click","#"+l,(function(n){n.preventDefault();var r=E.find("#"+u).val().trim(),o=E.find("#"+a).val().trim(),i=E.find("#"+c).val().trim();e.insertImage(t,r,o,i),t.hidePanelOrModal()})),this.$content=E}var T=this.$content;return T.empty(),T.append(f),T.append(g),T.append(w),T.append(k),d.val(""),m.val(""),S.val(""),setTimeout((function(){d.focus()})),T[0]},t.prototype.insertImage=function(t,e,n,r){void 0===n&&(n=""),void 0===r&&(r=""),e&&(t.restoreSelection(),this.isDisabled(t)||Ia(t,e,n,r))},t}(),Aa=function(){function t(){this.title=n("image.delete"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M826.8032 356.5312c-19.328 0-36.3776 15.6928-36.3776 35.0464v524.2624c0 19.328-16 34.56-35.328 34.56H264.9344c-19.328 0-35.5072-15.3088-35.5072-34.56V390.0416c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.6928-33.5104 35.0464V915.712c0 57.9328 44.6208 108.288 102.528 108.288H755.2c57.9328 0 108.0832-50.4576 108.0832-108.288V391.4752c-0.1024-19.2512-17.1264-34.944-36.48-34.944z" p-id="9577"></path><path d="M437.1712 775.7568V390.6048c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.616-33.5104 35.0464v385.152c0 19.328 14.1568 35.0464 33.5104 35.0464s33.5104-15.7184 33.5104-35.0464zM649.7024 775.7568V390.6048c0-19.328-17.0496-35.0464-36.3776-35.0464s-36.3776 15.616-36.3776 35.0464v385.152c0 19.328 17.0496 35.0464 36.3776 35.0464s36.3776-15.7184 36.3776-35.0464zM965.0432 217.0368h-174.6176V145.5104c0-57.9328-47.2064-101.76-104.6528-101.76h-350.976c-57.8304 0-105.3952 43.8528-105.3952 101.76v71.5264H54.784c-19.4304 0-35.0464 14.1568-35.0464 33.5104 0 19.328 15.616 33.5104 35.0464 33.5104h910.3616c19.328 0 35.0464-14.1568 35.0464-33.5104 0-19.3536-15.6928-33.5104-35.1488-33.5104z m-247.3728 0H297.3952V145.5104c0-19.328 18.2016-34.7648 37.4272-34.7648h350.976c19.1488 0 31.872 15.1296 31.872 34.7648v71.5264z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==e.getSelectedNodeByType(t,"image")},t.prototype.exec=function(t,n){this.isDisabled(t)||f.removeNodes(t,{match:function(t){return e.checkNodeType(t,"image")}})},t}();function Pa(){return Gu("w-e-edit-image")}var Va=function(){function t(){this.title=n("image.edit"),this.iconSvg=wi,this.tag="button",this.showModal=!0,this.modalWidth=300,this.$content=null,this.srcInputId=Pa(),this.altInputId=Pa(),this.hrefInputId=Pa(),this.buttonId=Pa()}return t.prototype.getValue=function(t){return""},t.prototype.getImageNode=function(t){return e.getSelectedNodeByType(t,"image")},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!p.isCollapsed(n)||null==e.getSelectedNodeByType(t,"image"))},t.prototype.getModalPositionNode=function(t){return this.getImageNode(t)},t.prototype.getModalContentElem=function(t){var e=this,i=this,u=i.srcInputId,a=i.altInputId,c=i.hrefInputId,l=i.buttonId,s=this.getImageNode(t);if(null==s)throw new Error("Not found selected image node");var f=Xo(r(n("image.src"),u),2),p=f[0],d=f[1],v=h(d),g=Xo(r(n("image.desc"),a),2),y=g[0],m=g[1],b=h(m),w=Xo(r(n("image.link"),c),2),x=w[0],S=w[1],k=h(S),E=Xo(o(l,n("common.ok")),1)[0];if(null==this.$content){var T=h("<div></div>");T.on("click","#"+l,(function(n){n.preventDefault();var r=T.find("#"+u).val(),o=T.find("#"+a).val(),i=T.find("#"+c).val();e.updateImage(t,r,o,i),t.hidePanelOrModal()})),this.$content=T}var M=this.$content;M.empty(),M.append(p),M.append(y),M.append(x),M.append(E);var H=s,O=H.src,I=H.alt,N=void 0===I?"":I,L=H.href,z=void 0===L?"":L;return v.val(O),b.val(N),k.val(z),setTimeout((function(){v.focus()})),M[0]},t.prototype.updateImage=function(t,e,n,r,o){void 0===n&&(n=""),void 0===r&&(r=""),void 0===o&&(o={}),e&&(t.restoreSelection(),this.isDisabled(t)||Na(t,e,n,r,o))},t}(),Ca=function(){function t(){this.title=n("image.viewLink"),this.iconSvg=xi,this.tag="button"}return t.prototype.getValue=function(t){var n=e.getSelectedNodeByType(t,"image");return n&&n.href||""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||!this.getValue(t)},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){if(!e||"string"!=typeof e)throw new Error("View image link failed, image.href is '"+e+"'");window.open(e,"_blank")}},t}(),Da=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.getSelectedNode=function(t){return e.getSelectedNodeByType(t,"image")},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedNode(t)},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var r=this.getSelectedNode(t);if(null!=r){var o=e.getHoverbar(t);o&&o.hideAndClean();var i=r.style,u={style:qo(qo({},void 0===i?{}:i),{width:this.value,height:""})};f.setNodes(t,u,{match:function(t){return e.checkNodeType(t,"image")}})}}},t}(),Ba=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="30%",e.value="30%",e}return $o(e,t),e}(Da),Ra=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="50%",e.value="50%",e}return $o(e,t),e}(Da),Fa=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title="100%",e.value="100%",e}return $o(e,t),e}(Da);var _a={onInsertedImage:function(t){},onUpdatedImage:function(t){},checkImage:function(t,e,n){return!0},parseImageSrc:function(t){return t}},$a={renderElems:[Ea],elemsToHtml:[Ta],parseElemsHtml:[Ma],menus:[{key:"insertImage",factory:function(){return new ja},config:_a},{key:"deleteImage",factory:function(){return new Aa}},{key:"editImage",factory:function(){return new Va},config:_a},{key:"viewImageLink",factory:function(){return new Ca}},{key:"imageWidth30",factory:function(){return new Ba}},{key:"imageWidth50",factory:function(){return new Ra}},{key:"imageWidth100",factory:function(){return new Fa}}],editorPlugin:function(t){var e=t.isInline,n=t.isVoid;t.insertNode;var r=t;return r.isInline=function(t){return"image"===t.type||e(t)},r.isVoid=function(t){return"image"===t.type||n(t)},r}};var qa={type:"todo",renderElem:function(t,n,r){var o=!1;r.isDisabled()&&(o=!0);var u=t.checked,a=i("div",{style:{margin:"5px 0"}},i("span",{contentEditable:!1,style:{marginRight:"0.5em"}},i("input",{type:"checkbox",checked:u,disabled:o,on:{change:function(n){var o=e.findPath(r,t),i={checked:n.target.checked};f.setNodes(r,i,{at:o})}}})),i("span",null,n));return a}};var Wa=function(){function t(){this.title=n("todo.todo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M278.755556 403.911111l-79.644445 79.644445L455.111111 739.555556l568.888889-568.888889-79.644444-79.644445L455.111111 580.266667l-176.355555-176.355556zM910.222222 910.222222H113.777778V113.777778h568.888889V0H113.777778C51.2 0 0 51.2 0 113.777778v796.444444c0 62.577778 51.2 113.777778 113.777778 113.777778h796.444444c62.577778 0 113.777778-51.2 113.777778-113.777778V455.111111h-113.777778v455.111111z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!!e.getSelectedNodeByType(t,"todo")},t.prototype.isDisabled=function(t){return null==t.selection||!!e.getSelectedElems(t).some((function(e){if(c.isVoid(t,e)&&c.isBlock(t,e))return!0;var n=e.type;return!!["pre","table","list-item"].includes(n)||void 0}))},t.prototype.exec=function(t,e){var n=this.isActive(t);f.setNodes(t,{type:n?"paragraph":"todo"})},t}();var Ga={selector:'div[data-w-e-type="todo"]',parseElemHtml:function(t,e,n){var r=h(t);0===(e=e.filter((function(t){return!!a.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:r.text().replace(/\s+/gm," ")}]);var o=!1;return null!=r.find('input[type="checkbox"]').attr("checked")&&(o=!0),{type:"todo",checked:o,children:e}}};var Ua={renderElems:[qa],elemsToHtml:[{type:"todo",elemToHtml:function(t,e){return'<div data-w-e-type="todo"><input type="checkbox" disabled '+(t.checked?"checked":"")+">"+e+"</div>"}}],preParseHtml:[{selector:"ul.w-e-todo",preParseHtml:function(t){var e=h(t).find("li"),n=h('<div data-w-e-type="todo"></div>'),r=e.find("input[type]");return n.append(r),e.children()[0].remove(),n[0].innerHTML=n[0].innerHTML+e[0].innerHTML,n[0]}}],parseElemsHtml:[Ga],menus:[{key:"todo",factory:function(){return new Wa}}],editorPlugin:function(t){var n=t.deleteBackward,r=t;return r.deleteBackward=function(r){var o=t.selection;if(o&&p.isCollapsed(o)){var i=e.getSelectedNodeByType(t,"todo");if(i&&0===s.string(i).length)return void f.setNodes(t,{type:"paragraph"},{mode:"highest"})}n(r)},r}};var Xa={type:"blockquote",renderElem:function(t,e,n){return i("blockquote",null,e)}};var Ya={type:"blockquote",elemToHtml:function(t,e){return"<blockquote>"+e+"</blockquote>"}};var Ja={selector:"blockquote:not([data-w-e-type])",parseElemHtml:function(t,e,n){var r=h(t);return 0===(e=e.filter((function(t){return!!a.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:r.text().replace(/\s+/gm," ")}]),{type:"blockquote",children:e}}},Ka=function(){function t(){this.title=n("blockQuote.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M894.6 907.1H605.4c-32.6 0-59-26.4-59-59V608.2l-4-14.9c0-315.9 125.5-485.1 376.5-507.5v59.8C752.7 180.4 711.3 315.8 711.3 442.4v41.2l31.5 12.3h151.8c32.6 0 59 26.4 59 59v293.2c0 32.5-26.4 59-59 59z m-472 0H133.4c-32.6 0-59-26.4-59-59V608.2l-4-14.9c0-315.9 125.5-485.1 376.5-507.5v59.8C280.7 180.4 239.3 315.8 239.3 442.4v41.2l31.5 12.3h151.8c32.6 0 59 26.4 59 59v293.2c0 32.5-26.4 59-59 59z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!!e.getSelectedNodeByType(t,"blockquote")},t.prototype.isDisabled=function(t){return null==t.selection||!Xo(c.nodes(t,{match:function(t){var n=e.getNodeType(t);return"paragraph"===n||"blockquote"===n},universal:!0,mode:"highest"}),1)[0]},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){var n=this.isActive(t)?"paragraph":"blockquote";f.setNodes(t,{type:n},{mode:"highest"})}},t}(),Qa={key:"blockquote",factory:function(){return new Ka}},Za=st([].slice),tc=qn("slice"),ec=re("species"),nc=J.Array,rc=Math.max;wn({target:"Array",proto:!0,forced:!tc},{slice:function(t,e){var n,r,o,i=bt(this),u=Qe(i),a=Ye(t,u),c=Ye(void 0===e?u:e,u);if(Sn(i)&&(n=i.constructor,(Vn(n)&&(n===nc||Sn(n.prototype))||xt(n)&&null===(n=n[ec]))&&(n=void 0),n===nc||void 0===n))return Za(i,a,c);for(r=new(void 0===n?nc:n)(rc(c-a,0)),o=0;a<c;a++,o++)a in i&&yo(r,o,i[a]);return r.length=o,r}});var oc={renderElems:[Xa],elemsToHtml:[Ya],parseElemsHtml:[Ja],menus:[Qa],editorPlugin:function(t){var n=t.insertBreak,r=t.insertText,o=t;return o.insertBreak=function(){var i=o.selection;if(null==i)return n();var u=Xo(c.nodes(t,{match:function(t){return e.checkNodeType(t,"blockquote")},universal:!0}),1)[0];if(!u)return n();var a=u[0],l=e.findPath(t,a),p=c.end(t,l);if(d.equals(p,i.focus)){var h=s.string(a);if(h&&"\n"===h.slice(-1)){t.deleteBackward("character");return void f.insertNodes(o,{type:"paragraph",children:[{text:""}]},{mode:"highest"})}}r("\n")},o}},ic=function(){function t(){this.title=n("emotion.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M512 1024C230.4 1024 0 793.6 0 512S230.4 0 512 0s512 230.4 512 512-230.4 512-512 512z m0-102.4c226.742857 0 409.6-182.857143 409.6-409.6S738.742857 102.4 512 102.4 102.4 285.257143 102.4 512s182.857143 409.6 409.6 409.6z m-204.8-358.4h409.6c0 113.371429-91.428571 204.8-204.8 204.8s-204.8-91.428571-204.8-204.8z m0-102.4c-43.885714 0-76.8-32.914286-76.8-76.8s32.914286-76.8 76.8-76.8 76.8 32.914286 76.8 76.8-32.914286 76.8-76.8 76.8z m409.6 0c-43.885714 0-76.8-32.914286-76.8-76.8s32.914286-76.8 76.8-76.8c43.885714 0 76.8 32.914286 76.8 76.8s-32.914286 76.8-76.8 76.8z"></path></svg>',this.tag="button",this.showDropPanel=!0,this.$content=null}return t.prototype.exec=function(t,e){},t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||!!Xo(c.nodes(t,{match:function(n){return"pre"===e.getNodeType(n)||!!c.isVoid(t,n)},universal:!0}),1)[0]},t.prototype.getPanelContentElem=function(t){if(null==this.$content){var e=h('<ul class="w-e-panel-content-emotion"></ul>');e.on("click","li",(function(e){var n=e.target;if(null!=n){e.preventDefault();var r=h(n).text();t.insertText(r)}})),this.$content=e}var n=this.$content;if(null==n)return document.createElement("ul");n.empty();var r=t.getMenuConfig("emotion").emotions;return(void 0===r?[]:r).forEach((function(t){var e=h("<li>"+t+"</li>");n.append(e)})),n[0]},t}();var uc={menus:[{key:"emotion",factory:function(){return new ic},config:{emotions:"😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇 🙂 🙃 😉 😌 😍 😘 😗 😙 😚 😋 😛 😝 😜 🤓 😎 😏 😒 😞 😔 😟 😕 🙁 😣 😖 😫 😩 😢 😭 😤 😠 😡 😳 😱 😨 🤗 🤔 😶 😑 😬 🙄 😯 😴 😷 🤑 😈 🤡 💩 👻 💀 👀 👣 👐 🙌 👏 🤝 👍 👎 👊 ✊ 🤛 🤜 🤞 ✌️ 🤘 👌 👈 👉 👆 👇 ☝️ ✋ 🤚 🖐 🖖 👋 🤙 💪 🖕 ✍️ 🙏".split(" ")}}]};var ac={1:"12px",2:"14px",3:"16px",4:"19px",5:"24px",6:"32px",7:"48px"};var cc={selector:"font",preParseHtml:function(t){var e=h(t);if("font"!==Bo(e))return t;var n=e.attr("size")||"";n&&(e.removeAttr("size"),e.css("font-size",ac[n]));var r=e.attr("face")||"";return r&&(e.removeAttr("face"),e.css("font-family",r)),e[0]}},lc=st("".indexOf);wn({target:"String",proto:!0,forced:!iu("includes")},{includes:function(t){return!!~lc(Yn(mt(this)),Yn(ru(t)),arguments.length>1?arguments[1]:void 0)}});var sc,fc,pc=function(t){return t&&t.Math==Math&&t},dc=pc("object"==typeof globalThis&&globalThis)||pc("object"==typeof window&&window)||pc("object"==typeof self&&self)||pc("object"==typeof q&&q)||function(){return this}()||Function("return this")(),hc=Function.prototype,vc=hc.apply,gc=hc.bind,yc=hc.call,mc="object"==typeof Reflect&&Reflect.apply||(gc?yc.bind(vc):function(){return yc.apply(vc,arguments)}),bc=Function.prototype,wc=bc.bind,xc=bc.call,Sc=wc&&wc.bind(xc),kc=wc?function(t){return t&&Sc(xc,t)}:function(t){return t&&function(){return xc.apply(t,arguments)}},Ec=function(t){return"function"==typeof t},Tc=function(t){try{return!!t()}catch(t){return!0}},Mc=!Tc((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),Hc=Function.prototype.call,Oc=Hc.bind?Hc.bind(Hc):function(){return Hc.apply(Hc,arguments)},Ic={}.propertyIsEnumerable,Nc=Object.getOwnPropertyDescriptor,Lc=Nc&&!Ic.call({1:2},1)?function(t){var e=Nc(this,t);return!!e&&e.enumerable}:Ic,zc={f:Lc},jc=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},Ac=kc({}.toString),Pc=kc("".slice),Vc=function(t){return Pc(Ac(t),8,-1)},Cc=dc.Object,Dc=kc("".split),Bc=Tc((function(){return!Cc("z").propertyIsEnumerable(0)}))?function(t){return"String"==Vc(t)?Dc(t,""):Cc(t)}:Cc,Rc=dc.TypeError,Fc=function(t){if(null==t)throw Rc("Can't call method on "+t);return t},_c=function(t){return Bc(Fc(t))},$c=function(t){return"object"==typeof t?null!==t:Ec(t)},qc={},Wc=function(t){return Ec(t)?t:void 0},Gc=function(t,e){return arguments.length<2?Wc(qc[t])||Wc(dc[t]):qc[t]&&qc[t][e]||dc[t]&&dc[t][e]},Uc=kc({}.isPrototypeOf),Xc=Gc("navigator","userAgent")||"",Yc=dc.process,Jc=dc.Deno,Kc=Yc&&Yc.versions||Jc&&Jc.version,Qc=Kc&&Kc.v8;Qc&&(fc=(sc=Qc.split("."))[0]>0&&sc[0]<4?1:+(sc[0]+sc[1])),!fc&&Xc&&(!(sc=Xc.match(/Edge\/(\d+)/))||sc[1]>=74)&&(sc=Xc.match(/Chrome\/(\d+)/))&&(fc=+sc[1]);var Zc=fc,tl=!!Object.getOwnPropertySymbols&&!Tc((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Zc&&Zc<41})),el=tl&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,nl=dc.Object,rl=el?function(t){return"symbol"==typeof t}:function(t){var e=Gc("Symbol");return Ec(e)&&Uc(e.prototype,nl(t))},ol=dc.String,il=dc.TypeError,ul=function(t){if(Ec(t))return t;throw il(function(t){try{return ol(t)}catch(t){return"Object"}}(t)+" is not a function")},al=dc.TypeError,cl=Object.defineProperty,ll=dc["__core-js_shared__"]||function(t,e){try{cl(dc,t,{value:e,configurable:!0,writable:!0})}catch(n){dc[t]=e}return e}("__core-js_shared__",{}),sl=G((function(t){(t.exports=function(t,e){return ll[t]||(ll[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"pure",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),fl=dc.Object,pl=function(t){return fl(Fc(t))},dl=kc({}.hasOwnProperty),hl=Object.hasOwn||function(t,e){return dl(pl(t),e)},vl=0,gl=Math.random(),yl=kc(1..toString),ml=function(t){return"Symbol("+(void 0===t?"":t)+")_"+yl(++vl+gl,36)},bl=sl("wks"),wl=dc.Symbol,xl=wl&&wl.for,Sl=el?wl:wl&&wl.withoutSetter||ml,kl=function(t){if(!hl(bl,t)||!tl&&"string"!=typeof bl[t]){var e="Symbol."+t;tl&&hl(wl,t)?bl[t]=wl[t]:bl[t]=el&&xl?xl(e):Sl(e)}return bl[t]},El=dc.TypeError,Tl=kl("toPrimitive"),Ml=function(t,e){if(!$c(t)||rl(t))return t;var n,r,o=null==(n=t[Tl])?void 0:ul(n);if(o){if(void 0===e&&(e="default"),r=Oc(o,t,e),!$c(r)||rl(r))return r;throw El("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&Ec(n=t.toString)&&!$c(r=Oc(n,t)))return r;if(Ec(n=t.valueOf)&&!$c(r=Oc(n,t)))return r;if("string"!==e&&Ec(n=t.toString)&&!$c(r=Oc(n,t)))return r;throw al("Can't convert object to primitive value")}(t,e)},Hl=function(t){var e=Ml(t,"string");return rl(e)?e:e+""},Ol=dc.document,Il=$c(Ol)&&$c(Ol.createElement),Nl=function(t){return Il?Ol.createElement(t):{}},Ll=!Mc&&!Tc((function(){return 7!=Object.defineProperty(Nl("div"),"a",{get:function(){return 7}}).a})),zl=Object.getOwnPropertyDescriptor,jl={f:Mc?zl:function(t,e){if(t=_c(t),e=Hl(e),Ll)try{return zl(t,e)}catch(t){}if(hl(t,e))return jc(!Oc(zc.f,t,e),t[e])}},Al=/#|\.prototype\./,Pl=function(t,e){var n=Cl[Vl(t)];return n==Bl||n!=Dl&&(Ec(e)?Tc(e):!!e)},Vl=Pl.normalize=function(t){return String(t).replace(Al,".").toLowerCase()},Cl=Pl.data={},Dl=Pl.NATIVE="N",Bl=Pl.POLYFILL="P",Rl=Pl,Fl=kc(kc.bind),_l=function(t,e){return ul(t),void 0===e?t:Fl?Fl(t,e):function(){return t.apply(e,arguments)}},$l=dc.String,ql=dc.TypeError,Wl=function(t){if($c(t))return t;throw ql($l(t)+" is not an object")},Gl=dc.TypeError,Ul=Object.defineProperty,Xl={f:Mc?Ul:function(t,e,n){if(Wl(t),e=Hl(e),Wl(n),Ll)try{return Ul(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Gl("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Yl=Mc?function(t,e,n){return Xl.f(t,e,jc(1,n))}:function(t,e,n){return t[e]=n,t},Jl=jl.f,Kl=function(t){var e=function(n,r,o){if(this instanceof e){switch(arguments.length){case 0:return new t;case 1:return new t(n);case 2:return new t(n,r)}return new t(n,r,o)}return mc(t,this,arguments)};return e.prototype=t.prototype,e},Ql=function(t,e){var n,r,o,i,u,a,c,l,s=t.target,f=t.global,p=t.stat,d=t.proto,h=f?dc:p?dc[s]:(dc[s]||{}).prototype,v=f?qc:qc[s]||Yl(qc,s,{})[s],g=v.prototype;for(o in e)n=!Rl(f?o:s+(p?".":"#")+o,t.forced)&&h&&hl(h,o),u=v[o],n&&(a=t.noTargetGet?(l=Jl(h,o))&&l.value:h[o]),i=n&&a?a:e[o],n&&typeof u==typeof i||(c=t.bind&&n?_l(i,dc):t.wrap&&n?Kl(i):d&&Ec(i)?kc(i):i,(t.sham||i&&i.sham||u&&u.sham)&&Yl(c,"sham",!0),Yl(v,o,c),d&&(hl(qc,r=s+"Prototype")||Yl(qc,r,{}),Yl(qc[r],o,i),t.real&&g&&!g[o]&&Yl(g,o,i)))},Zl=Array.isArray||function(t){return"Array"==Vc(t)},ts=Math.ceil,es=Math.floor,ns=function(t){var e=+t;return e!=e||0===e?0:(e>0?es:ts)(e)},rs=Math.min,os=function(t){return(e=t.length)>0?rs(ns(e),9007199254740991):0;var e},is=function(t,e,n){var r=Hl(e);r in t?Xl.f(t,r,jc(0,n)):t[r]=n},us={};us[kl("toStringTag")]="z";var as="[object z]"===String(us),cs=kl("toStringTag"),ls=dc.Object,ss="Arguments"==Vc(function(){return arguments}()),fs=as?Vc:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=ls(t),cs))?n:ss?Vc(e):"Object"==(r=Vc(e))&&Ec(e.callee)?"Arguments":r},ps=kc(Function.toString);Ec(ll.inspectSource)||(ll.inspectSource=function(t){return ps(t)});var ds=ll.inspectSource,hs=function(){},vs=[],gs=Gc("Reflect","construct"),ys=/^\s*(?:class|function)\b/,ms=kc(ys.exec),bs=!ys.exec(hs),ws=function(t){if(!Ec(t))return!1;try{return gs(hs,vs,t),!0}catch(t){return!1}},xs=!gs||Tc((function(){var t;return ws(ws.call)||!ws(Object)||!ws((function(){t=!0}))||t}))?function(t){if(!Ec(t))return!1;switch(fs(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return bs||!!ms(ys,ds(t))}:ws,Ss=kl("species"),ks=dc.Array,Es=function(t,e){return new(function(t){var e;return Zl(t)&&(e=t.constructor,(xs(e)&&(e===ks||Zl(e.prototype))||$c(e)&&null===(e=e[Ss]))&&(e=void 0)),void 0===e?ks:e}(t))(0===e?0:e)},Ts=kl("species"),Ms=kl("isConcatSpreadable"),Hs=dc.TypeError,Os=Zc>=51||!Tc((function(){var t=[];return t[Ms]=!1,t.concat()[0]!==t})),Is=function(t){return Zc>=51||!Tc((function(){var e=[];return(e.constructor={})[Ts]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}("concat"),Ns=function(t){if(!$c(t))return!1;var e=t[Ms];return void 0!==e?!!e:Zl(t)};Ql({target:"Array",proto:!0,forced:!Os||!Is},{concat:function(t){var e,n,r,o,i,u=pl(this),a=Es(u,0),c=0;for(e=-1,r=arguments.length;e<r;e++)if(Ns(i=-1===e?u:arguments[e])){if(c+(o=os(i))>9007199254740991)throw Hs("Maximum allowed index exceeded");for(n=0;n<o;n++,c++)n in i&&is(a,c,i[n])}else{if(c>=9007199254740991)throw Hs("Maximum allowed index exceeded");is(a,c++,i)}return a.length=c,a}});var Ls,zs=dc.String,js=function(t){if("Symbol"===fs(t))throw TypeError("Cannot convert a Symbol value to a string");return zs(t)},As=Math.max,Ps=Math.min,Vs=function(t,e){var n=ns(t);return n<0?As(n+e,0):Ps(n,e)},Cs=function(t){return function(e,n,r){var o,i=_c(e),u=os(i),a=Vs(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},Ds={includes:Cs(!0),indexOf:Cs(!1)},Bs={},Rs=Ds.indexOf,Fs=kc([].push),_s=function(t,e){var n,r=_c(t),o=0,i=[];for(n in r)!hl(Bs,n)&&hl(r,n)&&Fs(i,n);for(;e.length>o;)hl(r,n=e[o++])&&(~Rs(i,n)||Fs(i,n));return i},$s=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],qs=Object.keys||function(t){return _s(t,$s)},Ws=Mc?Object.defineProperties:function(t,e){Wl(t);for(var n,r=_c(e),o=qs(e),i=o.length,u=0;i>u;)Xl.f(t,n=o[u++],r[n]);return t},Gs=Gc("document","documentElement"),Us=sl("keys"),Xs=function(t){return Us[t]||(Us[t]=ml(t))},Ys=Xs("IE_PROTO"),Js=function(){},Ks=function(t){return"<script>"+t+"<\/script>"},Qs=function(t){t.write(Ks("")),t.close();var e=t.parentWindow.Object;return t=null,e},Zs=function(){try{Ls=new ActiveXObject("htmlfile")}catch(t){}var t,e;Zs="undefined"!=typeof document?document.domain&&Ls?Qs(Ls):((e=Nl("iframe")).style.display="none",Gs.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Ks("document.F=Object")),t.close(),t.F):Qs(Ls);for(var n=$s.length;n--;)delete Zs.prototype[$s[n]];return Zs()};Bs[Ys]=!0;var tf,ef,nf,rf=Object.create||function(t,e){var n;return null!==t?(Js.prototype=Wl(t),n=new Js,Js.prototype=null,n[Ys]=t):n=Zs(),void 0===e?n:Ws(n,e)},of=$s.concat("length","prototype"),uf={f:Object.getOwnPropertyNames||function(t){return _s(t,of)}},af=dc.Array,cf=Math.max,lf=uf.f,sf="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],ff=function(t){try{return lf(t)}catch(t){return function(t,e,n){for(var r=os(t),o=Vs(e,r),i=Vs(void 0===n?r:n,r),u=af(cf(i-o,0)),a=0;o<i;o++,a++)is(u,a,t[o]);return u.length=a,u}(sf)}},pf={f:function(t){return sf&&"Window"==Vc(t)?ff(t):lf(_c(t))}},df={f:Object.getOwnPropertySymbols},hf=kc([].slice),vf=function(t,e,n,r){r&&r.enumerable?t[e]=n:Yl(t,e,n)},gf={f:kl},yf=Xl.f,mf=function(t){var e=qc.Symbol||(qc.Symbol={});hl(e,t)||yf(e,t,{value:gf.f(t)})},bf=as?{}.toString:function(){return"[object "+fs(this)+"]"},wf=Xl.f,xf=kl("toStringTag"),Sf=function(t,e,n,r){if(t){var o=n?t:t.prototype;hl(o,xf)||wf(o,xf,{configurable:!0,value:e}),r&&!as&&Yl(o,"toString",bf)}},kf=dc.WeakMap,Ef=Ec(kf)&&/native code/.test(ds(kf)),Tf=dc.TypeError,Mf=dc.WeakMap;if(Ef||ll.state){var Hf=ll.state||(ll.state=new Mf),Of=kc(Hf.get),If=kc(Hf.has),Nf=kc(Hf.set);tf=function(t,e){if(If(Hf,t))throw new Tf("Object already initialized");return e.facade=t,Nf(Hf,t,e),e},ef=function(t){return Of(Hf,t)||{}},nf=function(t){return If(Hf,t)}}else{var Lf=Xs("state");Bs[Lf]=!0,tf=function(t,e){if(hl(t,Lf))throw new Tf("Object already initialized");return e.facade=t,Yl(t,Lf,e),e},ef=function(t){return hl(t,Lf)?t[Lf]:{}},nf=function(t){return hl(t,Lf)}}var zf={set:tf,get:ef,has:nf,enforce:function(t){return nf(t)?ef(t):tf(t,{})},getterFor:function(t){return function(e){var n;if(!$c(e)||(n=ef(e)).type!==t)throw Tf("Incompatible receiver, "+t+" required");return n}}},jf=kc([].push),Af=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,l,s,f){for(var p,d,h=pl(c),v=Bc(h),g=_l(l,s),y=os(v),m=0,b=f||Es,w=e?b(c,y):n||u?b(c,0):void 0;y>m;m++)if((a||m in v)&&(d=g(p=v[m],m,h),t))if(e)w[m]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return m;case 2:jf(w,p)}else switch(t){case 4:return!1;case 7:jf(w,p)}return i?-1:r||o?o:w}},Pf={forEach:Af(0),map:Af(1),filter:Af(2),some:Af(3),every:Af(4),find:Af(5),findIndex:Af(6),filterReject:Af(7)}.forEach,Vf=Xs("hidden"),Cf=kl("toPrimitive"),Df=zf.set,Bf=zf.getterFor("Symbol"),Rf=Object.prototype,Ff=dc.Symbol,_f=Ff&&Ff.prototype,$f=dc.TypeError,qf=dc.QObject,Wf=Gc("JSON","stringify"),Gf=jl.f,Uf=Xl.f,Xf=pf.f,Yf=zc.f,Jf=kc([].push),Kf=sl("symbols"),Qf=sl("op-symbols"),Zf=sl("string-to-symbol-registry"),tp=sl("symbol-to-string-registry"),ep=sl("wks"),np=!qf||!qf.prototype||!qf.prototype.findChild,rp=Mc&&Tc((function(){return 7!=rf(Uf({},"a",{get:function(){return Uf(this,"a",{value:7}).a}})).a}))?function(t,e,n){var r=Gf(Rf,e);r&&delete Rf[e],Uf(t,e,n),r&&t!==Rf&&Uf(Rf,e,r)}:Uf,op=function(t,e){var n=Kf[t]=rf(_f);return Df(n,{type:"Symbol",tag:t,description:e}),Mc||(n.description=e),n},ip=function(t,e,n){t===Rf&&ip(Qf,e,n),Wl(t);var r=Hl(e);return Wl(n),hl(Kf,r)?(n.enumerable?(hl(t,Vf)&&t[Vf][r]&&(t[Vf][r]=!1),n=rf(n,{enumerable:jc(0,!1)})):(hl(t,Vf)||Uf(t,Vf,jc(1,{})),t[Vf][r]=!0),rp(t,r,n)):Uf(t,r,n)},up=function(t,e){Wl(t);var n=_c(e),r=qs(n).concat(sp(n));return Pf(r,(function(e){Mc&&!Oc(ap,n,e)||ip(t,e,n[e])})),t},ap=function(t){var e=Hl(t),n=Oc(Yf,this,e);return!(this===Rf&&hl(Kf,e)&&!hl(Qf,e))&&(!(n||!hl(this,e)||!hl(Kf,e)||hl(this,Vf)&&this[Vf][e])||n)},cp=function(t,e){var n=_c(t),r=Hl(e);if(n!==Rf||!hl(Kf,r)||hl(Qf,r)){var o=Gf(n,r);return!o||!hl(Kf,r)||hl(n,Vf)&&n[Vf][r]||(o.enumerable=!0),o}},lp=function(t){var e=Xf(_c(t)),n=[];return Pf(e,(function(t){hl(Kf,t)||hl(Bs,t)||Jf(n,t)})),n},sp=function(t){var e=t===Rf,n=Xf(e?Qf:_c(t)),r=[];return Pf(n,(function(t){!hl(Kf,t)||e&&!hl(Rf,t)||Jf(r,Kf[t])})),r};if(tl||(_f=(Ff=function(){if(Uc(_f,this))throw $f("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?js(arguments[0]):void 0,e=ml(t),n=function(t){this===Rf&&Oc(n,Qf,t),hl(this,Vf)&&hl(this[Vf],e)&&(this[Vf][e]=!1),rp(this,e,jc(1,t))};return Mc&&np&&rp(Rf,e,{configurable:!0,set:n}),op(e,t)}).prototype,vf(_f,"toString",(function(){return Bf(this).tag})),vf(Ff,"withoutSetter",(function(t){return op(ml(t),t)})),zc.f=ap,Xl.f=ip,jl.f=cp,uf.f=pf.f=lp,df.f=sp,gf.f=function(t){return op(kl(t),t)},Mc&&Uf(_f,"description",{configurable:!0,get:function(){return Bf(this).description}})),Ql({global:!0,wrap:!0,forced:!tl,sham:!tl},{Symbol:Ff}),Pf(qs(ep),(function(t){mf(t)})),Ql({target:"Symbol",stat:!0,forced:!tl},{for:function(t){var e=js(t);if(hl(Zf,e))return Zf[e];var n=Ff(e);return Zf[e]=n,tp[n]=e,n},keyFor:function(t){if(!rl(t))throw $f(t+" is not a symbol");if(hl(tp,t))return tp[t]},useSetter:function(){np=!0},useSimple:function(){np=!1}}),Ql({target:"Object",stat:!0,forced:!tl,sham:!Mc},{create:function(t,e){return void 0===e?rf(t):up(rf(t),e)},defineProperty:ip,defineProperties:up,getOwnPropertyDescriptor:cp}),Ql({target:"Object",stat:!0,forced:!tl},{getOwnPropertyNames:lp,getOwnPropertySymbols:sp}),Ql({target:"Object",stat:!0,forced:Tc((function(){df.f(1)}))},{getOwnPropertySymbols:function(t){return df.f(pl(t))}}),Wf){var fp=!tl||Tc((function(){var t=Ff();return"[null]"!=Wf([t])||"{}"!=Wf({a:t})||"{}"!=Wf(Object(t))}));Ql({target:"JSON",stat:!0,forced:fp},{stringify:function(t,e,n){var r=hf(arguments),o=e;if(($c(e)||void 0!==t)&&!rl(t))return Zl(e)||(e=function(t,e){if(Ec(o)&&(e=Oc(o,this,t,e)),!rl(e))return e}),r[1]=e,mc(Wf,null,r)}})}if(!_f[Cf]){var pp=_f.valueOf;vf(_f,Cf,(function(t){return Oc(pp,this)}))}Sf(Ff,"Symbol"),Bs[Vf]=!0,mf("asyncIterator"),mf("hasInstance"),mf("isConcatSpreadable"),mf("iterator"),mf("match"),mf("matchAll"),mf("replace"),mf("search"),mf("species"),mf("split"),mf("toPrimitive"),mf("toStringTag"),mf("unscopables"),Sf(dc.JSON,"JSON",!0);var dp,hp,vp,gp=qc.Symbol,yp={},mp=Function.prototype,bp=Mc&&Object.getOwnPropertyDescriptor,wp=hl(mp,"name"),xp={EXISTS:wp,PROPER:wp&&"something"===function(){}.name,CONFIGURABLE:wp&&(!Mc||Mc&&bp(mp,"name").configurable)},Sp=!Tc((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),kp=Xs("IE_PROTO"),Ep=dc.Object,Tp=Ep.prototype,Mp=Sp?Ep.getPrototypeOf:function(t){var e=pl(t);if(hl(e,kp))return e[kp];var n=e.constructor;return Ec(n)&&e instanceof n?n.prototype:e instanceof Ep?Tp:null},Hp=kl("iterator"),Op=!1;[].keys&&("next"in(vp=[].keys())?(hp=Mp(Mp(vp)))!==Object.prototype&&(dp=hp):Op=!0);var Ip=null==dp||Tc((function(){var t={};return dp[Hp].call(t)!==t}));dp=Ip?{}:rf(dp),Ec(dp[Hp])||vf(dp,Hp,(function(){return this}));var Np={IteratorPrototype:dp,BUGGY_SAFARI_ITERATORS:Op},Lp=Np.IteratorPrototype,zp=function(){return this};dc.String,dc.TypeError;Object.setPrototypeOf||"__proto__"in{}&&function(){var t,e=!1,n={};try{(t=kc(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),e=n instanceof Array}catch(t){}}();var jp=xp.PROPER,Ap=Np.BUGGY_SAFARI_ITERATORS,Pp=kl("iterator"),Vp=function(){return this},Cp=function(t,e,n,r,o,i,u){!function(t,e,n,r){var o=e+" Iterator";t.prototype=rf(Lp,{next:jc(+!r,n)}),Sf(t,o,!1,!0),yp[o]=zp}(n,e,r);var a,c,l,s=function(t){if(t===o&&v)return v;if(!Ap&&t in d)return d[t];switch(t){case"keys":case"values":case"entries":return function(){return new n(this,t)}}return function(){return new n(this)}},f=e+" Iterator",p=!1,d=t.prototype,h=d[Pp]||d["@@iterator"]||o&&d[o],v=!Ap&&h||s(o),g="Array"==e&&d.entries||h;if(g&&(a=Mp(g.call(new t)))!==Object.prototype&&a.next&&(Sf(a,f,!0,!0),yp[f]=Vp),jp&&"values"==o&&h&&"values"!==h.name&&(p=!0,v=function(){return Oc(h,this)}),o)if(c={values:s("values"),keys:i?v:s("keys"),entries:s("entries")},u)for(l in c)(Ap||p||!(l in d))&&vf(d,l,c[l]);else Ql({target:e,proto:!0,forced:Ap||p},c);return u&&d[Pp]!==v&&vf(d,Pp,v,{name:o}),yp[e]=v,c},Dp=zf.set,Bp=zf.getterFor("Array Iterator");Cp(Array,"Array",(function(t,e){Dp(this,{type:"Array Iterator",target:_c(t),index:0,kind:e})}),(function(){var t=Bp(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values"),yp.Arguments=yp.Array;var Rp=kl("toStringTag");for(var Fp in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var _p=dc[Fp],$p=_p&&_p.prototype;$p&&fs($p)!==Rp&&Yl($p,Rp,Fp),yp[Fp]=yp.Array}var qp=gp;mf("asyncDispose"),mf("dispose"),mf("matcher"),mf("metadata"),mf("observable"),mf("patternMatch"),mf("replaceAll");var Wp=qp,Gp=kc("".charAt),Up=kc("".charCodeAt),Xp=kc("".slice),Yp=function(t){return function(e,n){var r,o,i=js(Fc(e)),u=ns(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=Up(i,u))<55296||r>56319||u+1===a||(o=Up(i,u+1))<56320||o>57343?t?Gp(i,u):r:t?Xp(i,u,u+2):o-56320+(r-55296<<10)+65536}},Jp={codeAt:Yp(!1),charAt:Yp(!0)}.charAt,Kp=zf.set,Qp=zf.getterFor("String Iterator");Cp(String,"String",(function(t){Kp(this,{type:"String Iterator",string:js(t),index:0})}),(function(){var t,e=Qp(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=Jp(n,r),e.index+=t.length,{value:t,done:!1})}));var Zp=gf.f("iterator"),td=G((function(t){function e(n){return"function"==typeof Wp&&"symbol"==typeof Zp?(t.exports=e=function(t){return typeof t},t.exports.default=t.exports,t.exports.__esModule=!0):(t.exports=e=function(t){return t&&"function"==typeof Wp&&t.constructor===Wp&&t!==Wp.prototype?"symbol":typeof t},t.exports.default=t.exports,t.exports.__esModule=!0),e(n)}t.exports=e,t.exports.default=t.exports,t.exports.__esModule=!0})),ed=W(td),nd=_e.EXISTS,rd=be.f,od=Function.prototype,id=st(od.toString),ud=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,ad=st(ud.exec);Q&&!nd&&rd(od,"name",{configurable:!0,get:function(){try{return ad(ud,id(this))[1]}catch(t){return""}}});var cd=function(){function t(){this.tag="select",this.width=80}return t.prototype.isActive=function(t){return!1},t.prototype.getValue=function(t){var e=this.mark,n=c.marks(t);return n&&n[e]?n[e]:""},t.prototype.isDisabled=function(t){return null==t.selection||(this.mark,!!Xo(c.nodes(t,{match:function(n){return"pre"===e.getNodeType(n)||!!c.isVoid(t,n)},universal:!0}),1)[0])},t.prototype.exec=function(t,e){var n=this.mark;e?t.addMark(n,e):t.removeMark(n)},t}(),ld=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("fontSize.title"),e.iconSvg=Ei,e.mark="fontSize",e}return $o(e,t),e.prototype.getOptions=function(t){var e=[],r=t.getMenuConfig(this.mark).fontSizeList,o=void 0===r?[]:r;e.push({text:n("fontSize.default"),value:""}),o.forEach((function(t){if("string"==typeof t)e.push({text:t,value:t});else if("object"===ed(t)){var n=t.name,r=t.value;e.push({text:n,value:r})}}));var i=this.getValue(t);return e.forEach((function(t){t.value===i?t.selected=!0:delete t.selected})),e},e}(cd),sd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("fontFamily.title"),e.iconSvg=Ti,e.mark="fontFamily",e.selectPanelWidth=150,e}return $o(e,t),e.prototype.getOptions=function(t){var e=[],r=t.getMenuConfig(this.mark).fontFamilyList,o=void 0===r?[]:r;e.push({text:n("fontFamily.default"),value:""}),o.forEach((function(t){if("string"==typeof t)e.push({text:t,value:t,styleForRenderMenuList:{"font-family":t}});else if("object"===ed(t)){var n=t.name,r=t.value;e.push({text:n,value:r,styleForRenderMenuList:{"font-family":r}})}}));var i=this.getValue(t);return e.forEach((function(t){t.value===i?t.selected=!0:delete t.selected})),e},e}(cd);var fd={renderStyle:function(t,e){var n=t,r=n.fontSize,o=n.fontFamily,i=e;return r&&Au(i,{fontSize:r}),o&&Au(i,{fontFamily:o}),i},styleToHtml:function(t,e){if(!a.isText(t))return e;var n,r=t,o=r.fontSize,i=r.fontFamily;return o||i?((Co(e)||"span"!==Bo(n=h(e)))&&(n=h("<span>"+e+"</span>")),o&&n.css("font-size",o),i&&n.css("font-family",i),Do(n)):e},preParseHtml:[cc],parseStyleHtml:function(t,e,n){var r=h(t);if(!a.isText(e))return e;var o=e,i=n.getMenuConfig("fontSize").fontSizeList,u=void 0===i?[]:i,c=Ro(r,"font-size"),l=u.find((function(t){return t.value&&t.value===c}))||u.includes(c);c&&l&&(o.fontSize=c);var s=n.getMenuConfig("fontFamily").fontFamilyList,f=void 0===s?[]:s,p=Ro(r,"font-family").replace(/"/g,""),d=f.find((function(t){return t.value&&t.value===p}))||f.includes(p);return p&&d&&(o.fontFamily=p),o},menus:[{key:"fontSize",factory:function(){return new ld},config:{fontSizeList:["12px",{name:"13px",value:"13px"},"14px","15px","16px","19px",{name:"22px",value:"22px"},"24px","29px","32px","40px","48px"]}},{key:"fontFamily",factory:function(){return new sd},config:{fontFamilyList:["黑体",{name:"仿宋",value:"仿宋"},"楷体","标楷体","华文仿宋","华文楷体",{name:"宋体",value:"宋体"},"微软雅黑","Arial","Tahoma","Verdana","Times New Roman","Courier New"]}}]};var pd={selector:"p,h1,h2,h3,h4,h5",preParseHtml:function(t){var e=h(t),n=Ro(e,"padding-left");return/\dem/.test(n)&&e.css("text-indent","2em"),/\dpx/.test(n)&&parseInt(n,10)%32==0&&e.css("text-indent","2em"),e[0]}};var dd=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){var e=Xo(c.nodes(t,{match:function(t){return!!t.indent},universal:!0}),1),n=e[0];return null==n?"":Xo(n,1)[0].indent||""},t.prototype.isActive=function(t){return!1},t.prototype.getMatchNode=function(t){var n=Xo(c.nodes(t,{match:function(t){var n=e.getNodeType(t);return"paragraph"===n||!!n.startsWith("header")},universal:!0,mode:"highest"}),1)[0];return null==n?null:n[0]},t}(),hd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("indent.decrease"),e.iconSvg=Mi,e}return $o(e,t),e.prototype.isDisabled=function(t){var e=this.getMatchNode(t);return null==e||!e.indent},e.prototype.exec=function(t,e){f.setNodes(t,{indent:null},{match:function(t){return l.isElement(t)}})},e}(dd),vd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("indent.increase"),e.iconSvg=Hi,e}return $o(e,t),e.prototype.isDisabled=function(t){var e=this.getMatchNode(t);return null==e||!!e.indent},e.prototype.exec=function(t,e){f.setNodes(t,{indent:"2em"},{match:function(t){return l.isElement(t)},mode:"highest"})},e}(dd),gd={renderStyle:function(t,e){if(!l.isElement(t))return e;var n=t.indent,r=e;return n&&Au(r,{textIndent:n}),r},styleToHtml:function(t,e){if(!l.isElement(t))return e;var n=t.indent;if(!n)return e;var r=h(e);return r.css("text-indent",n),Do(r)},preParseHtml:[pd],parseStyleHtml:function(t,e,n){var r=h(t);if(!l.isElement(e))return e;var o=e,i=Ro(r,"text-indent"),u=parseInt(i,10);return i&&u>0&&(o.indent=i),o},menus:[{key:"indent",factory:function(){return new vd}},{key:"delIndent",factory:function(){return new hd}}]};var yd=function(){function t(){this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.getMatchNode=function(t){var n=Xo(c.nodes(t,{match:function(t){var n=e.getNodeType(t);return"paragraph"===n||("blockquote"===n||!!n.startsWith("header"))},universal:!0,mode:"highest"}),1)[0];return null==n?null:n[0]},t.prototype.isDisabled=function(t){return null==t.selection||!!e.getSelectedElems(t).some((function(e){if(c.isVoid(t,e)&&c.isBlock(t,e))return!0;var n=e.type;return!!["pre","code"].includes(n)||void 0}))},t}(),md=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("justify.left"),e.iconSvg=Oi,e}return $o(e,t),e.prototype.exec=function(t,e){f.setNodes(t,{textAlign:"left"},{match:function(e){return l.isElement(e)&&!t.isInline(e)}})},e}(yd),bd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("justify.right"),e.iconSvg=Ii,e}return $o(e,t),e.prototype.exec=function(t,e){f.setNodes(t,{textAlign:"right"},{match:function(e){return l.isElement(e)&&!t.isInline(e)}})},e}(yd),wd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("justify.center"),e.iconSvg=Ni,e}return $o(e,t),e.prototype.exec=function(t,e){f.setNodes(t,{textAlign:"center"},{match:function(e){return l.isElement(e)&&!t.isInline(e)}})},e}(yd),xd=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.title=n("justify.justify"),e.iconSvg=Li,e}return $o(e,t),e.prototype.exec=function(t,e){f.setNodes(t,{textAlign:"justify"},{match:function(e){return l.isElement(e)&&!t.isInline(e)}})},e}(yd),Sd={renderStyle:function(t,e){if(!l.isElement(t))return e;var n=t.textAlign,r=e;return n&&Au(r,{textAlign:n}),r},styleToHtml:function(t,e){if(!l.isElement(t))return e;var n=t.textAlign;if(!n)return e;var r=h(e);return r.css("text-align",n),Do(r)},parseStyleHtml:function(t,e,n){var r=h(t);if(!l.isElement(e))return e;var o=e,i=Ro(r,"text-align");return i&&(o.textAlign=i),o},menus:[{key:"justifyLeft",factory:function(){return new md}},{key:"justifyRight",factory:function(){return new bd}},{key:"justifyCenter",factory:function(){return new wd}},{key:"justifyJustify",factory:function(){return new xd}}]};var kd=function(){function t(){this.title=n("lineHeight.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M964 788a8 8 0 0 1 8 8v98a8 8 0 0 1-8 8H438a8 8 0 0 1-8-8v-98a8 8 0 0 1 8-8h526zM198.93 144.306c6.668-5.798 16.774-5.094 22.573 1.574l122.26 140.582a16 16 0 0 1 3.927 10.5c0 8.836-7.164 16-16 16h-61.8a8 8 0 0 0-8 8v390.077h69.819a16 16 0 0 1 10.502 3.928c6.666 5.8 7.37 15.906 1.57 22.573L221.476 878.123a16 16 0 0 1-1.57 1.57c-6.668 5.8-16.774 5.097-22.574-1.57L75.051 737.538a16 16 0 0 1-3.928-10.5c0-8.837 7.163-16 16-16h69.822V312.96H87.127a16 16 0 0 1-10.502-3.928c-6.666-5.8-7.37-15.906-1.57-22.573l122.303-140.582a16 16 0 0 1 1.572-1.572zM964 465a8 8 0 0 1 8 8v98a8 8 0 0 1-8 8H438a8 8 0 0 1-8-8v-98a8 8 0 0 1 8-8h526z m0-323a8 8 0 0 1 8 8v98a8 8 0 0 1-8 8H438a8 8 0 0 1-8-8v-98a8 8 0 0 1 8-8h526z"></path></svg>',this.tag="select",this.width=80}return t.prototype.getOptions=function(t){var e=[],r=t.getMenuConfig("lineHeight").lineHeightList,o=void 0===r?[]:r;e.push({text:n("lineHeight.default"),value:""}),o.forEach((function(t){e.push({text:t,value:t})}));var i=this.getValue(t);return e.forEach((function(t){t.value===i?t.selected=!0:delete t.selected})),e},t.prototype.getMatchNode=function(t){var n=Xo(c.nodes(t,{match:function(t){var n=e.getNodeType(t);return!!n.startsWith("header")||!!["paragraph","blockquote","list-item"].includes(n)},universal:!0,mode:"highest"}),1)[0];return null==n?null:n[0]},t.prototype.isActive=function(t){return!1},t.prototype.getValue=function(t){var e=this.getMatchNode(t);return null==e?"":l.isElement(e)&&e.lineHeight||""},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getMatchNode(t)},t.prototype.exec=function(t,e){f.setNodes(t,{lineHeight:e.toString()},{mode:"highest"})},t}();var Ed={renderStyle:function(t,e){if(!l.isElement(t))return e;var n=t.lineHeight,r=e;return n&&Au(r,{lineHeight:n}),r},styleToHtml:function(t,e){if(!l.isElement(t))return e;var n=t.lineHeight;if(!n)return e;var r=h(e);return r.css("line-height",n),Do(r)},parseStyleHtml:function(t,e,n){var r=h(t);if(!l.isElement(e))return e;var o=e,i=n.getMenuConfig("lineHeight").lineHeightList,u=void 0===i?[]:i,a=Ro(r,"line-height");return a&&u.includes(a)&&(o.lineHeight=a),o},menus:[{key:"lineHeight",factory:function(){return new kd},config:{lineHeightList:["1","1.15","1.5","2","2.5","3"]}}]},Td=function(){function t(){this.title=n("undo.redo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M0.00032 576a510.72 510.72 0 0 0 173.344 384l84.672-96A383.136 383.136 0 0 1 128.00032 576C128.00032 363.936 299.93632 192 512.00032 192c106.048 0 202.048 42.976 271.52 112.48L640.00032 448h384V64l-149.984 149.984A510.272 510.272 0 0 0 512.00032 64C229.21632 64 0.00032 293.216 0.00032 576z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection},t.prototype.exec=function(t,e){"function"==typeof t.redo&&t.redo()},t}(),Md=function(){function t(){this.title=n("undo.undo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M512 64A510.272 510.272 0 0 0 149.984 213.984L0.032 64v384h384L240.512 304.48A382.784 382.784 0 0 1 512.032 192c212.064 0 384 171.936 384 384 0 114.688-50.304 217.632-130.016 288l84.672 96a510.72 510.72 0 0 0 173.344-384c0-282.784-229.216-512-512-512z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection},t.prototype.exec=function(t,e){"function"==typeof t.undo&&t.undo()},t}(),Hd={menus:[{key:"redo",factory:function(){return new Td}},{key:"undo",factory:function(){return new Md}}]};var Od={type:"divider",renderElem:function(t,n,r){var o=e.isNodeSelected(r,t);return u("div",{props:{contentEditable:!1,className:"w-e-textarea-divider"},dataset:{selected:o?"true":""},style:{},on:{mousedown:function(t){return t.preventDefault()}}},[u("hr")])}};var Id={type:"divider",elemToHtml:function(t,e){return"<hr/>"}};var Nd={selector:"hr:not([data-w-e-type])",parseElemHtml:function(t,e,n){return{type:"divider",children:[{text:""}]}}},Ld=function(){function t(){this.title=n("divider.title"),this.iconSvg='<svg viewBox="0 0 1092 1024"><path d="M0 51.2m51.2 0l989.866667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-989.866667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M0 460.8m51.2 0l170.666667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-170.666667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M819.2 460.8m51.2 0l170.666667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-170.666667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M409.6 460.8m51.2 0l170.666667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-170.666667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path><path d="M0 870.4m51.2 0l989.866667 0q51.2 0 51.2 51.2l0 0q0 51.2-51.2 51.2l-989.866667 0q-51.2 0-51.2-51.2l0 0q0-51.2 51.2-51.2Z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||!!e.getSelectedElems(t).some((function(n){if(t.isVoid(n))return!0;var r=e.getNodeType(n);return"table"===r||("pre"===r||void 0)}))},t.prototype.exec=function(t,e){f.insertNodes(t,{type:"divider",children:[{text:""}]},{mode:"highest"})},t}(),zd={renderElems:[Od],elemsToHtml:[Id],parseElemsHtml:[Nd],menus:[{key:"divider",factory:function(){return new Ld}}],editorPlugin:function(t){var n=t.isVoid,r=t.normalizeNode,o=t;return o.isVoid=function(t){return"divider"===t.type||n(t)},o.normalizeNode=function(t){var n=Xo(t,2),i=n[0],u=n[1];if("divider"!==e.getNodeType(i))return r([i,u]);e.isLastNode(o,i)&&f.insertNodes(o,e.genEmptyParagraph(),{at:[u[0]+1]})},o}},jd=_n.map;wn({target:"Array",proto:!0,forced:!qn("map")},{map:function(t){return jd(this,t,arguments.length>1?arguments[1]:void 0)}});var Ad=st([].join),Pd=gt!=Object,Vd=ai("join",",");wn({target:"Array",proto:!0,forced:Pd||!Vd},{join:function(t){return Ad(bt(this),void 0===t?",":t)}});var Cd=function(){function t(){this.title=n("codeBlock.title"),this.iconSvg='<svg viewBox="0 0 1280 1024"><path d="M832 736l96 96 320-320L928 192l-96 96 224 224zM448 288l-96-96L32 512l320 320 96-96-224-224zM701.312 150.528l69.472 18.944-192 704.032-69.472-18.944 192-704.032z"></path></svg>',this.tag="button"}return t.prototype.getSelectCodeElem=function(t){var n=e.getSelectedNodeByType(t,"code");if(null==n)return null;var r=e.getParentNode(t,n);return null==r||"pre"!==e.getNodeType(r)?null:n},t.prototype.getValue=function(t){var e=this.getSelectCodeElem(t);return null==e?"":e.language||""},t.prototype.isActive=function(t){return!!this.getSelectCodeElem(t)},t.prototype.isDisabled=function(t){if(null==t.selection)return!0;var n=e.getSelectedElems(t);return!!n.some((function(e){return t.isVoid(e)}))||!n.some((function(t){var n=e.getNodeType(t);if("pre"===n||"paragraph"===n)return!0}))},t.prototype.exec=function(t,e){this.isActive(t)?this.changeToPlainText(t):this.changeToCodeBlock(t,e.toString())},t.prototype.changeToPlainText=function(t){var e=this.getSelectCodeElem(t);if(null!=e){var n=s.string(e);f.removeNodes(t,{mode:"highest"});var r=n.split("\n").map((function(t){return{type:"paragraph",children:[{text:t}]}}));f.insertNodes(t,r,{mode:"highest"})}},t.prototype.changeToCodeBlock=function(t,e){var n,r,o=[],i=c.nodes(t,{match:function(e){return t.children.includes(e)},universal:!0});try{for(var u=Uo(i),a=u.next();!a.done;a=u.next()){var l=Xo(a.value,1)[0];l&&o.push(s.string(l))}}catch(t){n={error:t}}finally{try{a&&!a.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}f.removeNodes(t,{mode:"highest"});var p={type:"pre",children:[{type:"code",language:e,children:[{text:o.join("\n")}]}]};f.insertNodes(t,p,{mode:"highest"})},t}(),Dd={key:"codeBlock",factory:function(){return new Cd}};wn({target:"String",proto:!0,forced:ti("anchor")},{anchor:function(t){return Zo(this,"a","name",t)}}),Pr("match",(function(t,e,n){return[function(e){var n=mt(this),r=null==e?void 0:Bt(e,t);return r?tt(r,e,n):new RegExp(e)[t](Yn(n))},function(t){var r=ge(this),o=Yn(t),i=n(e,r,o);if(i.done)return i.value;if(!r.global)return Jr(r,o);var u=r.unicode;r.lastIndex=0;for(var a,c=[],l=0;null!==(a=Jr(r,o));){var s=Yn(a[0]);c[l]=s,""===s&&(r.lastIndex=Fr(o,Ke(r.lastIndex),u)),l++}return 0===l?null:c}]}));var Bd={type:"pre",renderElem:function(t,e,n){return i("pre",null,e)}},Rd={type:"code",renderElem:function(t,e,n){return i("code",null,e)}};var Fd={selector:"pre:not([data-w-e-type])",parseElemHtml:function(t,n,r){var o=h(t);return 0===(n=n.filter((function(t){return"code"===e.getNodeType(t)}))).length&&(n=[{type:"code",language:"",children:[{text:o[0].textContent||""}]}]),{type:"pre",children:n.filter((function(t){return"code"===e.getNodeType(t)}))}}};var _d={menus:[Dd],editorPlugin:function(t){var n=t.insertBreak,r=t.normalizeNode,o=t.insertData;t.insertNode;var i=t;return i.insertBreak=function(){var t=e.getSelectedNodeByType(i,"code");if(null!=t){var r=function(t,e){var n=e.selection;if(null==n)return"";var r=s.string(t),o=n.anchor.offset,i=r.slice(0,o).split("\n"),u=i.length;return 0===u?"":i[u-1]}(t,i);if(r){var o=r.match(/^\s+/);if(null!=o&&null!=o[0]){var u=o[0];return void i.insertText("\n"+u)}}i.insertText("\n")}else n()},i.normalizeNode=function(t){var n=Xo(t,2),o=n[0],u=n[1],a=e.getNodeType(o);("code"===a&&u.length<=1&&f.setNodes(i,{type:"paragraph"},{at:u}),"pre"===a)&&(e.isLastNode(i,o)&&f.insertNodes(i,e.genEmptyParagraph(),{at:[u[0]+1]}),"code"!==e.getNodeType(o.children[0])&&(f.unwrapNodes(i),f.setNodes(i,{type:"paragraph"},{mode:"highest"})));return r([o,u])},i.insertData=function(t){if(null!=e.getSelectedNodeByType(i,"code")){var n=t.getData("text/plain");c.insertText(i,n)}else o(t)},i},renderElems:[Bd,Rd],elemsToHtml:[{type:"code",elemToHtml:function(t,e){return"<code>"+e+"</code>"}},{type:"pre",elemToHtml:function(t,e){return"<pre>"+e+"</pre>"}}],preParseHtml:[{selector:"pre>code",preParseHtml:function(t){var e=h(t);if("code"!==Bo(e))return t;var n=e.find("xmp");if(0===n.length)return t;var r=n.text();return n.remove(),e.text(r),e[0]}}],parseElemsHtml:[{selector:"pre:not([data-w-e-type])>code",parseElemHtml:function(t,e,n){return{type:"code",language:"",children:[{text:h(t)[0].textContent||""}]}}},Fd]},$d=function(){function t(){this.title=n("fullScreen.title"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M133.705143 335.433143V133.851429h201.581714a29.622857 29.622857 0 0 0 29.622857-29.549715V68.754286a29.622857 29.622857 0 0 0-29.622857-29.622857H61.732571A22.893714 22.893714 0 0 0 38.765714 62.025143V335.725714c0 16.310857 13.238857 29.622857 29.622857 29.622857h35.547429a29.842286 29.842286 0 0 0 29.696-29.842285zM690.980571 133.851429h201.581715v201.654857c0 16.310857 13.238857 29.549714 29.622857 29.549714h35.547428a29.622857 29.622857 0 0 0 29.549715-29.549714V61.952a22.893714 22.893714 0 0 0-22.820572-22.893714h-273.554285a29.622857 29.622857 0 0 0-29.549715 29.622857v35.547428c0 16.310857 13.238857 29.696 29.622857 29.696zM335.286857 892.781714H133.705143V691.2a29.622857 29.622857 0 0 0-29.622857-29.622857H68.534857a29.622857 29.622857 0 0 0-29.549714 29.622857v273.554286c0 12.653714 10.24 22.893714 22.820571 22.893714h273.554286a29.622857 29.622857 0 0 0 29.696-29.622857v-35.547429a29.769143 29.769143 0 0 0-29.769143-29.696z m557.348572-201.581714v201.581714H690.907429a29.622857 29.622857 0 0 0-29.622858 29.622857v35.547429c0 16.310857 13.238857 29.622857 29.622858 29.622857h273.554285c12.580571 0 22.893714-10.313143 22.893715-22.893714V691.2a29.622857 29.622857 0 0 0-29.622858-29.622857h-35.547428a29.622857 29.622857 0 0 0-29.696 29.622857z"></path></svg>',this.tag="button",this.alwaysEnable=!0}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return t.isFullScreen},t.prototype.isDisabled=function(t){return!1},t.prototype.exec=function(t,e){t.isFullScreen?t.unFullScreen():t.fullScreen()},t}(),qd={menus:[{key:"fullScreen",factory:function(){return new $d}}]},Wd=function(){function t(){this.title=n("common.enter"),this.iconSvg='<svg viewBox="0 0 1255 1024"><path d="M1095.111111 731.477333h-625.777778V1024L0 658.318222 469.333333 292.408889v292.636444h625.777778V0h156.444445v731.477333z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){var e=t.selection;return null==e||!!p.isExpanded(e)},t.prototype.exec=function(t,e){var n=t.selection;if(null!=n){var r=[n.anchor.path[0]];f.insertNodes(t,{type:"paragraph",children:[{text:""}]},{at:r}),t.select(c.start(t,r))}},t}(),Gd=[_i,Ru,fd,gd,Sd,Ed,$a,zd,uc,la,_d,oc,Iu,Jo,Ua,Hd,qd,{menus:[{key:"enter",factory:function(){return new Wd}}]}];export{Gd as default,Ia as insertImageNode,La as isInsertImageMenuDisabled,Na as updateImageNode};
//# sourceMappingURL=index.esm.js.map
