{"name": "unset-value", "description": "Delete nested properties from an object using dot notation.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/unset-value", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<<EMAIL>> (https://github.com/wtgtybhertgeghgtwtg)", "<PERSON> <<EMAIL>> (http://twitter.com/jonschlinkert)"], "repository": "jonschlinkert/unset-value", "bugs": {"url": "https://github.com/jonschlinkert/unset-value/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "*", "should": "*"}, "keywords": ["del", "delete", "key", "object", "omit", "prop", "property", "remove", "unset", "value"], "verb": {"related": {"list": ["get-value", "get-values", "omit-value", "put-value", "set-value", "union-value", "upsert-value"]}, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}}