#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/20
# @File           : auth.py
# @IDE            : PyCharm
# @desc           : 认证相关API接口（登录、登出、权限验证等）

from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from apps.admin.depts import get_admin_db
from apps.admin.services.auth import AuthService
from apps.admin.schemas.auth import (
    LoginRequest, LoginResponse, RefreshTokenRequest,
    ChangePasswordRequest, UserProfileOut, UserProfileUpdate
)

app = APIRouter()


@app.post("/login", summary="用户登录")
async def login(
    request: Request,
    data: LoginRequest,
    db: AsyncSession = get_admin_db()
):
    """
    用户登录
    """
    service = AuthService(db)
    return await service.login(data, request)


@app.post("/logout", summary="用户登出")
async def logout(
    request: Request,
    db: AsyncSession = get_admin_db()
):
    """
    用户登出
    """
    service = AuthService(db)
    return await service.logout(request)


@app.post("/refresh-token", summary="刷新访问令牌")
async def refresh_token(
    data: RefreshTokenRequest,
    db: AsyncSession = get_admin_db()
):
    """
    刷新访问令牌
    """
    service = AuthService(db)
    return await service.refresh_token(data)


@app.get("/profile", summary="获取当前用户信息")
async def get_profile(
    db: AsyncSession = get_admin_db()
    # TODO: 添加当前用户认证依赖
):
    """
    获取当前用户信息
    """
    service = AuthService(db)
    return await service.get_profile()


@app.put("/profile", summary="更新当前用户信息")
async def update_profile(
    data: UserProfileUpdate,
    db: AsyncSession = get_admin_db()
    # TODO: 添加当前用户认证依赖
):
    """
    更新当前用户信息
    """
    service = AuthService(db)
    return await service.update_profile(data)


@app.post("/change-password", summary="修改当前用户密码")
async def change_password(
    data: ChangePasswordRequest,
    db: AsyncSession = get_admin_db()
    # TODO: 添加当前用户认证依赖
):
    """
    修改当前用户密码
    """
    service = AuthService(db)
    return await service.change_password(data)


@app.get("/permissions", summary="获取当前用户权限")
async def get_permissions(
    db: AsyncSession = get_admin_db()
    # TODO: 添加当前用户认证依赖
):
    """
    获取当前用户权限
    """
    service = AuthService(db)
    return await service.get_permissions()


@app.get("/user-menus", summary="获取当前用户菜单")
async def get_user_menus(
    db: AsyncSession = get_admin_db()
    # TODO: 添加当前用户认证依赖
):
    """
    获取当前用户菜单
    """
    service = AuthService(db)
    return await service.get_menus()


@app.post("/verify-token", summary="验证访问令牌")
async def verify_token(
    request: Request,
    db: AsyncSession = get_admin_db()
):
    """
    验证访问令牌
    """
    service = AuthService(db)
    return await service.verify_token(request)
