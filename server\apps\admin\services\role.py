#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : role.py
# @IDE            : PyCharm
# @desc           : 角色管理服务

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import joinedload
from apps.admin.depts import AdminService
from models.admin.auth.role import AdminRole
from models.admin.auth.menu import AdminMenu
from models.admin.auth.dept import AdminDept
from apps.admin.schemas.role import (
    RoleCreate, RoleUpdate, RoleOut, RoleListOut, RoleOptionOut
)
from apps.admin.params.role import RoleParams
from utils.response import SuccessResponse, ErrorResponse
from core.logging import logger
from typing import List, Optional, Dict, Any


class RoleService(AdminService):
    """
    角色管理服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.role_dal = self.get_dal(AdminRole, RoleOut)
        self.menu_dal = self.get_dal(AdminMenu)
        self.dept_dal = self.get_dal(AdminDept)

    async def get_roles(self, params: RoleParams, paging) -> dict:
        """
        获取角色列表
        """
        try:
            filters = {}
            
            # 构建查询条件
            if params.name:
                filters["name"] = ("like", params.name)
            if params.role_key:
                filters["role_key"] = ("like", params.role_key)
            if params.disabled is not None:
                filters["disabled"] = params.disabled
            if params.is_admin is not None:
                filters["is_admin"] = params.is_admin
            if params.create_datetime_start:
                filters["create_datetime"] = (">=", params.create_datetime_start)
            if params.create_datetime_end:
                filters["create_datetime"] = ("<=", params.create_datetime_end)

            # 获取数据
            datas, count = await self.role_dal.get_datas(
                page=paging.page,
                limit=paging.limit,
                v_return_count=True,
                v_order=paging.v_order,
                v_order_field=paging.v_order_field,
                v_schema=RoleListOut,
                **filters
            )

            return SuccessResponse(data=datas, count=count)

        except Exception as e:
            logger.error(f"获取角色列表失败: {e}")
            return ErrorResponse(msg=f"获取角色列表失败: {str(e)}")

    async def create_role(self, data: RoleCreate) -> dict:
        """
        创建角色
        """
        try:
            # 检查角色名称是否已存在
            existing_role = await self.role_dal.get_data(
                name=data.name,
                v_return_none=True
            )
            if existing_role:
                return ErrorResponse(msg="角色名称已存在")

            # 检查角色标识是否已存在
            existing_key = await self.role_dal.get_data(
                role_key=data.role_key,
                v_return_none=True
            )
            if existing_key:
                return ErrorResponse(msg="角色标识已存在")

            # 创建角色数据
            role_data = data.dict(exclude={'menu_ids', 'dept_ids'})
            
            # 创建角色
            role = await self.role_dal.create_data(data=role_data)

            # 关联菜单
            if data.menu_ids:
                await self._associate_role_menus(role.id, data.menu_ids)

            # 关联部门
            if data.dept_ids:
                await self._associate_role_depts(role.id, data.dept_ids)

            return SuccessResponse(data=RoleOut.from_orm(role), msg="创建成功")

        except Exception as e:
            logger.error(f"创建角色失败: {e}")
            return ErrorResponse(msg=f"创建失败: {str(e)}")

    async def update_role(self, role_id: int, data: RoleUpdate) -> dict:
        """
        更新角色
        """
        try:
            # 检查角色是否存在
            role = await self.role_dal.get_data(role_id, v_return_none=True)
            if not role:
                return ErrorResponse(msg="角色不存在")

            # 检查角色名称是否重复（如果要更新名称）
            if data.name and data.name != role.name:
                existing_role = await self.role_dal.get_data(
                    name=data.name,
                    v_return_none=True
                )
                if existing_role:
                    return ErrorResponse(msg="角色名称已存在")

            # 检查角色标识是否重复（如果要更新标识）
            if data.role_key and data.role_key != role.role_key:
                existing_key = await self.role_dal.get_data(
                    role_key=data.role_key,
                    v_return_none=True
                )
                if existing_key:
                    return ErrorResponse(msg="角色标识已存在")

            # 更新角色基本信息
            update_data = data.dict(exclude_unset=True, exclude={'menu_ids', 'dept_ids'})
            if update_data:
                await self.role_dal.put_data(role_id, update_data)

            # 更新菜单关联
            if data.menu_ids is not None:
                await self._update_role_menus(role_id, data.menu_ids)

            # 更新部门关联
            if data.dept_ids is not None:
                await self._update_role_depts(role_id, data.dept_ids)

            # 获取更新后的角色信息
            updated_role = await self.role_dal.get_data(role_id)
            
            return SuccessResponse(data=RoleOut.from_orm(updated_role), msg="更新成功")

        except Exception as e:
            logger.error(f"更新角色失败: {e}")
            return ErrorResponse(msg=f"更新失败: {str(e)}")

    async def delete_role(self, role_id: int) -> dict:
        """
        删除角色
        """
        try:
            return await self.batch_delete_roles([role_id])

        except Exception as e:
            logger.error(f"删除角色失败: {e}")
            return ErrorResponse(msg=f"删除失败: {str(e)}")

    async def batch_delete_roles(self, role_ids: List[int]) -> dict:
        """
        批量删除角色
        """
        try:
            # 检查是否包含超级管理员角色
            if 1 in role_ids:
                return ErrorResponse(msg="不能删除超级管理员角色")

            # 软删除角色
            await self.role_dal.delete_datas(role_ids, v_soft=True)

            return SuccessResponse(msg="删除成功")

        except Exception as e:
            logger.error(f"批量删除角色失败: {e}")
            return ErrorResponse(msg=f"删除失败: {str(e)}")

    async def get_role(self, role_id: int) -> dict:
        """
        获取角色详情
        """
        try:
            role = await self.role_dal.get_data(role_id, v_return_none=True)
            if not role:
                return ErrorResponse(msg="角色不存在")

            return SuccessResponse(data=RoleOut.from_orm(role))

        except Exception as e:
            logger.error(f"获取角色详情失败: {e}")
            return ErrorResponse(msg=f"获取角色详情失败: {str(e)}")

    async def assign_role_menus(self, role_id: int, menu_ids: List[int]) -> dict:
        """
        分配角色菜单
        """
        try:
            # 检查角色是否存在
            role = await self.role_dal.get_data(role_id, v_return_none=True)
            if not role:
                return ErrorResponse(msg="角色不存在")

            # 更新菜单关联
            await self._update_role_menus(role_id, menu_ids)

            return SuccessResponse(msg="菜单分配成功")

        except Exception as e:
            logger.error(f"分配角色菜单失败: {e}")
            return ErrorResponse(msg=f"分配失败: {str(e)}")

    async def assign_role_depts(self, role_id: int, dept_ids: List[int]) -> dict:
        """
        分配角色部门
        """
        try:
            # 检查角色是否存在
            role = await self.role_dal.get_data(role_id, v_return_none=True)
            if not role:
                return ErrorResponse(msg="角色不存在")

            # 更新部门关联
            await self._update_role_depts(role_id, dept_ids)

            return SuccessResponse(msg="部门分配成功")

        except Exception as e:
            logger.error(f"分配角色部门失败: {e}")
            return ErrorResponse(msg=f"分配失败: {str(e)}")

    async def get_role_menus(self, role_id: int) -> dict:
        """
        获取角色菜单
        """
        try:
            # 这里需要根据实际的关联表来实现
            # 暂时返回空列表
            return SuccessResponse(data=[])

        except Exception as e:
            logger.error(f"获取角色菜单失败: {e}")
            return ErrorResponse(msg=f"获取失败: {str(e)}")

    async def get_role_depts(self, role_id: int) -> dict:
        """
        获取角色部门
        """
        try:
            # 这里需要根据实际的关联表来实现
            # 暂时返回空列表
            return SuccessResponse(data=[])

        except Exception as e:
            logger.error(f"获取角色部门失败: {e}")
            return ErrorResponse(msg=f"获取失败: {str(e)}")

    async def enable_role(self, role_id: int) -> dict:
        """
        启用角色
        """
        try:
            return await self.update_role(role_id, RoleUpdate(disabled=False))

        except Exception as e:
            logger.error(f"启用角色失败: {e}")
            return ErrorResponse(msg=f"启用失败: {str(e)}")

    async def disable_role(self, role_id: int) -> dict:
        """
        禁用角色
        """
        try:
            return await self.update_role(role_id, RoleUpdate(disabled=True))

        except Exception as e:
            logger.error(f"禁用角色失败: {e}")
            return ErrorResponse(msg=f"禁用失败: {str(e)}")

    async def _associate_role_menus(self, role_id: int, menu_ids: List[int]):
        """
        关联角色菜单
        """
        # 这里需要根据实际的关联表来实现
        # 暂时跳过
        pass

    async def _associate_role_depts(self, role_id: int, dept_ids: List[int]):
        """
        关联角色部门
        """
        # 这里需要根据实际的关联表来实现
        # 暂时跳过
        pass

    async def _update_role_menus(self, role_id: int, menu_ids: List[int]):
        """
        更新角色菜单关联
        """
        # 这里需要根据实际的关联表来实现
        # 暂时跳过
        pass

    async def _update_role_depts(self, role_id: int, dept_ids: List[int]):
        """
        更新角色部门关联
        """
        # 这里需要根据实际的关联表来实现
        # 暂时跳过
        pass
