#!/usr/bin/python
# -*- coding: utf-8 -*-
# @version        : 1.0
# @Create Time    : 2025/07/21
# @File           : menu.py
# @IDE            : PyCharm
# @desc           : 菜单管理服务

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import joinedload
from apps.admin.depts import AdminService
from models.admin.auth.menu import AdminMenu
from apps.admin.schemas.menu import (
    MenuCreate, MenuUpdate, MenuOut, MenuTreeOut, 
    MenuOptionOut, MenuListOut, RouterOut
)
from apps.admin.params.menu import MenuParams
from utils.response import SuccessResponse, ErrorResponse
from core.logging import logger
from typing import List, Optional, Dict, Any


class MenuService(AdminService):
    """
    菜单管理服务类
    """
    
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self.menu_dal = self.get_dal(AdminMenu, MenuOut)

    async def get_menus(self, params: MenuParams, paging) -> dict:
        """
        获取菜单列表
        """
        try:
            filters = {}
            
            # 构建查询条件
            if params.title:
                filters["title"] = ("like", params.title)
            if params.name:
                filters["name"] = ("like", params.name)
            if params.path:
                filters["path"] = ("like", params.path)
            if params.menu_type:
                filters["menu_type"] = params.menu_type
            if params.parent_id is not None:
                filters["parent_id"] = params.parent_id
            if params.disabled is not None:
                filters["disabled"] = params.disabled
            if params.hidden is not None:
                filters["hidden"] = params.hidden
            if params.create_datetime_start:
                filters["create_datetime"] = (">=", params.create_datetime_start)
            if params.create_datetime_end:
                filters["create_datetime"] = ("<=", params.create_datetime_end)

            # 获取数据
            datas, count = await self.menu_dal.get_datas(
                page=paging.page,
                limit=paging.limit,
                v_return_count=True,
                v_order=paging.v_order,
                v_order_field=paging.v_order_field,
                v_schema=MenuListOut,
                **filters
            )

            return SuccessResponse(data=datas, count=count)

        except Exception as e:
            logger.error(f"获取菜单列表失败: {e}")
            return ErrorResponse(msg=f"获取菜单列表失败: {str(e)}")

    async def create_menu(self, data: MenuCreate) -> dict:
        """
        创建菜单
        """
        try:
            # 检查菜单名称是否已存在
            existing_menu = await self.menu_dal.get_data(
                name=data.name,
                v_return_none=True
            )
            if existing_menu:
                return ErrorResponse(msg="菜单名称已存在")

            # 检查路径是否已存在（同级路径不能重复）
            if data.path:
                existing_path = await self.menu_dal.get_data(
                    path=data.path,
                    parent_id=data.parent_id,
                    v_return_none=True
                )
                if existing_path:
                    return ErrorResponse(msg="同级菜单路径不能重复")

            # 验证父菜单是否存在
            if data.parent_id:
                parent_menu = await self.menu_dal.get_data(
                    data.parent_id,
                    v_return_none=True
                )
                if not parent_menu:
                    return ErrorResponse(msg="父菜单不存在")

            # 创建菜单
            menu_data = data.dict()
            menu = await self.menu_dal.create_data(data=menu_data)

            return SuccessResponse(data=MenuOut.from_orm(menu), msg="创建成功")

        except Exception as e:
            logger.error(f"创建菜单失败: {e}")
            return ErrorResponse(msg=f"创建失败: {str(e)}")

    async def update_menu(self, menu_id: int, data: MenuUpdate) -> dict:
        """
        更新菜单
        """
        try:
            # 检查菜单是否存在
            menu = await self.menu_dal.get_data(menu_id, v_return_none=True)
            if not menu:
                return ErrorResponse(msg="菜单不存在")

            # 检查菜单名称是否重复（如果要更新名称）
            if data.name and data.name != menu.name:
                existing_menu = await self.menu_dal.get_data(
                    name=data.name,
                    v_return_none=True
                )
                if existing_menu:
                    return ErrorResponse(msg="菜单名称已存在")

            # 检查路径是否重复（如果要更新路径）
            if data.path and data.path != menu.path:
                parent_id = data.parent_id if data.parent_id is not None else menu.parent_id
                existing_path = await self.menu_dal.get_data(
                    path=data.path,
                    parent_id=parent_id,
                    v_return_none=True
                )
                if existing_path and existing_path.id != menu_id:
                    return ErrorResponse(msg="同级菜单路径不能重复")

            # 验证父菜单是否存在（如果要更新父菜单）
            if data.parent_id and data.parent_id != menu.parent_id:
                # 不能将菜单设置为自己的子菜单
                if data.parent_id == menu_id:
                    return ErrorResponse(msg="不能将菜单设置为自己的子菜单")
                
                # 检查是否会形成循环引用
                if await self._check_circular_reference(menu_id, data.parent_id):
                    return ErrorResponse(msg="不能形成循环引用")
                
                parent_menu = await self.menu_dal.get_data(
                    data.parent_id,
                    v_return_none=True
                )
                if not parent_menu:
                    return ErrorResponse(msg="父菜单不存在")

            # 更新菜单
            update_data = data.dict(exclude_unset=True)
            await self.menu_dal.put_data(menu_id, update_data)

            # 获取更新后的菜单信息
            updated_menu = await self.menu_dal.get_data(menu_id)
            
            return SuccessResponse(data=MenuOut.from_orm(updated_menu), msg="更新成功")

        except Exception as e:
            logger.error(f"更新菜单失败: {e}")
            return ErrorResponse(msg=f"更新失败: {str(e)}")

    async def delete_menus(self, menu_ids: List[int]) -> dict:
        """
        批量删除菜单
        """
        try:
            # 检查是否有子菜单
            for menu_id in menu_ids:
                children = await self.menu_dal.get_datas(parent_id=menu_id)
                if children:
                    return ErrorResponse(msg=f"菜单ID {menu_id} 存在子菜单，无法删除")

            # 软删除菜单
            await self.menu_dal.delete_datas(menu_ids, v_soft=True)

            return SuccessResponse(msg="删除成功")

        except Exception as e:
            logger.error(f"删除菜单失败: {e}")
            return ErrorResponse(msg=f"删除失败: {str(e)}")

    async def get_menu(self, menu_id: int) -> dict:
        """
        获取菜单详情
        """
        try:
            menu = await self.menu_dal.get_data(menu_id, v_return_none=True)
            if not menu:
                return ErrorResponse(msg="菜单不存在")

            return SuccessResponse(data=MenuOut.from_orm(menu))

        except Exception as e:
            logger.error(f"获取菜单详情失败: {e}")
            return ErrorResponse(msg=f"获取菜单详情失败: {str(e)}")

    async def get_menu_tree(self, include_disabled: bool = False) -> dict:
        """
        获取菜单树
        """
        try:
            # 构建查询条件
            filters = {}
            if not include_disabled:
                filters["disabled"] = False

            # 获取所有菜单
            menus = await self.menu_dal.get_datas(
                v_schema=MenuTreeOut,
                v_order="asc",
                v_order_field="order",
                **filters
            )

            # 构建树结构
            tree = self._build_menu_tree(menus)

            return SuccessResponse(data=tree)

        except Exception as e:
            logger.error(f"获取菜单树失败: {e}")
            return ErrorResponse(msg=f"获取菜单树失败: {str(e)}")

    async def get_menu_options(self, exclude_id: Optional[int] = None) -> dict:
        """
        获取菜单选项（用于父菜单选择）
        """
        try:
            filters = {"disabled": False}
            if exclude_id:
                filters["id"] = ("!=", exclude_id)

            menus = await self.menu_dal.get_datas(
                v_schema=MenuOptionOut,
                v_order="asc",
                v_order_field="order",
                **filters
            )

            # 构建树结构选项
            options = self._build_menu_tree(menus)

            return SuccessResponse(data=options)

        except Exception as e:
            logger.error(f"获取菜单选项失败: {e}")
            return ErrorResponse(msg=f"获取菜单选项失败: {str(e)}")

    async def get_user_routers(self, user_id: int) -> dict:
        """
        获取用户路由（前端路由格式）
        """
        try:
            # 这里需要根据用户权限获取菜单
            # 暂时返回所有启用的菜单
            menus = await self.menu_dal.get_datas(
                disabled=False,
                hidden=False,
                v_order="asc",
                v_order_field="order"
            )

            # 转换为路由格式
            routers = []
            for menu in menus:
                if menu.parent_id is None:  # 只处理顶级菜单
                    router = RouterOut.from_menu(menu)
                    router.children = await self._build_router_children(menu.id, menus)
                    routers.append(router)

            return SuccessResponse(data=routers)

        except Exception as e:
            logger.error(f"获取用户路由失败: {e}")
            return ErrorResponse(msg=f"获取用户路由失败: {str(e)}")

    def _build_menu_tree(self, menus: List) -> List:
        """
        构建菜单树结构
        """
        menu_dict = {menu.id: menu for menu in menus}
        tree = []

        for menu in menus:
            if menu.parent_id is None:
                # 顶级菜单
                menu.children = self._get_menu_children(menu.id, menu_dict)
                tree.append(menu)

        return tree

    def _get_menu_children(self, parent_id: int, menu_dict: Dict) -> List:
        """
        递归获取子菜单
        """
        children = []
        for menu in menu_dict.values():
            if menu.parent_id == parent_id:
                menu.children = self._get_menu_children(menu.id, menu_dict)
                children.append(menu)
        
        # 按order排序
        children.sort(key=lambda x: x.order or 0)
        return children

    async def _build_router_children(self, parent_id: int, all_menus: List) -> List[RouterOut]:
        """
        构建路由子节点
        """
        children = []
        for menu in all_menus:
            if menu.parent_id == parent_id:
                router = RouterOut.from_menu(menu)
                router.children = await self._build_router_children(menu.id, all_menus)
                children.append(router)
        
        # 按order排序
        children.sort(key=lambda x: getattr(x, 'order', 0) or 0)
        return children

    async def _check_circular_reference(self, menu_id: int, parent_id: int) -> bool:
        """
        检查是否会形成循环引用
        """
        current_id = parent_id
        while current_id:
            if current_id == menu_id:
                return True
            
            parent_menu = await self.menu_dal.get_data(current_id, v_return_none=True)
            if not parent_menu:
                break
            
            current_id = parent_menu.parent_id
        
        return False
