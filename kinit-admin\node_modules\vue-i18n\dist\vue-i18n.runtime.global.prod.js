/*!
  * vue-i18n v9.9.1
  * (c) 2024 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const a="undefined"!=typeof window,n=(e,t=!1)=>t?Symbol.for(e):Symbol(e),l=(e,t,a)=>r({l:e,k:t,s:a}),r=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),s=e=>"[object Date]"===k(e),i=e=>"[object RegExp]"===k(e),c=e=>L(e)&&0===Object.keys(e).length,u=Object.assign;function m(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const f=Object.prototype.hasOwnProperty;function _(e,t){return f.call(e,t)}const g=Array.isArray,p=e=>"function"==typeof e,v=e=>"string"==typeof e,d=e=>"boolean"==typeof e,b=e=>null!==e&&"object"==typeof e,E=e=>b(e)&&p(e.then)&&p(e.catch),h=Object.prototype.toString,k=e=>h.call(e),L=e=>{if(!b(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function T(e){let t=e;return()=>++t}function F(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const y=e=>!b(e)||g(e);function N(e,t){if(y(e)||y(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:e,des:t}=a.pop();Object.keys(e).forEach((n=>{y(e[n])||y(t[n])?t[n]=e[n]:a.push({src:e[n],des:t[n]})}))}}function I(e,t,a={}){const{domain:n,messages:l,args:r}=a,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=n,o}const R=[];R[0]={w:[0],i:[3,0],"[":[4],o:[7]},R[1]={w:[1],".":[2],"[":[4],o:[7]},R[2]={w:[2],i:[3,0],0:[3,0]},R[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},R[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},R[5]={"'":[4,0],o:8,l:[5,0]},R[6]={'"':[4,0],o:8,l:[6,0]};const O=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function M(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function W(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(a=t,O.test(a)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var a}const w=new Map;function P(e,t){return b(e)?e[t]:null}const C=e=>e,D=e=>"",A="text",S=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,a,n)=>0===n?e+a:e+t+a),"")}(e),$=e=>null==e?"":g(e)||L(e)&&e.toString===h?JSON.stringify(e,null,2):String(e);function U(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function x(e={}){const t=e.locale,a=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),n=b(e.pluralRules)&&v(t)&&p(e.pluralRules[t])?e.pluralRules[t]:U,l=b(e.pluralRules)&&v(t)&&p(e.pluralRules[t])?U:void 0,r=e.list||[],s=e.named||{};o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,s);function i(t){const a=p(e.messages)?e.messages(t):!!b(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):D)}const c=L(e.processor)&&p(e.processor.normalize)?e.processor.normalize:S,m=L(e.processor)&&p(e.processor.interpolate)?e.processor.interpolate:$,f={list:e=>r[e],named:e=>s[e],plural:e=>e[n(a,e.length,l)],linked:(t,...a)=>{const[n,l]=a;let r="text",o="";1===a.length?b(n)?(o=n.modifier||o,r=n.type||r):v(n)&&(o=n||o):2===a.length&&(v(n)&&(o=n||o),v(l)&&(r=l||r));const s=i(t)(f),c="vnode"===r&&g(s)&&o?s[0]:s;return o?(u=o,e.modifiers?e.modifiers[u]:C)(c,r):c;var u},message:i,type:L(e.processor)&&v(e.processor.type)?e.processor.type:A,interpolate:m,normalize:c,values:u({},r,s)};return f}const H=T(17),j={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:H(),INVALID_ISO_DATE_ARGUMENT:H(),NOT_SUPPORT_NON_STRING_MESSAGE:H(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:H(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:H(),NOT_SUPPORT_LOCALE_TYPE:H(),__EXTEND_POINT__:H()};function V(e,t){return null!=t.locale?Y(t.locale):Y(e.locale)}let G;function Y(e){if(v(e))return e;if(p(e)){if(e.resolvedOnce&&null!=G)return G;if("Function"===e.constructor.name){const t=e();if(E(t))throw Error(j.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return G=t}throw Error(j.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(j.NOT_SUPPORT_LOCALE_TYPE)}function B(e,t,a){return[...new Set([a,...g(t)?t:b(t)?Object.keys(t):v(t)?[t]:[a]])]}function X(e,t,a){const n=v(a)?a:Q,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let r=l.__localeChainCache.get(n);if(!r){r=[];let e=[a];for(;g(e);)e=z(r,e,t);const o=g(t)||!L(t)?t:t.default?t.default:null;e=v(o)?[o]:o,g(e)&&z(r,e,!1),l.__localeChainCache.set(n,r)}return r}function z(e,t,a){let n=!0;for(let l=0;l<t.length&&d(n);l++){const r=t[l];v(r)&&(n=J(e,t[l],a))}return n}function J(e,t,a){let n;const l=t.split("-");do{n=q(e,l.join("-"),a),l.splice(-1,1)}while(l.length&&!0===n);return n}function q(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const l=t.replace(/!/g,"");e.push(l),(g(a)||L(a))&&a[l]&&(n=a[l])}return n}const Z="9.9.1",K=-1,Q="en-US",ee="",te=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let ae,ne,le;let re=null;const oe=e=>{re=e},se=()=>re;let ie=0;function ce(e={}){const t=p(e.onWarn)?e.onWarn:F,a=v(e.version)?e.version:Z,n=v(e.locale)||p(e.locale)?e.locale:Q,l=p(n)?Q:n,r=g(e.fallbackLocale)||L(e.fallbackLocale)||v(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:l,o=L(e.messages)?e.messages:{[l]:{}},s=L(e.datetimeFormats)?e.datetimeFormats:{[l]:{}},c=L(e.numberFormats)?e.numberFormats:{[l]:{}},m=u({},e.modifiers||{},{upper:(e,t)=>"text"===t&&v(e)?e.toUpperCase():"vnode"===t&&b(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&v(e)?e.toLowerCase():"vnode"===t&&b(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&v(e)?te(e):"vnode"===t&&b(e)&&"__v_isVNode"in e?te(e.children):e}),f=e.pluralRules||{},_=p(e.missing)?e.missing:null,E=!d(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,h=!d(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,k=!!e.fallbackFormat,T=!!e.unresolving,y=p(e.postTranslation)?e.postTranslation:null,N=L(e.processor)?e.processor:null,I=!d(e.warnHtmlMessage)||e.warnHtmlMessage,R=!!e.escapeParameter,O=p(e.messageCompiler)?e.messageCompiler:ae,M=p(e.messageResolver)?e.messageResolver:ne||P,W=p(e.localeFallbacker)?e.localeFallbacker:le||B,w=b(e.fallbackContext)?e.fallbackContext:void 0,C=e,D=b(C.__datetimeFormatters)?C.__datetimeFormatters:new Map,A=b(C.__numberFormatters)?C.__numberFormatters:new Map,S=b(C.__meta)?C.__meta:{};ie++;const $={version:a,cid:ie,locale:n,fallbackLocale:r,messages:o,modifiers:m,pluralRules:f,missing:_,missingWarn:E,fallbackWarn:h,fallbackFormat:k,unresolving:T,postTranslation:y,processor:N,warnHtmlMessage:I,escapeParameter:R,messageCompiler:O,messageResolver:M,localeFallbacker:W,fallbackContext:w,onWarn:t,__meta:S};return $.datetimeFormats=s,$.numberFormats=c,$.__datetimeFormatters=D,$.__numberFormatters=A,$}function ue(e,t,a,n,l){const{missing:r,onWarn:o}=e;if(null!==r){const n=r(e,a,t,l);return v(n)?n:t}return t}function me(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}const fe=e=>b(e)&&(0===e.t||0===e.type)&&("b"in e||"body"in e),_e=()=>"",ge=e=>p(e);function pe(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:l,messageCompiler:r,fallbackLocale:s,messages:i}=e,[c,u]=be(...t),f=d(u.missingWarn)?u.missingWarn:e.missingWarn,_=d(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,p=d(u.escapeParameter)?u.escapeParameter:e.escapeParameter,E=!!u.resolvedMessage,h=v(u.default)||d(u.default)?d(u.default)?r?c:()=>c:u.default:a?r?c:()=>c:"",k=a||""!==h,L=V(e,u);p&&function(e){g(e.list)?e.list=e.list.map((e=>v(e)?m(e):e)):b(e.named)&&Object.keys(e.named).forEach((t=>{v(e.named[t])&&(e.named[t]=m(e.named[t]))}))}(u);let[T,F,y]=E?[c,L,i[L]||{}]:ve(e,c,L,s,_,f),N=T,I=c;if(E||v(N)||fe(N)||ge(N)||k&&(N=h,I=N),!(E||(v(N)||fe(N)||ge(N))&&v(F)))return l?K:c;let R=!1;const O=ge(N)?N:de(e,c,F,N,I,(()=>{R=!0}));if(R)return N;const M=function(e,t,a,n){const{modifiers:l,pluralRules:r,messageResolver:s,fallbackLocale:i,fallbackWarn:c,missingWarn:u,fallbackContext:m}=e,f=n=>{let l=s(a,n);if(null==l&&m){const[,,e]=ve(m,n,t,i,c,u);l=s(e,n)}if(v(l)||fe(l)){let a=!1;const r=de(e,n,t,l,n,(()=>{a=!0}));return a?_e:r}return ge(l)?l:_e},_={locale:t,modifiers:l,pluralRules:r,messages:f};e.processor&&(_.processor=e.processor);n.list&&(_.list=n.list);n.named&&(_.named=n.named);o(n.plural)&&(_.pluralIndex=n.plural);return _}(e,F,y,u),W=function(e,t,a){const n=t(a);return n}(0,O,x(M));return n?n(W,c):W}function ve(e,t,a,n,l,r){const{messages:o,onWarn:s,messageResolver:i,localeFallbacker:c}=e,u=c(e,n,a);let m,f={},_=null;for(let g=0;g<u.length&&(m=u[g],f=o[m]||{},null===(_=i(f,t))&&(_=f[t]),!(v(_)||fe(_)||ge(_)));g++){const a=ue(e,t,m,0,"translate");a!==t&&(_=a)}return[_,m,f]}function de(e,t,a,n,r,o){const{messageCompiler:s,warnHtmlMessage:i}=e;if(ge(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==s){const e=()=>n;return e.locale=a,e.key=t,e}const c=s(n,function(e,t,a,n,r,o){return{locale:t,key:a,warnHtmlMessage:r,onError:e=>{throw o&&o(e),e},onCacheKey:e=>l(t,a,e)}}(0,a,r,0,i,o));return c.locale=a,c.key=t,c.source=n,c}function be(...e){const[t,a,n]=e,l={};if(!(v(t)||o(t)||ge(t)||fe(t)))throw Error(j.INVALID_ARGUMENT);const r=o(t)?String(t):(ge(t),t);return o(a)?l.plural=a:v(a)?l.default=a:L(a)&&!c(a)?l.named=a:g(a)&&(l.list=a),o(n)?l.plural=n:v(n)?l.default=n:L(n)&&u(l,n),[r,l]}function Ee(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[i,m,f,_]=ke(...t);d(f.missingWarn)?f.missingWarn:e.missingWarn;d(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const g=!!f.part,p=V(e,f),b=o(e,l,p);if(!v(i)||""===i)return new Intl.DateTimeFormat(p,_).format(m);let E,h={},k=null;for(let c=0;c<b.length&&(E=b[c],h=a[E]||{},k=h[i],!L(k));c++)ue(e,i,E,0,"datetime format");if(!L(k)||!v(E))return n?K:i;let T=`${E}__${i}`;c(_)||(T=`${T}__${JSON.stringify(_)}`);let F=s.get(T);return F||(F=new Intl.DateTimeFormat(E,u({},k,_)),s.set(T,F)),g?F.formatToParts(m):F.format(m)}const he=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ke(...e){const[t,a,n,l]=e,r={};let i,c={};if(v(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(j.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(a);try{i.toISOString()}catch(u){throw Error(j.INVALID_ISO_DATE_ARGUMENT)}}else if(s(t)){if(isNaN(t.getTime()))throw Error(j.INVALID_DATE_ARGUMENT);i=t}else{if(!o(t))throw Error(j.INVALID_ARGUMENT);i=t}return v(a)?r.key=a:L(a)&&Object.keys(a).forEach((e=>{he.includes(e)?c[e]=a[e]:r[e]=a[e]})),v(n)?r.locale=n:L(n)&&(c=n),L(l)&&(c=l),[r.key||"",i,r,c]}function Le(e,t,a){const n=e;for(const l in a){const e=`${t}__${l}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function Te(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__numberFormatters:s}=e,[i,m,f,_]=ye(...t);d(f.missingWarn)?f.missingWarn:e.missingWarn;d(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const g=!!f.part,p=V(e,f),b=o(e,l,p);if(!v(i)||""===i)return new Intl.NumberFormat(p,_).format(m);let E,h={},k=null;for(let c=0;c<b.length&&(E=b[c],h=a[E]||{},k=h[i],!L(k));c++)ue(e,i,E,0,"number format");if(!L(k)||!v(E))return n?K:i;let T=`${E}__${i}`;c(_)||(T=`${T}__${JSON.stringify(_)}`);let F=s.get(T);return F||(F=new Intl.NumberFormat(E,u({},k,_)),s.set(T,F)),g?F.formatToParts(m):F.format(m)}const Fe=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function ye(...e){const[t,a,n,l]=e,r={};let s={};if(!o(t))throw Error(j.INVALID_ARGUMENT);const i=t;return v(a)?r.key=a:L(a)&&Object.keys(a).forEach((e=>{Fe.includes(e)?s[e]=a[e]:r[e]=a[e]})),v(n)?r.locale=n:L(n)&&(s=n),L(l)&&(s=l),[r.key||"",i,r,s]}function Ne(e,t,a){const n=e;for(const l in a){const e=`${t}__${l}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}const Ie="9.9.1",Re=j.__EXTEND_POINT__,Oe=T(Re),Me={UNEXPECTED_RETURN_TYPE:Re,INVALID_ARGUMENT:Oe(),MUST_BE_CALL_SETUP_TOP:Oe(),NOT_INSTALLED:Oe(),NOT_AVAILABLE_IN_LEGACY_MODE:Oe(),REQUIRED_VALUE:Oe(),INVALID_VALUE:Oe(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Oe(),NOT_INSTALLED_WITH_PROVIDE:Oe(),UNEXPECTED_ERROR:Oe(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Oe(),BRIDGE_SUPPORT_VUE_2_ONLY:Oe(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Oe(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Oe(),__EXTEND_POINT__:Oe()};const We=n("__translateVNode"),we=n("__datetimeParts"),Pe=n("__numberParts"),Ce=n("__setPluralRules"),De=n("__injectWithOption"),Ae=n("__dispose");function Se(e){if(!b(e))return e;for(const t in e)if(_(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let l=e,r=!1;for(let e=0;e<n;e++){if(a[e]in l||(l[a[e]]={}),!b(l[a[e]])){r=!0;break}l=l[a[e]]}r||(l[a[n]]=e[t],delete e[t]),b(l[a[n]])&&Se(l[a[n]])}else b(e[t])&&Se(e[t]);return e}function $e(e,t){const{messages:a,__i18n:n,messageResolver:l,flatJson:r}=t,o=L(a)?a:g(n)?{}:{[e]:{}};if(g(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(o[t]=o[t]||{},N(a,o[t])):N(a,o)}else v(e)&&N(JSON.parse(e),o)})),null==l&&r)for(const s in o)_(o,s)&&Se(o[s]);return o}function Ue(e){return e.type}function xe(e,t,a){let n=b(t.messages)?t.messages:{};"__i18nGlobal"in a&&(n=$e(e.locale.value,{messages:n,__i18n:a.__i18nGlobal}));const l=Object.keys(n);if(l.length&&l.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),b(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(b(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function He(e){return t.createVNode(t.Text,null,e,0)}const je=()=>[],Ve=()=>!1;let Ge=0;function Ye(e){return(a,n,l,r)=>e(n,l,t.getCurrentInstance()||void 0,r)}function Be(e={},n){const{__root:l,__injectWithOption:r}=e,s=void 0===l,c=e.flatJson,m=a?t.ref:t.shallowRef;let f=!d(e.inheritLocale)||e.inheritLocale;const E=m(l&&f?l.locale.value:v(e.locale)?e.locale:Q),h=m(l&&f?l.fallbackLocale.value:v(e.fallbackLocale)||g(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:E.value),k=m($e(E.value,e)),T=m(L(e.datetimeFormats)?e.datetimeFormats:{[E.value]:{}}),F=m(L(e.numberFormats)?e.numberFormats:{[E.value]:{}});let y=l?l.missingWarn:!d(e.missingWarn)&&!i(e.missingWarn)||e.missingWarn,I=l?l.fallbackWarn:!d(e.fallbackWarn)&&!i(e.fallbackWarn)||e.fallbackWarn,R=l?l.fallbackRoot:!d(e.fallbackRoot)||e.fallbackRoot,O=!!e.fallbackFormat,M=p(e.missing)?e.missing:null,W=p(e.missing)?Ye(e.missing):null,w=p(e.postTranslation)?e.postTranslation:null,P=l?l.warnHtmlMessage:!d(e.warnHtmlMessage)||e.warnHtmlMessage,C=!!e.escapeParameter;const D=l?l.modifiers:L(e.modifiers)?e.modifiers:{};let A,S=e.pluralRules||l&&l.pluralRules;A=(()=>{s&&oe(null);const t={version:Ie,locale:E.value,fallbackLocale:h.value,messages:k.value,modifiers:D,pluralRules:S,missing:null===W?void 0:W,missingWarn:y,fallbackWarn:I,fallbackFormat:O,unresolving:!0,postTranslation:null===w?void 0:w,warnHtmlMessage:P,escapeParameter:C,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=T.value,t.numberFormats=F.value,t.__datetimeFormatters=L(A)?A.__datetimeFormatters:void 0,t.__numberFormatters=L(A)?A.__numberFormatters:void 0;const a=ce(t);return s&&oe(a),a})(),me(A,E.value,h.value);const $=t.computed({get:()=>E.value,set:e=>{E.value=e,A.locale=E.value}}),U=t.computed({get:()=>h.value,set:e=>{h.value=e,A.fallbackLocale=h.value,me(A,E.value,e)}}),x=t.computed((()=>k.value)),H=t.computed((()=>T.value)),j=t.computed((()=>F.value));const V=(e,t,a,n,r,i)=>{let c;E.value,h.value,k.value,T.value,F.value;try{0,s||(A.fallbackContext=l?se():void 0),c=e(A)}finally{s||(A.fallbackContext=void 0)}if("translate exists"!==a&&o(c)&&c===K||"translate exists"===a&&!c){const[e,a]=t();return l&&R?n(l):r(e)}if(i(c))return c;throw Error(Me.UNEXPECTED_RETURN_TYPE)};function G(...e){return V((t=>Reflect.apply(pe,null,[t,...e])),(()=>be(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>v(e)))}const Y={normalize:function(e){return e.map((e=>v(e)||o(e)||d(e)?He(String(e)):e))},interpolate:e=>e,type:"vnode"};function B(e){return k.value[e]||{}}Ge++,l&&a&&(t.watch(l.locale,(e=>{f&&(E.value=e,A.locale=e,me(A,E.value,h.value))})),t.watch(l.fallbackLocale,(e=>{f&&(h.value=e,A.fallbackLocale=e,me(A,E.value,h.value))})));const z={id:Ge,locale:$,fallbackLocale:U,get inheritLocale(){return f},set inheritLocale(e){f=e,e&&l&&(E.value=l.locale.value,h.value=l.fallbackLocale.value,me(A,E.value,h.value))},get availableLocales(){return Object.keys(k.value).sort()},messages:x,get modifiers(){return D},get pluralRules(){return S||{}},get isGlobal(){return s},get missingWarn(){return y},set missingWarn(e){y=e,A.missingWarn=y},get fallbackWarn(){return I},set fallbackWarn(e){I=e,A.fallbackWarn=I},get fallbackRoot(){return R},set fallbackRoot(e){R=e},get fallbackFormat(){return O},set fallbackFormat(e){O=e,A.fallbackFormat=O},get warnHtmlMessage(){return P},set warnHtmlMessage(e){P=e,A.warnHtmlMessage=e},get escapeParameter(){return C},set escapeParameter(e){C=e,A.escapeParameter=e},t:G,getLocaleMessage:B,setLocaleMessage:function(e,t){if(c){const a={[e]:t};for(const e in a)_(a,e)&&Se(a[e]);t=a[e]}k.value[e]=t,A.messages=k.value},mergeLocaleMessage:function(e,t){k.value[e]=k.value[e]||{};const a={[e]:t};if(c)for(const n in a)_(a,n)&&Se(a[n]);N(t=a[e],k.value[e]),A.messages=k.value},getPostTranslationHandler:function(){return p(w)?w:null},setPostTranslationHandler:function(e){w=e,A.postTranslation=e},getMissingHandler:function(){return M},setMissingHandler:function(e){null!==e&&(W=Ye(e)),M=e,A.missing=W},[Ce]:function(e){S=e,A.pluralRules=S}};return z.datetimeFormats=H,z.numberFormats=j,z.rt=function(...e){const[t,a,n]=e;if(n&&!b(n))throw Error(Me.INVALID_ARGUMENT);return G(t,a,u({resolvedMessage:!0},n||{}))},z.te=function(e,t){return V((()=>{if(!e)return!1;const a=B(v(t)?t:E.value),n=A.messageResolver(a,e);return fe(n)||ge(n)||v(n)}),(()=>[e]),"translate exists",(a=>Reflect.apply(a.te,a,[e,t])),Ve,(e=>d(e)))},z.tm=function(e){const t=function(e){let t=null;const a=X(A,h.value,E.value);for(let n=0;n<a.length;n++){const l=k.value[a[n]]||{},r=A.messageResolver(l,e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:l&&l.tm(e)||{}},z.d=function(...e){return V((t=>Reflect.apply(Ee,null,[t,...e])),(()=>ke(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>ee),(e=>v(e)))},z.n=function(...e){return V((t=>Reflect.apply(Te,null,[t,...e])),(()=>ye(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>ee),(e=>v(e)))},z.getDateTimeFormat=function(e){return T.value[e]||{}},z.setDateTimeFormat=function(e,t){T.value[e]=t,A.datetimeFormats=T.value,Le(A,e,t)},z.mergeDateTimeFormat=function(e,t){T.value[e]=u(T.value[e]||{},t),A.datetimeFormats=T.value,Le(A,e,t)},z.getNumberFormat=function(e){return F.value[e]||{}},z.setNumberFormat=function(e,t){F.value[e]=t,A.numberFormats=F.value,Ne(A,e,t)},z.mergeNumberFormat=function(e,t){F.value[e]=u(F.value[e]||{},t),A.numberFormats=F.value,Ne(A,e,t)},z[De]=r,z[We]=function(...e){return V((t=>{let a;const n=t;try{n.processor=Y,a=Reflect.apply(pe,null,[n,...e])}finally{n.processor=null}return a}),(()=>be(...e)),"translate",(t=>t[We](...e)),(e=>[He(e)]),(e=>g(e)))},z[we]=function(...e){return V((t=>Reflect.apply(Ee,null,[t,...e])),(()=>ke(...e)),"datetime format",(t=>t[we](...e)),je,(e=>v(e)||g(e)))},z[Pe]=function(...e){return V((t=>Reflect.apply(Te,null,[t,...e])),(()=>ye(...e)),"number format",(t=>t[Pe](...e)),je,(e=>v(e)||g(e)))},z}function Xe(e={},t){{const t=Be(function(e){const t=v(e.locale)?e.locale:Q,a=v(e.fallbackLocale)||g(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=p(e.missing)?e.missing:void 0,l=!d(e.silentTranslationWarn)&&!i(e.silentTranslationWarn)||!e.silentTranslationWarn,r=!d(e.silentFallbackWarn)&&!i(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!d(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,c=L(e.modifiers)?e.modifiers:{},m=e.pluralizationRules,f=p(e.postTranslation)?e.postTranslation:void 0,_=!v(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,b=!!e.escapeParameterHtml,E=!d(e.sync)||e.sync;let h=e.messages;if(L(e.sharedMessages)){const t=e.sharedMessages;h=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return u(n,t[a]),e}),h||{})}const{__i18n:k,__root:T,__injectWithOption:F}=e,y=e.datetimeFormats,N=e.numberFormats;return{locale:t,fallbackLocale:a,messages:h,flatJson:e.flatJson,datetimeFormats:y,numberFormats:N,missing:n,missingWarn:l,fallbackWarn:r,fallbackRoot:o,fallbackFormat:s,modifiers:c,pluralRules:m,postTranslation:f,warnHtmlMessage:_,escapeParameter:b,messageResolver:e.messageResolver,inheritLocale:E,__i18n:k,__root:T,__injectWithOption:F}}(e)),{__extender:a}=e,n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return d(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=d(e)?!e:e},get silentFallbackWarn(){return d(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=d(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,l]=e,r={};let o=null,s=null;if(!v(a))throw Error(Me.INVALID_ARGUMENT);const i=a;return v(n)?r.locale=n:g(n)?o=n:L(n)&&(s=n),g(l)?o=l:L(l)&&(s=l),Reflect.apply(t.t,t,[i,o||s||{},r])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,n,l]=e,r={plural:1};let s=null,i=null;if(!v(a))throw Error(Me.INVALID_ARGUMENT);const c=a;return v(n)?r.locale=n:o(n)?r.plural=n:g(n)?s=n:L(n)&&(i=n),v(l)?r.locale=l:g(l)?s=l:L(l)&&(i=l),Reflect.apply(t.t,t,[c,s||i||{},r])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1};return n.__extender=a,n}}const ze={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Je(e){return t.Fragment}const qe=t.defineComponent({name:"i18n-t",props:u({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>o(e)||!isNaN(e)}},ze),setup(e,a){const{slots:n,attrs:l}=a,r=e.i18n||it({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter((e=>"_"!==e)),s={};e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=v(e.plural)?+e.plural:e.plural);const i=function({slots:e},a){if(1===a.length&&"default"===a[0])return(e.default?e.default():[]).reduce(((e,a)=>[...e,...a.type===t.Fragment?a.children:[a]]),[]);return a.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),{})}(a,o),c=r[We](e.keypath,i,s),m=u({},l),f=v(e.tag)||b(e.tag)?e.tag:Je();return t.h(f,m,c)}}}),Ze=qe;function Ke(e,a,n,l){const{slots:r,attrs:o}=a;return()=>{const a={part:!0};let s={};e.locale&&(a.locale=e.locale),v(e.format)?a.key=e.format:b(e.format)&&(v(e.format.key)&&(a.key=e.format.key),s=Object.keys(e.format).reduce(((t,a)=>n.includes(a)?u({},t,{[a]:e.format[a]}):t),{}));const i=l(e.value,a,s);let c=[a.key];g(i)?c=i.map(((e,t)=>{const a=r[e.type],n=a?a({[e.type]:e.value,index:t,parts:i}):[e.value];var l;return g(l=n)&&!v(l[0])&&(n[0].key=`${e.type}-${t}`),n})):v(i)&&(c=[i]);const m=u({},o),f=v(e.tag)||b(e.tag)?e.tag:Je();return t.h(f,m,c)}}const Qe=t.defineComponent({name:"i18n-n",props:u({value:{type:Number,required:!0},format:{type:[String,Object]}},ze),setup(e,t){const a=e.i18n||it({useScope:"parent",__useComponent:!0});return Ke(e,t,Fe,((...e)=>a[Pe](...e)))}}),et=Qe,tt=t.defineComponent({name:"i18n-d",props:u({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ze),setup(e,t){const a=e.i18n||it({useScope:"parent",__useComponent:!0});return Ke(e,t,he,((...e)=>a[we](...e)))}}),at=tt;function nt(e){const n=t=>{const{instance:a,modifiers:n,value:l}=t;if(!a||!a.$)throw Error(Me.UNEXPECTED_ERROR);const r=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),o=lt(l);return[Reflect.apply(r.t,r,[...rt(o)]),r]};return{created:(l,r)=>{const[o,s]=n(r);a&&e.global===s&&(l.__i18nWatcher=t.watch(s.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),l.__composer=s,l.textContent=o},unmounted:e=>{a&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,n=lt(t);e.textContent=Reflect.apply(a.t,a,[...rt(n)])}},getSSRProps:e=>{const[t]=n(e);return{textContent:t}}}}function lt(e){if(v(e))return{path:e};if(L(e)){if(!("path"in e))throw Error(Me.REQUIRED_VALUE,"path");return e}throw Error(Me.INVALID_VALUE)}function rt(e){const{path:t,locale:a,args:n,choice:l,plural:r}=e,s={},i=n||{};return v(a)&&(s.locale=a),o(l)&&(s.plural=l),o(r)&&(s.plural=r),[t,i,s]}function ot(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Ce](t.pluralizationRules||e.pluralizationRules);const a=$e(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const st=n("global-vue-i18n");function it(e={}){const a=t.getCurrentInstance();if(null==a)throw Error(Me.MUST_BE_CALL_SETUP_TOP);if(!a.isCE&&null!=a.appContext.app&&!a.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Me.NOT_INSTALLED);const n=function(e){{const a=t.inject(e.isCE?st:e.appContext.app.__VUE_I18N_SYMBOL__);if(!a)throw function(e,...t){return I(e,null,void 0)}(e.isCE?Me.NOT_INSTALLED_WITH_PROVIDE:Me.UNEXPECTED_ERROR);return a}}(a),l=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),r=Ue(a),o=function(e,t){return c(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,r);if("legacy"===n.mode&&!e.__useComponent){if(!n.allowComposition)throw Error(Me.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,a,n,l={}){const r="local"===a,o=t.shallowRef(null);if(r&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(Me.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=d(l.inheritLocale)?l.inheritLocale:!v(l.locale),c=t.ref(!r||s?n.locale.value:v(l.locale)?l.locale:Q),u=t.ref(!r||s?n.fallbackLocale.value:v(l.fallbackLocale)||g(l.fallbackLocale)||L(l.fallbackLocale)||!1===l.fallbackLocale?l.fallbackLocale:c.value),m=t.ref($e(c.value,l)),f=t.ref(L(l.datetimeFormats)?l.datetimeFormats:{[c.value]:{}}),_=t.ref(L(l.numberFormats)?l.numberFormats:{[c.value]:{}}),b=r?n.missingWarn:!d(l.missingWarn)&&!i(l.missingWarn)||l.missingWarn,E=r?n.fallbackWarn:!d(l.fallbackWarn)&&!i(l.fallbackWarn)||l.fallbackWarn,h=r?n.fallbackRoot:!d(l.fallbackRoot)||l.fallbackRoot,k=!!l.fallbackFormat,T=p(l.missing)?l.missing:null,F=p(l.postTranslation)?l.postTranslation:null,y=r?n.warnHtmlMessage:!d(l.warnHtmlMessage)||l.warnHtmlMessage,N=!!l.escapeParameter,I=r?n.modifiers:L(l.modifiers)?l.modifiers:{},R=l.pluralRules||r&&n.pluralRules;function O(){return[c.value,u.value,m.value,f.value,_.value]}const M=t.computed({get:()=>o.value?o.value.locale.value:c.value,set:e=>{o.value&&(o.value.locale.value=e),c.value=e}}),W=t.computed({get:()=>o.value?o.value.fallbackLocale.value:u.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),u.value=e}}),w=t.computed((()=>o.value?o.value.messages.value:m.value)),P=t.computed((()=>f.value)),C=t.computed((()=>_.value));function D(){return o.value?o.value.getPostTranslationHandler():F}function A(e){o.value&&o.value.setPostTranslationHandler(e)}function S(){return o.value?o.value.getMissingHandler():T}function $(e){o.value&&o.value.setMissingHandler(e)}function U(e){return O(),e()}function x(...e){return o.value?U((()=>Reflect.apply(o.value.t,null,[...e]))):U((()=>""))}function H(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""}function j(...e){return o.value?U((()=>Reflect.apply(o.value.d,null,[...e]))):U((()=>""))}function V(...e){return o.value?U((()=>Reflect.apply(o.value.n,null,[...e]))):U((()=>""))}function G(e){return o.value?o.value.tm(e):{}}function Y(e,t){return!!o.value&&o.value.te(e,t)}function B(e){return o.value?o.value.getLocaleMessage(e):{}}function X(e,t){o.value&&(o.value.setLocaleMessage(e,t),m.value[e]=t)}function z(e,t){o.value&&o.value.mergeLocaleMessage(e,t)}function J(e){return o.value?o.value.getDateTimeFormat(e):{}}function q(e,t){o.value&&(o.value.setDateTimeFormat(e,t),f.value[e]=t)}function Z(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)}function K(e){return o.value?o.value.getNumberFormat(e):{}}function ee(e,t){o.value&&(o.value.setNumberFormat(e,t),_.value[e]=t)}function te(e,t){o.value&&o.value.mergeNumberFormat(e,t)}const ae={get id(){return o.value?o.value.id:-1},locale:M,fallbackLocale:W,messages:w,datetimeFormats:P,numberFormats:C,get inheritLocale(){return o.value?o.value.inheritLocale:s},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(m.value)},get modifiers(){return o.value?o.value.modifiers:I},get pluralRules(){return o.value?o.value.pluralRules:R},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:b},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:E},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:h},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:k},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:y},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:N},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:x,getPostTranslationHandler:D,setPostTranslationHandler:A,getMissingHandler:S,setMissingHandler:$,rt:H,d:j,n:V,tm:G,te:Y,getLocaleMessage:B,setLocaleMessage:X,mergeLocaleMessage:z,getDateTimeFormat:J,setDateTimeFormat:q,mergeDateTimeFormat:Z,getNumberFormat:K,setNumberFormat:ee,mergeNumberFormat:te};function ne(e){e.locale.value=c.value,e.fallbackLocale.value=u.value,Object.keys(m.value).forEach((t=>{e.mergeLocaleMessage(t,m.value[t])})),Object.keys(f.value).forEach((t=>{e.mergeDateTimeFormat(t,f.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=N,e.fallbackFormat=k,e.fallbackRoot=h,e.fallbackWarn=E,e.missingWarn=b,e.warnHtmlMessage=y}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(Me.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=o.value=e.proxy.$i18n.__composer;"global"===a?(c.value=t.locale.value,u.value=t.fallbackLocale.value,m.value=t.messages.value,f.value=t.datetimeFormats.value,_.value=t.numberFormats.value):r&&ne(t)})),ae}(a,o,l,e)}if("global"===o)return xe(l,e,r),l;if("parent"===o){let t=function(e,t,a=!1){let n=null;const l=t.root;let r=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,a);for(;null!=r;){const t=e;if("composition"===e.mode)n=t.__getInstance(r);else{const e=t.__getInstance(r);null!=e&&(n=e.__composer,a&&n&&!n[De]&&(n=null))}if(null!=n)break;if(l===r)break;r=r.parent}return n}(n,a,e.__useComponent);return null==t&&(t=l),t}const s=n;let m=s.__getInstance(a);if(null==m){const n=u({},e);"__i18n"in r&&(n.__i18n=r.__i18n),l&&(n.__root=l),m=Be(n),s.__composerExtend&&(m[Ae]=s.__composerExtend(m)),function(e,a,n){t.onMounted((()=>{}),a),t.onUnmounted((()=>{const t=n;e.__deleteInstance(a);const l=t[Ae];l&&(l(),delete t[Ae])}),a)}(s,a,m),s.__setInstance(a,m)}return m}const ct=["locale","fallbackLocale","availableLocales"],ut=["t","rt","d","n","tm","te"];return ne=function(e,t){if(!b(e))return null;let a=w.get(t);if(a||(a=function(e){const t=[];let a,n,l,r,o,s,i,c=-1,u=0,m=0;const f=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,l="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=l:n+=l},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=W(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!_()){if(r=M(a),i=R[u],o=i[r]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(l=a,!1===s())))return;if(7===u)return t}}(t),a&&w.set(t,a)),!a)return null;const n=a.length;let l=e,r=0;for(;r<n;){const e=l[a[r]];if(void 0===e)return null;if(p(l))return null;l=e,r++}return l},le=X,e.DatetimeFormat=tt,e.I18nD=at,e.I18nInjectionKey=st,e.I18nN=et,e.I18nT=Ze,e.NumberFormat=Qe,e.Translation=qe,e.VERSION=Ie,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(Me.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},a){const l=!d(e.legacy)||e.legacy,r=!d(e.globalInjection)||e.globalInjection,o=!l||!!e.allowComposition,s=new Map,[i,c]=function(e,a,n){const l=t.effectScope();{const t=a?l.run((()=>Xe(e))):l.run((()=>Be(e)));if(null==t)throw Error(Me.UNEXPECTED_ERROR);return[l,t]}}(e,l),u=n("");{const e={get mode(){return l?"legacy":"composition"},get allowComposition(){return o},async install(a,...n){if(a.__VUE_I18N_SYMBOL__=u,a.provide(a.__VUE_I18N_SYMBOL__,e),L(n[0])){const t=n[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let o=null;!l&&r&&(o=function(e,a){const n=Object.create(null);ct.forEach((e=>{const l=Object.getOwnPropertyDescriptor(a,e);if(!l)throw Error(Me.UNEXPECTED_ERROR);const r=t.isRef(l.value)?{get:()=>l.value.value,set(e){l.value.value=e}}:{get:()=>l.get&&l.get()};Object.defineProperty(n,e,r)})),e.config.globalProperties.$i18n=n,ut.forEach((t=>{const n=Object.getOwnPropertyDescriptor(a,t);if(!n||!n.value)throw Error(Me.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,n)}));const l=()=>{delete e.config.globalProperties.$i18n,ut.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return l}(a,e.global)),function(e,t,...a){const n=L(a[0])?a[0]:{},l=!!n.useI18nComponentName;(!d(n.globalInstall)||n.globalInstall)&&([l?"i18n":qe.name,"I18nT"].forEach((t=>e.component(t,qe))),[Qe.name,"I18nN"].forEach((t=>e.component(t,Qe))),[tt.name,"I18nD"].forEach((t=>e.component(t,tt)))),e.directive("t",nt(t))}(a,e,...n),l&&a.mixin(function(e,a,n){return{beforeCreate(){const l=t.getCurrentInstance();if(!l)throw Error(Me.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const t=r.i18n;if(r.__i18n&&(t.__i18n=r.__i18n),t.__root=a,this===this.$root)this.$i18n=ot(e,t);else{t.__injectWithOption=!0,t.__extender=n.__vueI18nExtend,this.$i18n=Xe(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=ot(e,r);else{this.$i18n=Xe({__i18n:r.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:a});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&xe(a,r,r),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(l,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(Me.UNEXPECTED_ERROR);const a=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,a.__disposer&&(a.__disposer(),delete a.__disposer,delete a.__extender),n.__deleteInstance(e),delete this.$i18n}}}(c,c.__composer,e));const s=a.unmount;a.unmount=()=>{o&&o(),e.dispose(),s()}},get global(){return c},dispose(){i.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=it,e.vTDirective=nt,e}({},Vue);
