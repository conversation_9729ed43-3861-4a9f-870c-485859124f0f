var i=Object.defineProperty;var o=(e,t)=>i(e,"name",{value:t,configurable:!0});const n=o((e,t)=>{const r=e[0]-t[0];if(r===0){const s=e[1]-t[1];return s===0?e[2]>=t[2]:s>0}return r>0},"isVersionGreaterOrEqual"),a=process.versions.node.split(".").map(Number),u=o((e,t=a)=>{for(let r=0;r<e.length;r+=1){const s=e[r];if(r===e.length-1||t[0]===s[0])return n(t,s)}return!1},"isFeatureSupported"),c=[[18,19,0],[20,6,0]],f=[[18,19,0],[20,10,0],[21,0,0]],l=[[21,0,0]],m=[[20,11,0],[21,3,0]];export{f as a,m as e,u as i,c as m,l as t};
