import{i18nAddResources as t,DomEditor as e,t as n,genModalInputElems as r,genModalButtonElems as o,createUploader as i}from"@wangeditor/core";import{Transforms as u,Range as a}from"slate";import{jsx as c,h as s}from"snabbdom";import f,{append as l,on as d,focus as p,attr as v,val as h,html as y,parent as g,hasClass as m,empty as b}from"dom7";import{nanoid as w}from"nanoid";t("en",{videoModule:{delete:"Delete",uploadVideo:"Upload video",insertVideo:"Insert video",videoSrc:"Video source",videoSrcPlaceHolder:"Video file url, or third-party <iframe>",videoPoster:"Video poster",videoPosterPlaceHolder:"Poster image url",ok:"Ok",editSize:"Edit size",width:"Width",height:"Height"}}),t("zh-CN",{videoModule:{delete:"删除视频",uploadVideo:"上传视频",insertVideo:"插入视频",videoSrc:"视频地址",videoSrcPlaceHolder:"视频文件 url 或第三方 <iframe>",videoPoster:"视频封面",videoPosterPlaceHolder:"封面图片 url",ok:"确定",editSize:"修改尺寸",width:"宽度",height:"高度"}});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var S=function(){return S=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},S.apply(this,arguments)};function x(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}c((r=r.apply(t,e||[])).next())}))}function O(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function E(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function j(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}function I(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=E(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,o){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,o,(e=t[n](e)).done,e.value)}))}}}var P="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function T(t){var e={exports:{}};return t(e,e.exports),e.exports}var M,A,k=function(t){return t&&t.Math==Math&&t},R=k("object"==typeof globalThis&&globalThis)||k("object"==typeof window&&window)||k("object"==typeof self&&self)||k("object"==typeof P&&P)||function(){return this}()||Function("return this")(),C=function(t){try{return!!t()}catch(t){return!0}},N=!C((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),L=Function.prototype.call,V=L.bind?L.bind(L):function(){return L.apply(L,arguments)},z={}.propertyIsEnumerable,F=Object.getOwnPropertyDescriptor,_={f:F&&!z.call({1:2},1)?function(t){var e=F(this,t);return!!e&&e.enumerable}:z},D=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},$=Function.prototype,H=$.bind,B=$.call,G=H&&H.bind(B),W=H?function(t){return t&&G(B,t)}:function(t){return t&&function(){return B.apply(t,arguments)}},U=W({}.toString),K=W("".slice),X=function(t){return K(U(t),8,-1)},Y=R.Object,q=W("".split),J=C((function(){return!Y("z").propertyIsEnumerable(0)}))?function(t){return"String"==X(t)?q(t,""):Y(t)}:Y,Q=R.TypeError,Z=function(t){if(null==t)throw Q("Can't call method on "+t);return t},tt=function(t){return J(Z(t))},et=function(t){return"function"==typeof t},nt=function(t){return"object"==typeof t?null!==t:et(t)},rt=function(t){return et(t)?t:void 0},ot=function(t,e){return arguments.length<2?rt(R[t]):R[t]&&R[t][e]},it=W({}.isPrototypeOf),ut=ot("navigator","userAgent")||"",at=R.process,ct=R.Deno,st=at&&at.versions||ct&&ct.version,ft=st&&st.v8;ft&&(A=(M=ft.split("."))[0]>0&&M[0]<4?1:+(M[0]+M[1])),!A&&ut&&(!(M=ut.match(/Edge\/(\d+)/))||M[1]>=74)&&(M=ut.match(/Chrome\/(\d+)/))&&(A=+M[1]);var lt=A,dt=!!Object.getOwnPropertySymbols&&!C((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&lt&&lt<41})),pt=dt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,vt=R.Object,ht=pt?function(t){return"symbol"==typeof t}:function(t){var e=ot("Symbol");return et(e)&&it(e.prototype,vt(t))},yt=R.String,gt=function(t){try{return yt(t)}catch(t){return"Object"}},mt=R.TypeError,bt=function(t){if(et(t))return t;throw mt(gt(t)+" is not a function")},wt=function(t,e){var n=t[e];return null==n?void 0:bt(n)},St=R.TypeError,xt=Object.defineProperty,Ot=function(t,e){try{xt(R,t,{value:e,configurable:!0,writable:!0})}catch(n){R[t]=e}return e},Et=R["__core-js_shared__"]||Ot("__core-js_shared__",{}),jt=T((function(t){(t.exports=function(t,e){return Et[t]||(Et[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),It=R.Object,Pt=function(t){return It(Z(t))},Tt=W({}.hasOwnProperty),Mt=Object.hasOwn||function(t,e){return Tt(Pt(t),e)},At=0,kt=Math.random(),Rt=W(1..toString),Ct=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Rt(++At+kt,36)},Nt=jt("wks"),Lt=R.Symbol,Vt=Lt&&Lt.for,zt=pt?Lt:Lt&&Lt.withoutSetter||Ct,Ft=function(t){if(!Mt(Nt,t)||!dt&&"string"!=typeof Nt[t]){var e="Symbol."+t;dt&&Mt(Lt,t)?Nt[t]=Lt[t]:Nt[t]=pt&&Vt?Vt(e):zt(e)}return Nt[t]},_t=R.TypeError,Dt=Ft("toPrimitive"),$t=function(t,e){if(!nt(t)||ht(t))return t;var n,r=wt(t,Dt);if(r){if(void 0===e&&(e="default"),n=V(r,t,e),!nt(n)||ht(n))return n;throw _t("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&et(n=t.toString)&&!nt(r=V(n,t)))return r;if(et(n=t.valueOf)&&!nt(r=V(n,t)))return r;if("string"!==e&&et(n=t.toString)&&!nt(r=V(n,t)))return r;throw St("Can't convert object to primitive value")}(t,e)},Ht=function(t){var e=$t(t,"string");return ht(e)?e:e+""},Bt=R.document,Gt=nt(Bt)&&nt(Bt.createElement),Wt=function(t){return Gt?Bt.createElement(t):{}},Ut=!N&&!C((function(){return 7!=Object.defineProperty(Wt("div"),"a",{get:function(){return 7}}).a})),Kt=Object.getOwnPropertyDescriptor,Xt={f:N?Kt:function(t,e){if(t=tt(t),e=Ht(e),Ut)try{return Kt(t,e)}catch(t){}if(Mt(t,e))return D(!V(_.f,t,e),t[e])}},Yt=R.String,qt=R.TypeError,Jt=function(t){if(nt(t))return t;throw qt(Yt(t)+" is not an object")},Qt=R.TypeError,Zt=Object.defineProperty,te={f:N?Zt:function(t,e,n){if(Jt(t),e=Ht(e),Jt(n),Ut)try{return Zt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Qt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},ee=N?function(t,e,n){return te.f(t,e,D(1,n))}:function(t,e,n){return t[e]=n,t},ne=W(Function.toString);et(Et.inspectSource)||(Et.inspectSource=function(t){return ne(t)});var re,oe,ie,ue=Et.inspectSource,ae=R.WeakMap,ce=et(ae)&&/native code/.test(ue(ae)),se=jt("keys"),fe=function(t){return se[t]||(se[t]=Ct(t))},le={},de=R.TypeError,pe=R.WeakMap;if(ce||Et.state){var ve=Et.state||(Et.state=new pe),he=W(ve.get),ye=W(ve.has),ge=W(ve.set);re=function(t,e){if(ye(ve,t))throw new de("Object already initialized");return e.facade=t,ge(ve,t,e),e},oe=function(t){return he(ve,t)||{}},ie=function(t){return ye(ve,t)}}else{var me=fe("state");le[me]=!0,re=function(t,e){if(Mt(t,me))throw new de("Object already initialized");return e.facade=t,ee(t,me,e),e},oe=function(t){return Mt(t,me)?t[me]:{}},ie=function(t){return Mt(t,me)}}var be={set:re,get:oe,has:ie,enforce:function(t){return ie(t)?oe(t):re(t,{})},getterFor:function(t){return function(e){var n;if(!nt(e)||(n=oe(e)).type!==t)throw de("Incompatible receiver, "+t+" required");return n}}},we=Function.prototype,Se=N&&Object.getOwnPropertyDescriptor,xe=Mt(we,"name"),Oe={EXISTS:xe,PROPER:xe&&"something"===function(){}.name,CONFIGURABLE:xe&&(!N||N&&Se(we,"name").configurable)},Ee=T((function(t){var e=Oe.CONFIGURABLE,n=be.get,r=be.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var a,c=!!u&&!!u.unsafe,s=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:n;et(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Mt(i,"name")||e&&i.name!==l)&&ee(i,"name",l),(a=r(i)).source||(a.source=o.join("string"==typeof l?l:""))),t!==R?(c?!f&&t[n]&&(s=!0):delete t[n],s?t[n]=i:ee(t,n,i)):s?t[n]=i:Ot(n,i)})(Function.prototype,"toString",(function(){return et(this)&&n(this).source||ue(this)}))})),je=Math.ceil,Ie=Math.floor,Pe=function(t){var e=+t;return e!=e||0===e?0:(e>0?Ie:je)(e)},Te=Math.max,Me=Math.min,Ae=function(t,e){var n=Pe(t);return n<0?Te(n+e,0):Me(n,e)},ke=Math.min,Re=function(t){return t>0?ke(Pe(t),9007199254740991):0},Ce=function(t){return Re(t.length)},Ne=function(t){return function(e,n,r){var o,i=tt(e),u=Ce(i),a=Ae(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},Le={includes:Ne(!0),indexOf:Ne(!1)},Ve=Le.indexOf,ze=W([].push),Fe=function(t,e){var n,r=tt(t),o=0,i=[];for(n in r)!Mt(le,n)&&Mt(r,n)&&ze(i,n);for(;e.length>o;)Mt(r,n=e[o++])&&(~Ve(i,n)||ze(i,n));return i},_e=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],De=_e.concat("length","prototype"),$e={f:Object.getOwnPropertyNames||function(t){return Fe(t,De)}},He={f:Object.getOwnPropertySymbols},Be=W([].concat),Ge=ot("Reflect","ownKeys")||function(t){var e=$e.f(Jt(t)),n=He.f;return n?Be(e,n(t)):e},We=function(t,e){for(var n=Ge(e),r=te.f,o=Xt.f,i=0;i<n.length;i++){var u=n[i];Mt(t,u)||r(t,u,o(e,u))}},Ue=/#|\.prototype\./,Ke=function(t,e){var n=Ye[Xe(t)];return n==Je||n!=qe&&(et(e)?C(e):!!e)},Xe=Ke.normalize=function(t){return String(t).replace(Ue,".").toLowerCase()},Ye=Ke.data={},qe=Ke.NATIVE="N",Je=Ke.POLYFILL="P",Qe=Ke,Ze=Xt.f,tn=function(t,e){var n,r,o,i,u,a=t.target,c=t.global,s=t.stat;if(n=c?R:s?R[a]||Ot(a,{}):(R[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=Ze(n,r))&&u.value:n[r],!Qe(c?r:a+(s?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;We(i,o)}(t.sham||o&&o.sham)&&ee(i,"sham",!0),Ee(n,r,i,t)}},en={};en[Ft("toStringTag")]="z";var nn,rn="[object z]"===String(en),on=Ft("toStringTag"),un=R.Object,an="Arguments"==X(function(){return arguments}()),cn=rn?X:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=un(t),on))?n:an?X(e):"Object"==(r=X(e))&&et(e.callee)?"Arguments":r},sn=R.String,fn=function(t){if("Symbol"===cn(t))throw TypeError("Cannot convert a Symbol value to a string");return sn(t)},ln="\t\n\v\f\r                　\u2028\u2029\ufeff",dn=W("".replace),pn="["+ln+"]",vn=RegExp("^"+pn+pn+"*"),hn=RegExp(pn+pn+"*$"),yn=function(t){return function(e){var n=fn(Z(e));return 1&t&&(n=dn(n,vn,"")),2&t&&(n=dn(n,hn,"")),n}},gn={start:yn(1),end:yn(2),trim:yn(3)},mn=Oe.PROPER,bn=gn.trim;function wn(t){return t.length?t[0].tagName.toLowerCase():""}function Sn(t,e,n){void 0===e&&(e="auto"),void 0===n&&(n="auto");var r=f(t);return r.attr("width",e),r.attr("height",n),r[0].outerHTML}tn({target:"String",proto:!0,forced:(nn="trim",C((function(){return!!ln[nn]()||"​᠎"!=="​᠎"[nn]()||mn&&ln[nn].name!==nn})))},{trim:function(){return bn(this)}}),tn({global:!0},{globalThis:R}),l&&(f.fn.append=l),d&&(f.fn.on=d),p&&(f.fn.focus=p),v&&(f.fn.attr=v),h&&(f.fn.val=h),y&&(f.fn.html=y),g&&(f.fn.parent=g),m&&(f.fn.hasClass=m),b&&(f.fn.empty=b);var xn={type:"video",renderElem:function(t,n,r){var o,i=t,u=i.src,a=void 0===u?"":u,f=i.poster,l=void 0===f?"":f,d=i.width,p=void 0===d?"auto":d,v=i.height,h=void 0===v?"auto":v,y=e.isNodeSelected(r,t);if(0===a.trim().indexOf("<iframe ")){var g=Sn(a,p,h);o=c("div",{className:"w-e-textarea-video-container","data-selected":y?"true":"",innerHTML:g})}else{var m=c("video",{poster:l,controls:!0},c("source",{src:a,type:"video/mp4"}),"Sorry, your browser doesn't support embedded videos.\n 抱歉，浏览器不支持 video 视频");"auto"!==p&&(m.data.width=p),"auto"!==h&&(m.data.height=h),o=c("div",{className:"w-e-textarea-video-container","data-selected":y?"true":""},m)}return s("div",{props:{contentEditable:!1},on:{mousedown:function(t){return t.preventDefault()}}},o)}};var On,En={type:"video",elemToHtml:function(t,e){var n=t,r=n.src,o=void 0===r?"":r,i=n.poster,u=void 0===i?"":i,a=n.width,c=void 0===a?"auto":a,s=n.height,f=void 0===s?"auto":s,l='<div data-w-e-type="video" data-w-e-is-void>\n';return 0===o.trim().indexOf("<iframe ")?l+=Sn(o,c,f):l+='<video poster="'+u+'" controls="true" width="'+c+'" height="'+f+'"><source src="'+o+'" type="video/mp4"/></video>',l+="\n</div>"}},jn=Object.keys||function(t){return Fe(t,_e)},In=N?Object.defineProperties:function(t,e){Jt(t);for(var n,r=tt(e),o=jn(e),i=o.length,u=0;i>u;)te.f(t,n=o[u++],r[n]);return t},Pn=ot("document","documentElement"),Tn=fe("IE_PROTO"),Mn=function(){},An=function(t){return"<script>"+t+"<\/script>"},kn=function(t){t.write(An("")),t.close();var e=t.parentWindow.Object;return t=null,e},Rn=function(){try{On=new ActiveXObject("htmlfile")}catch(t){}var t,e;Rn="undefined"!=typeof document?document.domain&&On?kn(On):((e=Wt("iframe")).style.display="none",Pn.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(An("document.F=Object")),t.close(),t.F):kn(On);for(var n=_e.length;n--;)delete Rn.prototype[_e[n]];return Rn()};le[Tn]=!0;var Cn=Object.create||function(t,e){var n;return null!==t?(Mn.prototype=Jt(t),n=new Mn,Mn.prototype=null,n[Tn]=t):n=Rn(),void 0===e?n:In(n,e)},Nn=Ft("unscopables"),Ln=Array.prototype;null==Ln[Nn]&&te.f(Ln,Nn,{configurable:!0,value:Cn(null)});var Vn=function(t){Ln[Nn][t]=!0},zn=Le.includes;tn({target:"Array",proto:!0},{includes:function(t){return zn(this,t,arguments.length>1?arguments[1]:void 0)}}),Vn("includes");var Fn={selector:"iframe,video,p",preParseHtml:function(t){var e=f(t),n=e;if("p"===wn(e)){var r=e.children();if(1===r.length){var o=r[0],i=o.tagName.toLowerCase();["iframe","video"].includes(i)&&(n=f(o))}}var u=wn(n);if("iframe"!==u&&"video"!==u)return n[0];if("video"===n.parent().attr("data-w-e-type"))return n[0];var a=f('<div data-w-e-type="video" data-w-e-is-void></div>');return a.append(n),a[0]}},_n=W(W.bind),Dn=function(t,e){return bt(t),void 0===e?t:_n?_n(t,e):function(){return t.apply(e,arguments)}},$n=Array.isArray||function(t){return"Array"==X(t)},Hn=function(){},Bn=[],Gn=ot("Reflect","construct"),Wn=/^\s*(?:class|function)\b/,Un=W(Wn.exec),Kn=!Wn.exec(Hn),Xn=function(t){if(!et(t))return!1;try{return Gn(Hn,Bn,t),!0}catch(t){return!1}},Yn=!Gn||C((function(){var t;return Xn(Xn.call)||!Xn(Object)||!Xn((function(){t=!0}))||t}))?function(t){if(!et(t))return!1;switch(cn(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Kn||!!Un(Wn,ue(t))}:Xn,qn=Ft("species"),Jn=R.Array,Qn=function(t,e){return new(function(t){var e;return $n(t)&&(e=t.constructor,(Yn(e)&&(e===Jn||$n(e.prototype))||nt(e)&&null===(e=e[qn]))&&(e=void 0)),void 0===e?Jn:e}(t))(0===e?0:e)},Zn=W([].push),tr=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,s,f,l){for(var d,p,v=Pt(c),h=J(v),y=Dn(s,f),g=Ce(h),m=0,b=l||Qn,w=e?b(c,g):n||u?b(c,0):void 0;g>m;m++)if((a||m in h)&&(p=y(d=h[m],m,v),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:Zn(w,d)}else switch(t){case 4:return!1;case 7:Zn(w,d)}return i?-1:r||o?o:w}},er={forEach:tr(0),map:tr(1),filter:tr(2),some:tr(3),every:tr(4),find:tr(5),findIndex:tr(6),filterReject:tr(7)},nr=er.find,rr=!0;"find"in[]&&Array(1).find((function(){rr=!1})),tn({target:"Array",proto:!0,forced:rr},{find:function(t){return nr(this,t,arguments.length>1?arguments[1]:void 0)}}),Vn("find");var or=rn?{}.toString:function(){return"[object "+cn(this)+"]"};function ir(t,e,n,r){return void 0===e&&(e=""),void 0===n&&(n="auto"),void 0===r&&(r="auto"),{type:"video",src:t,poster:e,width:n,height:r,children:[{text:""}]}}rn||Ee(Object.prototype,"toString",or,{unsafe:!0});var ur,ar,cr={selector:'div[data-w-e-type="video"]',parseElemHtml:function(t,e,n){var r=f(t),o="",i="",u="auto",a="auto",c=r.find("iframe");if(c.length>0)return u=c.attr("width")||"auto",a=c.attr("height")||"auto",ir(o=c[0].outerHTML,i,u,a);var s=r.find("video");return(o=s.attr("src")||"")||s.length>0&&(o=s.find("source").attr("src")||""),u=s.attr("width")||"auto",a=s.attr("height")||"auto",ir(o,i=s.attr("poster")||"",u,a)}},sr=function(){var t=Jt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},fr=R.RegExp,lr=C((function(){var t=fr("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),dr=lr||C((function(){return!fr("a","y").sticky})),pr={BROKEN_CARET:lr||C((function(){var t=fr("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:dr,UNSUPPORTED_Y:lr},vr=R.RegExp,hr=C((function(){var t=vr(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),yr=R.RegExp,gr=C((function(){var t=yr("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),mr=be.get,br=jt("native-string-replace",String.prototype.replace),wr=RegExp.prototype.exec,Sr=wr,xr=W("".charAt),Or=W("".indexOf),Er=W("".replace),jr=W("".slice),Ir=(ar=/b*/g,V(wr,ur=/a/,"a"),V(wr,ar,"a"),0!==ur.lastIndex||0!==ar.lastIndex),Pr=pr.BROKEN_CARET,Tr=void 0!==/()??/.exec("")[1];(Ir||Tr||Pr||hr||gr)&&(Sr=function(t){var e,n,r,o,i,u,a,c=this,s=mr(c),f=fn(t),l=s.raw;if(l)return l.lastIndex=c.lastIndex,e=V(Sr,l,f),c.lastIndex=l.lastIndex,e;var d=s.groups,p=Pr&&c.sticky,v=V(sr,c),h=c.source,y=0,g=f;if(p&&(v=Er(v,"y",""),-1===Or(v,"g")&&(v+="g"),g=jr(f,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==xr(f,c.lastIndex-1))&&(h="(?: "+h+")",g=" "+g,y++),n=new RegExp("^(?:"+h+")",v)),Tr&&(n=new RegExp("^"+h+"$(?!\\s)",v)),Ir&&(r=c.lastIndex),o=V(wr,p?n:c,g),p?o?(o.input=jr(o.input,y),o[0]=jr(o[0],y),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Ir&&o&&(c.lastIndex=c.global?o.index+o[0].length:r),Tr&&o&&o.length>1&&V(br,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&d)for(o.groups=u=Cn(null),i=0;i<d.length;i++)u[(a=d[i])[0]]=o[a[1]];return o});var Mr=Sr;tn({target:"RegExp",proto:!0,forced:/./.exec!==Mr},{exec:Mr});var Ar=Function.prototype,kr=Ar.apply,Rr=Ar.bind,Cr=Ar.call,Nr="object"==typeof Reflect&&Reflect.apply||(Rr?Cr.bind(kr):function(){return Cr.apply(kr,arguments)}),Lr=Ft("species"),Vr=RegExp.prototype,zr=W("".charAt),Fr=W("".charCodeAt),_r=W("".slice),Dr=function(t){return function(e,n){var r,o,i=fn(Z(e)),u=Pe(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=Fr(i,u))<55296||r>56319||u+1===a||(o=Fr(i,u+1))<56320||o>57343?t?zr(i,u):r:t?_r(i,u,u+2):o-56320+(r-55296<<10)+65536}},$r={codeAt:Dr(!1),charAt:Dr(!0)},Hr=$r.charAt,Br=function(t,e,n){return e+(n?Hr(t,e).length:1)},Gr=Math.floor,Wr=W("".charAt),Ur=W("".replace),Kr=W("".slice),Xr=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Yr=/\$([$&'`]|\d{1,2})/g,qr=function(t,e,n,r,o,i){var u=n+t.length,a=r.length,c=Yr;return void 0!==o&&(o=Pt(o),c=Xr),Ur(i,c,(function(i,c){var s;switch(Wr(c,0)){case"$":return"$";case"&":return t;case"`":return Kr(e,0,n);case"'":return Kr(e,u);case"<":s=o[Kr(c,1,-1)];break;default:var f=+c;if(0===f)return i;if(f>a){var l=Gr(f/10);return 0===l?i:l<=a?void 0===r[l-1]?Wr(c,1):r[l-1]+Wr(c,1):i}s=r[f-1]}return void 0===s?"":s}))},Jr=R.TypeError,Qr=function(t,e){var n=t.exec;if(et(n)){var r=V(n,t,e);return null!==r&&Jt(r),r}if("RegExp"===X(t))return V(Mr,t,e);throw Jr("RegExp#exec called on incompatible receiver")},Zr=Ft("replace"),to=Math.max,eo=Math.min,no=W([].concat),ro=W([].push),oo=W("".indexOf),io=W("".slice),uo="$0"==="a".replace(/./,"$0"),ao=!!/./[Zr]&&""===/./[Zr]("a","$0");function co(t){return void 0===t&&(t="r"),t+"-"+w()}!function(t,e,n,r){var o=Ft(t),i=!C((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!C((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[Lr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!u||n){var a=W(/./[o]),c=e(o,""[t],(function(t,e,n,r,o){var u=W(t),c=e.exec;return c===Mr||c===Vr.exec?i&&!o?{done:!0,value:a(e,n,r)}:{done:!0,value:u(n,e,r)}:{done:!1}}));Ee(String.prototype,t,c[0]),Ee(Vr,o,c[1])}r&&ee(Vr[o],"sham",!0)}("replace",(function(t,e,n){var r=ao?"$":"$0";return[function(t,n){var r=Z(this),o=null==t?void 0:wt(t,Zr);return o?V(o,t,r,n):V(e,fn(r),t,n)},function(t,o){var i=Jt(this),u=fn(t);if("string"==typeof o&&-1===oo(o,r)&&-1===oo(o,"$<")){var a=n(e,i,u,o);if(a.done)return a.value}var c=et(o);c||(o=fn(o));var s=i.global;if(s){var f=i.unicode;i.lastIndex=0}for(var l=[];;){var d=Qr(i,u);if(null===d)break;if(ro(l,d),!s)break;""===fn(d[0])&&(i.lastIndex=Br(u,Re(i.lastIndex),f))}for(var p,v="",h=0,y=0;y<l.length;y++){for(var g=fn((d=l[y])[0]),m=to(eo(Pe(d.index),u.length),0),b=[],w=1;w<d.length;w++)ro(b,void 0===(p=d[w])?p:String(p));var S=d.groups;if(c){var x=no([g],b,m,u);void 0!==S&&ro(x,S);var O=fn(Nr(o,void 0,x))}else O=qr(g,u,m,b,S,o);m>=h&&(v+=io(u,h,m)+O,h=m+g.length)}return v+io(u,h)}]}),!!C((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!uo||ao);var so=R.Promise,fo=function(t,e,n){for(var r in e)Ee(t,r,e[r],n);return t},lo=R.String,po=R.TypeError,vo=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=W(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),e=n instanceof Array}catch(t){}return function(n,r){return Jt(n),function(t){if("object"==typeof t||et(t))return t;throw po("Can't set "+lo(t)+" as a prototype")}(r),e?t(n,r):n.__proto__=r,n}}():void 0),ho=te.f,yo=Ft("toStringTag"),go=function(t,e,n){t&&!Mt(t=n?t:t.prototype,yo)&&ho(t,yo,{configurable:!0,value:e})},mo=Ft("species"),bo=R.TypeError,wo=function(t,e){if(it(e,t))return t;throw bo("Incorrect invocation")},So={},xo=Ft("iterator"),Oo=Array.prototype,Eo=Ft("iterator"),jo=function(t){if(null!=t)return wt(t,Eo)||wt(t,"@@iterator")||So[cn(t)]},Io=R.TypeError,Po=function(t,e,n){var r,o;Jt(t);try{if(!(r=wt(t,"return"))){if("throw"===e)throw n;return n}r=V(r,t)}catch(t){o=!0,r=t}if("throw"===e)throw n;if(o)throw r;return Jt(r),n},To=R.TypeError,Mo=function(t,e){this.stopped=t,this.result=e},Ao=Mo.prototype,ko=function(t,e,n){var r,o,i,u,a,c,s,f,l=n&&n.that,d=!(!n||!n.AS_ENTRIES),p=!(!n||!n.IS_ITERATOR),v=!(!n||!n.INTERRUPTED),h=Dn(e,l),y=function(t){return r&&Po(r,"normal",t),new Mo(!0,t)},g=function(t){return d?(Jt(t),v?h(t[0],t[1],y):h(t[0],t[1])):v?h(t,y):h(t)};if(p)r=t;else{if(!(o=jo(t)))throw To(gt(t)+" is not iterable");if(void 0!==(f=o)&&(So.Array===f||Oo[xo]===f)){for(i=0,u=Ce(t);u>i;i++)if((a=g(t[i]))&&it(Ao,a))return a;return new Mo(!1)}r=function(t,e){var n=arguments.length<2?jo(t):e;if(bt(n))return Jt(V(n,t));throw Io(gt(t)+" is not iterable")}(t,o)}for(c=r.next;!(s=V(c,r)).done;){try{a=g(s.value)}catch(t){Po(r,"throw",t)}if("object"==typeof a&&a&&it(Ao,a))return a}return new Mo(!1)},Ro=Ft("iterator"),Co=!1;try{var No=0,Lo={next:function(){return{done:!!No++}},return:function(){Co=!0}};Lo[Ro]=function(){return this},Array.from(Lo,(function(){throw 2}))}catch(t){}var Vo,zo,Fo,_o,Do=function(t,e){if(!e&&!Co)return!1;var n=!1;try{var r={};r[Ro]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(t){}return n},$o=R.TypeError,Ho=Ft("species"),Bo=function(t,e){var n,r=Jt(t).constructor;return void 0===r||null==(n=Jt(r)[Ho])?e:function(t){if(Yn(t))return t;throw $o(gt(t)+" is not a constructor")}(n)},Go=W([].slice),Wo=/(?:ipad|iphone|ipod).*applewebkit/i.test(ut),Uo="process"==X(R.process),Ko=R.setImmediate,Xo=R.clearImmediate,Yo=R.process,qo=R.Dispatch,Jo=R.Function,Qo=R.MessageChannel,Zo=R.String,ti=0,ei={};try{Vo=R.location}catch(t){}var ni=function(t){if(Mt(ei,t)){var e=ei[t];delete ei[t],e()}},ri=function(t){return function(){ni(t)}},oi=function(t){ni(t.data)},ii=function(t){R.postMessage(Zo(t),Vo.protocol+"//"+Vo.host)};Ko&&Xo||(Ko=function(t){var e=Go(arguments,1);return ei[++ti]=function(){Nr(et(t)?t:Jo(t),void 0,e)},zo(ti),ti},Xo=function(t){delete ei[t]},Uo?zo=function(t){Yo.nextTick(ri(t))}:qo&&qo.now?zo=function(t){qo.now(ri(t))}:Qo&&!Wo?(_o=(Fo=new Qo).port2,Fo.port1.onmessage=oi,zo=Dn(_o.postMessage,_o)):R.addEventListener&&et(R.postMessage)&&!R.importScripts&&Vo&&"file:"!==Vo.protocol&&!C(ii)?(zo=ii,R.addEventListener("message",oi,!1)):zo="onreadystatechange"in Wt("script")?function(t){Pn.appendChild(Wt("script")).onreadystatechange=function(){Pn.removeChild(this),ni(t)}}:function(t){setTimeout(ri(t),0)});var ui,ai,ci,si,fi,li,di,pi,vi={set:Ko,clear:Xo},hi=/ipad|iphone|ipod/i.test(ut)&&void 0!==R.Pebble,yi=/web0s(?!.*chrome)/i.test(ut),gi=Xt.f,mi=vi.set,bi=R.MutationObserver||R.WebKitMutationObserver,wi=R.document,Si=R.process,xi=R.Promise,Oi=gi(R,"queueMicrotask"),Ei=Oi&&Oi.value;Ei||(ui=function(){var t,e;for(Uo&&(t=Si.domain)&&t.exit();ai;){e=ai.fn,ai=ai.next;try{e()}catch(t){throw ai?si():ci=void 0,t}}ci=void 0,t&&t.enter()},Wo||Uo||yi||!bi||!wi?!hi&&xi&&xi.resolve?((di=xi.resolve(void 0)).constructor=xi,pi=Dn(di.then,di),si=function(){pi(ui)}):Uo?si=function(){Si.nextTick(ui)}:(mi=Dn(mi,R),si=function(){mi(ui)}):(fi=!0,li=wi.createTextNode(""),new bi(ui).observe(li,{characterData:!0}),si=function(){li.data=fi=!fi}));var ji,Ii,Pi,Ti,Mi=Ei||function(t){var e={fn:t,next:void 0};ci&&(ci.next=e),ai||(ai=e,si()),ci=e},Ai=function(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=r})),this.resolve=bt(e),this.reject=bt(n)},ki={f:function(t){return new Ai(t)}},Ri=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},Ci="object"==typeof window,Ni=vi.set,Li=Ft("species"),Vi="Promise",zi=be.getterFor(Vi),Fi=be.set,_i=be.getterFor(Vi),Di=so&&so.prototype,$i=so,Hi=Di,Bi=R.TypeError,Gi=R.document,Wi=R.process,Ui=ki.f,Ki=Ui,Xi=!!(Gi&&Gi.createEvent&&R.dispatchEvent),Yi=et(R.PromiseRejectionEvent),qi=!1,Ji=Qe(Vi,(function(){var t=ue($i),e=t!==String($i);if(!e&&66===lt)return!0;if(lt>=51&&/native code/.test(t))return!1;var n=new $i((function(t){t(1)})),r=function(t){t((function(){}),(function(){}))};return(n.constructor={})[Li]=r,!(qi=n.then((function(){}))instanceof r)||!e&&Ci&&!Yi})),Qi=Ji||!Do((function(t){$i.all(t).catch((function(){}))})),Zi=function(t){var e;return!(!nt(t)||!et(e=t.then))&&e},tu=function(t,e){if(!t.notified){t.notified=!0;var n=t.reactions;Mi((function(){for(var r=t.value,o=1==t.state,i=0;n.length>i;){var u,a,c,s=n[i++],f=o?s.ok:s.fail,l=s.resolve,d=s.reject,p=s.domain;try{f?(o||(2===t.rejection&&ou(t),t.rejection=1),!0===f?u=r:(p&&p.enter(),u=f(r),p&&(p.exit(),c=!0)),u===s.promise?d(Bi("Promise-chain cycle")):(a=Zi(u))?V(a,u,l,d):l(u)):d(r)}catch(t){p&&!c&&p.exit(),d(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&nu(t)}))}},eu=function(t,e,n){var r,o;Xi?((r=Gi.createEvent("Event")).promise=e,r.reason=n,r.initEvent(t,!1,!0),R.dispatchEvent(r)):r={promise:e,reason:n},!Yi&&(o=R["on"+t])?o(r):"unhandledrejection"===t&&function(t,e){var n=R.console;n&&n.error&&(1==arguments.length?n.error(t):n.error(t,e))}("Unhandled promise rejection",n)},nu=function(t){V(Ni,R,(function(){var e,n=t.facade,r=t.value;if(ru(t)&&(e=Ri((function(){Uo?Wi.emit("unhandledRejection",r,n):eu("unhandledrejection",n,r)})),t.rejection=Uo||ru(t)?2:1,e.error))throw e.value}))},ru=function(t){return 1!==t.rejection&&!t.parent},ou=function(t){V(Ni,R,(function(){var e=t.facade;Uo?Wi.emit("rejectionHandled",e):eu("rejectionhandled",e,t.value)}))},iu=function(t,e,n){return function(r){t(e,r,n)}},uu=function(t,e,n){t.done||(t.done=!0,n&&(t=n),t.value=e,t.state=2,tu(t,!0))},au=function(t,e,n){if(!t.done){t.done=!0,n&&(t=n);try{if(t.facade===e)throw Bi("Promise can't be resolved itself");var r=Zi(e);r?Mi((function(){var n={done:!1};try{V(r,e,iu(au,n,t),iu(uu,n,t))}catch(e){uu(n,e,t)}})):(t.value=e,t.state=1,tu(t,!1))}catch(e){uu({done:!1},e,t)}}};if(Ji&&(Hi=($i=function(t){wo(this,Hi),bt(t),V(ji,this);var e=zi(this);try{t(iu(au,e),iu(uu,e))}catch(t){uu(e,t)}}).prototype,(ji=function(t){Fi(this,{type:Vi,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=fo(Hi,{then:function(t,e){var n=_i(this),r=n.reactions,o=Ui(Bo(this,$i));return o.ok=!et(t)||t,o.fail=et(e)&&e,o.domain=Uo?Wi.domain:void 0,n.parent=!0,r[r.length]=o,0!=n.state&&tu(n,!1),o.promise},catch:function(t){return this.then(void 0,t)}}),Ii=function(){var t=new ji,e=zi(t);this.promise=t,this.resolve=iu(au,e),this.reject=iu(uu,e)},ki.f=Ui=function(t){return t===$i||t===Pi?new Ii(t):Ki(t)},et(so)&&Di!==Object.prototype)){Ti=Di.then,qi||(Ee(Di,"then",(function(t,e){var n=this;return new $i((function(t,e){V(Ti,n,t,e)})).then(t,e)}),{unsafe:!0}),Ee(Di,"catch",Hi.catch,{unsafe:!0}));try{delete Di.constructor}catch(t){}vo&&vo(Di,Hi)}function cu(t,e,n){return void 0===n&&(n=""),x(this,void 0,void 0,(function(){var r,o,i,a,c,s,f;return O(this,(function(l){switch(l.label){case 0:return e?(t.restoreSelection(),r=t.getMenuConfig("insertVideo"),o=r.onInsertedVideo,i=r.checkVideo,a=r.parseVideoSrc,[4,i(e,n)]):[2];case 1:return"string"==typeof(c=l.sent())?(t.alert(c,"error"),[2]):null==c?[2]:[4,a(e)];case 2:return 0!==(s=l.sent()).trim().indexOf("<iframe ")&&(s=s.replace(/</g,"&lt;").replace(/>/g,"&gt;")),f={type:"video",src:s,poster:n,children:[{text:""}]},Promise.resolve().then((function(){u.insertNodes(t,f)})),o(f),[2]}}))}))}function su(){return co("w-e-insert-video")}tn({global:!0,wrap:!0,forced:Ji},{Promise:$i}),go($i,Vi,!1),function(t){var e=ot(t),n=te.f;N&&e&&!e[mo]&&n(e,mo,{configurable:!0,get:function(){return this}})}(Vi),Pi=ot(Vi),tn({target:Vi,stat:!0,forced:Ji},{reject:function(t){var e=Ui(this);return V(e.reject,void 0,t),e.promise}}),tn({target:Vi,stat:!0,forced:Ji},{resolve:function(t){return function(t,e){if(Jt(t),nt(e)&&e.constructor===t)return e;var n=ki.f(t);return(0,n.resolve)(e),n.promise}(this,t)}}),tn({target:Vi,stat:!0,forced:Qi},{all:function(t){var e=this,n=Ui(e),r=n.resolve,o=n.reject,i=Ri((function(){var n=bt(e.resolve),i=[],u=0,a=1;ko(t,(function(t){var c=u++,s=!1;a++,V(n,e,t).then((function(t){s||(s=!0,i[c]=t,--a||r(i))}),o)})),--a||r(i)}));return i.error&&o(i.value),n.promise},race:function(t){var e=this,n=Ui(e),r=n.reject,o=Ri((function(){var o=bt(e.resolve);ko(t,(function(t){V(o,e,t).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}});var fu=function(){function t(){this.title=n("videoModule.insertVideo"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z"></path></svg>',this.tag="button",this.showModal=!0,this.modalWidth=320,this.$content=null,this.srcInputId=su(),this.posterInputId=su(),this.buttonId=su()}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!a.isCollapsed(n)||!!e.getSelectedElems(t).some((function(n){var r=e.getNodeType(n);return"pre"===r||("list-item"===r||!!t.isVoid(n))})))},t.prototype.getModalPositionNode=function(t){return null},t.prototype.getModalContentElem=function(t){var e=this,i=this,u=i.srcInputId,a=i.posterInputId,c=i.buttonId,s=j(r(n("videoModule.videoSrc"),u,n("videoModule.videoSrcPlaceHolder")),2),l=s[0],d=s[1],p=j(r(n("videoModule.videoPoster"),a,n("videoModule.videoPosterPlaceHolder")),2),v=p[0],h=p[1],y=f(d),g=f(h),m=j(o(c,n("videoModule.ok")),1)[0];if(null==this.$content){var b=f("<div></div>");b.on("click","#"+c,(function(n){return x(e,void 0,void 0,(function(){var e,r;return O(this,(function(o){switch(o.label){case 0:return n.preventDefault(),e=b.find("#"+u).val().trim(),r=b.find("#"+a).val().trim(),[4,cu(t,e,r)];case 1:return o.sent(),t.hidePanelOrModal(),[2]}}))}))})),this.$content=b}var w=this.$content;return w.empty(),w.append(l),w.append(v),w.append(m),y.val(""),g.val(""),setTimeout((function(){y.focus()})),w[0]},t}(),lu=W([].join),du=J!=Object,pu=function(t,e){var n=[][t];return!!n&&C((function(){n.call(null,e||function(){throw 1},1)}))}("join",",");tn({target:"Array",proto:!0,forced:du||!pu},{join:function(t){return lu(tt(this),void 0===t?",":t)}});var vu,hu,yu,gu=!C((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),mu=fe("IE_PROTO"),bu=R.Object,wu=bu.prototype,Su=gu?bu.getPrototypeOf:function(t){var e=Pt(t);if(Mt(e,mu))return e[mu];var n=e.constructor;return et(n)&&e instanceof n?n.prototype:e instanceof bu?wu:null},xu=Ft("iterator"),Ou=!1;[].keys&&("next"in(yu=[].keys())?(hu=Su(Su(yu)))!==Object.prototype&&(vu=hu):Ou=!0);var Eu=null==vu||C((function(){var t={};return vu[xu].call(t)!==t}));Eu&&(vu={}),et(vu[xu])||Ee(vu,xu,(function(){return this}));var ju={IteratorPrototype:vu,BUGGY_SAFARI_ITERATORS:Ou},Iu=ju.IteratorPrototype,Pu=function(){return this},Tu=Oe.PROPER,Mu=Oe.CONFIGURABLE,Au=ju.IteratorPrototype,ku=ju.BUGGY_SAFARI_ITERATORS,Ru=Ft("iterator"),Cu=function(){return this},Nu=function(t,e,n,r,o,i,u){!function(t,e,n,r){var o=e+" Iterator";t.prototype=Cn(Iu,{next:D(+!r,n)}),go(t,o,!1),So[o]=Pu}(n,e,r);var a,c,s,f=function(t){if(t===o&&h)return h;if(!ku&&t in p)return p[t];switch(t){case"keys":case"values":case"entries":return function(){return new n(this,t)}}return function(){return new n(this)}},l=e+" Iterator",d=!1,p=t.prototype,v=p[Ru]||p["@@iterator"]||o&&p[o],h=!ku&&v||f(o),y="Array"==e&&p.entries||v;if(y&&(a=Su(y.call(new t)))!==Object.prototype&&a.next&&(Su(a)!==Au&&(vo?vo(a,Au):et(a[Ru])||Ee(a,Ru,Cu)),go(a,l,!0)),Tu&&"values"==o&&v&&"values"!==v.name&&(Mu?ee(p,"name","values"):(d=!0,h=function(){return V(v,this)})),o)if(c={values:f("values"),keys:i?h:f("keys"),entries:f("entries")},u)for(s in c)(ku||d||!(s in p))&&Ee(p,s,c[s]);else tn({target:e,proto:!0,forced:ku||d},c);return p[Ru]!==h&&Ee(p,Ru,h,{name:o}),So[e]=h,c},Lu=be.set,Vu=be.getterFor("Array Iterator"),zu=Nu(Array,"Array",(function(t,e){Lu(this,{type:"Array Iterator",target:tt(t),index:0,kind:e})}),(function(){var t=Vu(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values");So.Arguments=So.Array,Vn("keys"),Vn("values"),Vn("entries");var Fu=$r.charAt,_u=be.set,Du=be.getterFor("String Iterator");Nu(String,"String",(function(t){_u(this,{type:"String Iterator",string:fn(t),index:0})}),(function(){var t,e=Du(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=Fu(n,r),e.index+=t.length,{value:t,done:!1})}));var $u=function(t,e,n){var r=Ht(e);r in t?te.f(t,r,D(0,n)):t[r]=n},Hu=R.Array,Bu=Math.max,Gu=$e.f,Wu="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Uu=function(t){try{return Gu(t)}catch(t){return function(t,e,n){for(var r=Ce(t),o=Ae(e,r),i=Ae(void 0===n?r:n,r),u=Hu(Bu(i-o,0)),a=0;o<i;o++,a++)$u(u,a,t[o]);return u.length=a,u}(Wu)}},Ku={f:function(t){return Wu&&"Window"==X(t)?Uu(t):Gu(tt(t))}},Xu=C((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),Yu=Object.isExtensible,qu=C((function(){Yu(1)}))||Xu?function(t){return!!nt(t)&&((!Xu||"ArrayBuffer"!=X(t))&&(!Yu||Yu(t)))}:Yu,Ju=!C((function(){return Object.isExtensible(Object.preventExtensions({}))})),Qu=T((function(t){var e=te.f,n=!1,r=Ct("meta"),o=0,i=function(t){e(t,r,{value:{objectID:"O"+o++,weakData:{}}})},u=t.exports={enable:function(){u.enable=function(){},n=!0;var t=$e.f,e=W([].splice),o={};o[r]=1,t(o).length&&($e.f=function(n){for(var o=t(n),i=0,u=o.length;i<u;i++)if(o[i]===r){e(o,i,1);break}return o},tn({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Ku.f}))},fastKey:function(t,e){if(!nt(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!Mt(t,r)){if(!qu(t))return"F";if(!e)return"E";i(t)}return t[r].objectID},getWeakData:function(t,e){if(!Mt(t,r)){if(!qu(t))return!0;if(!e)return!1;i(t)}return t[r].weakData},onFreeze:function(t){return Ju&&n&&qu(t)&&!Mt(t,r)&&i(t),t}};le[r]=!0})),Zu=Qu.getWeakData,ta=be.set,ea=be.getterFor,na=er.find,ra=er.findIndex,oa=W([].splice),ia=0,ua=function(t){return t.frozen||(t.frozen=new aa)},aa=function(){this.entries=[]},ca=function(t,e){return na(t.entries,(function(t){return t[0]===e}))};aa.prototype={get:function(t){var e=ca(this,t);if(e)return e[1]},has:function(t){return!!ca(this,t)},set:function(t,e){var n=ca(this,t);n?n[1]=e:this.entries.push([t,e])},delete:function(t){var e=ra(this.entries,(function(e){return e[0]===t}));return~e&&oa(this.entries,e,1),!!~e}};var sa,fa={getConstructor:function(t,e,n,r){var o=t((function(t,o){wo(t,i),ta(t,{type:e,id:ia++,frozen:void 0}),null!=o&&ko(o,t[r],{that:t,AS_ENTRIES:n})})),i=o.prototype,u=ea(e),a=function(t,e,n){var r=u(t),o=Zu(Jt(e),!0);return!0===o?ua(r).set(e,n):o[r.id]=n,t};return fo(i,{delete:function(t){var e=u(this);if(!nt(t))return!1;var n=Zu(t);return!0===n?ua(e).delete(t):n&&Mt(n,e.id)&&delete n[e.id]},has:function(t){var e=u(this);if(!nt(t))return!1;var n=Zu(t);return!0===n?ua(e).has(t):n&&Mt(n,e.id)}}),fo(i,n?{get:function(t){var e=u(this);if(nt(t)){var n=Zu(t);return!0===n?ua(e).get(t):n?n[e.id]:void 0}},set:function(t,e){return a(this,t,e)}}:{add:function(t){return a(this,t,!0)}}),o}},la=be.enforce,da=!R.ActiveXObject&&"ActiveXObject"in R,pa=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},va=function(t,e,n){var r=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),i=r?"set":"add",u=R[t],a=u&&u.prototype,c=u,s={},f=function(t){var e=W(a[t]);Ee(a,t,"add"==t?function(t){return e(this,0===t?0:t),this}:"delete"==t?function(t){return!(o&&!nt(t))&&e(this,0===t?0:t)}:"get"==t?function(t){return o&&!nt(t)?void 0:e(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!nt(t))&&e(this,0===t?0:t)}:function(t,n){return e(this,0===t?0:t,n),this})};if(Qe(t,!et(u)||!(o||a.forEach&&!C((function(){(new u).entries().next()})))))c=n.getConstructor(e,t,r,i),Qu.enable();else if(Qe(t,!0)){var l=new c,d=l[i](o?{}:-0,1)!=l,p=C((function(){l.has(1)})),v=Do((function(t){new u(t)})),h=!o&&C((function(){for(var t=new u,e=5;e--;)t[i](e,e);return!t.has(-0)}));v||((c=e((function(t,e){wo(t,a);var n=function(t,e,n){var r,o;return vo&&et(r=e.constructor)&&r!==n&&nt(o=r.prototype)&&o!==n.prototype&&vo(t,o),t}(new u,t,c);return null!=e&&ko(e,n[i],{that:n,AS_ENTRIES:r}),n}))).prototype=a,a.constructor=c),(p||h)&&(f("delete"),f("has"),r&&f("get")),(h||d)&&f(i),o&&a.clear&&delete a.clear}return s[t]=c,tn({global:!0,forced:c!=u},s),go(c,t),o||n.setStrong(c,t,r),c}("WeakMap",pa,fa);if(ce&&da){sa=fa.getConstructor(pa,"WeakMap",!0),Qu.enable();var ha=va.prototype,ya=W(ha.delete),ga=W(ha.has),ma=W(ha.get),ba=W(ha.set);fo(ha,{delete:function(t){if(nt(t)&&!qu(t)){var e=la(this);return e.frozen||(e.frozen=new sa),ya(this,t)||e.frozen.delete(t)}return ya(this,t)},has:function(t){if(nt(t)&&!qu(t)){var e=la(this);return e.frozen||(e.frozen=new sa),ga(this,t)||e.frozen.has(t)}return ga(this,t)},get:function(t){if(nt(t)&&!qu(t)){var e=la(this);return e.frozen||(e.frozen=new sa),ga(this,t)?ma(this,t):e.frozen.get(t)}return ma(this,t)},set:function(t,e){if(nt(t)&&!qu(t)){var n=la(this);n.frozen||(n.frozen=new sa),ga(this,t)?ba(this,t,e):n.frozen.set(t,e)}else ba(this,t,e);return this}})}var wa={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Sa=Wt("span").classList,xa=Sa&&Sa.constructor&&Sa.constructor.prototype,Oa=xa===Object.prototype?void 0:xa,Ea=Ft("iterator"),ja=Ft("toStringTag"),Ia=zu.values,Pa=function(t,e){if(t){if(t[Ea]!==Ia)try{ee(t,Ea,Ia)}catch(e){t[Ea]=Ia}if(t[ja]||ee(t,ja,e),wa[e])for(var n in zu)if(t[n]!==zu[n])try{ee(t,n,zu[n])}catch(e){t[n]=zu[n]}}};for(var Ta in wa)Pa(R[Ta]&&R[Ta].prototype,Ta);Pa(Oa,"DOMTokenList");var Ma=Oe.EXISTS,Aa=te.f,ka=Function.prototype,Ra=W(ka.toString),Ca=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,Na=W(Ca.exec);N&&!Ma&&Aa(ka,"name",{configurable:!0,get:function(){try{return Na(Ca,Ra(this))[1]}catch(t){return""}}});var La=Ft("species"),Va=function(t){return lt>=51||!C((function(){var e=[];return(e.constructor={})[La]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}("slice"),za=Ft("species"),Fa=R.Array,_a=Math.max;function Da(t){return t.getMenuConfig("uploadVideo")}tn({target:"Array",proto:!0,forced:!Va},{slice:function(t,e){var n,r,o,i=tt(this),u=Ce(i),a=Ae(t,u),c=Ae(void 0===e?u:e,u);if($n(i)&&(n=i.constructor,(Yn(n)&&(n===Fa||$n(n.prototype))||nt(n)&&null===(n=n[za]))&&(n=void 0),n===Fa||void 0===n))return Go(i,a,c);for(r=new(void 0===n?Fa:n)(_a(c-a,0)),o=0;a<c;a++,o++)a in i&&$u(r,o,i[a]);return r.length=o,r}});var $a=new WeakMap;function Ha(t,e){return x(this,void 0,void 0,(function(){var n,r,o,u;return O(this,(function(a){switch(a.label){case 0:return n=function(t){var e=$a.get(t);if(null!=e)return e;var n=Da(t),r=n.onSuccess,o=n.onProgress,u=n.onFailed,a=n.customInsert,c=n.onError;return e=i(S(S({},n),{onProgress:function(e){t.showProgressBar(e),o&&o(e)},onSuccess:function(e,n){if(a)return a(n,(function(e,n){return cu(t,e,n)})),void r(e,n);var o=n.errno,i=void 0===o?1:o,c=n.data,s=void 0===c?{}:c;if(0===i){var f=s.url,l=void 0===f?"":f,d=s.poster;cu(t,l,void 0===d?"":d),r(e,n)}else u(e,n)},onError:function(t,e,n){c(t,e,n)}})),$a.set(t,e),e}(t),r=e.name,o=e.type,u=e.size,n.addFile({name:r,type:o,size:u,data:e}),[4,n.upload()];case 1:return a.sent(),[2]}}))}))}var Ba=function(){function t(){this.title=n("videoModule.uploadVideo"),this.iconSvg='<svg viewBox="0 0 1056 1024"><path d="M805.902261 521.819882a251.441452 251.441452 0 0 0-251.011972 246.600033 251.051015 251.051015 0 1 0 502.023944 8.823877 253.237463 253.237463 0 0 0-251.011972-255.42391z m59.463561 240.001647v129.898403h-116.701631v-129.898403h-44.041298l101.279368-103.504859 101.279368 103.504859z" p-id="6802"></path><path d="M788.254507 0.000781H99.094092A98.663439 98.663439 0 0 0 0.001171 99.093701v590.067495a98.663439 98.663439 0 0 0 99.092921 99.092921h411.7549a266.434235 266.434235 0 0 1-2.186448-41.815807 275.843767 275.843767 0 0 1 275.180024-270.729042 270.650955 270.650955 0 0 1 103.504859 19.834201V99.093701A101.51363 101.51363 0 0 0 788.254507 0.000781zM295.054441 640.747004V147.507894l394.146189 246.600033z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){var n=this.getMenuConfig(t),r=n.allowedFileTypes,o=void 0===r?[]:r,i=n.customBrowseAndUpload;if(i)i((function(e,n){return cu(t,e,n)}));else{var u="";o.length>0&&(u='accept="'+o.join(", ")+'"');var a=f("body"),c=f('<input type="file" '+u+" multiple/>");c.hide(),a.append(c),c.click(),c.on("change",(function(){var e=c[0].files;!function(t,e){var n,r;x(this,void 0,void 0,(function(){var o,i,u,a,c,s;return O(this,(function(f){switch(f.label){case 0:if(null==e)return[2];o=Array.prototype.slice.call(e),i=Da(t).customUpload,f.label=1;case 1:f.trys.push([1,9,10,15]),u=I(o),f.label=2;case 2:return[4,u.next()];case 3:return(a=f.sent()).done?[3,8]:(c=a.value,i?[4,i(c,(function(e,n){return cu(t,e,n)}))]:[3,5]);case 4:return f.sent(),[3,7];case 5:return[4,Ha(t,c)];case 6:f.sent(),f.label=7;case 7:return[3,2];case 8:return[3,15];case 9:return s=f.sent(),n={error:s},[3,15];case 10:return f.trys.push([10,,13,14]),a&&!a.done&&(r=u.return)?[4,r.call(u)]:[3,12];case 11:f.sent(),f.label=12;case 12:return[3,14];case 13:if(n)throw n.error;return[7];case 14:return[7];case 15:return[2]}}))}))}(t,e)}))}},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!a.isCollapsed(n)||!!e.getSelectedElems(t).some((function(n){var r=e.getNodeType(n);return"pre"===r||("list-item"===r||!!t.isVoid(n))})))},t.prototype.getMenuConfig=function(t){return t.getMenuConfig("uploadVideo")},t}(),Ga=Oe.PROPER,Wa=RegExp.prototype,Ua=Wa.toString,Ka=W(sr),Xa=C((function(){return"/a/b"!=Ua.call({source:"a",flags:"b"})})),Ya=Ga&&"toString"!=Ua.name;function qa(){return co("w-e-insert-video")}(Xa||Ya)&&Ee(RegExp.prototype,"toString",(function(){var t=Jt(this),e=fn(t.source),n=t.flags;return"/"+e+"/"+fn(void 0===n&&it(Wa,t)&&!("flags"in Wa)?Ka(t):n)}),{unsafe:!0});var Ja=function(){function t(){this.title=n("videoModule.editSize"),this.tag="button",this.showModal=!0,this.modalWidth=320,this.$content=null,this.widthInputId=qa(),this.heightInputId=qa(),this.buttonId=qa()}return t.prototype.getSelectedVideoNode=function(t){return e.getSelectedNodeByType(t,"video")},t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){return null==t.selection||null==this.getSelectedVideoNode(t)},t.prototype.getModalPositionNode=function(t){return this.getSelectedVideoNode(t)},t.prototype.getModalContentElem=function(t){var i=this,a=i.widthInputId,c=i.heightInputId,s=i.buttonId,l=j(r(n("videoModule.width"),a,"auto"),2),d=l[0],p=l[1],v=f(p),h=j(r(n("videoModule.height"),c,"auto"),2),y=h[0],g=h[1],m=f(g),b=j(o(s,n("videoModule.ok")),1)[0];if(null==this.$content){var w=f("<div></div>");w.on("click","#"+s,(function(n){n.preventDefault();var r=w.find("#"+a).val().trim(),o=w.find("#"+c).val().trim(),i=parseInt(r),s=parseInt(o),f=i?i.toString():"auto",l=s?s.toString():"auto";t.restoreSelection(),u.setNodes(t,{width:f,height:l},{match:function(t){return e.checkNodeType(t,"video")}}),t.hidePanelOrModal()})),this.$content=w}var S=this.$content;S.empty(),S.append(d),S.append(y),S.append(b);var x=this.getSelectedVideoNode(t);if(null==x)return S[0];var O=x.width,E=void 0===O?"auto":O,I=x.height,P=void 0===I?"auto":I;return v.val(E),m.val(P),setTimeout((function(){v.focus()})),S[0]},t}();var Qa={renderElems:[xn],elemsToHtml:[En],preParseHtml:[Fn],parseElemsHtml:[cr],menus:[{key:"insertVideo",factory:function(){return new fu},config:{onInsertedVideo:function(t){},checkVideo:function(t,e){return!0},parseVideoSrc:function(t){return t}}},{key:"uploadVideo",factory:function(){return new Ba},config:{server:"",fieldName:"wangeditor-uploaded-video",maxFileSize:10485760,maxNumberOfFiles:5,allowedFileTypes:["video/*"],meta:{},metaWithUrl:!1,withCredentials:!1,timeout:3e4,onBeforeUpload:function(t){return t},onProgress:function(t){},onSuccess:function(t,e){},onFailed:function(t,e){console.error("'"+t.name+"' upload failed",e)},onError:function(t,e,n){console.error("'"+t.name+" upload error",e,n)}}},{key:"editVideoSize",factory:function(){return new Ja}}],editorPlugin:function(t){var n=t.isVoid,r=t.normalizeNode,o=t;return o.isVoid=function(t){return"video"===t.type||n(t)},o.normalizeNode=function(t){var n=j(t,2),i=n[0],a=n[1];"video"===e.getNodeType(i)&&(e.isLastNode(o,i)&&u.insertNodes(o,e.genEmptyParagraph(),{at:[a[0]+1]}));return r([i,a])},o}};export{Qa as default};
//# sourceMappingURL=index.esm.js.map
