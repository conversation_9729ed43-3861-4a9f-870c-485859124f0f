{"version": 3, "file": "index.js", "sources": ["../src/locale/index.ts", "../src/locale/en.ts", "../src/locale/zh-CN.ts", "../../../node_modules/core-js/internals/global.js", "../../../node_modules/core-js/internals/engine-v8-version.js", "../../../node_modules/core-js/internals/fails.js", "../../../node_modules/core-js/internals/descriptors.js", "../../../node_modules/core-js/internals/function-call.js", "../../../node_modules/core-js/internals/object-property-is-enumerable.js", "../../../node_modules/core-js/internals/create-property-descriptor.js", "../../../node_modules/core-js/internals/function-uncurry-this.js", "../../../node_modules/core-js/internals/classof-raw.js", "../../../node_modules/core-js/internals/indexed-object.js", "../../../node_modules/core-js/internals/require-object-coercible.js", "../../../node_modules/core-js/internals/to-indexed-object.js", "../../../node_modules/core-js/internals/is-callable.js", "../../../node_modules/core-js/internals/is-object.js", "../../../node_modules/core-js/internals/get-built-in.js", "../../../node_modules/core-js/internals/object-is-prototype-of.js", "../../../node_modules/core-js/internals/engine-user-agent.js", "../../../node_modules/core-js/internals/native-symbol.js", "../../../node_modules/core-js/internals/use-symbol-as-uid.js", "../../../node_modules/core-js/internals/is-symbol.js", "../../../node_modules/core-js/internals/try-to-string.js", "../../../node_modules/core-js/internals/a-callable.js", "../../../node_modules/core-js/internals/get-method.js", "../../../node_modules/core-js/internals/ordinary-to-primitive.js", "../../../node_modules/core-js/internals/set-global.js", "../../../node_modules/core-js/internals/shared-store.js", "../../../node_modules/core-js/internals/shared.js", "../../../node_modules/core-js/internals/to-object.js", "../../../node_modules/core-js/internals/has-own-property.js", "../../../node_modules/core-js/internals/uid.js", "../../../node_modules/core-js/internals/well-known-symbol.js", "../../../node_modules/core-js/internals/to-primitive.js", "../../../node_modules/core-js/internals/to-property-key.js", "../../../node_modules/core-js/internals/document-create-element.js", "../../../node_modules/core-js/internals/ie8-dom-define.js", "../../../node_modules/core-js/internals/object-get-own-property-descriptor.js", "../../../node_modules/core-js/internals/an-object.js", "../../../node_modules/core-js/internals/object-define-property.js", "../../../node_modules/core-js/internals/create-non-enumerable-property.js", "../../../node_modules/core-js/internals/inspect-source.js", "../../../node_modules/core-js/internals/internal-state.js", "../../../node_modules/core-js/internals/native-weak-map.js", "../../../node_modules/core-js/internals/shared-key.js", "../../../node_modules/core-js/internals/hidden-keys.js", "../../../node_modules/core-js/internals/function-name.js", "../../../node_modules/core-js/internals/redefine.js", "../../../node_modules/core-js/internals/to-integer-or-infinity.js", "../../../node_modules/core-js/internals/to-absolute-index.js", "../../../node_modules/core-js/internals/to-length.js", "../../../node_modules/core-js/internals/length-of-array-like.js", "../../../node_modules/core-js/internals/array-includes.js", "../../../node_modules/core-js/internals/object-keys-internal.js", "../../../node_modules/core-js/internals/enum-bug-keys.js", "../../../node_modules/core-js/internals/object-get-own-property-names.js", "../../../node_modules/core-js/internals/object-get-own-property-symbols.js", "../../../node_modules/core-js/internals/own-keys.js", "../../../node_modules/core-js/internals/copy-constructor-properties.js", "../../../node_modules/core-js/internals/is-forced.js", "../../../node_modules/core-js/internals/export.js", "../../../node_modules/core-js/internals/to-string-tag-support.js", "../../../node_modules/core-js/internals/string-html-forced.js", "../../../node_modules/core-js/internals/classof.js", "../../../node_modules/core-js/internals/to-string.js", "../../../node_modules/core-js/internals/create-html.js", "../../../node_modules/core-js/modules/es.string.anchor.js", "../../../node_modules/core-js/internals/regexp-flags.js", "../../../node_modules/core-js/internals/object-create.js", "../../../node_modules/core-js/internals/regexp-sticky-helpers.js", "../../../node_modules/core-js/internals/object-keys.js", "../../../node_modules/core-js/internals/object-define-properties.js", "../../../node_modules/core-js/internals/html.js", "../../../node_modules/core-js/internals/regexp-exec.js", "../../../node_modules/core-js/internals/regexp-unsupported-dot-all.js", "../../../node_modules/core-js/internals/regexp-unsupported-ncg.js", "../../../node_modules/core-js/modules/es.regexp.exec.js", "../../../node_modules/core-js/internals/is-array.js", "../../../node_modules/core-js/internals/is-constructor.js", "../../../node_modules/core-js/internals/create-property.js", "../../../node_modules/core-js/internals/array-method-has-species-support.js", "../../../node_modules/core-js/internals/array-slice.js", "../../../node_modules/core-js/modules/es.array.slice.js", "../src/module/plugin.ts", "../../../node_modules/core-js/internals/function-bind-context.js", "../../../node_modules/core-js/internals/array-species-constructor.js", "../../../node_modules/core-js/internals/array-species-create.js", "../../../node_modules/core-js/internals/array-iteration.js", "../../../node_modules/core-js/modules/es.array.map.js", "../../../node_modules/core-js/internals/object-to-string.js", "../src/module/helpers.ts", "../../../node_modules/core-js/modules/es.object.to-string.js", "../../../node_modules/core-js/modules/es.regexp.to-string.js", "../../../node_modules/core-js/internals/add-to-unscopables.js", "../../../node_modules/core-js/modules/es.array.find.js", "../../../node_modules/core-js/internals/function-apply.js", "../../../node_modules/core-js/internals/fix-regexp-well-known-symbol-logic.js", "../../../node_modules/core-js/internals/is-regexp.js", "../../../node_modules/core-js/internals/a-constructor.js", "../../../node_modules/core-js/internals/species-constructor.js", "../../../node_modules/core-js/internals/string-multibyte.js", "../../../node_modules/core-js/internals/advance-string-index.js", "../../../node_modules/core-js/internals/array-slice-simple.js", "../../../node_modules/core-js/internals/regexp-exec-abstract.js", "../../../node_modules/core-js/modules/es.string.split.js", "../../../node_modules/core-js/internals/whitespaces.js", "../../../node_modules/core-js/internals/string-trim.js", "../../../node_modules/core-js/internals/string-trim-forced.js", "../../../node_modules/core-js/modules/es.string.trim.js", "../src/utils/dom.ts", "../../../node_modules/core-js/modules/es.global-this.js", "../src/module/render-elem/render-cell.tsx", "../src/module/render-elem/index.ts", "../src/module/render-elem/render-table.tsx", "../src/module/render-elem/render-row.tsx", "../src/module/elem-to-html.ts", "../src/module/pre-parse-html.ts", "../../../node_modules/core-js/modules/es.array.filter.js", "../../../node_modules/core-js/internals/get-substitution.js", "../../../node_modules/core-js/modules/es.string.replace.js", "../src/module/parse-elem-html.ts", "../src/module/menu/InsertTable.ts", "../src/constants/svg.ts", "../src/module/menu/DeleteTable.ts", "../src/module/menu/InsertRow.ts", "../src/module/menu/DeleteRow.ts", "../../../node_modules/core-js/internals/dom-iterables.js", "../../../node_modules/core-js/internals/dom-token-list-prototype.js", "../../../node_modules/core-js/internals/array-for-each.js", "../../../node_modules/core-js/internals/array-method-is-strict.js", "../../../node_modules/core-js/modules/web.dom-collections.for-each.js", "../src/module/menu/InsertCol.ts", "../src/module/menu/DeleteCol.ts", "../src/module/menu/TableHeader.ts", "../src/module/menu/FullWidth.ts", "../src/module/index.ts", "../src/module/menu/index.ts"], "sourcesContent": ["/**\n * @description i18n entry\n * <AUTHOR>\n */\n\nimport { i18nAddResources } from '@wangeditor/core'\nimport enResources from './en'\nimport zhResources from './zh-CN'\n\ni18nAddResources('en', enResources)\ni18nAddResources('zh-CN', zhResources)\n", "/**\n * @description i18n en\n * <AUTHOR>\n */\n\nexport default {\n  tableModule: {\n    deleteCol: 'Delete column',\n    deleteRow: 'Delete row',\n    deleteTable: 'Delete table',\n    widthAuto: 'Width auto',\n    insertCol: 'Insert column',\n    insertRow: 'Insert row',\n    insertTable: 'Insert table',\n    header: 'Header',\n  },\n}\n", "/**\n * @description i18n zh-CN\n * <AUTHOR>\n */\n\nexport default {\n  tableModule: {\n    deleteCol: '删除列',\n    deleteRow: '删除行',\n    deleteTable: '删除表格',\n    widthAuto: '宽度自适应',\n    insertCol: '插入列',\n    insertRow: '插入行',\n    insertTable: '插入表格',\n    header: '表头',\n  },\n}\n", "var check = function (it) {\n  return it && it.Math == Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n", "var global = require('../internals/global');\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n", "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "var fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] != 7;\n});\n", "var call = Function.prototype.call;\n\nmodule.exports = call.bind ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n", "'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n", "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "var FunctionPrototype = Function.prototype;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\nvar callBind = bind && bind.bind(call);\n\nmodule.exports = bind ? function (fn) {\n  return fn && callBind(call, fn);\n} : function (fn) {\n  return fn && function () {\n    return call.apply(fn, arguments);\n  };\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n", "var global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar Object = global.Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split(it, '') : Object(it);\n} : Object;\n", "var global = require('../internals/global');\n\nvar TypeError = global.TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\nmodule.exports = function (argument) {\n  return typeof argument == 'function';\n};\n", "var isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(global[namespace]) : global[namespace] && global[namespace][method];\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n", "var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('navigator', 'userAgent') || '';\n", "/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol();\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  return !String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n", "/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n", "var global = require('../internals/global');\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Object = global.Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, Object(it));\n};\n", "var global = require('../internals/global');\n\nvar String = global.String;\n\nmodule.exports = function (argument) {\n  try {\n    return String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar TypeError = global.TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw TypeError(tryToString(argument) + ' is not a function');\n};\n", "var aCallable = require('../internals/a-callable');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return func == null ? undefined : aCallable(func);\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar TypeError = global.TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n", "var global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "var global = require('../internals/global');\nvar setGlobal = require('../internals/set-global');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\n\nmodule.exports = store;\n", "var IS_PURE = require('../internals/is-pure');\nvar store = require('../internals/shared-store');\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.19.3',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2021 <PERSON> (zloirock.ru)'\n});\n", "var global = require('../internals/global');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar Object = global.Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n", "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar WellKnownSymbolsStore = shared('wks');\nvar Symbol = global.Symbol;\nvar symbolFor = Symbol && Symbol['for'];\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name) || !(NATIVE_SYMBOL || typeof WellKnownSymbolsStore[name] == 'string')) {\n    var description = 'Symbol.' + name;\n    if (NATIVE_SYMBOL && hasOwn(Symbol, name)) {\n      WellKnownSymbolsStore[name] = Symbol[name];\n    } else if (USE_SYMBOL_AS_UID && symbolFor) {\n      WellKnownSymbolsStore[name] = symbolFor(description);\n    } else {\n      WellKnownSymbolsStore[name] = createWellKnownSymbol(description);\n    }\n  } return WellKnownSymbolsStore[name];\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TypeError = global.TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n", "var toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n", "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- requied for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a != 7;\n});\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n", "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar String = global.String;\nvar TypeError = global.TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw TypeError(String(argument) + ' is not an object');\n};\n", "var global = require('../internals/global');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar TypeError = global.TypeError;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n", "var NATIVE_WEAK_MAP = require('../internals/native-weak-map');\nvar global = require('../internals/global');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\nvar shared = require('../internals/shared-store');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\nvar TypeError = global.TypeError;\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP || shared.state) {\n  var store = shared.state || (shared.state = new WeakMap());\n  var wmget = uncurryThis(store.get);\n  var wmhas = uncurryThis(store.has);\n  var wmset = uncurryThis(store.set);\n  set = function (it, metadata) {\n    if (wmhas(store, it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    wmset(store, it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return wmget(store, it) || {};\n  };\n  has = function (it) {\n    return wmhas(store, it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    if (hasOwn(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return hasOwn(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return hasOwn(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar inspectSource = require('../internals/inspect-source');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(inspectSource(WeakMap));\n", "var shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "module.exports = {};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n", "var global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar setGlobal = require('../internals/set-global');\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\n\nvar getInternalState = InternalStateModule.get;\nvar enforceInternalState = InternalStateModule.enforce;\nvar TEMPLATE = String(String).split('String');\n\n(module.exports = function (O, key, value, options) {\n  var unsafe = options ? !!options.unsafe : false;\n  var simple = options ? !!options.enumerable : false;\n  var noTargetGet = options ? !!options.noTargetGet : false;\n  var name = options && options.name !== undefined ? options.name : key;\n  var state;\n  if (isCallable(value)) {\n    if (String(name).slice(0, 7) === 'Symbol(') {\n      name = '[' + String(name).replace(/^Symbol\\(([^)]*)\\)/, '$1') + ']';\n    }\n    if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n      createNonEnumerableProperty(value, 'name', name);\n    }\n    state = enforceInternalState(value);\n    if (!state.source) {\n      state.source = TEMPLATE.join(typeof name == 'string' ? name : '');\n    }\n  }\n  if (O === global) {\n    if (simple) O[key] = value;\n    else setGlobal(key, value);\n    return;\n  } else if (!unsafe) {\n    delete O[key];\n  } else if (!noTargetGet && O[key]) {\n    simple = true;\n  }\n  if (simple) O[key] = value;\n  else createNonEnumerableProperty(O, key, value);\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, 'toString', function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n});\n", "var ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- safe\n  return number !== number || number === 0 ? 0 : (number > 0 ? floor : ceil)(number);\n};\n", "var toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "var toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  return argument > 0 ? min(toIntegerOrInfinity(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "var toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n", "var toIndexedObject = require('../internals/to-indexed-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = lengthOfArrayLike(O);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar hasOwn = require('../internals/has-own-property');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar push = uncurryThis([].push);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !hasOwn(hiddenKeys, key) && hasOwn(O, key) && push(result, key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (hasOwn(O, key = names[i++])) {\n    ~indexOf(result, key) || push(result, key);\n  }\n  return result;\n};\n", "// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n", "var getBuiltIn = require('../internals/get-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar concat = uncurryThis([].concat);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? concat(keys, getOwnPropertySymbols(it)) : keys;\n};\n", "var hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n  }\n};\n", "var fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true\n    : value == NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "var global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar redefine = require('../internals/redefine');\nvar setGlobal = require('../internals/set-global');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target      - name of the target object\n  options.global      - target is the global object\n  options.stat        - export as static methods of target\n  options.proto       - export as prototype methods of target\n  options.real        - real prototype method for the `pure` version\n  options.forced      - export even if the native feature is available\n  options.bind        - bind methods to the target, required for the `pure` version\n  options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe      - use the simple assignment of property instead of delete + defineProperty\n  options.sham        - add a flag to not completely full polyfills\n  options.enumerable  - export as enumerable property\n  options.noTargetGet - prevent calling a getter on target\n  options.name        - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || setGlobal(TARGET, {});\n  } else {\n    target = (global[TARGET] || {}).prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.noTargetGet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    // extend global\n    redefine(target, key, sourceProperty, options);\n  }\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "var fails = require('../internals/fails');\n\n// check the existence of a method, lowercase\n// of a tag and escaping quotes in arguments\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    var test = ''[METHOD_NAME]('\"');\n    return test !== test.toLowerCase() || test.split('\"').length > 3;\n  });\n};\n", "var global = require('../internals/global');\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar Object = global.Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) == 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "var global = require('../internals/global');\nvar classof = require('../internals/classof');\n\nvar String = global.String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw TypeError('Cannot convert a Symbol value to a string');\n  return String(argument);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\n\nvar quot = /\"/g;\nvar replace = uncurryThis(''.replace);\n\n// `CreateHTML` abstract operation\n// https://tc39.es/ecma262/#sec-createhtml\nmodule.exports = function (string, tag, attribute, value) {\n  var S = toString(requireObjectCoercible(string));\n  var p1 = '<' + tag;\n  if (attribute !== '') p1 += ' ' + attribute + '=\"' + replace(toString(value), quot, '&quot;') + '\"';\n  return p1 + '>' + S + '</' + tag + '>';\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar createHTML = require('../internals/create-html');\nvar forcedStringHTMLMethod = require('../internals/string-html-forced');\n\n// `String.prototype.anchor` method\n// https://tc39.es/ecma262/#sec-string.prototype.anchor\n$({ target: 'String', proto: true, forced: forcedStringHTMLMethod('anchor') }, {\n  anchor: function anchor(name) {\n    return createHTML(this, 'a', 'name', name);\n  }\n});\n", "'use strict';\nvar anObject = require('../internals/an-object');\n\n// `RegExp.prototype.flags` getter implementation\n// https://tc39.es/ecma262/#sec-get-regexp.prototype.flags\nmodule.exports = function () {\n  var that = anObject(this);\n  var result = '';\n  if (that.global) result += 'g';\n  if (that.ignoreCase) result += 'i';\n  if (that.multiline) result += 'm';\n  if (that.dotAll) result += 's';\n  if (that.unicode) result += 'u';\n  if (that.sticky) result += 'y';\n  return result;\n};\n", "/* global ActiveXObject -- old IE, WSH */\nvar anObject = require('../internals/an-object');\nvar defineProperties = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\n\nvar GT = '>';\nvar LT = '<';\nvar PROTOTYPE = 'prototype';\nvar SCRIPT = 'script';\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar EmptyConstructor = function () { /* empty */ };\n\nvar scriptTag = function (content) {\n  return LT + SCRIPT + GT + content + LT + '/' + SCRIPT + GT;\n};\n\n// Create object with fake `null` prototype: use ActiveX Object with cleared prototype\nvar NullProtoObjectViaActiveX = function (activeXDocument) {\n  activeXDocument.write(scriptTag(''));\n  activeXDocument.close();\n  var temp = activeXDocument.parentWindow.Object;\n  activeXDocument = null; // avoid memory leak\n  return temp;\n};\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar NullProtoObjectViaIFrame = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var JS = 'java' + SCRIPT + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  // https://github.com/zloirock/core-js/issues/475\n  iframe.src = String(JS);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(scriptTag('document.F=Object'));\n  iframeDocument.close();\n  return iframeDocument.F;\n};\n\n// Check for document.domain and active x support\n// No need to use active x approach when document.domain is not set\n// see https://github.com/es-shims/es5-shim/issues/150\n// variation of https://github.com/kitcambridge/es5-shim/commit/4f738ac066346\n// avoid IE GC bug\nvar activeXDocument;\nvar NullProtoObject = function () {\n  try {\n    activeXDocument = new ActiveXObject('htmlfile');\n  } catch (error) { /* ignore */ }\n  NullProtoObject = typeof document != 'undefined'\n    ? document.domain && activeXDocument\n      ? NullProtoObjectViaActiveX(activeXDocument) // old IE\n      : NullProtoObjectViaIFrame()\n    : NullProtoObjectViaActiveX(activeXDocument); // WSH\n  var length = enumBugKeys.length;\n  while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys[length]];\n  return NullProtoObject();\n};\n\nhiddenKeys[IE_PROTO] = true;\n\n// `Object.create` method\n// https://tc39.es/ecma262/#sec-object.create\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    EmptyConstructor[PROTOTYPE] = anObject(O);\n    result = new EmptyConstructor();\n    EmptyConstructor[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = NullProtoObject();\n  return Properties === undefined ? result : defineProperties(result, Properties);\n};\n", "var fails = require('../internals/fails');\nvar global = require('../internals/global');\n\n// babel-minify and Closure Compiler transpiles RegExp('a', 'y') -> /a/y and it causes SyntaxError\nvar $RegExp = global.RegExp;\n\nvar UNSUPPORTED_Y = fails(function () {\n  var re = $RegExp('a', 'y');\n  re.lastIndex = 2;\n  return re.exec('abcd') != null;\n});\n\n// UC Browser bug\n// https://github.com/zloirock/core-js/issues/1008\nvar MISSED_STICKY = UNSUPPORTED_Y || fails(function () {\n  return !$RegExp('a', 'y').sticky;\n});\n\nvar BROKEN_CARET = UNSUPPORTED_Y || fails(function () {\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=773687\n  var re = $RegExp('^r', 'gy');\n  re.lastIndex = 2;\n  return re.exec('str') != null;\n});\n\nmodule.exports = {\n  BROKEN_CARET: BROKEN_CARET,\n  MISSED_STICKY: MISSED_STICKY,\n  UNSUPPORTED_Y: UNSUPPORTED_Y\n};\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// `Object.keys` method\n// https://tc39.es/ecma262/#sec-object.keys\n// eslint-disable-next-line es/no-object-keys -- safe\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar objectKeys = require('../internals/object-keys');\n\n// `Object.defineProperties` method\n// https://tc39.es/ecma262/#sec-object.defineproperties\n// eslint-disable-next-line es/no-object-defineproperties -- safe\nmodule.exports = DESCRIPTORS ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var props = toIndexedObject(Properties);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], props[key]);\n  return O;\n};\n", "var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('document', 'documentElement');\n", "'use strict';\n/* eslint-disable regexp/no-empty-capturing-group, regexp/no-empty-group, regexp/no-lazy-ends -- testing */\n/* eslint-disable regexp/no-useless-quantifier -- testing */\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = require('../internals/to-string');\nvar regexpFlags = require('../internals/regexp-flags');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar shared = require('../internals/shared');\nvar create = require('../internals/object-create');\nvar getInternalState = require('../internals/internal-state').get;\nvar UNSUPPORTED_DOT_ALL = require('../internals/regexp-unsupported-dot-all');\nvar UNSUPPORTED_NCG = require('../internals/regexp-unsupported-ncg');\n\nvar nativeReplace = shared('native-string-replace', String.prototype.replace);\nvar nativeExec = RegExp.prototype.exec;\nvar patchedExec = nativeExec;\nvar charAt = uncurryThis(''.charAt);\nvar indexOf = uncurryThis(''.indexOf);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\n\nvar UPDATES_LAST_INDEX_WRONG = (function () {\n  var re1 = /a/;\n  var re2 = /b*/g;\n  call(nativeExec, re1, 'a');\n  call(nativeExec, re2, 'a');\n  return re1.lastIndex !== 0 || re2.lastIndex !== 0;\n})();\n\nvar UNSUPPORTED_Y = stickyHelpers.BROKEN_CARET;\n\n// nonparticipating capturing group, copied from es5-shim's String#split patch.\nvar NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;\n\nvar PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED || UNSUPPORTED_Y || UNSUPPORTED_DOT_ALL || UNSUPPORTED_NCG;\n\nif (PATCH) {\n  patchedExec = function exec(string) {\n    var re = this;\n    var state = getInternalState(re);\n    var str = toString(string);\n    var raw = state.raw;\n    var result, reCopy, lastIndex, match, i, object, group;\n\n    if (raw) {\n      raw.lastIndex = re.lastIndex;\n      result = call(patchedExec, raw, str);\n      re.lastIndex = raw.lastIndex;\n      return result;\n    }\n\n    var groups = state.groups;\n    var sticky = UNSUPPORTED_Y && re.sticky;\n    var flags = call(regexpFlags, re);\n    var source = re.source;\n    var charsAdded = 0;\n    var strCopy = str;\n\n    if (sticky) {\n      flags = replace(flags, 'y', '');\n      if (indexOf(flags, 'g') === -1) {\n        flags += 'g';\n      }\n\n      strCopy = stringSlice(str, re.lastIndex);\n      // Support anchored sticky behavior.\n      if (re.lastIndex > 0 && (!re.multiline || re.multiline && charAt(str, re.lastIndex - 1) !== '\\n')) {\n        source = '(?: ' + source + ')';\n        strCopy = ' ' + strCopy;\n        charsAdded++;\n      }\n      // ^(? + rx + ) is needed, in combination with some str slicing, to\n      // simulate the 'y' flag.\n      reCopy = new RegExp('^(?:' + source + ')', flags);\n    }\n\n    if (NPCG_INCLUDED) {\n      reCopy = new RegExp('^' + source + '$(?!\\\\s)', flags);\n    }\n    if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;\n\n    match = call(nativeExec, sticky ? reCopy : re, strCopy);\n\n    if (sticky) {\n      if (match) {\n        match.input = stringSlice(match.input, charsAdded);\n        match[0] = stringSlice(match[0], charsAdded);\n        match.index = re.lastIndex;\n        re.lastIndex += match[0].length;\n      } else re.lastIndex = 0;\n    } else if (UPDATES_LAST_INDEX_WRONG && match) {\n      re.lastIndex = re.global ? match.index + match[0].length : lastIndex;\n    }\n    if (NPCG_INCLUDED && match && match.length > 1) {\n      // Fix browsers whose `exec` methods don't consistently return `undefined`\n      // for NPCG, like IE8. NOTE: This doesn' work for /(.?)?/\n      call(nativeReplace, match[0], reCopy, function () {\n        for (i = 1; i < arguments.length - 2; i++) {\n          if (arguments[i] === undefined) match[i] = undefined;\n        }\n      });\n    }\n\n    if (match && groups) {\n      match.groups = object = create(null);\n      for (i = 0; i < groups.length; i++) {\n        group = groups[i];\n        object[group[0]] = match[group[1]];\n      }\n    }\n\n    return match;\n  };\n}\n\nmodule.exports = patchedExec;\n", "var fails = require('../internals/fails');\nvar global = require('../internals/global');\n\n// babel-minify and Closure Compiler transpiles RegExp('.', 's') -> /./s and it causes SyntaxError\nvar $RegExp = global.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('.', 's');\n  return !(re.dotAll && re.exec('\\n') && re.flags === 's');\n});\n", "var fails = require('../internals/fails');\nvar global = require('../internals/global');\n\n// babel-minify and Closure Compiler transpiles RegExp('(?<a>b)', 'g') -> /(?<a>b)/g and it causes SyntaxError\nvar $RegExp = global.RegExp;\n\nmodule.exports = fails(function () {\n  var re = $RegExp('(?<a>b)', 'g');\n  return re.exec('b').groups.a !== 'b' ||\n    'b'.replace(re, '$<a>c') !== 'bc';\n});\n", "'use strict';\nvar $ = require('../internals/export');\nvar exec = require('../internals/regexp-exec');\n\n// `RegExp.prototype.exec` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.exec\n$({ target: 'RegExp', proto: true, forced: /./.exec !== exec }, {\n  exec: exec\n});\n", "var classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) == 'Array';\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof');\nvar getBuiltIn = require('../internals/get-built-in');\nvar inspectSource = require('../internals/inspect-source');\n\nvar noop = function () { /* empty */ };\nvar empty = [];\nvar construct = getBuiltIn('Reflect', 'construct');\nvar constructorRegExp = /^\\s*(?:class|function)\\b/;\nvar exec = uncurryThis(constructorRegExp.exec);\nvar INCORRECT_TO_STRING = !constructorRegExp.exec(noop);\n\nvar isConstructorModern = function (argument) {\n  if (!isCallable(argument)) return false;\n  try {\n    construct(noop, empty, argument);\n    return true;\n  } catch (error) {\n    return false;\n  }\n};\n\nvar isConstructorLegacy = function (argument) {\n  if (!isCallable(argument)) return false;\n  switch (classof(argument)) {\n    case 'AsyncFunction':\n    case 'GeneratorFunction':\n    case 'AsyncGeneratorFunction': return false;\n    // we can't check .prototype since constructors produced by .bind haven't it\n  } return INCORRECT_TO_STRING || !!exec(constructorRegExp, inspectSource(argument));\n};\n\n// `IsConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-isconstructor\nmodule.exports = !construct || fails(function () {\n  var called;\n  return isConstructorModern(isConstructorModern.call)\n    || !isConstructorModern(Object)\n    || !isConstructorModern(function () { called = true; })\n    || called;\n}) ? isConstructorLegacy : isConstructorModern;\n", "'use strict';\nvar toPropertyKey = require('../internals/to-property-key');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = function (object, key, value) {\n  var propertyKey = toPropertyKey(key);\n  if (propertyKey in object) definePropertyModule.f(object, propertyKey, createPropertyDescriptor(0, value));\n  else object[propertyKey] = value;\n};\n", "var fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar V8_VERSION = require('../internals/engine-v8-version');\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (METHOD_NAME) {\n  // We can't use this feature detection in V8 since it causes\n  // deoptimization and serious performance degradation\n  // https://github.com/zloirock/core-js/issues/677\n  return V8_VERSION >= 51 || !fails(function () {\n    var array = [];\n    var constructor = array.constructor = {};\n    constructor[SPECIES] = function () {\n      return { foo: 1 };\n    };\n    return array[METHOD_NAME](Boolean).foo !== 1;\n  });\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis([].slice);\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar isArray = require('../internals/is-array');\nvar isConstructor = require('../internals/is-constructor');\nvar isObject = require('../internals/is-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar createProperty = require('../internals/create-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar arrayMethodHasSpeciesSupport = require('../internals/array-method-has-species-support');\nvar un$Slice = require('../internals/array-slice');\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('slice');\n\nvar SPECIES = wellKnownSymbol('species');\nvar Array = global.Array;\nvar max = Math.max;\n\n// `Array.prototype.slice` method\n// https://tc39.es/ecma262/#sec-array.prototype.slice\n// fallback for not array-like ES3 strings and DOM objects\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  slice: function slice(start, end) {\n    var O = toIndexedObject(this);\n    var length = lengthOfArrayLike(O);\n    var k = toAbsoluteIndex(start, length);\n    var fin = toAbsoluteIndex(end === undefined ? length : end, length);\n    // inline `ArraySpeciesCreate` for usage native `Array#slice` where it's possible\n    var Constructor, result, n;\n    if (isArray(O)) {\n      Constructor = O.constructor;\n      // cross-realm fallback\n      if (isConstructor(Constructor) && (Constructor === Array || isArray(Constructor.prototype))) {\n        Constructor = undefined;\n      } else if (isObject(Constructor)) {\n        Constructor = Constructor[SPECIES];\n        if (Constructor === null) Constructor = undefined;\n      }\n      if (Constructor === Array || Constructor === undefined) {\n        return un$Slice(O, k, fin);\n      }\n    }\n    result = new (Constructor === undefined ? Array : Constructor)(max(fin - k, 0));\n    for (n = 0; k < fin; k++, n++) if (k in O) createProperty(result, n, O[k]);\n    result.length = n;\n    return result;\n  }\n});\n", "/**\n * @description editor 插件，重写 editor API\n * <AUTHOR>\n */\n\nimport {\n  Editor,\n  Transforms,\n  Location,\n  Point,\n  Element as SlateElement,\n  Descendant,\n  NodeEntry,\n  Node,\n  BaseText,\n  Path,\n} from 'slate'\nimport { IDomEditor, DomEditor } from '@wangeditor/core'\n\n// table cell 内部的删除处理\nfunction deleteHandler(newEditor: IDomEditor): boolean {\n  const { selection } = newEditor\n  if (selection == null) return false\n\n  const [cellNodeEntry] = Editor.nodes(newEditor, {\n    match: n => DomEditor.checkNodeType(n, 'table-cell'),\n  })\n  if (cellNodeEntry) {\n    const [, cellPath] = cellNodeEntry\n    const start = Editor.start(newEditor, cellPath)\n\n    if (Point.equals(selection.anchor, start)) {\n      return true // 阻止删除 cell\n    }\n  }\n\n  return false\n}\n\n/**\n * 判断该 location 有没有命中 table\n * @param editor editor\n * @param location location\n */\nfunction isTableLocation(editor: IDomEditor, location: Location): boolean {\n  const tables = Editor.nodes(editor, {\n    at: location,\n    match: n => {\n      const type = DomEditor.getNodeType(n)\n      return type === 'table'\n    },\n  })\n  let hasTable = false\n  for (const table of tables) {\n    hasTable = true // 找到了 table\n  }\n  return hasTable\n}\n\nfunction withTable<T extends IDomEditor>(editor: T): T {\n  const {\n    insertBreak,\n    deleteBackward,\n    deleteForward,\n    normalizeNode,\n    insertData,\n    handleTab,\n    selectAll,\n  } = editor\n  const newEditor = editor\n\n  // 重写 insertBreak - cell 内换行，只换行文本，不拆分 node\n  newEditor.insertBreak = () => {\n    const selectedNode = DomEditor.getSelectedNodeByType(newEditor, 'table')\n    if (selectedNode != null) {\n      // 选中了 table ，则在 cell 内换行\n      newEditor.insertText('\\n')\n      return\n    }\n\n    // 未选中 table ，默认的换行\n    insertBreak()\n  }\n\n  // 重写 delete - cell 内删除，只删除文字，不删除 node\n  newEditor.deleteBackward = unit => {\n    const res = deleteHandler(newEditor)\n    if (res) return // 命中 table cell ，自己处理删除\n\n    // 防止从 table 后面的 p 删除时，删除最后一个 cell - issues/4221\n    const { selection } = newEditor\n    if (selection) {\n      const before = Editor.before(newEditor, selection) // 前一个 location\n      if (before) {\n        const isTableOnBeforeLocation = isTableLocation(newEditor, before) // before 是否是 table\n        const isTableOnCurSelection = isTableLocation(newEditor, selection) // 当前是否是 table\n        if (isTableOnBeforeLocation && !isTableOnCurSelection) {\n          return // 如果当前不是 table ，前面是 table ，则不执行删除。否则会删除 table 最后一个 cell\n        }\n      }\n    }\n\n    // 执行默认的删除\n    deleteBackward(unit)\n  }\n\n  // 重写 handleTab 在table内按tab时跳到下一个单元格\n  newEditor.handleTab = () => {\n    const selectedNode = DomEditor.getSelectedNodeByType(newEditor, 'table')\n    if (selectedNode) {\n      const above = Editor.above(editor) as NodeEntry<SlateElement>\n\n      // 常规情况下选中文字外层 table-cell 进行跳转\n      if (DomEditor.checkNodeType(above[0], 'table-cell')) {\n        Transforms.select(editor, above[1])\n      }\n\n      let next = Editor.next(editor)\n      if (next) {\n        if (next[0] && (next[0] as BaseText).text) {\n          // 多个单元格同时选中按 tab 导致错位修复\n          next = (Editor.above(editor, { at: next[1] }) as NodeEntry<Descendant>) ?? next\n        }\n        Transforms.select(editor, next[1])\n      } else {\n        const topLevelNodes = newEditor.children || []\n        const topLevelNodesLength = topLevelNodes.length\n        // 在最后一个单元格按tab时table末尾如果没有p则插入p后光标切到p上\n        if (DomEditor.checkNodeType(topLevelNodes[topLevelNodesLength - 1], 'table')) {\n          const p = DomEditor.genEmptyParagraph()\n          Transforms.insertNodes(newEditor, p, { at: [topLevelNodesLength] })\n          // 在表格末尾插入p后再次执行使光标切到p上\n          newEditor.handleTab()\n        }\n      }\n      return\n    }\n\n    handleTab()\n  }\n\n  newEditor.deleteForward = unit => {\n    const res = deleteHandler(newEditor)\n    if (res) return // 命中 table cell ，自己处理删除\n\n    // 执行默认的删除\n    deleteForward(unit)\n  }\n\n  // 重新 normalize\n  newEditor.normalizeNode = ([node, path]) => {\n    const type = DomEditor.getNodeType(node)\n    if (type !== 'table') {\n      // 未命中 table ，执行默认的 normalizeNode\n      return normalizeNode([node, path])\n    }\n\n    // -------------- table 是 editor 最后一个节点，需要后面插入 p --------------\n    const isLast = DomEditor.isLastNode(newEditor, node)\n    if (isLast) {\n      const p = DomEditor.genEmptyParagraph()\n      Transforms.insertNodes(newEditor, p, { at: [path[0] + 1] })\n    }\n  }\n\n  // 重写 insertData - 粘贴文本\n  newEditor.insertData = (data: DataTransfer) => {\n    const tableNode = DomEditor.getSelectedNodeByType(newEditor, 'table')\n    if (tableNode == null) {\n      insertData(data) // 执行默认的 insertData\n      return\n    }\n\n    // 获取文本，并插入到 cell\n    const text = data.getData('text/plain')\n\n    // 单图或图文 插入\n    if (text === '\\n' || /<img[^>]+>/.test(data.getData('text/html'))) {\n      insertData(data)\n      return\n    }\n\n    Editor.insertText(newEditor, text)\n  }\n\n  // 重写 table-cell 中的全选\n  newEditor.selectAll = () => {\n    const selection = newEditor.selection\n    if (selection == null) {\n      selectAll()\n      return\n    }\n\n    const cell = DomEditor.getSelectedNodeByType(newEditor, 'table-cell')\n    if (cell == null) {\n      selectAll()\n      return\n    }\n\n    const { anchor, focus } = selection\n    if (!Path.equals(anchor.path.slice(0, 3), focus.path.slice(0, 3))) {\n      // 选中了多个 cell ，忽略\n      selectAll()\n      return\n    }\n\n    const text = Node.string(cell)\n    const textLength = text.length\n    if (textLength === 0) {\n      selectAll()\n      return\n    }\n\n    const path = DomEditor.findPath(newEditor, cell)\n    const start = Editor.start(newEditor, path)\n    const end = Editor.end(newEditor, path)\n    const newSelection = {\n      anchor: start,\n      focus: end,\n    }\n    newEditor.select(newSelection) // 选中 table-cell 内部的全部文字\n  }\n\n  // 可继续修改其他 newEditor API ...\n\n  // 返回 editor ，重要！\n  return newEditor\n}\n\nexport default withTable\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nvar bind = uncurryThis(uncurryThis.bind);\n\n// optional / simple context binding\nmodule.exports = function (fn, that) {\n  aCallable(fn);\n  return that === undefined ? fn : bind ? bind(fn, that) : function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n", "var global = require('../internals/global');\nvar isArray = require('../internals/is-array');\nvar isConstructor = require('../internals/is-constructor');\nvar isObject = require('../internals/is-object');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar SPECIES = wellKnownSymbol('species');\nvar Array = global.Array;\n\n// a part of `ArraySpeciesCreate` abstract operation\n// https://tc39.es/ecma262/#sec-arrayspeciescreate\nmodule.exports = function (originalArray) {\n  var C;\n  if (isArray(originalArray)) {\n    C = originalArray.constructor;\n    // cross-realm fallback\n    if (isConstructor(C) && (C === Array || isArray(C.prototype))) C = undefined;\n    else if (isObject(C)) {\n      C = C[SPECIES];\n      if (C === null) C = undefined;\n    }\n  } return C === undefined ? Array : C;\n};\n", "var arraySpeciesConstructor = require('../internals/array-species-constructor');\n\n// `ArraySpeciesCreate` abstract operation\n// https://tc39.es/ecma262/#sec-arrayspeciescreate\nmodule.exports = function (originalArray, length) {\n  return new (arraySpeciesConstructor(originalArray))(length === 0 ? 0 : length);\n};\n", "var bind = require('../internals/function-bind-context');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar IndexedObject = require('../internals/indexed-object');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar arraySpeciesCreate = require('../internals/array-species-create');\n\nvar push = uncurryThis([].push);\n\n// `Array.prototype.{ forEach, map, filter, some, every, find, findIndex, filterReject }` methods implementation\nvar createMethod = function (TYPE) {\n  var IS_MAP = TYPE == 1;\n  var IS_FILTER = TYPE == 2;\n  var IS_SOME = TYPE == 3;\n  var IS_EVERY = TYPE == 4;\n  var IS_FIND_INDEX = TYPE == 6;\n  var IS_FILTER_REJECT = TYPE == 7;\n  var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;\n  return function ($this, callbackfn, that, specificCreate) {\n    var O = toObject($this);\n    var self = IndexedObject(O);\n    var boundFunction = bind(callbackfn, that);\n    var length = lengthOfArrayLike(self);\n    var index = 0;\n    var create = specificCreate || arraySpeciesCreate;\n    var target = IS_MAP ? create($this, length) : IS_FILTER || IS_FILTER_REJECT ? create($this, 0) : undefined;\n    var value, result;\n    for (;length > index; index++) if (NO_HOLES || index in self) {\n      value = self[index];\n      result = boundFunction(value, index, O);\n      if (TYPE) {\n        if (IS_MAP) target[index] = result; // map\n        else if (result) switch (TYPE) {\n          case 3: return true;              // some\n          case 5: return value;             // find\n          case 6: return index;             // findIndex\n          case 2: push(target, value);      // filter\n        } else switch (TYPE) {\n          case 4: return false;             // every\n          case 7: push(target, value);      // filterReject\n        }\n      }\n    }\n    return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.forEach` method\n  // https://tc39.es/ecma262/#sec-array.prototype.foreach\n  forEach: createMethod(0),\n  // `Array.prototype.map` method\n  // https://tc39.es/ecma262/#sec-array.prototype.map\n  map: createMethod(1),\n  // `Array.prototype.filter` method\n  // https://tc39.es/ecma262/#sec-array.prototype.filter\n  filter: createMethod(2),\n  // `Array.prototype.some` method\n  // https://tc39.es/ecma262/#sec-array.prototype.some\n  some: createMethod(3),\n  // `Array.prototype.every` method\n  // https://tc39.es/ecma262/#sec-array.prototype.every\n  every: createMethod(4),\n  // `Array.prototype.find` method\n  // https://tc39.es/ecma262/#sec-array.prototype.find\n  find: createMethod(5),\n  // `Array.prototype.findIndex` method\n  // https://tc39.es/ecma262/#sec-array.prototype.findIndex\n  findIndex: createMethod(6),\n  // `Array.prototype.filterReject` method\n  // https://github.com/tc39/proposal-array-filtering\n  filterReject: createMethod(7)\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar $map = require('../internals/array-iteration').map;\nvar arrayMethodHasSpeciesSupport = require('../internals/array-method-has-species-support');\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('map');\n\n// `Array.prototype.map` method\n// https://tc39.es/ecma262/#sec-array.prototype.map\n// with adding support of @@species\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  map: function map(callbackfn /* , thisArg */) {\n    return $map(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar classof = require('../internals/classof');\n\n// `Object.prototype.toString` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.tostring\nmodule.exports = TO_STRING_TAG_SUPPORT ? {}.toString : function toString() {\n  return '[object ' + classof(this) + ']';\n};\n", "/**\n * @description table menu helpers\n * <AUTHOR>\n */\n\nimport { DomEditor, IDomEditor } from '@wangeditor/core'\nimport { TableElement, TableCellElement } from './custom-types'\n\n/**\n * 获取第一行所有 cells\n * @param tableNode table node\n */\nexport function getFirstRowCells(tableNode: TableElement): TableCellElement[] {\n  const rows = tableNode.children || [] // 所有行\n  if (rows.length === 0) return []\n  const firstRow = rows[0] || {} // 第一行\n  const cells = firstRow.children || [] // 第一行所有 cell\n  return cells\n}\n\n/**\n * 表格是否带有表头？\n * @param tableNode table node\n */\nexport function isTableWithHeader(tableNode: TableElement): boolean {\n  const firstRowCells = getFirstRowCells(tableNode)\n  return firstRowCells.every(cell => !!cell.isHeader)\n}\n\n/**\n * 单元格是否在第一行\n * @param editor editor\n * @param cellNode cell node\n */\nexport function isCellInFirstRow(editor: IDomEditor, cellNode: TableCellElement): boolean {\n  const rowNode = DomEditor.getParentNode(editor, cellNode)\n  if (rowNode == null) return false\n  const tableNode = DomEditor.getParentNode(editor, rowNode)\n  if (tableNode == null) return false\n\n  const firstRowCells = getFirstRowCells(tableNode as TableElement)\n  return firstRowCells.some(c => c === cellNode)\n}\n", "var TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar redefine = require('../internals/redefine');\nvar toString = require('../internals/object-to-string');\n\n// `Object.prototype.toString` method\n// https://tc39.es/ecma262/#sec-object.prototype.tostring\nif (!TO_STRING_TAG_SUPPORT) {\n  redefine(Object.prototype, 'toString', toString, { unsafe: true });\n}\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar redefine = require('../internals/redefine');\nvar anObject = require('../internals/an-object');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar $toString = require('../internals/to-string');\nvar fails = require('../internals/fails');\nvar regExpFlags = require('../internals/regexp-flags');\n\nvar TO_STRING = 'toString';\nvar RegExpPrototype = RegExp.prototype;\nvar n$ToString = RegExpPrototype[TO_STRING];\nvar getFlags = uncurryThis(regExpFlags);\n\nvar NOT_GENERIC = fails(function () { return n$ToString.call({ source: 'a', flags: 'b' }) != '/a/b'; });\n// FF44- RegExp#toString has a wrong name\nvar INCORRECT_NAME = PROPER_FUNCTION_NAME && n$ToString.name != TO_STRING;\n\n// `RegExp.prototype.toString` method\n// https://tc39.es/ecma262/#sec-regexp.prototype.tostring\nif (NOT_GENERIC || INCORRECT_NAME) {\n  redefine(RegExp.prototype, TO_STRING, function toString() {\n    var R = anObject(this);\n    var p = $toString(R.source);\n    var rf = R.flags;\n    var f = $toString(rf === undefined && isPrototypeOf(RegExpPrototype, R) && !('flags' in RegExpPrototype) ? getFlags(R) : rf);\n    return '/' + p + '/' + f;\n  }, { unsafe: true });\n}\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\nvar create = require('../internals/object-create');\nvar definePropertyModule = require('../internals/object-define-property');\n\nvar UNSCOPABLES = wellKnownSymbol('unscopables');\nvar ArrayPrototype = Array.prototype;\n\n// Array.prototype[@@unscopables]\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\nif (ArrayPrototype[UNSCOPABLES] == undefined) {\n  definePropertyModule.f(ArrayPrototype, UNSCOPABLES, {\n    configurable: true,\n    value: create(null)\n  });\n}\n\n// add a key to Array.prototype[@@unscopables]\nmodule.exports = function (key) {\n  ArrayPrototype[UNSCOPABLES][key] = true;\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar $find = require('../internals/array-iteration').find;\nvar addToUnscopables = require('../internals/add-to-unscopables');\n\nvar FIND = 'find';\nvar SKIPS_HOLES = true;\n\n// Shouldn't skip holes\nif (FIND in []) Array(1)[FIND](function () { SKIPS_HOLES = false; });\n\n// `Array.prototype.find` method\n// https://tc39.es/ecma262/#sec-array.prototype.find\n$({ target: 'Array', proto: true, forced: SKIPS_HOLES }, {\n  find: function find(callbackfn /* , that = undefined */) {\n    return $find(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n\n// https://tc39.es/ecma262/#sec-array.prototype-@@unscopables\naddToUnscopables(FIND);\n", "var FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar bind = FunctionPrototype.bind;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (bind ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n", "'use strict';\n// TODO: Remove from `core-js@4` since it's moved to entry points\nrequire('../modules/es.regexp.exec');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar redefine = require('../internals/redefine');\nvar regexpExec = require('../internals/regexp-exec');\nvar fails = require('../internals/fails');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\nvar SPECIES = wellKnownSymbol('species');\nvar RegExpPrototype = RegExp.prototype;\n\nmodule.exports = function (KEY, exec, FORCED, SHAM) {\n  var SYMBOL = wellKnownSymbol(KEY);\n\n  var DELEGATES_TO_SYMBOL = !fails(function () {\n    // String methods call symbol-named RegEp methods\n    var O = {};\n    O[SYMBOL] = function () { return 7; };\n    return ''[KEY](O) != 7;\n  });\n\n  var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {\n    // Symbol-named RegExp methods call .exec\n    var execCalled = false;\n    var re = /a/;\n\n    if (KEY === 'split') {\n      // We can't use real regex here since it causes deoptimization\n      // and serious performance degradation in V8\n      // https://github.com/zloirock/core-js/issues/306\n      re = {};\n      // RegExp[@@split] doesn't call the regex's exec method, but first creates\n      // a new one. We need to return the patched regex when creating the new one.\n      re.constructor = {};\n      re.constructor[SPECIES] = function () { return re; };\n      re.flags = '';\n      re[SYMBOL] = /./[SYMBOL];\n    }\n\n    re.exec = function () { execCalled = true; return null; };\n\n    re[SYMBOL]('');\n    return !execCalled;\n  });\n\n  if (\n    !DELEGATES_TO_SYMBOL ||\n    !DELEGATES_TO_EXEC ||\n    FORCED\n  ) {\n    var uncurriedNativeRegExpMethod = uncurryThis(/./[SYMBOL]);\n    var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {\n      var uncurriedNativeMethod = uncurryThis(nativeMethod);\n      var $exec = regexp.exec;\n      if ($exec === regexpExec || $exec === RegExpPrototype.exec) {\n        if (DELEGATES_TO_SYMBOL && !forceStringMethod) {\n          // The native String method already delegates to @@method (this\n          // polyfilled function), leasing to infinite recursion.\n          // We avoid it by directly calling the native @@method method.\n          return { done: true, value: uncurriedNativeRegExpMethod(regexp, str, arg2) };\n        }\n        return { done: true, value: uncurriedNativeMethod(str, regexp, arg2) };\n      }\n      return { done: false };\n    });\n\n    redefine(String.prototype, KEY, methods[0]);\n    redefine(RegExpPrototype, SYMBOL, methods[1]);\n  }\n\n  if (SHAM) createNonEnumerableProperty(RegExpPrototype[SYMBOL], 'sham', true);\n};\n", "var isObject = require('../internals/is-object');\nvar classof = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar MATCH = wellKnownSymbol('match');\n\n// `IsRegExp` abstract operation\n// https://tc39.es/ecma262/#sec-isregexp\nmodule.exports = function (it) {\n  var isRegExp;\n  return isObject(it) && ((isRegExp = it[MATCH]) !== undefined ? !!isRegExp : classof(it) == 'RegExp');\n};\n", "var global = require('../internals/global');\nvar isConstructor = require('../internals/is-constructor');\nvar tryToString = require('../internals/try-to-string');\n\nvar TypeError = global.TypeError;\n\n// `Assert: IsConstructor(argument) is true`\nmodule.exports = function (argument) {\n  if (isConstructor(argument)) return argument;\n  throw TypeError(tryToString(argument) + ' is not a constructor');\n};\n", "var anObject = require('../internals/an-object');\nvar aConstructor = require('../internals/a-constructor');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar SPECIES = wellKnownSymbol('species');\n\n// `SpeciesConstructor` abstract operation\n// https://tc39.es/ecma262/#sec-speciesconstructor\nmodule.exports = function (O, defaultConstructor) {\n  var C = anObject(O).constructor;\n  var S;\n  return C === undefined || (S = anObject(C)[SPECIES]) == undefined ? defaultConstructor : aConstructor(S);\n};\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar charAt = uncurryThis(''.charAt);\nvar charCodeAt = uncurryThis(''.charCodeAt);\nvar stringSlice = uncurryThis(''.slice);\n\nvar createMethod = function (CONVERT_TO_STRING) {\n  return function ($this, pos) {\n    var S = toString(requireObjectCoercible($this));\n    var position = toIntegerOrInfinity(pos);\n    var size = S.length;\n    var first, second;\n    if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;\n    first = charCodeAt(S, position);\n    return first < 0xD800 || first > 0xDBFF || position + 1 === size\n      || (second = charCodeAt(S, position + 1)) < 0xDC00 || second > 0xDFFF\n        ? CONVERT_TO_STRING\n          ? charAt(S, position)\n          : first\n        : CONVERT_TO_STRING\n          ? stringSlice(S, position, position + 2)\n          : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.codePointAt` method\n  // https://tc39.es/ecma262/#sec-string.prototype.codepointat\n  codeAt: createMethod(false),\n  // `String.prototype.at` method\n  // https://github.com/mathiasbynens/String.prototype.at\n  charAt: createMethod(true)\n};\n", "'use strict';\nvar charAt = require('../internals/string-multibyte').charAt;\n\n// `AdvanceStringIndex` abstract operation\n// https://tc39.es/ecma262/#sec-advancestringindex\nmodule.exports = function (S, index, unicode) {\n  return index + (unicode ? charAt(S, index).length : 1);\n};\n", "var global = require('../internals/global');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar createProperty = require('../internals/create-property');\n\nvar Array = global.Array;\nvar max = Math.max;\n\nmodule.exports = function (O, start, end) {\n  var length = lengthOfArrayLike(O);\n  var k = toAbsoluteIndex(start, length);\n  var fin = toAbsoluteIndex(end === undefined ? length : end, length);\n  var result = Array(max(fin - k, 0));\n  for (var n = 0; k < fin; k++, n++) createProperty(result, n, O[k]);\n  result.length = n;\n  return result;\n};\n", "var global = require('../internals/global');\nvar call = require('../internals/function-call');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar classof = require('../internals/classof-raw');\nvar regexpExec = require('../internals/regexp-exec');\n\nvar TypeError = global.TypeError;\n\n// `RegExpExec` abstract operation\n// https://tc39.es/ecma262/#sec-regexpexec\nmodule.exports = function (R, S) {\n  var exec = R.exec;\n  if (isCallable(exec)) {\n    var result = call(exec, R, S);\n    if (result !== null) anObject(result);\n    return result;\n  }\n  if (classof(R) === 'RegExp') return call(regexpExec, R, S);\n  throw TypeError('RegExp#exec called on incompatible receiver');\n};\n", "'use strict';\nvar apply = require('../internals/function-apply');\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fixRegExpWellKnownSymbolLogic = require('../internals/fix-regexp-well-known-symbol-logic');\nvar isRegExp = require('../internals/is-regexp');\nvar anObject = require('../internals/an-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar speciesConstructor = require('../internals/species-constructor');\nvar advanceStringIndex = require('../internals/advance-string-index');\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar getMethod = require('../internals/get-method');\nvar arraySlice = require('../internals/array-slice-simple');\nvar callRegExpExec = require('../internals/regexp-exec-abstract');\nvar regexpExec = require('../internals/regexp-exec');\nvar stickyHelpers = require('../internals/regexp-sticky-helpers');\nvar fails = require('../internals/fails');\n\nvar UNSUPPORTED_Y = stickyHelpers.UNSUPPORTED_Y;\nvar MAX_UINT32 = 0xFFFFFFFF;\nvar min = Math.min;\nvar $push = [].push;\nvar exec = uncurryThis(/./.exec);\nvar push = uncurryThis($push);\nvar stringSlice = uncurryThis(''.slice);\n\n// Chrome 51 has a buggy \"split\" implementation when RegExp#exec !== nativeExec\n// Weex JS has frozen built-in prototypes, so use try / catch wrapper\nvar SPLIT_WORKS_WITH_OVERWRITTEN_EXEC = !fails(function () {\n  // eslint-disable-next-line regexp/no-empty-group -- required for testing\n  var re = /(?:)/;\n  var originalExec = re.exec;\n  re.exec = function () { return originalExec.apply(this, arguments); };\n  var result = 'ab'.split(re);\n  return result.length !== 2 || result[0] !== 'a' || result[1] !== 'b';\n});\n\n// @@split logic\nfixRegExpWellKnownSymbolLogic('split', function (SPLIT, nativeSplit, maybeCallNative) {\n  var internalSplit;\n  if (\n    'abbc'.split(/(b)*/)[1] == 'c' ||\n    // eslint-disable-next-line regexp/no-empty-group -- required for testing\n    'test'.split(/(?:)/, -1).length != 4 ||\n    'ab'.split(/(?:ab)*/).length != 2 ||\n    '.'.split(/(.?)(.?)/).length != 4 ||\n    // eslint-disable-next-line regexp/no-empty-capturing-group, regexp/no-empty-group -- required for testing\n    '.'.split(/()()/).length > 1 ||\n    ''.split(/.?/).length\n  ) {\n    // based on es5-shim implementation, need to rework it\n    internalSplit = function (separator, limit) {\n      var string = toString(requireObjectCoercible(this));\n      var lim = limit === undefined ? MAX_UINT32 : limit >>> 0;\n      if (lim === 0) return [];\n      if (separator === undefined) return [string];\n      // If `separator` is not a regex, use native split\n      if (!isRegExp(separator)) {\n        return call(nativeSplit, string, separator, lim);\n      }\n      var output = [];\n      var flags = (separator.ignoreCase ? 'i' : '') +\n                  (separator.multiline ? 'm' : '') +\n                  (separator.unicode ? 'u' : '') +\n                  (separator.sticky ? 'y' : '');\n      var lastLastIndex = 0;\n      // Make `global` and avoid `lastIndex` issues by working with a copy\n      var separatorCopy = new RegExp(separator.source, flags + 'g');\n      var match, lastIndex, lastLength;\n      while (match = call(regexpExec, separatorCopy, string)) {\n        lastIndex = separatorCopy.lastIndex;\n        if (lastIndex > lastLastIndex) {\n          push(output, stringSlice(string, lastLastIndex, match.index));\n          if (match.length > 1 && match.index < string.length) apply($push, output, arraySlice(match, 1));\n          lastLength = match[0].length;\n          lastLastIndex = lastIndex;\n          if (output.length >= lim) break;\n        }\n        if (separatorCopy.lastIndex === match.index) separatorCopy.lastIndex++; // Avoid an infinite loop\n      }\n      if (lastLastIndex === string.length) {\n        if (lastLength || !exec(separatorCopy, '')) push(output, '');\n      } else push(output, stringSlice(string, lastLastIndex));\n      return output.length > lim ? arraySlice(output, 0, lim) : output;\n    };\n  // Chakra, V8\n  } else if ('0'.split(undefined, 0).length) {\n    internalSplit = function (separator, limit) {\n      return separator === undefined && limit === 0 ? [] : call(nativeSplit, this, separator, limit);\n    };\n  } else internalSplit = nativeSplit;\n\n  return [\n    // `String.prototype.split` method\n    // https://tc39.es/ecma262/#sec-string.prototype.split\n    function split(separator, limit) {\n      var O = requireObjectCoercible(this);\n      var splitter = separator == undefined ? undefined : getMethod(separator, SPLIT);\n      return splitter\n        ? call(splitter, separator, O, limit)\n        : call(internalSplit, toString(O), separator, limit);\n    },\n    // `RegExp.prototype[@@split]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@split\n    //\n    // NOTE: This cannot be properly polyfilled in engines that don't support\n    // the 'y' flag.\n    function (string, limit) {\n      var rx = anObject(this);\n      var S = toString(string);\n      var res = maybeCallNative(internalSplit, rx, S, limit, internalSplit !== nativeSplit);\n\n      if (res.done) return res.value;\n\n      var C = speciesConstructor(rx, RegExp);\n\n      var unicodeMatching = rx.unicode;\n      var flags = (rx.ignoreCase ? 'i' : '') +\n                  (rx.multiline ? 'm' : '') +\n                  (rx.unicode ? 'u' : '') +\n                  (UNSUPPORTED_Y ? 'g' : 'y');\n\n      // ^(? + rx + ) is needed, in combination with some S slicing, to\n      // simulate the 'y' flag.\n      var splitter = new C(UNSUPPORTED_Y ? '^(?:' + rx.source + ')' : rx, flags);\n      var lim = limit === undefined ? MAX_UINT32 : limit >>> 0;\n      if (lim === 0) return [];\n      if (S.length === 0) return callRegExpExec(splitter, S) === null ? [S] : [];\n      var p = 0;\n      var q = 0;\n      var A = [];\n      while (q < S.length) {\n        splitter.lastIndex = UNSUPPORTED_Y ? 0 : q;\n        var z = callRegExpExec(splitter, UNSUPPORTED_Y ? stringSlice(S, q) : S);\n        var e;\n        if (\n          z === null ||\n          (e = min(toLength(splitter.lastIndex + (UNSUPPORTED_Y ? q : 0)), S.length)) === p\n        ) {\n          q = advanceStringIndex(S, q, unicodeMatching);\n        } else {\n          push(A, stringSlice(S, p, q));\n          if (A.length === lim) return A;\n          for (var i = 1; i <= z.length - 1; i++) {\n            push(A, z[i]);\n            if (A.length === lim) return A;\n          }\n          q = p = e;\n        }\n      }\n      push(A, stringSlice(S, p));\n      return A;\n    }\n  ];\n}, !SPLIT_WORKS_WITH_OVERWRITTEN_EXEC, UNSUPPORTED_Y);\n", "// a string of all valid unicode whitespaces\nmodule.exports = '\\u0009\\u000A\\u000B\\u000C\\u000D\\u0020\\u00A0\\u1680\\u2000\\u2001\\u2002' +\n  '\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200A\\u202F\\u205F\\u3000\\u2028\\u2029\\uFEFF';\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar toString = require('../internals/to-string');\nvar whitespaces = require('../internals/whitespaces');\n\nvar replace = uncurryThis(''.replace);\nvar whitespace = '[' + whitespaces + ']';\nvar ltrim = RegExp('^' + whitespace + whitespace + '*');\nvar rtrim = RegExp(whitespace + whitespace + '*$');\n\n// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation\nvar createMethod = function (TYPE) {\n  return function ($this) {\n    var string = toString(requireObjectCoercible($this));\n    if (TYPE & 1) string = replace(string, ltrim, '');\n    if (TYPE & 2) string = replace(string, rtrim, '');\n    return string;\n  };\n};\n\nmodule.exports = {\n  // `String.prototype.{ trimLeft, trimStart }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimstart\n  start: createMethod(1),\n  // `String.prototype.{ trimRight, trimEnd }` methods\n  // https://tc39.es/ecma262/#sec-string.prototype.trimend\n  end: createMethod(2),\n  // `String.prototype.trim` method\n  // https://tc39.es/ecma262/#sec-string.prototype.trim\n  trim: createMethod(3)\n};\n", "var PROPER_FUNCTION_NAME = require('../internals/function-name').PROPER;\nvar fails = require('../internals/fails');\nvar whitespaces = require('../internals/whitespaces');\n\nvar non = '\\u200B\\u0085\\u180E';\n\n// check that a method works with the correct list\n// of whitespaces and has a correct name\nmodule.exports = function (METHOD_NAME) {\n  return fails(function () {\n    return !!whitespaces[METHOD_NAME]()\n      || non[METHOD_NAME]() !== non\n      || (PROPER_FUNCTION_NAME && whitespaces[METHOD_NAME].name !== METHOD_NAME);\n  });\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar $trim = require('../internals/string-trim').trim;\nvar forcedStringTrimMethod = require('../internals/string-trim-forced');\n\n// `String.prototype.trim` method\n// https://tc39.es/ecma262/#sec-string.prototype.trim\n$({ target: 'String', proto: true, forced: forcedStringTrimMethod('trim') }, {\n  trim: function trim() {\n    return $trim(this);\n  }\n});\n", "/**\n * @description DOM 操作\n * <AUTHOR>\n */\n\nimport $, {\n  append,\n  on,\n  focus,\n  attr,\n  val,\n  html,\n  dataset,\n  addClass,\n  removeClass,\n  children,\n  each,\n  find,\n  Dom7Array,\n} from 'dom7'\nexport { Dom7Array } from 'dom7'\n\nif (append) $.fn.append = append\nif (on) $.fn.on = on\nif (focus) $.fn.focus = focus\nif (attr) $.fn.attr = attr\nif (val) $.fn.val = val\nif (html) $.fn.html = html\nif (dataset) $.fn.dataset = dataset\nif (addClass) $.fn.addClass = addClass\nif (removeClass) $.fn.removeClass = removeClass\nif (children) $.fn.children = children\nif (each) $.fn.each = each\nif (find) $.fn.find = find\n\nexport default $\n\n/**\n * 获取 tagName lower-case\n * @param $elem $elem\n */\nexport function getTagName($elem: Dom7Array): string {\n  if ($elem.length) return $elem[0].tagName.toLowerCase()\n  return ''\n}\n\n/**\n * 获取 $elem 某一个 style 值\n * @param $elem $elem\n * @param styleKey style key\n */\nexport function getStyleValue($elem: Dom7Array, styleKey: string): string {\n  let res = ''\n\n  const styleStr = $elem.attr('style') || '' // 如 'line-height: 2.5; color: red;'\n  const styleArr = styleStr.split(';') // 如 ['line-height: 2.5', ' color: red', '']\n  const length = styleArr.length\n  for (let i = 0; i < length; i++) {\n    const styleItemStr = styleArr[i] // 如 'line-height: 2.5'\n    if (styleItemStr) {\n      const arr = styleItemStr.split(':') // ['line-height', ' 2.5']\n      if (arr[0].trim() === styleKey) {\n        res = arr[1].trim()\n      }\n    }\n  }\n\n  return res\n}\n\n// COMPAT: This is required to prevent TypeScript aliases from doing some very\n// weird things for Slate's types with the same name as globals. (2019/11/27)\n// https://github.com/microsoft/TypeScript/issues/35002\nimport DOMNode = globalThis.Node\nimport DOMComment = globalThis.Comment\nimport DOMElement = globalThis.Element\nimport DOMText = globalThis.Text\nimport DOMRange = globalThis.Range\nimport DOMSelection = globalThis.Selection\nimport DOMStaticRange = globalThis.StaticRange\nexport { DOMNode, DOMComment, DOMElement, DOMText, DOMRange, DOMSelection, DOMStaticRange }\n", "var $ = require('../internals/export');\nvar global = require('../internals/global');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({ global: true }, {\n  globalThis: global\n});\n", "/**\n * @description render cell\n * <AUTHOR>\n */\n\nimport throttle from 'lodash.throttle'\nimport { Element as SlateElement, Transforms, Location } from 'slate'\nimport { jsx, VNode } from 'snabbdom'\nimport { IDomEditor, DomEditor } from '@wangeditor/core'\nimport { TableCellElement } from '../custom-types'\nimport { isCellInFirstRow } from '../helpers'\nimport $ from '../../utils/dom'\n\n// 拖拽列宽相关信息\nlet isMouseDownForResize = false\nlet clientXWhenMouseDown = 0\nlet cellWidthWhenMouseDown = 0\nlet cellPathWhenMouseDown: Location | null = null\nlet editorWhenMouseDown: IDomEditor | null = null\nconst $body = $('body')\n\nfunction onMouseDown(event: Event) {\n  const elem = event.target as HTMLElement\n  if (elem.tagName !== 'TH' && elem.tagName !== 'TD') return\n\n  if (elem.style.cursor !== 'col-resize') return\n  elem.style.cursor = 'auto'\n\n  event.preventDefault()\n\n  // 记录必要信息\n  isMouseDownForResize = true\n  const { clientX } = event as MouseEvent\n  clientXWhenMouseDown = clientX\n  const { width } = elem.getBoundingClientRect()\n  cellWidthWhenMouseDown = width\n\n  // 绑定事件\n  $body.on('mousemove', onMouseMove)\n  $body.on('mouseup', onMouseUp)\n}\n$body.on('mousedown', onMouseDown) // 绑定事件\n\nfunction onMouseUp(event: Event) {\n  isMouseDownForResize = false\n  editorWhenMouseDown = null\n  cellPathWhenMouseDown = null\n\n  // 解绑事件\n  $body.off('mousemove', onMouseMove)\n  $body.off('mouseup', onMouseUp)\n}\n\nconst onMouseMove = throttle(function (event: Event) {\n  if (!isMouseDownForResize) return\n  if (editorWhenMouseDown == null || cellPathWhenMouseDown == null) return\n  event.preventDefault()\n\n  const { clientX } = event as MouseEvent\n  let newWith = cellWidthWhenMouseDown + (clientX - clientXWhenMouseDown) // 计算新宽度\n  newWith = Math.floor(newWith * 100) / 100 // 保留小数点后两位\n  if (newWith < 30) newWith = 30 // 最小宽度\n\n  // 这是宽度\n  Transforms.setNodes(\n    editorWhenMouseDown,\n    { width: newWith.toString() },\n    {\n      at: cellPathWhenMouseDown,\n    }\n  )\n}, 100)\n\nfunction renderTableCell(\n  cellNode: SlateElement,\n  children: VNode[] | null,\n  editor: IDomEditor\n): VNode {\n  const isFirstRow = isCellInFirstRow(editor, cellNode as TableCellElement)\n  const { colSpan = 1, rowSpan = 1, isHeader = false } = cellNode as TableCellElement\n\n  // ------------------ 不是第一行，直接渲染 <td> ------------------\n  if (!isFirstRow) {\n    return (\n      <td colSpan={colSpan} rowSpan={rowSpan}>\n        {children}\n      </td>\n    )\n  }\n\n  // ------------------ 是第一行：1. 判断 th ；2. 拖拽列宽 ------------------\n  const Tag = isHeader ? 'th' : 'td'\n\n  const vnode = (\n    <Tag\n      colSpan={colSpan}\n      rowSpan={rowSpan}\n      style={{ borderRightWidth: '3px' }}\n      on={{\n        mousemove: throttle(function (this: VNode, event: MouseEvent) {\n          const elem = this.elm as HTMLElement\n          if (elem == null) return\n          const { left, width, top, height } = elem.getBoundingClientRect()\n          const { clientX, clientY } = event\n\n          if (isMouseDownForResize) return // 此时正在修改列宽\n\n          // 非 mousedown 状态，计算 cursor 样式\n          const matchX = clientX > left + width - 5 && clientX < left + width // X 轴，是否接近 cell 右侧？\n          const matchY = clientY > top && clientY < top + height // Y 轴，是否在 cell 之内\n          // X Y 轴都接近，则修改鼠标样式\n          if (matchX && matchY) {\n            elem.style.cursor = 'col-resize'\n            editorWhenMouseDown = editor\n            cellPathWhenMouseDown = DomEditor.findPath(editor, cellNode)\n          } else {\n            if (!isMouseDownForResize) {\n              elem.style.cursor = 'auto'\n              editorWhenMouseDown = null\n              cellPathWhenMouseDown = null\n            }\n          }\n        }, 100),\n      }}\n    >\n      {children}\n    </Tag>\n  )\n  return vnode\n}\n\nexport default renderTableCell\n", "/**\n * @description render elem\n * <AUTHOR>\n */\n\nimport renderTable from './render-table'\nimport renderTableRow from './render-row'\nimport renderTableCell from './render-cell'\n\nexport const renderTableConf = {\n  type: 'table',\n  renderElem: renderTable,\n}\n\nexport const renderTableRowConf = {\n  type: 'table-row',\n  renderElem: renderTableRow,\n}\n\nexport const renderTableCellConf = {\n  type: 'table-cell',\n  renderElem: renderTableCell,\n}\n", "/**\n * @description render table\n * <AUTHOR>\n */\n\nimport { Editor, Element as SlateElement, Range, Point, Path } from 'slate'\nimport { jsx, VNode } from 'snabbdom'\nimport { IDomEditor, DomEditor } from '@wangeditor/core'\nimport { TableElement } from '../custom-types'\nimport { getFirstRowCells } from '../helpers'\n\n/**\n * 计算 table 是否可编辑。如果选区跨域 table 和外部内容，删除，会导致 table 结构打乱。所以，有时要让 table 不可编辑\n * @param editor editor\n * @param tableElem table elem\n */\nfunction getContentEditable(editor: IDomEditor, tableElem: SlateElement): boolean {\n  if (editor.isDisabled()) return false\n\n  const { selection } = editor\n  if (selection == null) return true\n  if (Range.isCollapsed(selection)) return true\n\n  const { anchor, focus } = selection\n  const tablePath = DomEditor.findPath(editor, tableElem)\n\n  const tableStart = Editor.start(editor, tablePath)\n  const tableEnd = Editor.end(editor, tablePath)\n  const isAnchorInTable =\n    Point.compare(anchor, tableEnd) <= 0 && Point.compare(anchor, tableStart) >= 0\n  const isFocusInTable =\n    Point.compare(focus, tableEnd) <= 0 && Point.compare(focus, tableStart) >= 0\n\n  // 选区在 table 内部，且选中了同一个单元格。表格可以编辑\n  if (isAnchorInTable && isFocusInTable) {\n    if (Path.equals(anchor.path.slice(0, 3), focus.path.slice(0, 3))) {\n      return true\n    }\n  }\n\n  return false\n}\n\nfunction renderTable(elemNode: SlateElement, children: VNode[] | null, editor: IDomEditor): VNode {\n  // 是否可编辑\n  const editable = getContentEditable(editor, elemNode)\n\n  // 宽度\n  const { width = 'auto' } = elemNode as TableElement\n\n  // 是否选中\n  const selected = DomEditor.isNodeSelected(editor, elemNode)\n\n  // 第一行的 cells ，以计算列宽\n  const firstRowCells = getFirstRowCells(elemNode as TableElement)\n\n  const vnode = (\n    <div\n      className=\"table-container\"\n      data-selected={selected}\n      on={{\n        mousedown: (e: MouseEvent) => {\n          // @ts-ignore 阻止光标定位到 table 后面\n          if (e.target.tagName === 'DIV') e.preventDefault()\n\n          if (editor.isDisabled()) return\n\n          // 是否需要定位到 table 内部\n          const tablePath = DomEditor.findPath(editor, elemNode)\n          const tableStart = Editor.start(editor, tablePath)\n          const { selection } = editor\n          if (selection == null) {\n            editor.select(tableStart) // 选中 table 内部\n            return\n          }\n          const { path } = selection.anchor\n          if (path[0] === tablePath[0]) return // 当前选区，就在 table 内部\n\n          editor.select(tableStart) // 选中 table 内部\n        },\n      }}\n    >\n      <table width={width} contentEditable={editable}>\n        <colgroup>\n          {firstRowCells.map(cell => {\n            const { width = 'auto' } = cell\n            return <col width={width}></col>\n          })}\n        </colgroup>\n        <tbody>{children}</tbody>\n      </table>\n    </div>\n  )\n  return vnode\n}\n\nexport default renderTable\n", "/**\n * @description render row\n * <AUTHOR>\n */\n\nimport { Element as SlateElement } from 'slate'\nimport { jsx, VNode } from 'snabbdom'\nimport { IDomEditor } from '@wangeditor/core'\n\nfunction renderTableRow(\n  elemNode: SlateElement,\n  children: VNode[] | null,\n  editor: IDomEditor\n): VNode {\n  const vnode = <tr>{children}</tr>\n  return vnode\n}\n\nexport default renderTableRow\n", "/**\n * @description to html\n * <AUTHOR>\n */\n\nimport { Element } from 'slate'\nimport { TableCellElement, TableRowElement, TableElement } from './custom-types'\n\nfunction tableToHtml(elemNode: Element, childrenHtml: string): string {\n  const { width = 'auto' } = elemNode as TableElement\n\n  return `<table style=\"width: ${width};\"><tbody>${childrenHtml}</tbody></table>`\n}\n\nfunction tableRowToHtml(elem: Element, childrenHtml: string): string {\n  return `<tr>${childrenHtml}</tr>`\n}\n\nfunction tableCellToHtml(cellNode: Element, childrenHtml: string): string {\n  const {\n    colSpan = 1,\n    rowSpan = 1,\n    isHeader = false,\n    width = 'auto',\n  } = cellNode as TableCellElement\n  const tag = isHeader ? 'th' : 'td'\n  return `<${tag} colSpan=\"${colSpan}\" rowSpan=\"${rowSpan}\" width=\"${width}\">${childrenHtml}</${tag}>`\n}\n\nexport const tableToHtmlConf = {\n  type: 'table',\n  elemToHtml: tableToHtml,\n}\n\nexport const tableRowToHtmlConf = {\n  type: 'table-row',\n  elemToHtml: tableRowToHtml,\n}\n\nexport const tableCellToHtmlConf = {\n  type: 'table-cell',\n  elemToHtml: tableCellToHtml,\n}\n", "/**\n * @description pre parse html\n * <AUTHOR>\n */\n\nimport $, { getTagName, DOMElement } from '../utils/dom'\n\n/**\n * pre-prase table ，去掉 <tbody>\n * @param table table elem\n */\nfunction preParse(tableElem: DOMElement): DOMElement {\n  const $table = $(tableElem)\n  const tagName = getTagName($table)\n  if (tagName !== 'table') return tableElem\n\n  // 没有 <tbody> 则直接返回\n  const $tbody = $table.find('tbody')\n  if ($tbody.length === 0) return tableElem\n\n  // 去掉 <tbody> ，把 <tr> 移动到 <table> 下面\n  const $tr = $table.find('tr')\n  $table.append($tr)\n  $tbody.remove()\n\n  return $table[0]\n}\n\nexport const preParseTableHtmlConf = {\n  selector: 'table',\n  preParseHtml: preParse,\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar $filter = require('../internals/array-iteration').filter;\nvar arrayMethodHasSpeciesSupport = require('../internals/array-method-has-species-support');\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('filter');\n\n// `Array.prototype.filter` method\n// https://tc39.es/ecma262/#sec-array.prototype.filter\n// with adding support of @@species\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  filter: function filter(callbackfn /* , thisArg */) {\n    return $filter(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "var uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar floor = Math.floor;\nvar charAt = uncurryThis(''.charAt);\nvar replace = uncurryThis(''.replace);\nvar stringSlice = uncurryThis(''.slice);\nvar SUBSTITUTION_SYMBOLS = /\\$([$&'`]|\\d{1,2}|<[^>]*>)/g;\nvar SUBSTITUTION_SYMBOLS_NO_NAMED = /\\$([$&'`]|\\d{1,2})/g;\n\n// `GetSubstitution` abstract operation\n// https://tc39.es/ecma262/#sec-getsubstitution\nmodule.exports = function (matched, str, position, captures, namedCaptures, replacement) {\n  var tailPos = position + matched.length;\n  var m = captures.length;\n  var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;\n  if (namedCaptures !== undefined) {\n    namedCaptures = toObject(namedCaptures);\n    symbols = SUBSTITUTION_SYMBOLS;\n  }\n  return replace(replacement, symbols, function (match, ch) {\n    var capture;\n    switch (charAt(ch, 0)) {\n      case '$': return '$';\n      case '&': return matched;\n      case '`': return stringSlice(str, 0, position);\n      case \"'\": return stringSlice(str, tailPos);\n      case '<':\n        capture = namedCaptures[stringSlice(ch, 1, -1)];\n        break;\n      default: // \\d\\d?\n        var n = +ch;\n        if (n === 0) return match;\n        if (n > m) {\n          var f = floor(n / 10);\n          if (f === 0) return match;\n          if (f <= m) return captures[f - 1] === undefined ? charAt(ch, 1) : captures[f - 1] + charAt(ch, 1);\n          return match;\n        }\n        capture = captures[n - 1];\n    }\n    return capture === undefined ? '' : capture;\n  });\n};\n", "'use strict';\nvar apply = require('../internals/function-apply');\nvar call = require('../internals/function-call');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fixRegExpWellKnownSymbolLogic = require('../internals/fix-regexp-well-known-symbol-logic');\nvar fails = require('../internals/fails');\nvar anObject = require('../internals/an-object');\nvar isCallable = require('../internals/is-callable');\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\nvar toLength = require('../internals/to-length');\nvar toString = require('../internals/to-string');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nvar advanceStringIndex = require('../internals/advance-string-index');\nvar getMethod = require('../internals/get-method');\nvar getSubstitution = require('../internals/get-substitution');\nvar regExpExec = require('../internals/regexp-exec-abstract');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar REPLACE = wellKnownSymbol('replace');\nvar max = Math.max;\nvar min = Math.min;\nvar concat = uncurryThis([].concat);\nvar push = uncurryThis([].push);\nvar stringIndexOf = uncurryThis(''.indexOf);\nvar stringSlice = uncurryThis(''.slice);\n\nvar maybeToString = function (it) {\n  return it === undefined ? it : String(it);\n};\n\n// IE <= 11 replaces $0 with the whole match, as if it was $&\n// https://stackoverflow.com/questions/6024666/getting-ie-to-replace-a-regex-with-the-literal-string-0\nvar REPLACE_KEEPS_$0 = (function () {\n  // eslint-disable-next-line regexp/prefer-escape-replacement-dollar-char -- required for testing\n  return 'a'.replace(/./, '$0') === '$0';\n})();\n\n// Safari <= 13.0.3(?) substitutes nth capture where n>m with an empty string\nvar REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE = (function () {\n  if (/./[REPLACE]) {\n    return /./[REPLACE]('a', '$0') === '';\n  }\n  return false;\n})();\n\nvar REPLACE_SUPPORTS_NAMED_GROUPS = !fails(function () {\n  var re = /./;\n  re.exec = function () {\n    var result = [];\n    result.groups = { a: '7' };\n    return result;\n  };\n  // eslint-disable-next-line regexp/no-useless-dollar-replacements -- false positive\n  return ''.replace(re, '$<a>') !== '7';\n});\n\n// @@replace logic\nfixRegExpWellKnownSymbolLogic('replace', function (_, nativeReplace, maybeCallNative) {\n  var UNSAFE_SUBSTITUTE = REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE ? '$' : '$0';\n\n  return [\n    // `String.prototype.replace` method\n    // https://tc39.es/ecma262/#sec-string.prototype.replace\n    function replace(searchValue, replaceValue) {\n      var O = requireObjectCoercible(this);\n      var replacer = searchValue == undefined ? undefined : getMethod(searchValue, REPLACE);\n      return replacer\n        ? call(replacer, searchValue, O, replaceValue)\n        : call(nativeReplace, toString(O), searchValue, replaceValue);\n    },\n    // `RegExp.prototype[@@replace]` method\n    // https://tc39.es/ecma262/#sec-regexp.prototype-@@replace\n    function (string, replaceValue) {\n      var rx = anObject(this);\n      var S = toString(string);\n\n      if (\n        typeof replaceValue == 'string' &&\n        stringIndexOf(replaceValue, UNSAFE_SUBSTITUTE) === -1 &&\n        stringIndexOf(replaceValue, '$<') === -1\n      ) {\n        var res = maybeCallNative(nativeReplace, rx, S, replaceValue);\n        if (res.done) return res.value;\n      }\n\n      var functionalReplace = isCallable(replaceValue);\n      if (!functionalReplace) replaceValue = toString(replaceValue);\n\n      var global = rx.global;\n      if (global) {\n        var fullUnicode = rx.unicode;\n        rx.lastIndex = 0;\n      }\n      var results = [];\n      while (true) {\n        var result = regExpExec(rx, S);\n        if (result === null) break;\n\n        push(results, result);\n        if (!global) break;\n\n        var matchStr = toString(result[0]);\n        if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);\n      }\n\n      var accumulatedResult = '';\n      var nextSourcePosition = 0;\n      for (var i = 0; i < results.length; i++) {\n        result = results[i];\n\n        var matched = toString(result[0]);\n        var position = max(min(toIntegerOrInfinity(result.index), S.length), 0);\n        var captures = [];\n        // NOTE: This is equivalent to\n        //   captures = result.slice(1).map(maybeToString)\n        // but for some reason `nativeSlice.call(result, 1, result.length)` (called in\n        // the slice polyfill when slicing native arrays) \"doesn't work\" in safari 9 and\n        // causes a crash (https://pastebin.com/N21QzeQA) when trying to debug it.\n        for (var j = 1; j < result.length; j++) push(captures, maybeToString(result[j]));\n        var namedCaptures = result.groups;\n        if (functionalReplace) {\n          var replacerArgs = concat([matched], captures, position, S);\n          if (namedCaptures !== undefined) push(replacerArgs, namedCaptures);\n          var replacement = toString(apply(replaceValue, undefined, replacerArgs));\n        } else {\n          replacement = getSubstitution(matched, S, position, captures, namedCaptures, replaceValue);\n        }\n        if (position >= nextSourcePosition) {\n          accumulatedResult += stringSlice(S, nextSourcePosition, position) + replacement;\n          nextSourcePosition = position + matched.length;\n        }\n      }\n      return accumulatedResult + stringSlice(S, nextSourcePosition);\n    }\n  ];\n}, !REPLACE_SUPPORTS_NAMED_GROUPS || !REPLACE_KEEPS_$0 || REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE);\n", "/**\n * @description parse html\n * <AUTHOR>\n */\n\nimport { Descendant, Text } from 'slate'\nimport { IDomEditor, DomEditor } from '@wangeditor/core'\nimport { TableCellElement, TableRowElement, TableElement } from './custom-types'\nimport $, { getTagName, getStyleValue, DOMElement } from '../utils/dom'\n\nfunction parseCellHtml(\n  elem: DOMElement,\n  children: Descendant[],\n  editor: IDomEditor\n): TableCellElement {\n  const $elem = $(elem)\n\n  children = children.filter(child => {\n    if (Text.isText(child)) return true\n    if (editor.isInline(child)) return true\n    return false\n  })\n\n  // 无 children ，则用纯文本\n  if (children.length === 0) {\n    children = [{ text: $elem.text().replace(/\\s+/gm, ' ') }]\n  }\n\n  const colSpan = parseInt($elem.attr('colSpan') || '1')\n  const rowSpan = parseInt($elem.attr('rowSpan') || '1')\n  const width = $elem.attr('width') || 'auto'\n\n  return {\n    type: 'table-cell',\n    isHeader: getTagName($elem) === 'th',\n    colSpan,\n    rowSpan,\n    width,\n    // @ts-ignore\n    children,\n  }\n}\n\nexport const parseCellHtmlConf = {\n  selector: 'td:not([data-w-e-type]),th:not([data-w-e-type])', // data-w-e-type 属性，留给自定义元素，保证扩展性\n  parseElemHtml: parseCellHtml,\n}\n\nfunction parseRowHtml(\n  elem: DOMElement,\n  children: Descendant[],\n  editor: IDomEditor\n): TableRowElement {\n  return {\n    type: 'table-row',\n    // @ts-ignore\n    children: children.filter(child => DomEditor.getNodeType(child) === 'table-cell'),\n  }\n}\n\nexport const parseRowHtmlConf = {\n  selector: 'tr:not([data-w-e-type])', // data-w-e-type 属性，留给自定义元素，保证扩展性\n  parseElemHtml: parseRowHtml,\n}\n\nfunction parseTableHtml(\n  elem: DOMElement,\n  children: Descendant[],\n  editor: IDomEditor\n): TableElement {\n  const $elem = $(elem)\n\n  // 计算宽度\n  let width = 'auto'\n  if (getStyleValue($elem, 'width') === '100%') width = '100%'\n  if ($elem.attr('width') === '100%') width = '100%' // 兼容 v4 格式\n\n  return {\n    type: 'table',\n    width,\n    // @ts-ignore\n    children: children.filter(child => DomEditor.getNodeType(child) === 'table-row'),\n  }\n}\n\nexport const parseTableHtmlConf = {\n  selector: 'table:not([data-w-e-type])', // data-w-e-type 属性，留给自定义元素，保证扩展性\n  parseElemHtml: parseTableHtml,\n}\n", "/**\n * @description insert table menu\n * <AUTHOR>\n */\n\nimport { Editor, Transforms, Range, Node } from 'slate'\nimport { IDropPanelMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport $, { Dom7Array, DOMElement } from '../../utils/dom'\nimport { genRandomStr } from '../../utils/util'\nimport { TABLE_SVG } from '../../constants/svg'\nimport { TableElement, TableCellElement, TableRowElement } from '../custom-types'\n\nfunction genTableNode(rowNum: number, colNum: number): TableElement {\n  // 拼接 rows\n  const rows: TableRowElement[] = []\n  for (let i = 0; i < rowNum; i++) {\n    // 拼接 cells\n    const cells: TableCellElement[] = []\n    for (let j = 0; j < colNum; j++) {\n      const cellNode: TableCellElement = {\n        type: 'table-cell',\n        children: [{ text: '' }],\n      }\n      if (i === 0) {\n        cellNode.isHeader = true // 第一行默认是 th\n      }\n      cells.push(cellNode)\n    }\n\n    // 生成 row\n    rows.push({\n      type: 'table-row',\n      children: cells,\n    })\n  }\n\n  return {\n    type: 'table',\n    width: 'auto',\n    children: rows,\n  }\n}\n\n/**\n * 生成唯一的 DOM ID\n */\nfunction genDomID(): string {\n  return genRandomStr('w-e-insert-table')\n}\n\nclass InsertTable implements IDropPanelMenu {\n  title = t('tableModule.insertTable')\n  iconSvg = TABLE_SVG\n  tag = 'button'\n  showDropPanel = true // 点击 button 时显示 dropPanel\n  private $content: Dom7Array | null = null\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 插入菜单，不需要 value\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 任何时候，都不用激活 menu\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    // 点击菜单时，弹出 modal 之前，不需要执行其他代码\n    // 此处空着即可\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true // 选区非折叠，禁用\n\n    const selectedElems = DomEditor.getSelectedElems(editor)\n    const hasVoidOrPreOrTable = selectedElems.some(elem => {\n      const type = DomEditor.getNodeType(elem)\n      if (type === 'pre') return true\n      if (type === 'table') return true\n      if (type === 'list-item') return true\n      if (editor.isVoid(elem)) return true\n      return false\n    })\n    if (hasVoidOrPreOrTable) return true // 匹配到，禁用\n\n    return false\n  }\n\n  /**\n   *  获取 panel 内容\n   * @param editor editor\n   */\n  getPanelContentElem(editor: IDomEditor): DOMElement {\n    // 已有，直接返回\n    if (this.$content) return this.$content[0]\n\n    // 初始化\n    const $content = $('<div class=\"w-e-panel-content-table\"></div>')\n    const $info = $('<span>0 &times; 0</span>') // 显示行列数量\n\n    // 渲染 10 * 10 table ，以快速创建表格\n    const $table = $('<table></table>')\n    for (let i = 0; i < 10; i++) {\n      const $tr = $('<tr></tr>')\n      for (let j = 0; j < 10; j++) {\n        const $td = $('<td></td>')\n        $td.attr('data-x', j.toString())\n        $td.attr('data-y', i.toString())\n        $tr.append($td)\n\n        // 绑定 mouseenter\n        $td.on('mouseenter', (e: Event) => {\n          const { target } = e\n          if (target == null) return\n          const $focusTd = $(target)\n          const { x: focusX, y: focusY } = $focusTd.dataset()\n\n          // 显示行列数量\n          $info[0].innerHTML = `${focusX + 1} &times; ${focusY + 1}`\n\n          // 修改 table td 样式\n          $table.children().each(tr => {\n            $(tr)\n              .children()\n              .each(td => {\n                const $td = $(td)\n                const { x, y } = $td.dataset()\n                if (x <= focusX && y <= focusY) {\n                  $td.addClass('active')\n                } else {\n                  $td.removeClass('active')\n                }\n              })\n          })\n        })\n\n        // 绑定 click\n        $td.on('click', (e: Event) => {\n          e.preventDefault()\n          const { target } = e\n          if (target == null) return\n          const $td = $(target)\n          const { x, y } = $td.dataset()\n          this.insertTable(editor, y + 1, x + 1)\n        })\n      }\n      $table.append($tr)\n    }\n    $content.append($table)\n    $content.append($info)\n\n    // 记录，并返回\n    this.$content = $content\n    return $content[0]\n  }\n\n  private insertTable(editor: IDomEditor, rowNumStr: string, colNumStr: string) {\n    const rowNum = parseInt(rowNumStr, 10)\n    const colNum = parseInt(colNumStr, 10)\n    if (!rowNum || !colNum) return\n    if (rowNum <= 0 || colNum <= 0) return\n\n    // 如果当前是空 p ，则删除该 p\n    if (DomEditor.isSelectedEmptyParagraph(editor)) {\n      Transforms.removeNodes(editor, { mode: 'highest' })\n    }\n\n    // 插入表格\n    const tableNode = genTableNode(rowNum, colNum)\n    Transforms.insertNodes(editor, tableNode, { mode: 'highest' })\n  }\n}\n\nexport default InsertTable\n", "/**\n * @description icon svg\n * <AUTHOR>\n */\n\n/**\n * 【注意】svg 字符串的长度 ，否则会导致代码体积过大\n * 尽量选择 https://www.iconfont.cn/collections/detail?spm=a313x.7781069.0.da5a778a4&cid=20293\n * 找不到再从 iconfont.com 搜索\n */\n\n// 表格\nexport const TABLE_SVG =\n  '<svg viewBox=\"0 0 1024 1024\"><path d=\"M0 64v896h1024V64H0z m384 576v-192h256v192h-256z m256 64v192h-256v-192h256z m0-512v192h-256V192h256zM320 192v192H64V192h256z m-256 256h256v192H64v-192z m640 0h256v192h-256v-192z m0-64V192h256v192h-256zM64 704h256v192H64v-192z m640 192v-192h256v192h-256z\"></path></svg>'\n\n// 垃圾桶（删除）\nexport const TRASH_SVG =\n  '<svg viewBox=\"0 0 1024 1024\"><path d=\"M826.8032 356.5312c-19.328 0-36.3776 15.6928-36.3776 35.0464v524.2624c0 19.328-16 34.56-35.328 34.56H264.9344c-19.328 0-35.5072-15.3088-35.5072-34.56V390.0416c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.6928-33.5104 35.0464V915.712c0 57.9328 44.6208 108.288 102.528 108.288H755.2c57.9328 0 108.0832-50.4576 108.0832-108.288V391.4752c-0.1024-19.2512-17.1264-34.944-36.48-34.944z\" p-id=\"9577\"></path><path d=\"M437.1712 775.7568V390.6048c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.616-33.5104 35.0464v385.152c0 19.328 14.1568 35.0464 33.5104 35.0464s33.5104-15.7184 33.5104-35.0464zM649.7024 775.7568V390.6048c0-19.328-17.0496-35.0464-36.3776-35.0464s-36.3776 15.616-36.3776 35.0464v385.152c0 19.328 17.0496 35.0464 36.3776 35.0464s36.3776-15.7184 36.3776-35.0464zM965.0432 217.0368h-174.6176V145.5104c0-57.9328-47.2064-101.76-104.6528-101.76h-350.976c-57.8304 0-105.3952 43.8528-105.3952 101.76v71.5264H54.784c-19.4304 0-35.0464 14.1568-35.0464 33.5104 0 19.328 15.616 33.5104 35.0464 33.5104h910.3616c19.328 0 35.0464-14.1568 35.0464-33.5104 0-19.3536-15.6928-33.5104-35.1488-33.5104z m-247.3728 0H297.3952V145.5104c0-19.328 18.2016-34.7648 37.4272-34.7648h350.976c19.1488 0 31.872 15.1296 31.872 34.7648v71.5264z\"></path></svg>'\n\n// 表格 添加行\nexport const ADD_ROW_SVG =\n  '<svg viewBox=\"0 0 1048 1024\"><path d=\"M707.7888 521.0112h-147.456v-147.456H488.2432v147.456h-147.456v68.8128h147.456v147.456h72.0896v-147.456h147.456zM0 917.504V0h1048.576v917.504H0zM327.68 65.536H65.536v196.608H327.68V65.536z m327.68 0H393.216v196.608h262.144V65.536z m327.68 0h-262.144v196.608h262.144V65.536z m0 258.8672H65.536v462.0288H983.04V324.4032z\"></path></svg>'\n\n// 表格 删除行\nexport const DEL_ROW_SVG =\n  '<svg viewBox=\"0 0 1048 1024\"><path d=\"M907.6736 586.5472L747.1104 425.984l163.84-163.84-78.6432-78.6432-163.84 163.84L507.904 186.7776 429.2608 262.144l163.84 163.84-167.1168 167.1168 78.6432 78.6432 167.1168-167.1168 160.5632 160.5632 75.3664-78.6432zM0 917.504V0h1048.576v917.504H0z m983.04-327.68h-22.9376l-65.536-65.536H983.04V327.68h-91.7504l65.536-65.536h26.2144V65.536H65.536v196.608h317.8496l65.536 65.536H65.536v196.608h380.1088l-65.536 65.536H65.536v196.608H983.04v-196.608z\"></path></svg>'\n\n// 表格 添加列\nexport const ADD_COL_SVG =\n  '<svg viewBox=\"0 0 1048 1024\"><path d=\"M327.68 193.3312v186.7776H140.9024v91.7504H327.68v186.7776h88.4736V471.8592h190.0544V380.1088H416.1536V193.3312zM0 917.504V0h1048.576v917.504H0zM655.36 65.536H65.536v720.896H655.36V65.536z m327.68 0h-262.144v196.608h262.144V65.536z m0 262.144h-262.144v196.608h262.144V327.68z m0 262.144h-262.144v196.608h262.144v-196.608z\"></path></svg>'\n\n// 表格 删除列\nexport const DEL_COL_SVG =\n  '<svg viewBox=\"0 0 1048 1024\"><path d=\"M327.68 510.976L393.216 445.44v-13.1072L327.68 366.7968V510.976z m327.68-78.4384l65.536-65.536V507.904L655.36 442.368v-9.8304z m393.216 484.9664V0H0v917.504h1048.576z m-65.536-131.072h-262.144v-52.4288l-13.1072 13.1072-52.4288-52.4288v91.7504H393.216v-91.7504l-52.4288 52.4288-13.1072-13.1072v52.4288H65.536V65.536H327.68v121.2416l36.0448-36.0448 29.4912 29.4912V62.2592h262.144V180.224l49.152-49.152 16.384 16.384V62.2592h262.144V786.432z m-294.912-108.1344l-160.5632-160.5632-167.1168 167.1168-78.6432-78.6432 167.1168-167.1168L288.3584 278.528l78.6432-78.6432 160.5632 160.5632 163.84-163.84 78.6432 78.6432-163.84 163.84 160.5632 160.5632-78.6432 78.6432z\"></path></svg>'\n\n// 表头\nexport const TABLE_HEADER_SVG =\n  '<svg viewBox=\"0 0 1024 1024\"><path d=\"M704 128l-64 0L384 128 320 128 0 128l0 256 0 64 0 192 0 64 0 256 320 0 64 0 256 0 64 0 320 0 0-256 0-64L1024 448 1024 384 1024 128 704 128zM640 640 384 640 384 448l256 0L640 640zM64 448l256 0 0 192L64 640 64 448zM320 896 64 896l0-192 256 0L320 896zM640 896 384 896l0-192 256 0L640 896zM960 896l-256 0 0-192 256 0L960 896zM960 640l-256 0L704 448l256 0L960 640z\"></path></svg>'\n\n// 宽度\nexport const FULL_WIDTH_SVG =\n  '<svg viewBox=\"0 0 1228 1024\"><path d=\"M862.514337 563.200461H404.581995v121.753478a13.311987 13.311987 0 0 1-6.655993 11.468789 10.23999 10.23999 0 0 1-12.083188-1.433599l-204.799795-179.199821a13.721586 13.721586 0 0 1 0-20.479979l204.799795-179.302221a10.23999 10.23999 0 0 1 12.185588-1.535998 13.209587 13.209587 0 0 1 6.553593 11.673588v115.097485h457.932342V319.693504a11.571188 11.571188 0 0 1 18.841582-10.239989l204.799795 179.19982a13.721586 13.721586 0 0 1 0 20.47998l-204.799795 179.199821a10.23999 10.23999 0 0 1-12.185588 1.535998 13.311987 13.311987 0 0 1-6.655994-11.571188V563.200461zM136.499064 14.951409v993.893406a15.257585 15.257585 0 0 1-15.155185 15.052785H15.155185A15.155185 15.155185 0 0 1 0 1008.844815V14.951409a15.257585 15.257585 0 0 1 15.155185-15.052785h106.086294a15.155185 15.155185 0 0 1 15.257585 15.155185zM1228.798771 14.951409v993.893406a15.257585 15.257585 0 0 1-15.155185 15.052785h-106.188693a15.155185 15.155185 0 0 1-15.155185-15.052785V14.951409a15.257585 15.257585 0 0 1 15.155185-15.052785h106.086293A15.155185 15.155185 0 0 1 1228.798771 15.053809z\"></path></svg>'\n", "/**\n * @description del table menu\n * <AUTHOR>\n */\n\nimport { Transforms } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { TRASH_SVG } from '../../constants/svg'\n\nclass DeleteTable implements IButtonMenu {\n  readonly title = t('tableModule.deleteTable')\n  readonly iconSvg = TRASH_SVG\n  readonly tag = 'button'\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 无需获取 val\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 无需 active\n    return false\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    if (editor.selection == null) return true\n\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table')\n    if (tableNode == null) {\n      // 选区未处于 table node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    // 删除表格\n    Transforms.removeNodes(editor, { mode: 'highest' })\n  }\n}\n\nexport default DeleteTable\n", "/**\n * @description insert row menu\n * <AUTHOR>\n */\n\nimport { Editor, Transforms, Range, Path } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { ADD_ROW_SVG } from '../../constants/svg'\nimport { TableRowElement, TableCellElement } from '../custom-types'\n\nclass InsertRow implements IButtonMenu {\n  readonly title = t('tableModule.insertRow')\n  readonly iconSvg = ADD_ROW_SVG\n  readonly tag = 'button'\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 无需获取 val\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 无需 active\n    return false\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true\n\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table')\n    if (tableNode == null) {\n      // 选区未处于 table cell node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    const [cellEntry] = Editor.nodes(editor, {\n      match: n => DomEditor.checkNodeType(n, 'table-cell'),\n      universal: true,\n    })\n    const [cellNode, cellPath] = cellEntry\n\n    // 获取 cell length ，即多少列\n    const rowNode = DomEditor.getParentNode(editor, cellNode)\n    const cellsLength = rowNode?.children.length || 0\n    if (cellsLength === 0) return\n\n    // 拼接新的 row\n    const newRow: TableRowElement = { type: 'table-row', children: [] }\n    for (let i = 0; i < cellsLength; i++) {\n      const cell: TableCellElement = {\n        type: 'table-cell',\n        children: [{ text: '' }],\n      }\n      newRow.children.push(cell)\n    }\n\n    // 插入 row\n    const rowPath = Path.parent(cellPath) // 获取 tr 的 path\n    const newRowPath = Path.next(rowPath)\n    Transforms.insertNodes(editor, newRow, { at: newRowPath })\n  }\n}\n\nexport default InsertRow\n", "/**\n * @description del row menu\n * <AUTHOR>\n */\n\nimport { Editor, Transforms, Range } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { DEL_ROW_SVG } from '../../constants/svg'\n\nclass DeleteRow implements IButtonMenu {\n  readonly title = t('tableModule.deleteRow')\n  readonly iconSvg = DEL_ROW_SVG\n  readonly tag = 'button'\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 无需获取 val\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 无需 active\n    return false\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true\n\n    const rowNode = DomEditor.getSelectedNodeByType(editor, 'table-row')\n    if (rowNode == null) {\n      // 选区未处于 table row node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    const [rowEntry] = Editor.nodes(editor, {\n      match: n => DomEditor.checkNodeType(n, 'table-row'),\n      universal: true,\n    })\n    const [rowNode, rowPath] = rowEntry\n\n    const tableNode = DomEditor.getParentNode(editor, rowNode)\n    const rowsLength = tableNode?.children.length || 0\n    if (rowsLength <= 1) {\n      // row 只有一行，则删掉整个表格\n      Transforms.removeNodes(editor, { mode: 'highest' })\n      return\n    }\n\n    // row > 1 行，则删掉这一行\n    Transforms.removeNodes(editor, { at: rowPath })\n  }\n}\n\nexport default DeleteRow\n", "// iterable DOM collections\n// flag - `iterable` interface - 'entries', 'keys', 'values', 'forEach' methods\nmodule.exports = {\n  CSSRuleList: 0,\n  CSSStyleDeclaration: 0,\n  CSSValueList: 0,\n  ClientRectList: 0,\n  DOMRectList: 0,\n  DOMStringList: 0,\n  DOMTokenList: 1,\n  DataTransferItemList: 0,\n  FileList: 0,\n  HTMLAllCollection: 0,\n  HTMLCollection: 0,\n  HTMLFormElement: 0,\n  HTMLSelectElement: 0,\n  MediaList: 0,\n  MimeTypeArray: 0,\n  NamedNodeMap: 0,\n  NodeList: 1,\n  PaintRequestList: 0,\n  Plugin: 0,\n  PluginArray: 0,\n  SVGLengthList: 0,\n  SVGNumberList: 0,\n  SVGPathSegList: 0,\n  SVGPointList: 0,\n  SVGStringList: 0,\n  SVGTransformList: 0,\n  SourceBufferList: 0,\n  StyleSheetList: 0,\n  TextTrackCueList: 0,\n  TextTrackList: 0,\n  TouchList: 0\n};\n", "// in old WebKit versions, `element.classList` is not an instance of global `DOMTokenList`\nvar documentCreateElement = require('../internals/document-create-element');\n\nvar classList = documentCreateElement('span').classList;\nvar DOMTokenListPrototype = classList && classList.constructor && classList.constructor.prototype;\n\nmodule.exports = DOMTokenListPrototype === Object.prototype ? undefined : DOMTokenListPrototype;\n", "'use strict';\nvar $forEach = require('../internals/array-iteration').forEach;\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\n\nvar STRICT_METHOD = arrayMethodIsStrict('forEach');\n\n// `Array.prototype.forEach` method implementation\n// https://tc39.es/ecma262/#sec-array.prototype.foreach\nmodule.exports = !STRICT_METHOD ? function forEach(callbackfn /* , thisArg */) {\n  return $forEach(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n// eslint-disable-next-line es/no-array-prototype-foreach -- safe\n} : [].forEach;\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = function (METHOD_NAME, argument) {\n  var method = [][METHOD_NAME];\n  return !!method && fails(function () {\n    // eslint-disable-next-line no-useless-call,no-throw-literal -- required for testing\n    method.call(null, argument || function () { throw 1; }, 1);\n  });\n};\n", "var global = require('../internals/global');\nvar DOMIterables = require('../internals/dom-iterables');\nvar DOMTokenListPrototype = require('../internals/dom-token-list-prototype');\nvar forEach = require('../internals/array-for-each');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\nvar handlePrototype = function (CollectionPrototype) {\n  // some Chrome versions have non-configurable methods on DOMTokenList\n  if (CollectionPrototype && CollectionPrototype.forEach !== forEach) try {\n    createNonEnumerableProperty(CollectionPrototype, 'forEach', forEach);\n  } catch (error) {\n    CollectionPrototype.forEach = forEach;\n  }\n};\n\nfor (var COLLECTION_NAME in DOMIterables) {\n  if (DOMIterables[COLLECTION_NAME]) {\n    handlePrototype(global[COLLECTION_NAME] && global[COLLECTION_NAME].prototype);\n  }\n}\n\nhandlePrototype(DOMTokenListPrototype);\n", "/**\n * @description insert col menu\n * <AUTHOR>\n */\n\nimport isEqual from 'lodash.isequal'\nimport { Editor, Element, Transforms, Range, Node } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { ADD_COL_SVG } from '../../constants/svg'\nimport { TableCellElement, TableElement } from '../custom-types'\nimport { isTableWithHeader } from '../helpers'\n\nclass InsertCol implements IButtonMenu {\n  readonly title = t('tableModule.insertCol')\n  readonly iconSvg = ADD_COL_SVG\n  readonly tag = 'button'\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 无需获取 val\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 无需 active\n    return false\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true\n\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table')\n    if (tableNode == null) {\n      // 选区未处于 table cell node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    const [cellEntry] = Editor.nodes(editor, {\n      match: n => DomEditor.checkNodeType(n, 'table-cell'),\n      universal: true,\n    })\n    const [selectedCellNode, selectedCellPath] = cellEntry\n\n    const rowNode = DomEditor.getParentNode(editor, selectedCellNode)\n    if (rowNode == null) return\n    const tableNode = DomEditor.getParentNode(editor, rowNode) as TableElement\n    if (tableNode == null) return\n\n    // 遍历所有 rows ，挨个添加 cell\n    const rows = tableNode.children || []\n    rows.forEach((row, rowIndex) => {\n      if (!Element.isElement(row)) return\n\n      const cells = row.children || []\n      // 遍历一个 row 的所有 cells\n      cells.forEach((cell: Node) => {\n        const path = DomEditor.findPath(editor, cell)\n        if (\n          path.length === selectedCellPath.length &&\n          isEqual(path.slice(-1), selectedCellPath.slice(-1)) // 俩数组，最后一位相同\n        ) {\n          // 如果当前 td 的 path 和选中 td 的 path ，最后一位相同，说明是同一列\n          // 则在其后插入一个 cell\n          const newCell: TableCellElement = { type: 'table-cell', children: [{ text: '' }] }\n          if (rowIndex === 0 && isTableWithHeader(tableNode)) {\n            newCell.isHeader = true\n          }\n          Transforms.insertNodes(editor, newCell, { at: path })\n        }\n      })\n    })\n  }\n}\n\nexport default InsertCol\n", "/**\n * @description del col menu\n * <AUTHOR>\n */\n\nimport isEqual from 'lodash.isequal'\nimport { Editor, Element, Transforms, Range, Node } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { DEL_COL_SVG } from '../../constants/svg'\n\nclass DeleteCol implements IButtonMenu {\n  readonly title = t('tableModule.deleteCol')\n  readonly iconSvg = DEL_COL_SVG\n  readonly tag = 'button'\n\n  getValue(editor: IDomEditor): string | boolean {\n    // 无需获取 val\n    return ''\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    // 无需 active\n    return false\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true\n\n    const cellNode = DomEditor.getSelectedNodeByType(editor, 'table-cell')\n    if (cellNode == null) {\n      // 选区未处于 table cell node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    const [cellEntry] = Editor.nodes(editor, {\n      match: n => DomEditor.checkNodeType(n, 'table-cell'),\n      universal: true,\n    })\n    const [selectedCellNode, selectedCellPath] = cellEntry\n\n    // 如果只有一列，则删除整个表格\n    const rowNode = DomEditor.getParentNode(editor, selectedCellNode)\n    const colLength = rowNode?.children.length || 0\n    if (!rowNode || colLength <= 1) {\n      Transforms.removeNodes(editor, { mode: 'highest' }) // 删除整个表格\n      return\n    }\n\n    // ------------------------- 不只有 1 列，则继续 -------------------------\n\n    const tableNode = DomEditor.getParentNode(editor, rowNode)\n    if (tableNode == null) return\n\n    // 遍历所有 rows ，挨个删除 cell\n    const rows = tableNode.children || []\n    rows.forEach(row => {\n      if (!Element.isElement(row)) return\n\n      const cells = row.children || []\n      // 遍历一个 row 的所有 cells\n      cells.forEach((cell: Node) => {\n        const path = DomEditor.findPath(editor, cell)\n        if (\n          path.length === selectedCellPath.length &&\n          isEqual(path.slice(-1), selectedCellPath.slice(-1)) // 俩数组，最后一位相同\n        ) {\n          // 如果当前 td 的 path 和选中 td 的 path ，最后一位相同，说明是同一列\n          // 删除当前的 cell\n          Transforms.removeNodes(editor, { at: path })\n        }\n      })\n    })\n  }\n}\n\nexport default DeleteCol\n", "/**\n * @description table header menu\n * <AUTHOR>\n */\n\nimport { Transforms, Range } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { TABLE_HEADER_SVG } from '../../constants/svg'\nimport { TableElement } from '../custom-types'\nimport { getFirstRowCells, isTableWithHeader } from '../helpers'\n\nclass TableHeader implements IButtonMenu {\n  readonly title = t('tableModule.header')\n  readonly iconSvg = TABLE_HEADER_SVG\n  readonly tag = 'button'\n\n  // 是否已设置表头\n  getValue(editor: IDomEditor): string | boolean {\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table') as TableElement\n    if (tableNode == null) return false\n\n    return isTableWithHeader(tableNode)\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    return !!this.getValue(editor)\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true\n\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table')\n    if (tableNode == null) {\n      // 选区未处于 table node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    // 已经设置了表头，则取消。未设置表头，则设置\n    const newValue = value ? false : true\n\n    // 获取第一行所有 cell\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table') as TableElement\n    if (tableNode == null) return\n    const firstRowCells = getFirstRowCells(tableNode)\n\n    // 设置 isHeader 属性\n    firstRowCells.forEach(cell =>\n      Transforms.setNodes(\n        editor,\n        { isHeader: newValue },\n        {\n          at: DomEditor.findPath(editor, cell),\n        }\n      )\n    )\n  }\n}\n\nexport default TableHeader\n", "/**\n * @description table full width menu\n * <AUTHOR>\n */\n\nimport { Transforms, Range } from 'slate'\nimport { IButtonMenu, IDomEditor, DomEditor, t } from '@wangeditor/core'\nimport { FULL_WIDTH_SVG } from '../../constants/svg'\nimport { TableElement } from '../custom-types'\n\nclass TableFullWidth implements IButtonMenu {\n  readonly title = t('tableModule.widthAuto')\n  readonly iconSvg = FULL_WIDTH_SVG\n  readonly tag = 'button'\n\n  // 是否已设置 宽度自适应\n  getValue(editor: IDomEditor): string | boolean {\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table')\n    if (tableNode == null) return false\n    return (tableNode as TableElement).width === '100%'\n  }\n\n  isActive(editor: IDomEditor): boolean {\n    return !!this.getValue(editor)\n  }\n\n  isDisabled(editor: IDomEditor): boolean {\n    const { selection } = editor\n    if (selection == null) return true\n    if (!Range.isCollapsed(selection)) return true\n\n    const tableNode = DomEditor.getSelectedNodeByType(editor, 'table')\n    if (tableNode == null) {\n      // 选区未处于 table node ，则禁用\n      return true\n    }\n    return false\n  }\n\n  exec(editor: IDomEditor, value: string | boolean) {\n    if (this.isDisabled(editor)) return\n\n    const props: Partial<TableElement> = {\n      width: value ? 'auto' : '100%', // 切换 'auto' 和 '100%'\n    }\n    Transforms.setNodes(editor, props, { mode: 'highest' })\n  }\n}\n\nexport default TableFullWidth\n", "/**\n * @description table module\n * <AUTHOR>\n */\n\nimport { IModuleConf } from '@wangeditor/core'\nimport withTable from './plugin'\nimport { renderTableConf, renderTableRowConf, renderTableCellConf } from './render-elem/index'\nimport { tableToHtmlConf, tableRowToHtmlConf, tableCellToHtmlConf } from './elem-to-html'\nimport { preParseTableHtmlConf } from './pre-parse-html'\nimport { parseCellHtmlConf, parseRowHtmlConf, parseTableHtmlConf } from './parse-elem-html'\nimport {\n  insertTableMenuConf,\n  deleteTableMenuConf,\n  insertTableRowConf,\n  deleteTableRowConf,\n  insertTableColConf,\n  deleteTableColConf,\n  tableHeaderMenuConf,\n  tableFullWidthMenuConf,\n} from './menu/index'\n\nconst table: Partial<IModuleConf> = {\n  renderElems: [renderTableConf, renderTableRowConf, renderTableCellConf],\n  elemsToHtml: [tableToHtmlConf, tableRowToHtmlConf, tableCellToHtmlConf],\n  preParseHtml: [preParseTableHtmlConf],\n  parseElemsHtml: [parseCellHtmlConf, parseRowHtmlConf, parseTableHtmlConf],\n  menus: [\n    insertTableMenuConf,\n    deleteTableMenuConf,\n    insertTableRowConf,\n    deleteTableRowConf,\n    insertTableColConf,\n    deleteTableColConf,\n    tableHeaderMenuConf,\n    tableFullWidthMenuConf,\n  ],\n  editorPlugin: withTable,\n}\n\nexport default table\n", "/**\n * @description table menu\n * <AUTHOR>\n */\n\nimport InsertTable from './InsertTable'\nimport DeleteTable from './DeleteTable'\nimport InsertRow from './InsertRow'\nimport DeleteRow from './DeleteRow'\nimport InsertCol from './InsertCol'\nimport DeleteCol from './DeleteCol'\nimport TableHander from './TableHeader'\nimport FullWidth from './FullWidth'\n\nexport const insertTableMenuConf = {\n  key: 'insertTable',\n  factory() {\n    return new InsertTable()\n  },\n}\n\nexport const deleteTableMenuConf = {\n  key: 'deleteTable',\n  factory() {\n    return new DeleteTable()\n  },\n}\n\nexport const insertTableRowConf = {\n  key: 'insertTableRow',\n  factory() {\n    return new InsertRow()\n  },\n}\n\nexport const deleteTableRowConf = {\n  key: 'deleteTableRow',\n  factory() {\n    return new DeleteRow()\n  },\n}\n\nexport const insertTableColConf = {\n  key: 'insertTableCol',\n  factory() {\n    return new InsertCol()\n  },\n}\n\nexport const deleteTableColConf = {\n  key: 'deleteTableCol',\n  factory() {\n    return new DeleteCol()\n  },\n}\n\nexport const tableHeaderMenuConf = {\n  key: 'tableHeader',\n  factory() {\n    return new TableHander()\n  },\n}\n\nexport const tableFullWidthMenuConf = {\n  key: 'tableFullWidth',\n  factory() {\n    return new FullWidth()\n  },\n}\n"], "names": ["tableModule", "deleteCol", "deleteRow", "deleteTable", "widthAuto", "insertCol", "insertRow", "insertTable", "header", "match", "version", "check", "it", "Math", "globalThis", "window", "self", "global", "this", "Function", "exec", "error", "fails", "Object", "defineProperty", "get", "call", "prototype", "bind", "apply", "arguments", "$propertyIsEnumerable", "propertyIsEnumerable", "getOwnPropertyDescriptor", "V", "descriptor", "enumerable", "bitmap", "value", "configurable", "writable", "FunctionPrototype", "callBind", "fn", "toString", "uncurryThis", "stringSlice", "slice", "split", "classof", "TypeError", "undefined", "IndexedObject", "requireObjectCoercible", "argument", "isCallable", "aFunction", "namespace", "method", "length", "isPrototypeOf", "getBuiltIn", "process", "<PERSON><PERSON>", "versions", "v8", "userAgent", "getOwnPropertySymbols", "symbol", "Symbol", "String", "sham", "V8_VERSION", "NATIVE_SYMBOL", "iterator", "USE_SYMBOL_AS_UID", "$Symbol", "tryToString", "P", "func", "aCallable", "key", "SHARED", "setGlobal", "module", "store", "push", "mode", "copyright", "hasOwnProperty", "hasOwn", "toObject", "id", "postfix", "random", "WellKnownSymbolsStore", "shared", "symbolFor", "createWellKnownSymbol", "withoutSetter", "uid", "name", "description", "TO_PRIMITIVE", "wellKnownSymbol", "input", "pref", "isObject", "isSymbol", "result", "exoticToPrim", "getMethod", "val", "valueOf", "ordinaryToPrimitive", "toPrimitive", "document", "EXISTS", "createElement", "DESCRIPTORS", "a", "$getOwnPropertyDescriptor", "O", "toIndexedObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IE8_DOM_DEFINE", "createPropertyDescriptor", "propertyIsEnumerableModule", "f", "$defineProperty", "Attributes", "anObject", "object", "definePropertyModule", "functionToString", "inspectSource", "set", "has", "WeakMap", "test", "keys", "OBJECT_ALREADY_INITIALIZED", "NATIVE_WEAK_MAP", "state", "wmget", "wmhas", "wmset", "metadata", "facade", "STATE", "sharedKey", "hiddenKeys", "createNonEnumerableProperty", "enforce", "getter<PERSON>or", "TYPE", "type", "getDescriptor", "PROPER", "CONFIGURABLE", "CONFIGURABLE_FUNCTION_NAME", "require$$0", "getInternalState", "InternalStateModule", "enforceInternalState", "TEMPLATE", "options", "unsafe", "simple", "noTargetGet", "replace", "source", "join", "ceil", "floor", "number", "max", "min", "index", "integer", "toIntegerOrInfinity", "obj", "to<PERSON><PERSON><PERSON>", "createMethod", "IS_INCLUDES", "$this", "el", "fromIndex", "lengthOfArrayLike", "toAbsoluteIndex", "indexOf", "includes", "names", "i", "enumBugKeys", "concat", "getOwnPropertyNames", "internalObjectKeys", "getOwnPropertyNamesModule", "getOwnPropertySymbolsModule", "target", "ownKeys", "getOwnPropertyDescriptorModule", "replacement", "isForced", "feature", "detection", "data", "normalize", "POLYFILL", "NATIVE", "string", "toLowerCase", "targetProperty", "sourceProperty", "TARGET", "GLOBAL", "STATIC", "stat", "forced", "copyConstructorProperties", "redefine", "METHOD_NAME", "TO_STRING_TAG", "CORRECT_ARGUMENTS", "classofRaw", "TO_STRING_TAG_SUPPORT", "tag", "tryGet", "callee", "quot", "proto", "anchor", "attribute", "S", "p1", "activeXDocument", "that", "ignoreCase", "multiline", "dotAll", "unicode", "sticky", "$RegExp", "RegExp", "UNSUPPORTED_Y", "re", "lastIndex", "MISSED_STICKY", "BROKEN_CARET", "defineProperties", "Properties", "props", "objectKeys", "IE_PROTO", "EmptyConstructor", "scriptTag", "content", "LT", "NullProtoObjectViaActiveX", "write", "close", "temp", "parentWindow", "NullProtoObject", "ActiveXObject", "iframeDocument", "iframe", "domain", "documentCreateElement", "style", "display", "html", "append<PERSON><PERSON><PERSON>", "src", "contentWindow", "open", "F", "re1", "re2", "create", "flags", "groups", "nativeReplace", "nativeExec", "patchedExec", "char<PERSON>t", "UPDATES_LAST_INDEX_WRONG", "stickyHelpers", "NPCG_INCLUDED", "UNSUPPORTED_DOT_ALL", "UNSUPPORTED_NCG", "reCopy", "group", "str", "raw", "regexpFlags", "charsAdded", "strCopy", "Array", "isArray", "noop", "empty", "construct", "constructorRegExp", "INCORRECT_TO_STRING", "isConstructorModern", "called", "propertyKey", "SPECIES", "array", "constructor", "foo", "Boolean", "HAS_SPECIES_SUPPORT", "arrayMethodHasSpeciesSupport", "delete<PERSON><PERSON><PERSON>", "newEditor", "selection", "cellNodeEntry", "__read", "Editor", "nodes", "n", "Dom<PERSON><PERSON>or", "checkNodeType", "cellPath", "start", "Point", "equals", "isTableLocation", "editor", "location", "tables", "at", "getNodeType", "hasTable", "tables_1", "__values", "end", "<PERSON><PERSON><PERSON><PERSON>", "k", "fin", "isConstructor", "un$Slice", "createProperty", "originalArray", "C", "IS_MAP", "IS_FILTER", "IS_SOME", "IS_EVERY", "IS_FIND_INDEX", "IS_FILTER_REJECT", "NO_HOLES", "callbackfn", "specificCreate", "boundFunction", "arraySpeciesCreate", "for<PERSON>ach", "map", "filter", "some", "every", "find", "findIndex", "filterReject", "$map", "getFirstRowCells", "tableNode", "rows", "children", "isTableWithHeader", "cell", "<PERSON><PERSON><PERSON><PERSON>", "PROPER_FUNCTION_NAME", "TO_STRING", "RegExpPrototype", "n$ToString", "getFlags", "regExpFlags", "NOT_GENERIC", "INCORRECT_NAME", "R", "p", "$toString", "rf", "UNSCOPABLES", "ArrayPrototype", "$find", "FIND", "SKIPS_HOLES", "Reflect", "KEY", "FORCED", "SHAM", "SYMBOL", "DELEGATES_TO_SYMBOL", "DELEGATES_TO_EXEC", "execCalled", "uncurriedNativeRegExpMethod", "methods", "nativeMethod", "regexp", "arg2", "forceStringMethod", "uncurriedNativeMethod", "$exec", "regexpExec", "done", "MATCH", "defaultConstructor", "aConstructor", "charCodeAt", "CONVERT_TO_STRING", "pos", "first", "second", "position", "size", "codeAt", "MAX_UINT32", "$push", "SPLIT_WORKS_WITH_OVERWRITTEN_EXEC", "originalExec", "SPLIT", "nativeSplit", "maybeCallNative", "internalSplit", "separator", "limit", "isRegExp", "lim", "last<PERSON><PERSON><PERSON>", "output", "lastLastIndex", "separatorCopy", "arraySlice", "splitter", "rx", "res", "speciesConstructor", "unicodeMatching", "callRegExpExec", "q", "A", "e", "z", "advanceStringIndex", "whitespace", "whitespaces", "ltrim", "rtrim", "trim", "$trim", "getTagName", "$elem", "tagName", "forcedStringTrimMethod", "append", "$", "on", "focus", "attr", "dataset", "addClass", "removeClass", "each", "isMouseDownForResize", "clientXWhenMouseDown", "cellWidthWhenMouseDown", "cellPathWhenMouseDown", "editorWhenMouseDown", "$body", "onMouseUp", "event", "off", "onMouseMove", "elem", "cursor", "preventDefault", "clientX", "width", "getBoundingClientRect", "throttle", "newWith", "Transforms", "setNodes", "renderTableConf", "renderElem", "elemNode", "editable", "tableElem", "isDisabled", "Range", "isCollapsed", "tablePath", "<PERSON><PERSON><PERSON>", "tableStart", "tableEnd", "isAnchorInTable", "compare", "isFocusInTable", "Path", "path", "getContentEditable", "_a", "selected", "isNodeSelected", "firstRowCells", "vnode", "jsx", "className", "mousedown", "select", "contentEditable", "renderTableRowConf", "renderTableCellConf", "cellNode", "isFirstRow", "rowNode", "getParentNode", "c", "isCellInFirstRow", "_b", "colSpan", "_c", "rowSpan", "_d", "Tag", "borderRightWidth", "mousemove", "elm", "left", "top", "height", "clientY", "tableToHtmlConf", "elemToHtml", "childrenHtml", "tableRowToHtmlConf", "tableCellToHtmlConf", "_e", "preParseTableHtmlConf", "selector", "preParseHtml", "$table", "$tbody", "$tr", "remove", "$filter", "SUBSTITUTION_SYMBOLS", "SUBSTITUTION_SYMBOLS_NO_NAMED", "matched", "captures", "namedCaptures", "tailPos", "m", "symbols", "ch", "capture", "REPLACE", "stringIndexOf", "REPLACE_KEEPS_$0", "REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE", "_", "UNSAFE_SUBSTITUTE", "searchValue", "replaceValue", "replacer", "functionalReplace", "fullUnicode", "results", "regExpExec", "accumulatedResult", "nextSourcePosition", "j", "replacer<PERSON><PERSON><PERSON>", "getSubstitution", "parseCellHtmlConf", "parseElemHtml", "child", "Text", "isText", "isInline", "text", "parseInt", "parseRowHtmlConf", "parseTableHtmlConf", "styleKey", "styleArr", "styleItemStr", "arr", "getStyleValue", "t", "InsertTable", "getSelectedElems", "isVoid", "$content", "$info", "$td", "focusX", "focusY", "innerHTML", "tr", "td", "x", "y", "_this", "rowNumStr", "colNumStr", "row<PERSON>um", "colNum", "isSelectedEmptyParagraph", "removeNodes", "cells", "genTableNode", "insertNodes", "DeleteTable", "getSelectedNodeByType", "InsertRow", "universal", "cells<PERSON>ength", "newRow", "row<PERSON><PERSON>", "parent", "newRowPath", "next", "DeleteRow", "CSSRuleList", "CSSStyleDeclaration", "CSSValueList", "ClientRectList", "DOMRectList", "DOMStringList", "DOMTokenList", "DataTransferItemList", "FileList", "HTMLAllCollection", "HTMLCollection", "HTMLFormElement", "HTMLSelectElement", "MediaList", "MimeTypeArray", "NamedNodeMap", "NodeList", "PaintRequestList", "Plugin", "PluginArray", "SVGLengthList", "SVGNumberList", "SVGPathSegList", "SVGPointList", "SVGStringList", "SVGTransformList", "SourceBufferList", "StyleSheetList", "TextTrackCueList", "TextTrackList", "TouchList", "classList", "DOMTokenListPrototype", "$forEach", "STRICT_METHOD", "arrayMethodIsStrict", "handlePrototype", "CollectionPrototype", "COLLECTION_NAME", "DOMIterables", "InsertCol", "selectedCellNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "row", "rowIndex", "Element", "isElement", "isEqual", "newCell", "DeleteCol", "co<PERSON><PERSON><PERSON><PERSON>", "TableHeader", "getValue", "newValue", "TableFull<PERSON>idth", "table", "renderElems", "elemsToHtml", "parseElemsHtml", "menus", "factory", "TableHander", "FullWidth", "editor<PERSON><PERSON><PERSON>", "insertBreak", "deleteBackward", "deleteForward", "normalizeNode", "insertData", "handleTab", "selectAll", "insertText", "unit", "before", "isTableOnBeforeLocation", "isTableOnCurSelection", "above", "topLevelNodes", "topLevelNodesLength", "genEmptyParagraph", "node", "isLastNode", "getData", "Node", "newSelection"], "mappings": "gqBASiB,KCJF,CACbA,YAAa,CACXC,UAAW,gBACXC,UAAW,aACXC,YAAa,eACbC,UAAW,aACXC,UAAW,gBACXC,UAAW,aACXC,YAAa,eACbC,OAAQ,+BDJK,QELF,CACbR,YAAa,CACXC,UAAW,MACXC,UAAW,MACXC,YAAa,OACbC,UAAW,QACXC,UAAW,MACXC,UAAW,MACXC,YAAa,OACbC,OAAQ,8NCdZ,ICOIC,EAAOC,EDPPC,EAAQ,SAAUC,GACpB,OAAOA,GAAMA,EAAGC,MAAQA,MAAQD,KAMhCD,EAA2B,iBAAdG,YAA0BA,aACvCH,EAAuB,iBAAVI,QAAsBA,SAEnCJ,EAAqB,iBAARK,MAAoBA,OACjCL,EAAuB,iBAAVM,GAAsBA,IAEnC,WAAe,OAAOC,KAAtB,IAAoCC,SAAS,cAATA,KEbrB,SAAUC,GACzB,IACE,QAASA,IACT,MAAOC,GACP,OAAO,OCDOC,GAAM,WAEtB,OAA8E,GAAvEC,OAAOC,eAAe,GAAI,EAAG,CAAEC,IAAK,WAAc,OAAO,KAAQ,MCLtEC,EAAOP,SAASQ,UAAUD,OAEbA,EAAKE,KAAOF,EAAKE,KAAKF,GAAQ,WAC7C,OAAOA,EAAKG,MAAMH,EAAMI,YCFtBC,EAAwB,GAAGC,qBAE3BC,EAA2BV,OAAOU,8BAGpBA,IAA6BF,EAAsBL,KAAK,CAAE,EAAG,GAAK,GAI1D,SAA8BQ,GACtD,IAAIC,EAAaF,EAAyBf,KAAMgB,GAChD,QAASC,GAAcA,EAAWC,YAChCL,KCba,SAAUM,EAAQC,GACjC,MAAO,CACLF,aAAuB,EAATC,GACdE,eAAyB,EAATF,GAChBG,WAAqB,EAATH,GACZC,MAAOA,ICLPG,EAAoBtB,SAASQ,UAC7BC,EAAOa,EAAkBb,KACzBF,EAAOe,EAAkBf,KACzBgB,EAAWd,GAAQA,EAAKA,KAAKF,KAEhBE,EAAO,SAAUe,GAChC,OAAOA,GAAMD,EAAShB,EAAMiB,IAC1B,SAAUA,GACZ,OAAOA,GAAM,WACX,OAAOjB,EAAKG,MAAMc,EAAIb,aCPtBc,EAAWC,EAAY,GAAGD,UAC1BE,EAAcD,EAAY,GAAGE,SAEhB,SAAUnC,GACzB,OAAOkC,EAAYF,EAAShC,GAAK,GAAI,ICDnCW,EAASN,EAAOM,OAChByB,EAAQH,EAAY,GAAGG,SAGV1B,GAAM,WAGrB,OAAQC,EAAO,KAAKS,qBAAqB,MACtC,SAAUpB,GACb,MAAsB,UAAfqC,EAAQrC,GAAkBoC,EAAMpC,EAAI,IAAMW,EAAOX,IACtDW,ECbA2B,EAAYjC,EAAOiC,YAIN,SAAUtC,GACzB,GAAUuC,MAANvC,EAAiB,MAAMsC,EAAU,wBAA0BtC,GAC/D,OAAOA,KCJQ,SAAUA,GACzB,OAAOwC,EAAcC,EAAuBzC,OCH7B,SAAU0C,GACzB,MAA0B,mBAAZA,KCDC,SAAU1C,GACzB,MAAoB,iBAANA,EAAwB,OAAPA,EAAc2C,EAAW3C,ICAtD4C,EAAY,SAAUF,GACxB,OAAOC,EAAWD,GAAYA,OAAWH,KAG1B,SAAUM,EAAWC,GACpC,OAAO5B,UAAU6B,OAAS,EAAIH,EAAUvC,EAAOwC,IAAcxC,EAAOwC,IAAcxC,EAAOwC,GAAWC,MCNrFb,EAAY,GAAGe,iBCAfC,EAAW,YAAa,cAAgB,GfCrDC,EAAU7C,EAAO6C,QACjBC,EAAO9C,EAAO8C,KACdC,EAAWF,GAAWA,EAAQE,UAAYD,GAAQA,EAAKrD,QACvDuD,EAAKD,GAAYA,EAASC,GAG1BA,IAIFvD,GAHAD,EAAQwD,EAAGjB,MAAM,MAGD,GAAK,GAAKvC,EAAM,GAAK,EAAI,IAAMA,EAAM,GAAKA,EAAM,MAK7DC,GAAWwD,MACdzD,EAAQyD,EAAUzD,MAAM,iBACVA,EAAM,IAAM,MACxBA,EAAQyD,EAAUzD,MAAM,oBACbC,GAAWD,EAAM,IAIhC,MAAiBC,MgBrBEa,OAAO4C,wBAA0B7C,GAAM,WACxD,IAAI8C,EAASC,SAGb,OAAQC,OAAOF,MAAa7C,OAAO6C,aAAmBC,UAEnDA,OAAOE,MAAQC,GAAcA,EAAa,QCR9BC,IACXJ,OAAOE,MACkB,iBAAnBF,OAAOK,SCCfnD,EAASN,EAAOM,SAEHoD,EAAoB,SAAU/D,GAC7C,MAAoB,iBAANA,GACZ,SAAUA,GACZ,IAAIgE,EAAUf,EAAW,UACzB,OAAON,EAAWqB,IAAYhB,EAAcgB,EAAQjD,UAAWJ,EAAOX,KCVpE0D,GAASrD,EAAOqD,UAEH,SAAUhB,GACzB,IACE,OAAOgB,GAAOhB,GACd,MAAOjC,GACP,MAAO,WCJP6B,GAAYjC,EAAOiC,aAGN,SAAUI,GACzB,GAAIC,EAAWD,GAAW,OAAOA,EACjC,MAAMJ,GAAU2B,GAAYvB,GAAY,0BCLzB,SAAUpB,EAAG4C,GAC5B,IAAIC,EAAO7C,EAAE4C,GACb,OAAe,MAARC,OAAe5B,EAAY6B,GAAUD,ICD1C7B,GAAYjC,EAAOiC,UCFnB1B,GAAiBD,OAAOC,kBAEX,SAAUyD,EAAK3C,GAC9B,IACEd,GAAeP,EAAQgE,EAAK,CAAE3C,MAAOA,EAAOC,cAAc,EAAMC,UAAU,IAC1E,MAAOnB,GACPJ,EAAOgE,GAAO3C,EACd,OAAOA,GCPP4C,GAAS,wBACDjE,EAAOiE,KAAWC,GAAUD,GAAQ,uBCD/CE,UAAiB,SAAUH,EAAK3C,GAC/B,OAAO+C,GAAMJ,KAASI,GAAMJ,QAAiB9B,IAAVb,EAAsBA,EAAQ,MAChE,WAAY,IAAIgD,KAAK,CACtB5E,QAAS,SACT6E,KAAyB,SACzBC,UAAW,4CCLTjE,GAASN,EAAOM,UAIH,SAAU+B,GACzB,OAAO/B,GAAO8B,EAAuBC,KCLnCmC,GAAiB5C,EAAY,GAAG4C,mBAInBlE,OAAOmE,QAAU,SAAgB9E,EAAIqE,GACpD,OAAOQ,GAAeE,GAAS/E,GAAKqE,ICNlCW,GAAK,EACLC,GAAUhF,KAAKiF,SACflD,GAAWC,EAAY,GAAID,aAEd,SAAUqC,GACzB,MAAO,gBAAqB9B,IAAR8B,EAAoB,GAAKA,GAAO,KAAOrC,KAAWgD,GAAKC,GAAS,KCAlFE,GAAwBC,GAAO,OAC/B3B,GAASpD,EAAOoD,OAChB4B,GAAY5B,IAAUA,GAAY,IAClC6B,GAAwBvB,EAAoBN,GAASA,IAAUA,GAAO8B,eAAiBC,MAE1E,SAAUC,GACzB,IAAKX,GAAOK,GAAuBM,KAAW5B,GAAuD,iBAA/BsB,GAAsBM,GAAoB,CAC9G,IAAIC,EAAc,UAAYD,EAC1B5B,GAAiBiB,GAAOrB,GAAQgC,GAClCN,GAAsBM,GAAQhC,GAAOgC,GAErCN,GAAsBM,GADb1B,GAAqBsB,GACAA,GAAUK,GAEVJ,GAAsBI,GAEtD,OAAOP,GAAsBM,ICd7BnD,GAAYjC,EAAOiC,UACnBqD,GAAeC,GAAgB,kBAIlB,SAAUC,EAAOC,GAChC,IAAKC,EAASF,IAAUG,EAASH,GAAQ,OAAOA,EAChD,IACII,EADAC,EAAeC,GAAUN,EAAOF,IAEpC,GAAIO,EAAc,CAGhB,QAFa3D,IAATuD,IAAoBA,EAAO,WAC/BG,EAASnF,EAAKoF,EAAcL,EAAOC,IAC9BC,EAASE,IAAWD,EAASC,GAAS,OAAOA,EAClD,MAAM3D,GAAU,2CAGlB,YADaC,IAATuD,IAAoBA,EAAO,URdhB,SAAUD,EAAOC,GAChC,IAAI/D,EAAIqE,EACR,GAAa,WAATN,GAAqBnD,EAAWZ,EAAK8D,EAAM7D,YAAc+D,EAASK,EAAMtF,EAAKiB,EAAI8D,IAAS,OAAOO,EACrG,GAAIzD,EAAWZ,EAAK8D,EAAMQ,WAAaN,EAASK,EAAMtF,EAAKiB,EAAI8D,IAAS,OAAOO,EAC/E,GAAa,WAATN,GAAqBnD,EAAWZ,EAAK8D,EAAM7D,YAAc+D,EAASK,EAAMtF,EAAKiB,EAAI8D,IAAS,OAAOO,EACrG,MAAM9D,GAAU,2CQUTgE,CAAoBT,EAAOC,OCnBnB,SAAUpD,GACzB,IAAI2B,EAAMkC,GAAY7D,EAAU,UAChC,OAAOsD,EAAS3B,GAAOA,EAAMA,EAAM,ICJjCmC,GAAWnG,EAAOmG,SAElBC,GAASV,EAASS,KAAaT,EAASS,GAASE,kBAEpC,SAAU1G,GACzB,OAAOyG,GAASD,GAASE,cAAc1G,GAAM,QCH7B2G,IAAgBjG,GAAM,WAEtC,OAEQ,GAFDC,OAAOC,eAAe8F,GAAc,OAAQ,IAAK,CACtD7F,IAAK,WAAc,OAAO,KACzB+F,KCCDC,GAA4BlG,OAAOU,+BAI3BsF,EAAcE,GAA4B,SAAkCC,EAAG5C,GAGzF,GAFA4C,EAAIC,EAAgBD,GACpB5C,EAAI8C,GAAc9C,GACd+C,GAAgB,IAClB,OAAOJ,GAA0BC,EAAG5C,GACpC,MAAOzD,IACT,GAAIqE,GAAOgC,EAAG5C,GAAI,OAAOgD,GAA0BpG,EAAKqG,EAA2BC,EAAGN,EAAG5C,GAAI4C,EAAE5C,MCjB7FR,GAASrD,EAAOqD,OAChBpB,GAAYjC,EAAOiC,aAGN,SAAUI,GACzB,GAAIqD,EAASrD,GAAW,OAAOA,EAC/B,MAAMJ,GAAUoB,GAAOhB,GAAY,sBCHjCJ,GAAYjC,EAAOiC,UAEnB+E,GAAkB1G,OAAOC,qBAIjB+F,EAAcU,GAAkB,SAAwBP,EAAG5C,EAAGoD,GAIxE,GAHAC,GAAST,GACT5C,EAAI8C,GAAc9C,GAClBqD,GAASD,GACLL,GAAgB,IAClB,OAAOI,GAAgBP,EAAG5C,EAAGoD,GAC7B,MAAO7G,IACT,GAAI,QAAS6G,GAAc,QAASA,EAAY,MAAMhF,GAAU,2BAEhE,MADI,UAAWgF,IAAYR,EAAE5C,GAAKoD,EAAW5F,OACtCoF,OCjBQH,EAAc,SAAUa,EAAQnD,EAAK3C,GACpD,OAAO+F,GAAqBL,EAAEI,EAAQnD,EAAK6C,EAAyB,EAAGxF,KACrE,SAAU8F,EAAQnD,EAAK3C,GAEzB,OADA8F,EAAOnD,GAAO3C,EACP8F,GCJLE,GAAmBzF,EAAY1B,SAASyB,UAGvCW,EAAW8B,GAAMkD,iBACpBlD,GAAMkD,cAAgB,SAAU3H,GAC9B,OAAO0H,GAAiB1H,KAI5B,ICAI4H,GAAK/G,GAAKgH,MDAGpD,GAAMkD,cETnBG,GAAUzH,EAAOyH,WAEJnF,EAAWmF,KAAY,cAAcC,KAAKJ,GAAcG,KCHrEE,GAAO5C,GAAO,WAED,SAAUf,GACzB,OAAO2D,GAAK3D,KAAS2D,GAAK3D,GAAOmB,GAAInB,QCNtB,GHUb4D,GAA6B,6BAC7B3F,GAAYjC,EAAOiC,UACnBwF,GAAUzH,EAAOyH,QAgBrB,GAAII,IAAmB9C,GAAO+C,MAAO,CACnC,IAAI1D,GAAQW,GAAO+C,QAAU/C,GAAO+C,MAAQ,IAAIL,IAC5CM,GAAQnG,EAAYwC,GAAM5D,KAC1BwH,GAAQpG,EAAYwC,GAAMoD,KAC1BS,GAAQrG,EAAYwC,GAAMmD,KAC9BA,GAAM,SAAU5H,EAAIuI,GAClB,GAAIF,GAAM5D,GAAOzE,GAAK,MAAM,IAAIsC,GAAU2F,IAG1C,OAFAM,EAASC,OAASxI,EAClBsI,GAAM7D,GAAOzE,EAAIuI,GACVA,GAET1H,GAAM,SAAUb,GACd,OAAOoI,GAAM3D,GAAOzE,IAAO,IAE7B6H,GAAM,SAAU7H,GACd,OAAOqI,GAAM5D,GAAOzE,QAEjB,CACL,IAAIyI,GAAQC,GAAU,SACtBC,GAAWF,KAAS,EACpBb,GAAM,SAAU5H,EAAIuI,GAClB,GAAIzD,GAAO9E,EAAIyI,IAAQ,MAAM,IAAInG,GAAU2F,IAG3C,OAFAM,EAASC,OAASxI,EAClB4I,GAA4B5I,EAAIyI,GAAOF,GAChCA,GAET1H,GAAM,SAAUb,GACd,OAAO8E,GAAO9E,EAAIyI,IAASzI,EAAGyI,IAAS,IAEzCZ,GAAM,SAAU7H,GACd,OAAO8E,GAAO9E,EAAIyI,KAItB,OAAiB,CACfb,IAAKA,GACL/G,IAAKA,GACLgH,IAAKA,GACLgB,QAnDY,SAAU7I,GACtB,OAAO6H,GAAI7H,GAAMa,GAAIb,GAAM4H,GAAI5H,EAAI,KAmDnC8I,UAhDc,SAAUC,GACxB,OAAO,SAAU/I,GACf,IAAImI,EACJ,IAAKpC,EAAS/F,KAAQmI,EAAQtH,GAAIb,IAAKgJ,OAASD,EAC9C,MAAMzG,GAAU,0BAA4ByG,EAAO,aACnD,OAAOZ,KIrBTtG,GAAoBtB,SAASQ,UAE7BkI,GAAgBtC,GAAehG,OAAOU,yBAEtCoF,GAAS3B,GAAOjD,GAAmB,WAKtB,CACf4E,OAAQA,GACRyC,OALWzC,IAA0D,cAAhD,aAAuChB,KAM5D0D,aALiB1C,MAAYE,GAAgBA,GAAesC,GAAcpH,GAAmB,QAAQF,iCCHvG,IAAIyH,EAA6BC,GAAsCF,aAEnEG,EAAmBC,GAAoB1I,IACvC2I,EAAuBD,GAAoBV,QAC3CY,EAAW/F,OAAOA,QAAQtB,MAAM,WAEnCoC,UAAiB,SAAUsC,EAAGzC,EAAK3C,EAAOgI,GACzC,IAIIvB,EAJAwB,IAASD,KAAYA,EAAQC,OAC7BC,IAASF,KAAYA,EAAQlI,WAC7BqI,IAAcH,KAAYA,EAAQG,YAClCpE,EAAOiE,QAA4BnH,IAAjBmH,EAAQjE,KAAqBiE,EAAQjE,KAAOpB,EAE9D1B,EAAWjB,KACoB,YAA7BgC,OAAO+B,GAAMtD,MAAM,EAAG,KACxBsD,EAAO,IAAM/B,OAAO+B,GAAMqE,QAAQ,qBAAsB,MAAQ,OAE7DhF,GAAOpD,EAAO,SAAY0H,GAA8B1H,EAAM+D,OAASA,IAC1EmD,GAA4BlH,EAAO,OAAQ+D,IAE7C0C,EAAQqB,EAAqB9H,IAClBqI,SACT5B,EAAM4B,OAASN,EAASO,KAAoB,iBAARvE,EAAmBA,EAAO,MAG9DqB,IAAMzG,GAIEsJ,GAEAE,GAAe/C,EAAEzC,KAC3BuF,GAAS,UAFF9C,EAAEzC,GAIPuF,EAAQ9C,EAAEzC,GAAO3C,EAChBkH,GAA4B9B,EAAGzC,EAAK3C,IATnCkI,EAAQ9C,EAAEzC,GAAO3C,EAChB6C,GAAUF,EAAK3C,KAUrBnB,SAASQ,UAAW,YAAY,WACjC,OAAO4B,EAAWrC,OAASgJ,EAAiBhJ,MAAMyJ,QAAUpC,GAAcrH,YC5CxE2J,GAAOhK,KAAKgK,KACZC,GAAQjK,KAAKiK,SAIA,SAAUxH,GACzB,IAAIyH,GAAUzH,EAEd,OAAOyH,GAAWA,GAAqB,IAAXA,EAAe,GAAKA,EAAS,EAAID,GAAQD,IAAME,ICNzEC,GAAMnK,KAAKmK,IACXC,GAAMpK,KAAKoK,OAKE,SAAUC,EAAOvH,GAChC,IAAIwH,EAAUC,GAAoBF,GAClC,OAAOC,EAAU,EAAIH,GAAIG,EAAUxH,EAAQ,GAAKsH,GAAIE,EAASxH,ICR3DsH,GAAMpK,KAAKoK,OAIE,SAAU3H,GACzB,OAAOA,EAAW,EAAI2H,GAAIG,GAAoB9H,GAAW,kBAAoB,MCH9D,SAAU+H,GACzB,OAAOC,GAASD,EAAI1H,SCAlB4H,GAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAOC,EAAIC,GAC1B,IAGIrJ,EAHAoF,EAAIC,EAAgB8D,GACpB9H,EAASiI,GAAkBlE,GAC3BwD,EAAQW,GAAgBF,EAAWhI,GAIvC,GAAI6H,GAAeE,GAAMA,GAAI,KAAO/H,EAASuH,GAG3C,IAFA5I,EAAQoF,EAAEwD,OAEG5I,EAAO,OAAO,OAEtB,KAAMqB,EAASuH,EAAOA,IAC3B,IAAKM,GAAeN,KAASxD,IAAMA,EAAEwD,KAAWQ,EAAI,OAAOF,GAAeN,GAAS,EACnF,OAAQM,IAAgB,ICjB1BM,GDqBa,CAGfC,SAAUR,IAAa,GAGvBO,QAASP,IAAa,IC3B6BO,QAGjDxG,GAAOzC,EAAY,GAAGyC,SAET,SAAU8C,EAAQ4D,GACjC,IAGI/G,EAHAyC,EAAIC,EAAgBS,GACpB6D,EAAI,EACJpF,EAAS,GAEb,IAAK5B,KAAOyC,GAAIhC,GAAO6D,GAAYtE,IAAQS,GAAOgC,EAAGzC,IAAQK,GAAKuB,EAAQ5B,GAE1E,KAAO+G,EAAMrI,OAASsI,GAAOvG,GAAOgC,EAAGzC,EAAM+G,EAAMC,SAChDH,GAAQjF,EAAQ5B,IAAQK,GAAKuB,EAAQ5B,IAExC,OAAO4B,MCjBQ,CACf,cACA,iBACA,gBACA,uBACA,iBACA,WACA,WCLE0C,GAAa2C,GAAYC,OAAO,SAAU,mBAKlC5K,OAAO6K,qBAAuB,SAA6B1E,GACrE,OAAO2E,GAAmB3E,EAAG6B,YCRnBhI,OAAO4C,uBCKfgI,GAAStJ,EAAY,GAAGsJ,WAGXtI,EAAW,UAAW,YAAc,SAAiBjD,GACpE,IAAIgI,EAAO0D,GAA0BtE,EAAEG,GAASvH,IAC5CuD,EAAwBoI,GAA4BvE,EACxD,OAAO7D,EAAwBgI,GAAOvD,EAAMzE,EAAsBvD,IAAOgI,MCP1D,SAAU4D,EAAQ7B,GAIjC,IAHA,IAAI/B,EAAO6D,GAAQ9B,GACfnJ,EAAiB6G,GAAqBL,EACtC/F,EAA2ByK,GAA+B1E,EACrDiE,EAAI,EAAGA,EAAIrD,EAAKjF,OAAQsI,IAAK,CACpC,IAAIhH,EAAM2D,EAAKqD,GACVvG,GAAO8G,EAAQvH,IAAMzD,EAAegL,EAAQvH,EAAKhD,EAAyB0I,EAAQ1F,MCRvF0H,GAAc,kBAEdC,GAAW,SAAUC,EAASC,GAChC,IAAIxK,EAAQyK,GAAKC,GAAUH,IAC3B,OAAOvK,GAAS2K,IACZ3K,GAAS4K,KACT3J,EAAWuJ,GAAaxL,EAAMwL,KAC5BA,IAGJE,GAAYJ,GAASI,UAAY,SAAUG,GAC7C,OAAO7I,OAAO6I,GAAQzC,QAAQiC,GAAa,KAAKS,eAG9CL,GAAOH,GAASG,KAAO,GACvBG,GAASN,GAASM,OAAS,IAC3BD,GAAWL,GAASK,SAAW,OAElBL,GCpBb3K,GAA2BgI,GAA2DjC,KAsBzE,SAAUsC,EAASK,GAClC,IAGY6B,EAAQvH,EAAKoI,EAAgBC,EAAgBnL,EAHrDoL,EAASjD,EAAQkC,OACjBgB,EAASlD,EAAQrJ,OACjBwM,EAASnD,EAAQoD,KASrB,GANElB,EADEgB,EACOvM,EACAwM,EACAxM,EAAOsM,IAAWpI,GAAUoI,EAAQ,KAEnCtM,EAAOsM,IAAW,IAAI5L,UAEtB,IAAKsD,KAAO0F,EAAQ,CAQ9B,GAPA2C,EAAiB3C,EAAO1F,GAGtBoI,EAFE/C,EAAQG,aACVtI,EAAaF,GAAyBuK,EAAQvH,KACf9C,EAAWG,MACpBkK,EAAOvH,IACtB2H,GAASY,EAASvI,EAAMsI,GAAUE,EAAS,IAAM,KAAOxI,EAAKqF,EAAQqD,cAE5CxK,IAAnBkK,EAA8B,CAC3C,UAAWC,UAAyBD,EAAgB,SACpDO,GAA0BN,EAAgBD,IAGxC/C,EAAQ/F,MAAS8I,GAAkBA,EAAe9I,OACpDiF,GAA4B8D,EAAgB,QAAQ,GAGtDO,GAASrB,EAAQvH,EAAKqI,EAAgBhD,KCjDtC3B,GAAO,GAEXA,GAHoBnC,GAAgB,gBAGd,IAEtB,ICH2BsH,MDGO,eAAjBxJ,OAAOqE,IEDpBoF,GAAgBvH,GAAgB,eAChCjF,GAASN,EAAOM,OAGhByM,GAAuE,aAAnDC,EAAW,WAAc,OAAOnM,UAArB,OAUlBoM,GAAwBD,EAAa,SAAUrN,GAC9D,IAAI8G,EAAGyG,EAAKtH,EACZ,YAAc1D,IAAPvC,EAAmB,YAAqB,OAAPA,EAAc,OAEM,iBAAhDuN,EAXD,SAAUvN,EAAIqE,GACzB,IACE,OAAOrE,EAAGqE,GACV,MAAO5D,KAQS+M,CAAO1G,EAAInG,GAAOX,GAAKmN,KAA8BI,EAEnEH,GAAoBC,EAAWvG,GAEH,WAA3Bb,EAASoH,EAAWvG,KAAmBnE,EAAWmE,EAAE2G,QAAU,YAAcxH,GCzB/EvC,GAASrD,EAAOqD,UAEH,SAAUhB,GACzB,GAA0B,WAAtBL,GAAQK,GAAwB,MAAMJ,UAAU,6CACpD,OAAOoB,GAAOhB,ICHZgL,GAAO,KACP5D,GAAU7H,EAAY,GAAG6H,YCE3B,CAAE8B,OAAQ,SAAU+B,OAAO,EAAMZ,QJHRG,GIGuC,SJFzDxM,GAAM,WACX,IAAIqH,EAAO,GAAGmF,IAAa,KAC3B,OAAOnF,IAASA,EAAKyE,eAAiBzE,EAAK3F,MAAM,KAAKW,OAAS,OIAY,CAC7E6K,OAAQ,SAAgBnI,GACtB,ODA+B8H,ECAP,IDAYM,ECAP,ODAkBnM,ECAV+D,EDCnCqI,EAAI9L,GAASS,ECDGnC,ODEhByN,EAAK,IAAMR,EACG,KAAdM,IAAkBE,GAAM,IAAMF,EAAY,KAAO/D,GAAQ9H,GAASN,GAAQgM,GAAM,UAAY,KACzFK,EAAK,IAAMD,EAAI,KAAOP,EAAM,IAJpB,IAAkBA,EAAKM,EAAWnM,EAC7CoM,EACAC,KENN,IC+CIC,MD/Ca,WACf,IAAIC,EAAO1G,GAASjH,MAChB2F,EAAS,GAOb,OANIgI,EAAK5N,SAAQ4F,GAAU,KACvBgI,EAAKC,aAAYjI,GAAU,KAC3BgI,EAAKE,YAAWlI,GAAU,KAC1BgI,EAAKG,SAAQnI,GAAU,KACvBgI,EAAKI,UAASpI,GAAU,KACxBgI,EAAKK,SAAQrI,GAAU,KACpBA,GEVLsI,GAAUlO,EAAOmO,OAEjBC,GAAgB/N,GAAM,WACxB,IAAIgO,EAAKH,GAAQ,IAAK,KAEtB,OADAG,EAAGC,UAAY,EACW,MAAnBD,EAAGlO,KAAK,WAKboO,GAAgBH,IAAiB/N,GAAM,WACzC,OAAQ6N,GAAQ,IAAK,KAAKD,aAUX,CACfO,aARiBJ,IAAiB/N,GAAM,WAExC,IAAIgO,EAAKH,GAAQ,KAAM,MAEvB,OADAG,EAAGC,UAAY,EACU,MAAlBD,EAAGlO,KAAK,UAKfoO,cAAeA,GACfH,cAAeA,OCtBA9N,OAAOqH,MAAQ,SAAclB,GAC5C,OAAO2E,GAAmB3E,EAAGwE,QCEd3E,EAAchG,OAAOmO,iBAAmB,SAA0BhI,EAAGiI,GACpFxH,GAAST,GAMT,IALA,IAIIzC,EAJA2K,EAAQjI,EAAgBgI,GACxB/G,EAAOiH,GAAWF,GAClBhM,EAASiF,EAAKjF,OACduH,EAAQ,EAELvH,EAASuH,GAAO7C,GAAqBL,EAAEN,EAAGzC,EAAM2D,EAAKsC,KAAU0E,EAAM3K,IAC5E,OAAOyC,MCfQ7D,EAAW,WAAY,mBJWpCiM,GAAWxG,GAAU,YAErByG,GAAmB,aAEnBC,GAAY,SAAUC,GACxB,MAAOC,WAAmBD,EAAnBC,KAAAA,WAILC,GAA4B,SAAUvB,GACxCA,EAAgBwB,MAAMJ,GAAU,KAChCpB,EAAgByB,QAChB,IAAIC,EAAO1B,EAAgB2B,aAAahP,OAExC,OADAqN,EAAkB,KACX0B,GA0BLE,GAAkB,WACpB,IACE5B,GAAkB,IAAI6B,cAAc,YACpC,MAAOpP,IAzBoB,IAIzBqP,EAFAC,EAwBJH,GAAqC,oBAAZpJ,SACrBA,SAASwJ,QAAUhC,GACjBuB,GAA0BvB,MA1B5B+B,EAASE,GAAsB,WAG5BC,MAAMC,QAAU,OACvBC,GAAKC,YAAYN,GAEjBA,EAAOO,IAAM5M,OALJ,gBAMToM,EAAiBC,EAAOQ,cAAc/J,UACvBgK,OACfV,EAAeN,MAAMJ,GAAU,sBAC/BU,EAAeL,QACRK,EAAeW,GAiBlBlB,GAA0BvB,IAE9B,IADA,IAAIjL,EAASuI,GAAYvI,OAClBA,YAAiB6M,GAAyB,UAAEtE,GAAYvI,IAC/D,OAAO6M,SAGEV,KAAY,EAIvB,IKhDMwB,GACAC,ML+CWhQ,OAAOiQ,QAAU,SAAgB9J,EAAGiI,GACnD,IAAI9I,EAQJ,OAPU,OAANa,GACFqI,GAA0B,UAAI5H,GAAST,GACvCb,EAAS,IAAIkJ,GACbA,GAA0B,UAAI,KAE9BlJ,EAAOiJ,IAAYpI,GACdb,EAAS2J,UACMrN,IAAfwM,EAA2B9I,EAAS6I,GAAiB7I,EAAQ8I,IM5ElER,GAAUlO,EAAOmO,UAEJ9N,GAAM,WACrB,IAAIgO,EAAKH,GAAQ,IAAK,KACtB,QAASG,EAAGN,QAAUM,EAAGlO,KAAK,OAAsB,MAAbkO,EAAGmC,UCJxCtC,GAAUlO,EAAOmO,UAEJ9N,GAAM,WACrB,IAAIgO,EAAKH,GAAQ,UAAW,KAC5B,MAAiC,MAA1BG,EAAGlO,KAAK,KAAKsQ,OAAOlK,GACI,OAA7B,IAAIkD,QAAQ4E,EAAI,YFChBpF,GAAmBD,GAAuCxI,IAI1DkQ,GAAgB3L,GAAO,wBAAyB1B,OAAO3C,UAAU+I,SACjEkH,GAAaxC,OAAOzN,UAAUP,KAC9ByQ,GAAcD,GACdE,GAASjP,EAAY,GAAGiP,QACxBhG,GAAUjJ,EAAY,GAAGiJ,SACzBpB,GAAU7H,EAAY,GAAG6H,SACzB5H,GAAcD,EAAY,GAAGE,OAE7BgP,IAEER,GAAM,MACV7P,EAAKkQ,GAFDN,GAAM,IAEY,KACtB5P,EAAKkQ,GAAYL,GAAK,KACG,IAAlBD,GAAI/B,WAAqC,IAAlBgC,GAAIhC,WAGhCF,GAAgB2C,GAAcvC,aAG9BwC,QAAuC9O,IAAvB,OAAO/B,KAAK,IAAI,IAExB2Q,IAA4BE,IAAiB5C,IAAiB6C,IAAuBC,MAG/FN,GAAc,SAAc1E,GAC1B,IAIItG,EAAQuL,EAAQ7C,EAAW9O,EAAOwL,EAAG7D,EAAQiK,EAJ7C/C,EAAKpO,KACL6H,EAAQmB,GAAiBoF,GACzBgD,EAAM1P,GAASuK,GACfoF,EAAMxJ,EAAMwJ,IAGhB,GAAIA,EAIF,OAHAA,EAAIhD,UAAYD,EAAGC,UACnB1I,EAASnF,EAAKmQ,GAAaU,EAAKD,GAChChD,EAAGC,UAAYgD,EAAIhD,UACZ1I,EAGT,IAAI6K,EAAS3I,EAAM2I,OACfxC,EAASG,IAAiBC,EAAGJ,OAC7BuC,EAAQ/P,EAAK8Q,GAAalD,GAC1B3E,EAAS2E,EAAG3E,OACZ8H,EAAa,EACbC,EAAUJ,EA+Cd,GA7CIpD,IACFuC,EAAQ/G,GAAQ+G,EAAO,IAAK,KACC,IAAzB3F,GAAQ2F,EAAO,OACjBA,GAAS,KAGXiB,EAAU5P,GAAYwP,EAAKhD,EAAGC,WAE1BD,EAAGC,UAAY,KAAOD,EAAGP,WAAaO,EAAGP,WAA+C,OAAlC+C,GAAOQ,EAAKhD,EAAGC,UAAY,MACnF5E,EAAS,OAASA,EAAS,IAC3B+H,EAAU,IAAMA,EAChBD,KAIFL,EAAS,IAAIhD,OAAO,OAASzE,EAAS,IAAK8G,IAGzCQ,KACFG,EAAS,IAAIhD,OAAO,IAAMzE,EAAS,WAAY8G,IAE7CM,KAA0BxC,EAAYD,EAAGC,WAE7C9O,EAAQiB,EAAKkQ,GAAY1C,EAASkD,EAAS9C,EAAIoD,GAE3CxD,EACEzO,GACFA,EAAMgG,MAAQ3D,GAAYrC,EAAMgG,MAAOgM,GACvChS,EAAM,GAAKqC,GAAYrC,EAAM,GAAIgS,GACjChS,EAAMyK,MAAQoE,EAAGC,UACjBD,EAAGC,WAAa9O,EAAM,GAAGkD,QACpB2L,EAAGC,UAAY,EACbwC,IAA4BtR,IACrC6O,EAAGC,UAAYD,EAAGrO,OAASR,EAAMyK,MAAQzK,EAAM,GAAGkD,OAAS4L,GAEzD0C,IAAiBxR,GAASA,EAAMkD,OAAS,GAG3CjC,EAAKiQ,GAAelR,EAAM,GAAI2R,GAAQ,WACpC,IAAKnG,EAAI,EAAGA,EAAInK,UAAU6B,OAAS,EAAGsI,SACf9I,IAAjBrB,UAAUmK,KAAkBxL,EAAMwL,QAAK9I,MAK7C1C,GAASiR,EAEX,IADAjR,EAAMiR,OAAStJ,EAASoJ,GAAO,MAC1BvF,EAAI,EAAGA,EAAIyF,EAAO/N,OAAQsI,IAE7B7D,GADAiK,EAAQX,EAAOzF,IACF,IAAMxL,EAAM4R,EAAM,IAInC,OAAO5R,IAIX,OAAiBoR,MG9Gf,CAAErF,OAAQ,SAAU+B,OAAO,EAAMZ,OAAQ,IAAIvM,OAASA,IAAQ,CAC9DA,KAAMA,KCFR,OAAiBuR,MAAMC,SAAW,SAAiBtP,GACjD,MAA4B,SAArBL,EAAQK,ICCbuP,GAAO,aACPC,GAAQ,GACRC,GAAYlP,EAAW,UAAW,aAClCmP,GAAoB,2BACpB5R,GAAOyB,EAAYmQ,GAAkB5R,MACrC6R,IAAuBD,GAAkB5R,KAAKyR,IAE9CK,GAAsB,SAAU5P,GAClC,IAAKC,EAAWD,GAAW,OAAO,EAClC,IAEE,OADAyP,GAAUF,GAAMC,GAAOxP,IAChB,EACP,MAAOjC,GACP,OAAO,QAgBO0R,IAAazR,GAAM,WACnC,IAAI6R,EACJ,OAAOD,GAAoBA,GAAoBxR,QACzCwR,GAAoB3R,UACpB2R,IAAoB,WAAcC,GAAS,MAC5CA,KAjBmB,SAAU7P,GAClC,IAAKC,EAAWD,GAAW,OAAO,EAClC,OAAQL,GAAQK,IACd,IAAK,gBACL,IAAK,oBACL,IAAK,yBAA0B,OAAO,EAEtC,OAAO2P,MAAyB7R,GAAK4R,GAAmBzK,GAAcjF,KAW/C4P,MCrCV,SAAU9K,EAAQnD,EAAK3C,GACtC,IAAI8Q,EAAcxL,GAAc3C,GAC5BmO,KAAehL,EAAQC,GAAqBL,EAAEI,EAAQgL,EAAatL,EAAyB,EAAGxF,IAC9F8F,EAAOgL,GAAe9Q,GCJzB+Q,GAAU7M,GAAgB,cAEb,SAAUsH,GAIzB,OAAOtJ,GAAc,KAAOlD,GAAM,WAChC,IAAIgS,EAAQ,GAKZ,OAJkBA,EAAMC,YAAc,IAC1BF,IAAW,WACrB,MAAO,CAAEG,IAAK,IAE2B,IAApCF,EAAMxF,GAAa2F,SAASD,WCdtB3Q,EAAY,GAAGE,OCY5B2Q,GAAsBC,GAA6B,SAEnDN,GAAU7M,GAAgB,WAC1BmM,GAAQ1R,EAAO0R,MACf3H,GAAMnK,KAAKmK,8RCEf,SAAS4I,GAAcC,OACbC,EAAcD,eACL,MAAbC,EAAmB,OAAO,MAEvBC,EAADC,GAAkBC,SAAOC,MAAML,EAAW,CAC9CpT,MAAO,SAAA0T,UAAKC,YAAUC,cAAcF,EAAG,0BAErCJ,EAAe,KACRO,EAAHN,GAAeD,QACfQ,EAAQN,SAAOM,MAAMV,EAAWS,MAElCE,QAAMC,OAAOX,EAAUtF,OAAQ+F,UAC1B,SAIJ,EAQT,SAASG,GAAgBC,EAAoBC,WACrCC,EAASZ,SAAOC,MAAMS,EAAQ,CAClCG,GAAIF,EACJnU,MAAO,SAAA0T,SAEW,UADHC,YAAUW,YAAYZ,MAInCa,GAAW,UACK,IAAAC;;;;;;;;;;;;;;;ySAAAC,CAAAL,iCAAQ,SAC1BG,GAAW,2GAENA,KDjCP,CAAExI,OAAQ,QAAS+B,OAAO,EAAMZ,QAAS+F,IAAuB,CAChE3Q,MAAO,SAAewR,EAAOY,GAC3B,IAKIC,EAAavO,EAAQsN,EALrBzM,EAAIC,EAAgBzG,MACpByC,EAASiI,GAAkBlE,GAC3B2N,EAAIxJ,GAAgB0I,EAAO5Q,GAC3B2R,EAAMzJ,QAAwB1I,IAARgS,EAAoBxR,EAASwR,EAAKxR,GAG5D,GAAIiP,GAAQlL,KACV0N,EAAc1N,EAAE6L,aAEZgC,GAAcH,KAAiBA,IAAgBzC,IAASC,GAAQwC,EAAYzT,aAErEgF,EAASyO,IAEE,QADpBA,EAAcA,EAAY/B,QAF1B+B,OAAcjS,GAKZiS,IAAgBzC,SAAyBxP,IAAhBiS,GAC3B,OAAOI,GAAS9N,EAAG2N,EAAGC,GAI1B,IADAzO,EAAS,SAAqB1D,IAAhBiS,EAA4BzC,GAAQyC,GAAapK,GAAIsK,EAAMD,EAAG,IACvElB,EAAI,EAAGkB,EAAIC,EAAKD,IAAKlB,IAASkB,KAAK3N,GAAG+N,GAAe5O,EAAQsN,EAAGzM,EAAE2N,IAEvE,OADAxO,EAAOlD,OAASwQ,EACTtN,KE5CX,IAAIjF,GAAOiB,EAAYA,EAAYjB,MCG/ByR,GAAU7M,GAAgB,WAC1BmM,GAAQ1R,EAAO0R,SCHF,SAAU+C,EAAe/R,GACxC,OAAO,IDMQ,SAAU+R,GACzB,IAAIC,EASF,OARE/C,GAAQ8C,KACVC,EAAID,EAAcnC,aAEdgC,GAAcI,KAAOA,IAAMhD,IAASC,GAAQ+C,EAAEhU,aACzCgF,EAASgP,IAEN,QADVA,EAAIA,EAAEtC,QAFuDsC,OAAIxS,SAKtDA,IAANwS,EAAkBhD,GAAQgD,GChBCD,GAA7B,CAAwD,IAAX/R,EAAe,EAAIA,ICErE2B,GAAOzC,EAAY,GAAGyC,MAGtBiG,GAAe,SAAU5B,GAC3B,IAAIiM,EAAiB,GAARjM,EACTkM,EAAoB,GAARlM,EACZmM,EAAkB,GAARnM,EACVoM,EAAmB,GAARpM,EACXqM,EAAwB,GAARrM,EAChBsM,EAA2B,GAARtM,EACnBuM,EAAmB,GAARvM,GAAaqM,EAC5B,OAAO,SAAUvK,EAAO0K,EAAYtH,EAAMuH,GASxC,IARA,IAOI9T,EAAOuE,EAPPa,EAAI/B,GAAS8F,GACbzK,EAAOoC,EAAcsE,GACrB2O,EHfS,SAAU1T,EAAIkM,GAE7B,OADA7J,GAAUrC,QACMQ,IAAT0L,EAAqBlM,EAAKf,GAAOA,GAAKe,EAAIkM,GAAQ,WACvD,OAAOlM,EAAGd,MAAMgN,EAAM/M,YGYFF,CAAKuU,EAAYtH,GACjClL,EAASiI,GAAkB5K,GAC3BkK,EAAQ,EACRsG,EAAS4E,GAAkBE,GAC3B9J,EAASoJ,EAASpE,EAAO/F,EAAO9H,GAAUkS,GAAaI,EAAmBzE,EAAO/F,EAAO,QAAKtI,EAE3FQ,EAASuH,EAAOA,IAAS,IAAIgL,GAAYhL,KAASlK,KAEtD6F,EAASwP,EADT/T,EAAQtB,EAAKkK,GACiBA,EAAOxD,GACjCiC,GACF,GAAIiM,EAAQpJ,EAAOtB,GAASrE,OACvB,GAAIA,EAAQ,OAAQ8C,GACvB,KAAK,EAAG,OAAO,EACf,KAAK,EAAG,OAAOrH,EACf,KAAK,EAAG,OAAO4I,EACf,KAAK,EAAG5F,GAAKkH,EAAQlK,QAChB,OAAQqH,GACb,KAAK,EAAG,OAAO,EACf,KAAK,EAAGrE,GAAKkH,EAAQlK,GAI3B,OAAO0T,GAAiB,EAAIF,GAAWC,EAAWA,EAAWvJ,OAIhD,CAGf+J,QAAShL,GAAa,GAGtBiL,IAAKjL,GAAa,GAGlBkL,OAAQlL,GAAa,GAGrBmL,KAAMnL,GAAa,GAGnBoL,MAAOpL,GAAa,GAGpBqL,KAAMrL,GAAa,GAGnBsL,UAAWtL,GAAa,GAGxBuL,aAAcvL,GAAa,ICrEzBwL,GAAO9M,GAAwCuM,OAQjD,CAAEhK,OAAQ,QAAS+B,OAAO,EAAMZ,QALRgG,GAA6B,QAKW,CAChE6C,IAAK,SAAaL,GAChB,OAAOY,GAAK7V,KAAMiV,EAAYrU,UAAU6B,OAAS,EAAI7B,UAAU,QAAKqB,MCNxE,OAAiB+K,GAAwB,GAAGtL,SAAW,WACrD,MAAO,WAAaK,GAAQ/B,MAAQ,cCKtB8V,GAAiBC,OACzBC,EAAOD,EAAUE,UAAY,UACf,IAAhBD,EAAKvT,OAAqB,IACbuT,EAAK,IAAM,IACLC,UAAY,YAQrBC,GAAkBH,UACVD,GAAiBC,GAClBN,OAAM,SAAAU,WAAUA,EAAKC,YCpBvCpJ,IACHL,GAAStM,OAAOI,UAAW,WAAYiB,GAAU,CAAE2H,QAAQ,ICL7D,IAAIgN,GAAuBtN,GAAsCH,OAQ7D0N,GAAY,WACZC,GAAkBrI,OAAOzN,UACzB+V,GAAaD,GAAyB,SACtCE,GAAW9U,EAAY+U,IAEvBC,GAAcvW,GAAM,WAAc,MAAuD,QAAhDoW,GAAWhW,KAAK,CAAEiJ,OAAQ,IAAK8G,MAAO,SAE/EqG,GAAiBP,IAAwBG,GAAWrR,MAAQmR,IAI5DK,IAAeC,KACjBjK,GAASuB,OAAOzN,UAAW6V,IAAW,WACpC,IAAIO,EAAI5P,GAASjH,MACb8W,EAAIC,GAAUF,EAAEpN,QAChBuN,EAAKH,EAAEtG,MAEX,MAAO,IAAMuG,EAAI,IADTC,QAAiB9U,IAAP+U,GAAoBtU,EAAc6T,GAAiBM,MAAQ,UAAWN,IAAmBE,GAASI,GAAKG,KAExH,CAAE3N,QAAQ,ICxBf,IAAI4N,GAAc3R,GAAgB,eAC9B4R,GAAiBzF,MAAMhR,UAIQwB,MAA/BiV,GAAeD,KACjB9P,GAAqBL,EAAEoQ,GAAgBD,GAAa,CAClD5V,cAAc,EACdD,MAAOkP,GAAO,QAKlB,IAA2BvM,GCfvBoT,GAAQpO,GAAwC2M,KAGhD0B,GAAO,OACPC,IAAc,EAGdD,KAAQ,IAAI3F,MAAM,GAAO,MAAE,WAAc4F,IAAc,QAIzD,CAAE/L,OAAQ,QAAS+B,OAAO,EAAMZ,OAAQ4K,IAAe,CACvD3B,KAAM,SAAcT,GAClB,OAAOkC,GAAMnX,KAAMiV,EAAYrU,UAAU6B,OAAS,EAAI7B,UAAU,QAAKqB,MDE9C8B,GCGVqT,GDFfF,GAAeD,IAAalT,KAAO,EElBrC,IAAIxC,GAAoBtB,SAASQ,UAC7BE,GAAQY,GAAkBZ,MAC1BD,GAAOa,GAAkBb,KACzBF,GAAOe,GAAkBf,QAGM,iBAAX8W,SAAuBA,QAAQ3W,QAAUD,GAAOF,GAAKE,KAAKC,IAAS,WACzF,OAAOH,GAAKG,MAAMA,GAAOC,aCGvBuR,GAAU7M,GAAgB,WAC1BiR,GAAkBrI,OAAOzN,aAEZ,SAAU8W,EAAKrX,EAAMsX,EAAQC,GAC5C,IAAIC,EAASpS,GAAgBiS,GAEzBI,GAAuBvX,GAAM,WAE/B,IAAIoG,EAAI,GAER,OADAA,EAAEkR,GAAU,WAAc,OAAO,GACZ,GAAd,GAAGH,GAAK/Q,MAGboR,EAAoBD,IAAwBvX,GAAM,WAEpD,IAAIyX,GAAa,EACbzJ,EAAK,IAkBT,MAhBY,UAARmJ,KAIFnJ,EAAK,IAGFiE,YAAc,GACjBjE,EAAGiE,YAAYF,IAAW,WAAc,OAAO/D,GAC/CA,EAAGmC,MAAQ,GACXnC,EAAGsJ,GAAU,IAAIA,IAGnBtJ,EAAGlO,KAAO,WAAiC,OAAnB2X,GAAa,EAAa,MAElDzJ,EAAGsJ,GAAQ,KACHG,KAGV,IACGF,IACAC,GACDJ,EACA,CACA,IAAIM,EAA8BnW,EAAY,IAAI+V,IAC9CK,EAAU7X,EAAKwX,EAAQ,GAAGH,IAAM,SAAUS,EAAcC,EAAQ7G,EAAK8G,EAAMC,GAC7E,IAAIC,EAAwBzW,EAAYqW,GACpCK,EAAQJ,EAAO/X,KACnB,OAAImY,IAAUC,IAAcD,IAAU9B,GAAgBrW,KAChDyX,IAAwBQ,EAInB,CAAEI,MAAM,EAAMnX,MAAO0W,EAA4BG,EAAQ7G,EAAK8G,IAEhE,CAAEK,MAAM,EAAMnX,MAAOgX,EAAsBhH,EAAK6G,EAAQC,IAE1D,CAAEK,MAAM,MAGjB5L,GAASvJ,OAAO3C,UAAW8W,EAAKQ,EAAQ,IACxCpL,GAAS4J,GAAiBmB,EAAQK,EAAQ,IAGxCN,GAAMnP,GAA4BiO,GAAgBmB,GAAS,QAAQ,ICpErEc,GAAQlT,GAAgB,SCAxBtD,GAAYjC,EAAOiC,UCAnBmQ,GAAU7M,GAAgB,cAIb,SAAUkB,EAAGiS,GAC5B,IACIjL,EADAiH,EAAIxN,GAAST,GAAG6L,YAEpB,YAAapQ,IAANwS,GAAiDxS,OAA7BuL,EAAIvG,GAASwN,GAAGtC,KAAyBsG,EDJrD,SAAUrW,GACzB,GAAIiS,GAAcjS,GAAW,OAAOA,EACpC,MAAMJ,GAAU2B,GAAYvB,GAAY,yBCEiDsW,CAAalL,ICNpGoD,GAASjP,EAAY,GAAGiP,QACxB+H,GAAahX,EAAY,GAAGgX,YAC5B/W,GAAcD,EAAY,GAAGE,OAE7BwI,GAAe,SAAUuO,GAC3B,OAAO,SAAUrO,EAAOsO,GACtB,IAGIC,EAAOC,EAHPvL,EAAI9L,GAASS,EAAuBoI,IACpCyO,EAAW9O,GAAoB2O,GAC/BI,EAAOzL,EAAE/K,OAEb,OAAIuW,EAAW,GAAKA,GAAYC,EAAaL,EAAoB,QAAK3W,GACtE6W,EAAQH,GAAWnL,EAAGwL,IACP,OAAUF,EAAQ,OAAUE,EAAW,IAAMC,IACtDF,EAASJ,GAAWnL,EAAGwL,EAAW,IAAM,OAAUD,EAAS,MAC3DH,EACEhI,GAAOpD,EAAGwL,GACVF,EACFF,EACEhX,GAAY4L,EAAGwL,EAAUA,EAAW,GACVD,EAAS,OAAlCD,EAAQ,OAAU,IAA0B,QCvBrDlI,GD2Ba,CAGfsI,OAAQ7O,IAAa,GAGrBuG,OAAQvG,IAAa,ICjC+BuG,UAIrC,SAAUpD,EAAGxD,EAAO+D,GACnC,OAAO/D,GAAS+D,EAAU6C,GAAOpD,EAAGxD,GAAOvH,OAAS,ICDlDgP,GAAQ1R,EAAO0R,MACf3H,GAAMnK,KAAKmK,OAEE,SAAUtD,EAAG6M,EAAOY,GAKnC,IAJA,IAAIxR,EAASiI,GAAkBlE,GAC3B2N,EAAIxJ,GAAgB0I,EAAO5Q,GAC3B2R,EAAMzJ,QAAwB1I,IAARgS,EAAoBxR,EAASwR,EAAKxR,GACxDkD,EAAS8L,GAAM3H,GAAIsK,EAAMD,EAAG,IACvBlB,EAAI,EAAGkB,EAAIC,EAAKD,IAAKlB,IAAKsB,GAAe5O,EAAQsN,EAAGzM,EAAE2N,IAE/D,OADAxO,EAAOlD,OAASwQ,EACTtN,GCRL3D,GAAYjC,EAAOiC,aAIN,SAAU6U,EAAGrJ,GAC5B,IAAItN,EAAO2W,EAAE3W,KACb,GAAImC,EAAWnC,GAAO,CACpB,IAAIyF,EAASnF,EAAKN,EAAM2W,EAAGrJ,GAE3B,OADe,OAAX7H,GAAiBsB,GAAStB,GACvBA,EAET,GAAmB,WAAf5D,EAAQ8U,GAAiB,OAAOrW,EAAK8X,GAAYzB,EAAGrJ,GACxD,MAAMxL,GAAU,gDCAdmM,GAAgB2C,GAAc3C,cAC9BgL,GAAa,WACbpP,GAAMpK,KAAKoK,IACXqP,GAAQ,GAAGhV,KACXlE,GAAOyB,EAAY,IAAIzB,MACvBkE,GAAOzC,EAAYyX,IACnBxX,GAAcD,EAAY,GAAGE,OAI7BwX,IAAqCjZ,GAAM,WAE7C,IAAIgO,EAAK,OACLkL,EAAelL,EAAGlO,KACtBkO,EAAGlO,KAAO,WAAc,OAAOoZ,EAAa3Y,MAAMX,KAAMY,YACxD,IAAI+E,EAAS,KAAK7D,MAAMsM,GACxB,OAAyB,IAAlBzI,EAAOlD,QAA8B,MAAdkD,EAAO,IAA4B,MAAdA,EAAO,SAI9B,SAAS,SAAU4T,EAAOC,EAAaC,GACnE,IAAIC,EAqDJ,OAzCEA,EAV2B,KAA3B,OAAO5X,MAAM,QAAQ,IAEc,GAAnC,OAAOA,MAAM,QAAS,GAAGW,QACO,GAAhC,KAAKX,MAAM,WAAWW,QACU,GAAhC,IAAIX,MAAM,YAAYW,QAEtB,IAAIX,MAAM,QAAQW,OAAS,GAC3B,GAAGX,MAAM,MAAMW,OAGC,SAAUkX,EAAWC,GACnC,IP7CqBla,EACrBma,EO4CI5N,EAASvK,GAASS,EAAuBnC,OACzC8Z,OAAgB7X,IAAV2X,EAAsBT,GAAaS,IAAU,EACvD,GAAY,IAARE,EAAW,MAAO,GACtB,QAAkB7X,IAAd0X,EAAyB,MAAO,CAAC1N,GAErC,IPhDGxG,EAFkB/F,EOkDPia,WPhDiC1X,KAA1B4X,EAAWna,EAAG8Y,KAA0BqB,EAA0B,UAAf9X,EAAQrC,IOiD9E,OAAOc,EAAKgZ,EAAavN,EAAQ0N,EAAWG,GAW9C,IATA,IAQIva,EAAO8O,EAAW0L,EARlBC,EAAS,GACTzJ,GAASoJ,EAAU/L,WAAa,IAAM,KAC7B+L,EAAU9L,UAAY,IAAM,KAC5B8L,EAAU5L,QAAU,IAAM,KAC1B4L,EAAU3L,OAAS,IAAM,IAClCiM,EAAgB,EAEhBC,EAAgB,IAAIhM,OAAOyL,EAAUlQ,OAAQ8G,EAAQ,MAElDhR,EAAQiB,EAAK8X,GAAY4B,EAAejO,QAC7CoC,EAAY6L,EAAc7L,WACV4L,IACd7V,GAAK4V,EAAQpY,GAAYqK,EAAQgO,EAAe1a,EAAMyK,QAClDzK,EAAMkD,OAAS,GAAKlD,EAAMyK,MAAQiC,EAAOxJ,QAAQ9B,GAAMyY,GAAOY,EAAQG,GAAW5a,EAAO,IAC5Fwa,EAAaxa,EAAM,GAAGkD,OACtBwX,EAAgB5L,EACZ2L,EAAOvX,QAAUqX,KAEnBI,EAAc7L,YAAc9O,EAAMyK,OAAOkQ,EAAc7L,YAK7D,OAHI4L,IAAkBhO,EAAOxJ,QACvBsX,GAAe7Z,GAAKga,EAAe,KAAK9V,GAAK4V,EAAQ,IACpD5V,GAAK4V,EAAQpY,GAAYqK,EAAQgO,IACjCD,EAAOvX,OAASqX,EAAMK,GAAWH,EAAQ,EAAGF,GAAOE,GAGnD,IAAIlY,WAAMG,EAAW,GAAGQ,OACjB,SAAUkX,EAAWC,GACnC,YAAqB3X,IAAd0X,GAAqC,IAAVC,EAAc,GAAKpZ,EAAKgZ,EAAaxZ,KAAM2Z,EAAWC,IAErEJ,EAEhB,CAGL,SAAeG,EAAWC,GACxB,IAAIpT,EAAIrE,EAAuBnC,MAC3Boa,EAAwBnY,MAAb0X,OAAyB1X,EAAY4D,GAAU8T,EAAWJ,GACzE,OAAOa,EACH5Z,EAAK4Z,EAAUT,EAAWnT,EAAGoT,GAC7BpZ,EAAKkZ,EAAehY,GAAS8E,GAAImT,EAAWC,IAOlD,SAAU3N,EAAQ2N,GAChB,IAAIS,EAAKpT,GAASjH,MACdwN,EAAI9L,GAASuK,GACbqO,EAAMb,EAAgBC,EAAeW,EAAI7M,EAAGoM,EAAOF,IAAkBF,GAEzE,GAAIc,EAAI/B,KAAM,OAAO+B,EAAIlZ,MAEzB,IAAIqT,EAAI8F,GAAmBF,EAAInM,QAE3BsM,EAAkBH,EAAGtM,QACrBwC,GAAS8J,EAAGzM,WAAa,IAAM,KACtByM,EAAGxM,UAAY,IAAM,KACrBwM,EAAGtM,QAAU,IAAM,KACnBI,GAAgB,IAAM,KAI/BiM,EAAW,IAAI3F,EAAEtG,GAAgB,OAASkM,EAAG5Q,OAAS,IAAM4Q,EAAI9J,GAChEuJ,OAAgB7X,IAAV2X,EAAsBT,GAAaS,IAAU,EACvD,GAAY,IAARE,EAAW,MAAO,GACtB,GAAiB,IAAbtM,EAAE/K,OAAc,OAAuC,OAAhCgY,GAAeL,EAAU5M,GAAc,CAACA,GAAK,GAIxE,IAHA,IAAIsJ,EAAI,EACJ4D,EAAI,EACJC,EAAI,GACDD,EAAIlN,EAAE/K,QAAQ,CACnB2X,EAAS/L,UAAYF,GAAgB,EAAIuM,EACzC,IACIE,EADAC,EAAIJ,GAAeL,EAAUjM,GAAgBvM,GAAY4L,EAAGkN,GAAKlN,GAErE,GACQ,OAANqN,IACCD,EAAI7Q,GAAIK,GAASgQ,EAAS/L,WAAaF,GAAgBuM,EAAI,IAAKlN,EAAE/K,WAAaqU,EAEhF4D,EAAII,GAAmBtN,EAAGkN,EAAGF,OACxB,CAEL,GADApW,GAAKuW,EAAG/Y,GAAY4L,EAAGsJ,EAAG4D,IACtBC,EAAElY,SAAWqX,EAAK,OAAOa,EAC7B,IAAK,IAAI5P,EAAI,EAAGA,GAAK8P,EAAEpY,OAAS,EAAGsI,IAEjC,GADA3G,GAAKuW,EAAGE,EAAE9P,IACN4P,EAAElY,SAAWqX,EAAK,OAAOa,EAE/BD,EAAI5D,EAAI8D,GAIZ,OADAxW,GAAKuW,EAAG/Y,GAAY4L,EAAGsJ,IAChB6D,OAGTtB,GAAmClL,IC1JvC,OAAiB,gDCIb3E,GAAU7H,EAAY,GAAG6H,SACzBuR,GAAa,IAAMC,GAAc,IACjCC,GAAQ/M,OAAO,IAAM6M,GAAaA,GAAa,KAC/CG,GAAQhN,OAAO6M,GAAaA,GAAa,MAGzC1Q,GAAe,SAAU5B,GAC3B,OAAO,SAAU8B,GACf,IAAI0B,EAASvK,GAASS,EAAuBoI,IAG7C,OAFW,EAAP9B,IAAUwD,EAASzC,GAAQyC,EAAQgP,GAAO,KACnC,EAAPxS,IAAUwD,EAASzC,GAAQyC,EAAQiP,GAAO,KACvCjP,OAIM,CAGfoH,MAAOhJ,GAAa,GAGpB4J,IAAK5J,GAAa,GAGlB8Q,KAAM9Q,GAAa,IC7BjBgM,GAAuBtN,GAAsCH,OCE7DwS,GAAQrS,GAAoCoS,cCuChCE,GAAWC,UACrBA,EAAM7Y,OAAe6Y,EAAM,GAAGC,QAAQrP,cACnC,MDpCP,CAAEZ,OAAQ,SAAU+B,OAAO,EAAMZ,ODClB,SAAUG,GACzB,OAAOxM,GAAM,WACX,QAAS4a,GAAYpO,MANf,QAAA,MAOGA,MACHyJ,IAAwB2E,GAAYpO,GAAazH,OAASyH,KCLzB4O,CAAuB,SAAW,CAC3EL,KAAM,WACJ,OAAOC,GAAMpb,YEJf,CAAED,QAAQ,GAAQ,CAClBH,WAAYG,IDgBV0b,WAAQC,UAAEja,GAAGga,OAASA,UACtBE,OAAID,UAAEja,GAAGka,GAAKA,MACdC,UAAOF,UAAEja,GAAGma,MAAQA,SACpBC,SAAMH,UAAEja,GAAGoa,KAAOA,QAClB/V,QAAK4V,UAAEja,GAAGqE,IAAMA,OAChBgK,SAAM4L,UAAEja,GAAGqO,KAAOA,QAClBgM,YAASJ,UAAEja,GAAGqa,QAAUA,WACxBC,aAAUL,UAAEja,GAAGsa,SAAWA,YAC1BC,gBAAaN,UAAEja,GAAGua,YAAcA,eAChC/F,aAAUyF,UAAEja,GAAGwU,SAAWA,YAC1BgG,SAAMP,UAAEja,GAAGwa,KAAOA,QAClBvG,SAAMgG,UAAEja,GAAGiU,KAAOA,QEnBtB,IAAIwG,IAAuB,EACvBC,GAAuB,EACvBC,GAAyB,EACzBC,GAAyC,KACzCC,GAAyC,KACvCC,GAAQb,UAAE,QAwBhB,SAASc,GAAUC,GACjBP,IAAuB,EACvBI,GAAsB,KACtBD,GAAwB,KAGxBE,GAAMG,IAAI,YAAaC,IACvBJ,GAAMG,IAAI,UAAWF,IATvBD,GAAMZ,GAAG,aApBT,SAAqBc,OACbG,EAAOH,EAAMnR,WACE,OAAjBsR,EAAKrB,SAAqC,OAAjBqB,EAAKrB,UAER,eAAtBqB,EAAKhN,MAAMiN,QACfD,EAAKhN,MAAMiN,OAAS,OAEpBJ,EAAMK,iBAGNZ,IAAuB,MACfa,EAAYN,UACpBN,GAAuBY,MACfC,EAAUJ,EAAKK,8BACvBb,GAAyBY,EAGzBT,GAAMZ,GAAG,YAAagB,IACtBJ,GAAMZ,GAAG,UAAWa,QActB,IAAMG,GAAcO,WAAS,SAAUT,MAChCP,IACsB,MAAvBI,IAAwD,MAAzBD,IACnCI,EAAMK,qBAEEC,EAAYN,UAChBU,EAAUf,IAA0BW,EAAUZ,KAClDgB,EAAUxd,KAAKiK,MAAgB,IAAVuT,GAAiB,KACxB,KAAIA,EAAU,IAG5BC,aAAWC,SACTf,GACA,CAAEU,MAAOG,EAAQzb,YACjB,CACEkS,GAAIyI,QAGP,KC9DI,IAAMiB,GAAkB,CAC7B5U,KAAM,QACN6U,WCgCF,SAAqBC,EAAwBvH,EAA0BxC,OAE/DgK,EA7BR,SAA4BhK,EAAoBiK,MAC1CjK,EAAOkK,aAAc,OAAO,MAExB/K,EAAca,eACL,MAAbb,EAAmB,OAAO,KAC1BgL,QAAMC,YAAYjL,GAAY,OAAO,MAEjCtF,EAAkBsF,SAAVgJ,EAAUhJ,QACpBkL,EAAY5K,YAAU6K,SAAStK,EAAQiK,GAEvCM,EAAajL,SAAOM,MAAMI,EAAQqK,GAClCG,EAAWlL,SAAOkB,IAAIR,EAAQqK,GAC9BI,EACJ5K,QAAM6K,QAAQ7Q,EAAQ2Q,IAAa,GAAK3K,QAAM6K,QAAQ7Q,EAAQ0Q,IAAe,EACzEI,EACJ9K,QAAM6K,QAAQvC,EAAOqC,IAAa,GAAK3K,QAAM6K,QAAQvC,EAAOoC,IAAe,WAGzEE,GAAmBE,GACjBC,OAAK9K,OAAOjG,EAAOgR,KAAKzc,MAAM,EAAG,GAAI+Z,EAAM0C,KAAKzc,MAAM,EAAG,KAU9C0c,CAAmB9K,EAAQ+J,GAGpCgB,EAAmBhB,QAAnBR,aAAQ,SAGVyB,EAAWvL,YAAUwL,eAAejL,EAAQ+J,GAG5CmB,EAAgB7I,GAAiB0H,GAEjCoB,EACJC,aACEC,UAAU,kCACKL,EACf9C,GAAI,CACFoD,UAAW,SAACnE,MAEe,QAArBA,EAAEtP,OAAOiQ,SAAmBX,EAAEkC,kBAE9BrJ,EAAOkK,kBAGLG,EAAY5K,YAAU6K,SAAStK,EAAQ+J,GACvCQ,EAAajL,SAAOM,MAAMI,EAAQqK,GAChClL,EAAca,eACL,MAAbb,EAIaA,EAAUtF,YAClB,KAAOwQ,EAAU,IAE1BrK,EAAOuL,OAAOhB,QANZvK,EAAOuL,OAAOhB,OAUpBa,eAAO7B,MAAOA,EAAOiC,gBAAiBxB,GACpCoB,sBACGF,EAAcrJ,KAAI,SAAAa,OACTqI,EAAmBrI,QAAnB6G,aAAQ,gBACT6B,aAAK7B,MAAOA,QAGvB6B,mBAAQ5I,YAIP2I,ID/EIM,GAAqB,CAChCxW,KAAM,YACN6U,WEPF,SACEC,EACAvH,EACAxC,UAEcoL,gBAAK5I,KFKRkJ,GAAsB,CACjCzW,KAAM,aACN6U,WDoDF,SACE6B,EACAnJ,EACAxC,OAEM4L,WrB5CyB5L,EAAoB2L,OAC7CE,EAAUpM,YAAUqM,cAAc9L,EAAQ2L,MACjC,MAAXE,EAAiB,OAAO,MACtBvJ,EAAY7C,YAAUqM,cAAc9L,EAAQ6L,UACjC,MAAbvJ,GAEkBD,GAAiBC,GAClBP,MAAK,SAAAgK,UAAKA,IAAMJ,KqBqClBK,CAAiBhM,EAAQ2L,GACtCZ,EAAiDY,EAA/CM,YAAAC,aAAU,IAAGC,YAAAC,aAAU,IAAGC,aAAA1J,oBAG7BiJ,SAEDR,YAAIc,QAASA,EAASE,QAASA,GAC5B5J,OAMD8J,EAAM3J,EAAW,KAAO,KAExBwI,EACJC,MAACkB,GACCJ,QAASA,EACTE,QAASA,EACTjQ,MAAO,CAAEoQ,iBAAkB,OAC3BrE,GAAI,CACFsE,UAAW/C,WAAS,SAAuBT,OACnCG,EAAO5c,KAAKkgB,OACN,MAARtD,OACE4B,EAA+B5B,EAAKK,wBAAlCkD,SAAMnD,UAAOoD,QAAKC,WAClBtD,EAAqBN,UAAZ6D,EAAY7D,cAEzBP,GAGWa,EAAUoD,EAAOnD,EAAQ,GAAKD,EAAUoD,EAAOnD,IAC/CsD,EAAUF,GAAOE,EAAUF,EAAMC,IAG9CzD,EAAKhN,MAAMiN,OAAS,aACpBP,GAAsB7I,EACtB4I,GAAwBnJ,YAAU6K,SAAStK,EAAQ2L,IAE9ClD,KACHU,EAAKhN,MAAMiN,OAAS,OACpBP,GAAsB,KACtBD,GAAwB,SAG3B,OAGJpG,UAGE2I,IInGF,IAAM2B,GAAkB,CAC7B7X,KAAM,QACN8X,WAvBF,SAAqBhD,EAAmBiD,OAC9BjC,EAAmBhB,cAEpB,oCAFS,uBAEiCiD,uBAuBtCC,GAAqB,CAChChY,KAAM,YACN8X,WAtBF,SAAwB5D,EAAe6D,SAC9B,OAAOA,YAwBHE,GAAsB,CACjCjY,KAAM,aACN8X,WAvBF,SAAyBpB,EAAmBqB,OACpCjC,EAKFY,EAJFM,YAAAC,aAAU,IACVC,YAAAC,aAAU,IACVC,aAAA1J,gBACAwK,UAEI3T,EAAMmJ,EAAW,KAAO,WACvB,IAAInJ,eAAgB0S,gBAAqBE,0BAHtC,eAGmEY,OAAiBxT,QCEzF,IAAM4T,GAAwB,CACnCC,SAAU,QACVC,aAnBF,SAAkBrD,OACVsD,EAAStF,UAAEgC,MAED,UADArC,GAAW2F,GACF,OAAOtD,MAG1BuD,EAASD,EAAOtL,KAAK,YACL,IAAlBuL,EAAOxe,OAAc,OAAOib,MAG1BwD,EAAMF,EAAOtL,KAAK,aACxBsL,EAAOvF,OAAOyF,GACdD,EAAOE,SAEAH,EAAO,KCvBZI,GAAUrY,GAAwCwM,UAQpD,CAAEjK,OAAQ,QAAS+B,OAAO,EAAMZ,QALRgG,GAA6B,WAKW,CAChE8C,OAAQ,SAAgBN,GACtB,OAAOmM,GAAQphB,KAAMiV,EAAYrU,UAAU6B,OAAS,EAAI7B,UAAU,QAAKqB,MCT3E,IAAI2H,GAAQjK,KAAKiK,MACbgH,GAASjP,EAAY,GAAGiP,QACxBpH,GAAU7H,EAAY,GAAG6H,SACzB5H,GAAcD,EAAY,GAAGE,OAC7Bwf,GAAuB,8BACvBC,GAAgC,yBAInB,SAAUC,EAASnQ,EAAK4H,EAAUwI,EAAUC,EAAehW,GAC1E,IAAIiW,EAAU1I,EAAWuI,EAAQ9e,OAC7Bkf,EAAIH,EAAS/e,OACbmf,EAAUN,GAKd,YAJsBrf,IAAlBwf,IACFA,EAAgBhd,GAASgd,GACzBG,EAAUP,IAEL7X,GAAQiC,EAAamW,GAAS,SAAUriB,EAAOsiB,GACpD,IAAIC,EACJ,OAAQlR,GAAOiR,EAAI,IACjB,IAAK,IAAK,MAAO,IACjB,IAAK,IAAK,OAAON,EACjB,IAAK,IAAK,OAAO3f,GAAYwP,EAAK,EAAG4H,GACrC,IAAK,IAAK,OAAOpX,GAAYwP,EAAKsQ,GAClC,IAAK,IACHI,EAAUL,EAAc7f,GAAYigB,EAAI,GAAI,IAC5C,MACF,QACE,IAAI5O,GAAK4O,EACT,GAAU,IAAN5O,EAAS,OAAO1T,EACpB,GAAI0T,EAAI0O,EAAG,CACT,IAAI7a,EAAI8C,GAAMqJ,EAAI,IAClB,OAAU,IAANnM,EAAgBvH,EAChBuH,GAAK6a,OAA8B1f,IAApBuf,EAAS1a,EAAI,GAAmB8J,GAAOiR,EAAI,GAAKL,EAAS1a,EAAI,GAAK8J,GAAOiR,EAAI,GACzFtiB,EAETuiB,EAAUN,EAASvO,EAAI,GAE3B,YAAmBhR,IAAZ6f,EAAwB,GAAKA,MCvBpCC,GAAUzc,GAAgB,WAC1BwE,GAAMnK,KAAKmK,IACXC,GAAMpK,KAAKoK,IACXkB,GAAStJ,EAAY,GAAGsJ,QACxB7G,GAAOzC,EAAY,GAAGyC,MACtB4d,GAAgBrgB,EAAY,GAAGiJ,SAC/BhJ,GAAcD,EAAY,GAAGE,OAQ7BogB,GAEgC,OAA3B,IAAIzY,QAAQ,IAAK,MAItB0Y,KACE,IAAIH,KAC6B,KAA5B,IAAIA,IAAS,IAAK,SAiBC,WAAW,SAAUI,EAAG1R,EAAegJ,GACnE,IAAI2I,EAAoBF,GAA+C,IAAM,KAE7E,MAAO,CAGL,SAAiBG,EAAaC,GAC5B,IAAI9b,EAAIrE,EAAuBnC,MAC3BuiB,EAA0BtgB,MAAfogB,OAA2BpgB,EAAY4D,GAAUwc,EAAaN,IAC7E,OAAOQ,EACH/hB,EAAK+hB,EAAUF,EAAa7b,EAAG8b,GAC/B9hB,EAAKiQ,EAAe/O,GAAS8E,GAAI6b,EAAaC,IAIpD,SAAUrW,EAAQqW,GAChB,IAAIjI,EAAKpT,GAASjH,MACdwN,EAAI9L,GAASuK,GAEjB,GACyB,iBAAhBqW,IAC6C,IAApDN,GAAcM,EAAcF,KACW,IAAvCJ,GAAcM,EAAc,MAC5B,CACA,IAAIhI,EAAMb,EAAgBhJ,EAAe4J,EAAI7M,EAAG8U,GAChD,GAAIhI,EAAI/B,KAAM,OAAO+B,EAAIlZ,MAG3B,IAAIohB,EAAoBngB,EAAWigB,GAC9BE,IAAmBF,EAAe5gB,GAAS4gB,IAEhD,IAAIviB,EAASsa,EAAGta,OAChB,GAAIA,EAAQ,CACV,IAAI0iB,EAAcpI,EAAGtM,QACrBsM,EAAGhM,UAAY,EAGjB,IADA,IAAIqU,EAAU,KACD,CACX,IAAI/c,EAASgd,GAAWtI,EAAI7M,GAC5B,GAAe,OAAX7H,EAAiB,MAGrB,GADAvB,GAAKse,EAAS/c,IACT5F,EAAQ,MAGI,KADF2B,GAASiE,EAAO,MACV0U,EAAGhM,UAAYyM,GAAmBtN,EAAGpD,GAASiQ,EAAGhM,WAAYoU,IAKpF,IAFA,IA/EwB/iB,EA+EpBkjB,EAAoB,GACpBC,EAAqB,EAChB9X,EAAI,EAAGA,EAAI2X,EAAQjgB,OAAQsI,IAAK,CAWvC,IARA,IAAIwW,EAAU7f,IAFdiE,EAAS+c,EAAQ3X,IAEa,IAC1BiO,EAAWlP,GAAIC,GAAIG,GAAoBvE,EAAOqE,OAAQwD,EAAE/K,QAAS,GACjE+e,EAAW,GAMNsB,EAAI,EAAGA,EAAInd,EAAOlD,OAAQqgB,IAAK1e,GAAKod,OA3FrCvf,KADcvC,EA4F+CiG,EAAOmd,IA3FxDpjB,EAAK0D,OAAO1D,IA4FhC,IAAI+hB,EAAgB9b,EAAO6K,OAC3B,GAAIgS,EAAmB,CACrB,IAAIO,EAAe9X,GAAO,CAACsW,GAAUC,EAAUxI,EAAUxL,QACnCvL,IAAlBwf,GAA6Brd,GAAK2e,EAActB,GACpD,IAAIhW,EAAc/J,GAASf,GAAM2hB,OAAcrgB,EAAW8gB,SAE1DtX,EAAcuX,GAAgBzB,EAAS/T,EAAGwL,EAAUwI,EAAUC,EAAea,GAE3EtJ,GAAY6J,IACdD,GAAqBhhB,GAAY4L,EAAGqV,EAAoB7J,GAAYvN,EACpEoX,EAAqB7J,EAAWuI,EAAQ9e,QAG5C,OAAOmgB,EAAoBhhB,GAAY4L,EAAGqV,SAvFXziB,GAAM,WACzC,IAAIgO,EAAK,IAOT,OANAA,EAAGlO,KAAO,WACR,IAAIyF,EAAS,GAEb,OADAA,EAAO6K,OAAS,CAAElK,EAAG,KACdX,GAGyB,MAA3B,GAAG6D,QAAQ4E,EAAI,aAkFc6T,IAAoBC,IC5FnD,IAAMe,GAAoB,CAC/BnC,SAAU,kDACVoC,cAnCF,SACEtG,EACA3G,EACAxC,OAEM6H,EAAQI,UAAEkB,GASQ,KAPxB3G,EAAWA,EAASV,QAAO,SAAA4N,WACrBC,OAAKC,OAAOF,MACZ1P,EAAO6P,SAASH,OAKT1gB,SACXwT,EAAW,CAAC,CAAEsN,KAAMjI,EAAMiI,OAAO/Z,QAAQ,QAAS,YAG9CmW,EAAU6D,SAASlI,EAAMO,KAAK,YAAc,KAC5CgE,EAAU2D,SAASlI,EAAMO,KAAK,YAAc,KAC5CmB,EAAQ1B,EAAMO,KAAK,UAAY,aAE9B,CACLnT,KAAM,aACN0N,SAAgC,OAAtBiF,GAAWC,GACrBqE,UACAE,UACA7C,QAEA/G,cAqBG,IAAMwN,GAAmB,CAC9B3C,SAAU,0BACVoC,cAdF,SACEtG,EACA3G,EACAxC,SAEO,CACL/K,KAAM,YAENuN,SAAUA,EAASV,QAAO,SAAA4N,SAA0C,eAAjCjQ,YAAUW,YAAYsP,SA6BtD,IAAMO,GAAqB,CAChC5C,SAAU,6BACVoC,cAtBF,SACEtG,EACA3G,EACAxC,OAEM6H,EAAQI,UAAEkB,GAGZI,EAAQ,aAC0B,kBXvBV1B,EAAkBqI,WAC1CrJ,EAAM,GAGJsJ,GADWtI,EAAMO,KAAK,UAAY,IACd/Z,MAAM,KAC1BW,EAASmhB,EAASnhB,OACfsI,EAAI,EAAGA,EAAItI,EAAQsI,IAAK,KACzB8Y,EAAeD,EAAS7Y,MAC1B8Y,EAAc,KACVC,EAAMD,EAAa/hB,MAAM,KAC3BgiB,EAAI,GAAG3I,SAAWwI,IACpBrJ,EAAMwJ,EAAI,GAAG3I,gBAKZb,EWOHyJ,CAAczI,EAAO,WAAqB0B,EAAQ,QAC1B,SAAxB1B,EAAMO,KAAK,WAAqBmB,EAAQ,QAErC,CACLtU,KAAM,QACNsU,QAEA/G,SAAUA,EAASV,QAAO,SAAA4N,SAA0C,cAAjCjQ,YAAUW,YAAYsP,SC/B7D,0CACUa,IAAE,wCCtCV,8TDwCM,6BACU,gBACqB,YAErCC,qBAAA,SAASxQ,SAEA,IAGTwQ,qBAAA,SAASxQ,UAEA,GAGTwQ,iBAAA,SAAKxQ,EAAoBrS,KAKzB6iB,uBAAA,SAAWxQ,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,MAEDM,YAAUgR,iBAAiBzQ,GACP+B,MAAK,SAAAoH,OACvClU,EAAOwK,YAAUW,YAAY+I,SACtB,QAATlU,IACS,UAATA,IACS,cAATA,KACA+K,EAAO0Q,OAAOvH,UAYtBqH,gCAAA,SAAoBxQ,iBAEdzT,KAAKokB,SAAU,OAAOpkB,KAAKokB,SAAS,WAGlCA,EAAW1I,UAAE,+CACb2I,EAAQ3I,UAAE,4BAGVsF,EAAStF,UAAE,mBACR3Q,EAAI,EAAGA,EAAI,GAAIA,IAAK,SACrBmW,EAAMxF,UAAE,aACLoH,EAAI,EAAGA,EAAI,GAAIA,IAAK,KACrBwB,EAAM5I,UAAE,aACd4I,EAAIzI,KAAK,SAAUiH,EAAEphB,YACrB4iB,EAAIzI,KAAK,SAAU9Q,EAAErJ,YACrBwf,EAAIzF,OAAO6I,GAGXA,EAAI3I,GAAG,cAAc,SAACf,OACZtP,EAAWsP,YACL,MAAVtP,OAEEkT,EADW9C,UAAEpQ,GACuBwQ,UAA/ByI,MAAWC,MAGtBH,EAAM,GAAGI,UAAeF,EAAS,eAAaC,EAAS,GAGvDxD,EAAO/K,WAAWgG,MAAK,SAAAyI,GACrBhJ,UAAEgJ,GACCzO,WACAgG,MAAK,SAAA0I,OACEL,EAAM5I,UAAEiJ,GACRnG,EAAW8F,EAAIxI,UAAb8I,MAAGC,MACPD,GAAKL,GAAUM,GAAKL,EACtBF,EAAIvI,SAAS,UAEbuI,EAAItI,YAAY,oBAO1BsI,EAAI3I,GAAG,SAAS,SAACf,GACfA,EAAEkC,qBACMxR,EAAWsP,YACL,MAAVtP,OAEEkT,EADM9C,UAAEpQ,GACOwQ,UAAb8I,MAAGC,MACXC,EAAKzlB,YAAYoU,EAAQoR,EAAI,EAAGD,EAAI,OAGxC5D,EAAOvF,OAAOyF,UAEhBkD,EAAS3I,OAAOuF,GAChBoD,EAAS3I,OAAO4I,QAGXD,SAAWA,EACTA,EAAS,IAGVH,wBAAR,SAAoBxQ,EAAoBsR,EAAmBC,OACnDC,EAASzB,SAASuB,EAAW,IAC7BG,EAAS1B,SAASwB,EAAW,OAC9BC,GAAWC,KACZD,GAAU,GAAKC,GAAU,IAGzBhS,YAAUiS,yBAAyB1R,IACrC2J,aAAWgI,YAAY3R,EAAQ,CAAEpP,KAAM,gBAInC0R,EA/JV,SAAsBkP,EAAgBC,WAE9BlP,EAA0B,GACvBjL,EAAI,EAAGA,EAAIka,EAAQla,IAAK,SAEzBsa,EAA4B,GACzBvC,EAAI,EAAGA,EAAIoC,EAAQpC,IAAK,KACzB1D,EAA6B,CACjC1W,KAAM,aACNuN,SAAU,CAAC,CAAEsN,KAAM,MAEX,IAANxY,IACFqU,EAAShJ,UAAW,GAEtBiP,EAAMjhB,KAAKgb,GAIbpJ,EAAK5R,KAAK,CACRsE,KAAM,YACNuN,SAAUoP,UAIP,CACL3c,KAAM,QACNsU,MAAO,OACP/G,SAAUD,GAoIQsP,CAAaL,EAAQC,GACvC9H,aAAWmI,YAAY9R,EAAQsC,EAAW,CAAE1R,KAAM,yDElKnC2f,IAAE,wCDOnB,gxCCLe,gBAEfwB,qBAAA,SAAS/R,SAEA,IAGT+R,qBAAA,SAAS/R,UAEA,GAGT+R,uBAAA,SAAW/R,UACe,MAApBA,EAAOb,WAGM,MADCM,YAAUuS,sBAAsBhS,EAAQ,UAQ5D+R,iBAAA,SAAK/R,EAAoBrS,GACnBpB,KAAK2d,WAAWlK,IAGpB2J,aAAWgI,YAAY3R,EAAQ,CAAEpP,KAAM,wDC5BxB2f,IAAE,sCFUnB,+XERe,gBAEf0B,qBAAA,SAASjS,SAEA,IAGTiS,qBAAA,SAASjS,UAEA,GAGTiS,uBAAA,SAAWjS,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,IAGN,MADCM,YAAUuS,sBAAsBhS,EAAQ,WAQ5DiS,iBAAA,SAAKjS,EAAoBrS,OACnBpB,KAAK2d,WAAWlK,QAEd+K,EAAA1L,GAAcC,SAAOC,MAAMS,EAAQ,CACvClU,MAAO,SAAA0T,UAAKC,YAAUC,cAAcF,EAAG,eACvC0S,WAAW,OAEPjG,EAAA5M,WAACsM,OAAUhM,OAGXkM,EAAUpM,YAAUqM,cAAc9L,EAAQ2L,GAC1CwG,GAActG,MAAAA,SAAAA,EAASrJ,SAASxT,SAAU,KAC5B,IAAhBmjB,WAGEC,EAA0B,CAAEnd,KAAM,YAAauN,SAAU,IACtDlL,EAAI,EAAGA,EAAI6a,EAAa7a,IAAK,CAKpC8a,EAAO5P,SAAS7R,KAJe,CAC7BsE,KAAM,aACNuN,SAAU,CAAC,CAAEsN,KAAM,WAMjBuC,EAAUzH,OAAK0H,OAAO3S,GACtB4S,EAAa3H,OAAK4H,KAAKH,GAC7B1I,aAAWmI,YAAY9R,EAAQoS,EAAQ,CAAEjS,GAAIoS,kDCvD9BhC,IAAE,sCHenB,+fGbe,gBAEfkC,qBAAA,SAASzS,SAEA,IAGTyS,qBAAA,SAASzS,UAEA,GAGTyS,uBAAA,SAAWzS,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,IAGR,MADCM,YAAUuS,sBAAsBhS,EAAQ,eAQ1DyS,iBAAA,SAAKzS,EAAoBrS,OACnBpB,KAAK2d,WAAWlK,QAEd+K,EAAA1L,GAAaC,SAAOC,MAAMS,EAAQ,CACtClU,MAAO,SAAA0T,UAAKC,YAAUC,cAAcF,EAAG,cACvC0S,WAAW,OAEPjG,EAAA5M,WAACwM,OAASwG,OAEV/P,EAAY7C,YAAUqM,cAAc9L,EAAQ6L,KAC/BvJ,MAAAA,SAAAA,EAAWE,SAASxT,SAAU,IAC/B,EAEhB2a,aAAWgI,YAAY3R,EAAQ,CAAEpP,KAAM,YAKzC+Y,aAAWgI,YAAY3R,EAAQ,CAAEG,GAAIkS,cCrDxB,CACfK,YAAa,EACbC,oBAAqB,EACrBC,aAAc,EACdC,eAAgB,EAChBC,YAAa,EACbC,cAAe,EACfC,aAAc,EACdC,qBAAsB,EACtBC,SAAU,EACVC,kBAAmB,EACnBC,eAAgB,EAChBC,gBAAiB,EACjBC,kBAAmB,EACnBC,UAAW,EACXC,cAAe,EACfC,aAAc,EACdC,SAAU,EACVC,iBAAkB,EAClBC,OAAQ,EACRC,YAAa,EACbC,cAAe,EACfC,cAAe,EACfC,eAAgB,EAChBC,aAAc,EACdC,cAAe,EACfC,iBAAkB,EAClBC,iBAAkB,EAClBC,eAAgB,EAChBC,iBAAkB,EAClBC,cAAe,EACfC,UAAW,GC9BTC,GAAYvY,GAAsB,QAAQuY,UAC1CC,GAAwBD,IAAaA,GAAU7V,aAAe6V,GAAU7V,YAAY5R,aAEvE0nB,KAA0B9nB,OAAOI,eAAYwB,EAAYkmB,GCLtEC,GAAWrf,GAAwCsM,QAGnDgT,GCDa,SAAUzb,EAAaxK,GACtC,IAAII,EAAS,GAAGoK,GAChB,QAASpK,GAAUpC,GAAM,WAEvBoC,EAAOhC,KAAK,KAAM4B,GAAY,WAAc,MAAM,GAAM,MDHxCkmB,CAAoB,cAItBD,GAGd,GAAGhT,QAH2B,SAAiBJ,GACjD,OAAOmT,GAASpoB,KAAMiV,EAAYrU,UAAU6B,OAAS,EAAI7B,UAAU,QAAKqB,IEHtEsmB,GAAkB,SAAUC,GAE9B,GAAIA,GAAuBA,EAAoBnT,UAAYA,GAAS,IAClE/M,GAA4BkgB,EAAqB,UAAWnT,IAC5D,MAAOlV,GACPqoB,EAAoBnT,QAAUA,KAIlC,IAAK,IAAIoT,MAAmBC,GACtBA,GAAaD,KACfF,GAAgBxoB,EAAO0oB,KAAoB1oB,EAAO0oB,IAAiBhoB,WAIvE8nB,GAAgBJ,ICThB,0CACmBnE,IAAE,sCTgBnB,kYSde,gBAEf2E,qBAAA,SAASlV,SAEA,IAGTkV,qBAAA,SAASlV,UAEA,GAGTkV,uBAAA,SAAWlV,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,IAGN,MADCM,YAAUuS,sBAAsBhS,EAAQ,WAQ5DkV,iBAAA,SAAKlV,EAAoBrS,OACnBpB,KAAK2d,WAAWlK,QAEd+K,EAAA1L,GAAcC,SAAOC,MAAMS,EAAQ,CACvClU,MAAO,SAAA0T,UAAKC,YAAUC,cAAcF,EAAG,eACvC0S,WAAW,OAEPjG,EAAA5M,WAAC8V,OAAkBC,OAEnBvJ,EAAUpM,YAAUqM,cAAc9L,EAAQmV,MACjC,MAAXtJ,OACEvJ,EAAY7C,YAAUqM,cAAc9L,EAAQ6L,MACjC,MAAbvJ,GAGSA,EAAUE,UAAY,IAC9BZ,SAAQ,SAACyT,EAAKC,GACZC,UAAQC,UAAUH,KAETA,EAAI7S,UAAY,IAExBZ,SAAQ,SAACc,OACPmI,EAAOpL,YAAU6K,SAAStK,EAAQ0C,MAEtCmI,EAAK7b,SAAWomB,EAAiBpmB,QACjCymB,UAAQ5K,EAAKzc,OAAO,GAAIgnB,EAAiBhnB,OAAO,IAChD,KAGMsnB,EAA4B,CAAEzgB,KAAM,aAAcuN,SAAU,CAAC,CAAEsN,KAAM,MAC1D,IAAbwF,GAAkB7S,GAAkBH,KACtCoT,EAAQ/S,UAAW,GAErBgH,aAAWmI,YAAY9R,EAAQ0V,EAAS,CAAEvV,GAAI0K,yDC9DrC0F,IAAE,sCVsBnB,otBUpBe,gBAEfoF,qBAAA,SAAS3V,SAEA,IAGT2V,qBAAA,SAAS3V,UAEA,GAGT2V,uBAAA,SAAW3V,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,IAGP,MADCM,YAAUuS,sBAAsBhS,EAAQ,gBAQ3D2V,iBAAA,SAAK3V,EAAoBrS,OACnBpB,KAAK2d,WAAWlK,QAEd+K,EAAA1L,GAAcC,SAAOC,MAAMS,EAAQ,CACvClU,MAAO,SAAA0T,UAAKC,YAAUC,cAAcF,EAAG,eACvC0S,WAAW,OAEPjG,EAAA5M,WAAC8V,OAAkBC,OAGnBvJ,EAAUpM,YAAUqM,cAAc9L,EAAQmV,GAC1CS,GAAY/J,MAAAA,SAAAA,EAASrJ,SAASxT,SAAU,MACzC6c,GAAW+J,GAAa,EAC3BjM,aAAWgI,YAAY3R,EAAQ,CAAEpP,KAAM,qBAMnC0R,EAAY7C,YAAUqM,cAAc9L,EAAQ6L,MACjC,MAAbvJ,GAGSA,EAAUE,UAAY,IAC9BZ,SAAQ,SAAAyT,GACNE,UAAQC,UAAUH,KAETA,EAAI7S,UAAY,IAExBZ,SAAQ,SAACc,OACPmI,EAAOpL,YAAU6K,SAAStK,EAAQ0C,GAEtCmI,EAAK7b,SAAWomB,EAAiBpmB,QACjCymB,UAAQ5K,EAAKzc,OAAO,GAAIgnB,EAAiBhnB,OAAO,KAIhDub,aAAWgI,YAAY3R,EAAQ,CAAEG,GAAI0K,wDC/D5B0F,IAAE,mCXyBnB,waWvBe,gBAGfsF,qBAAA,SAAS7V,OACDsC,EAAY7C,YAAUuS,sBAAsBhS,EAAQ,gBACzC,MAAbsC,GAEGG,GAAkBH,IAG3BuT,qBAAA,SAAS7V,WACEzT,KAAKupB,SAAS9V,IAGzB6V,uBAAA,SAAW7V,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,IAGN,MADCM,YAAUuS,sBAAsBhS,EAAQ,WAQ5D6V,iBAAA,SAAK7V,EAAoBrS,OACnBpB,KAAK2d,WAAWlK,QAGd+V,GAAWpoB,EAGX2U,EAAY7C,YAAUuS,sBAAsBhS,EAAQ,YACzC,MAAbsC,EACkBD,GAAiBC,GAGzBV,SAAQ,SAAAc,UACpBiH,aAAWC,SACT5J,EACA,CAAE2C,SAAUoT,GACZ,CACE5V,GAAIV,YAAU6K,SAAStK,EAAQ0C,qDC/CtB6N,IAAE,sCZ8BnB,mmCY5Be,gBAGfyF,qBAAA,SAAShW,OACDsC,EAAY7C,YAAUuS,sBAAsBhS,EAAQ,gBACzC,MAAbsC,GACyC,SAArCA,EAA2BiH,OAGrCyM,qBAAA,SAAShW,WACEzT,KAAKupB,SAAS9V,IAGzBgW,uBAAA,SAAWhW,OACDb,EAAca,mBACL,MAAbb,KACCgL,QAAMC,YAAYjL,IAGN,MADCM,YAAUuS,sBAAsBhS,EAAQ,WAQ5DgW,iBAAA,SAAKhW,EAAoBrS,OACnBpB,KAAK2d,WAAWlK,QAEd/E,EAA+B,CACnCsO,MAAO5b,EAAQ,OAAS,QAE1Bgc,aAAWC,SAAS5J,EAAQ/E,EAAO,CAAErK,KAAM,mBCvBzCqlB,GAA8B,CAClCC,YAAa,CAACrM,GAAiB4B,GAAoBC,IACnDyK,YAAa,CAACrJ,GAAiBG,GAAoBC,IACnDI,aAAc,CAACF,IACfgJ,eAAgB,CAAC5G,GAAmBQ,GAAkBC,IACtDoG,MAAO,CCb0B,CACjC/lB,IAAK,cACLgmB,0BACS,IAAI9F,KAIoB,CACjClgB,IAAK,cACLgmB,0BACS,IAAIvE,KAImB,CAChCzhB,IAAK,iBACLgmB,0BACS,IAAIrE,KAImB,CAChC3hB,IAAK,iBACLgmB,0BACS,IAAI7D,KAImB,CAChCniB,IAAK,iBACLgmB,0BACS,IAAIpB,KAImB,CAChC5kB,IAAK,iBACLgmB,0BACS,IAAIX,KAIoB,CACjCrlB,IAAK,cACLgmB,0BACS,IAAIC,KAIuB,CACpCjmB,IAAK,iBACLgmB,0BACS,IAAIE,MD7BbC,apDsBF,SAAyCzW,OAErC0W,EAOE1W,cANF2W,EAME3W,iBALF4W,EAKE5W,gBAJF6W,EAIE7W,gBAHF8W,EAGE9W,aAFF+W,EAEE/W,YADFgX,EACEhX,YACEd,EAAYc,SAGlBd,EAAUwX,YAAc,WAEF,MADCjX,YAAUuS,sBAAsB9S,EAAW,SAQhEwX,IALExX,EAAU+X,WAAW,OASzB/X,EAAUyX,eAAiB,SAAAO,OACbjY,GAAcC,QAIlBC,EAAcD,eAClBC,EAAW,KACPgY,EAAS7X,SAAO6X,OAAOjY,EAAWC,MACpCgY,EAAQ,KACJC,EAA0BrX,GAAgBb,EAAWiY,GACrDE,EAAwBtX,GAAgBb,EAAWC,MACrDiY,IAA4BC,UAOpCV,EAAeO,KAIjBhY,EAAU6X,UAAY,oBACCtX,YAAUuS,sBAAsB9S,EAAW,cAExDoY,EAAQhY,SAAOgY,MAAMtX,GAGvBP,YAAUC,cAAc4X,EAAM,GAAI,eACpC3N,aAAW4B,OAAOvL,EAAQsX,EAAM,QAG9B9E,EAAOlT,SAAOkT,KAAKxS,MACnBwS,EACEA,EAAK,IAAOA,EAAK,GAAgB1C,OAEnC0C,YAAQlT,SAAOgY,MAAMtX,EAAQ,CAAEG,GAAIqS,EAAK,oBAAmCA,GAE7E7I,aAAW4B,OAAOvL,EAAQwS,EAAK,QAC1B,KACC+E,EAAgBrY,EAAUsD,UAAY,GACtCgV,EAAsBD,EAAcvoB,UAEtCyQ,YAAUC,cAAc6X,EAAcC,EAAsB,GAAI,SAAU,KACtEnU,EAAI5D,YAAUgY,oBACpB9N,aAAWmI,YAAY5S,EAAWmE,EAAG,CAAElD,GAAI,CAACqX,KAE5CtY,EAAU6X,mBAMhBA,KAGF7X,EAAU0X,cAAgB,SAAAM,GACZjY,GAAcC,IAI1B0X,EAAcM,IAIhBhY,EAAU2X,cAAgB,SAAC9L,OAAAkB,EAAA5M,QAACqY,OAAM7M,UAEnB,UADApL,YAAUW,YAAYsX,UAG1Bb,EAAc,CAACa,EAAM7M,OAIfpL,YAAUkY,WAAWzY,EAAWwY,GACnC,KACJrU,EAAI5D,YAAUgY,oBACpB9N,aAAWmI,YAAY5S,EAAWmE,EAAG,CAAElD,GAAI,CAAC0K,EAAK,GAAK,OAK1D3L,EAAU4X,WAAa,SAAC1e,MAEL,MADCqH,YAAUuS,sBAAsB9S,EAAW,cAOvD4Q,EAAO1X,EAAKwf,QAAQ,cAGb,OAAT9H,GAAiB,aAAa9b,KAAKoE,EAAKwf,QAAQ,cAClDd,EAAW1e,GAIbkH,SAAO2X,WAAW/X,EAAW4Q,QAb3BgH,EAAW1e,IAiBf8G,EAAU8X,UAAY,eACd7X,EAAYD,EAAUC,aACX,MAAbA,OAKEuD,EAAOjD,YAAUuS,sBAAsB9S,EAAW,iBAC5C,MAARwD,OAKI7I,EAAkBsF,SAAVgJ,EAAUhJ,WACrByL,OAAK9K,OAAOjG,EAAOgR,KAAKzc,MAAM,EAAG,GAAI+Z,EAAM0C,KAAKzc,MAAM,EAAG,OAQ3C,IAFNypB,OAAKrf,OAAOkK,GACD1T,YAMlB6b,EAAOpL,YAAU6K,SAASpL,EAAWwD,GAGrCoV,EAAe,CACnBje,OAHYyF,SAAOM,MAAMV,EAAW2L,GAIpC1C,MAHU7I,SAAOkB,IAAItB,EAAW2L,IAKlC3L,EAAUqM,OAAOuM,QAXfd,SAPAA,SAPAA,SANAA,KAqCG9X"}