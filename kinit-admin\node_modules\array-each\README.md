# array-each [![NPM version](https://img.shields.io/npm/v/array-each.svg?style=flat)](https://www.npmjs.com/package/array-each) [![NPM monthly downloads](https://img.shields.io/npm/dm/array-each.svg?style=flat)](https://npmjs.org/package/array-each)  [![NPM total downloads](https://img.shields.io/npm/dt/array-each.svg?style=flat)](https://npmjs.org/package/array-each) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/array-each.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/array-each)

> Loop over each item in an array and call the given function on every element.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save array-each
```

## Usage

### [each](index.js#L34)

Loop over each item in an array and call the given function on every element.

**Params**

* `array` **{Array}**
* `fn` **{Function}**
* `thisArg` **{Object}**: (optional) pass a `thisArg` to be used as the context in which to call the function.
* `returns` **{undefined}**

**Example**

```js
each(['a', 'b', 'c'], function(ele) {
  return ele + ele;
});
//=> ['aa', 'bb', 'cc']

each(['a', 'b', 'c'], function(ele, i) {
  return i + ele;
});
//=> ['0a', '1b', '2c']
```

## About

### Related projects

* [arr-filter](https://www.npmjs.com/package/arr-filter): Faster alternative to javascript's native filter method. | [homepage](https://github.com/jonschlinkert/arr-filter "Faster alternative to javascript's native filter method.")
* [arr-map](https://www.npmjs.com/package/arr-map): Faster, node.js focused alternative to JavaScript's native array map. | [homepage](https://github.com/jonschlinkert/arr-map "Faster, node.js focused alternative to JavaScript's native array map.")
* [collection-map](https://www.npmjs.com/package/collection-map): Returns an array of mapped values from an array or object. | [homepage](https://github.com/jonschlinkert/collection-map "Returns an array of mapped values from an array or object.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.2, on February 26, 2017._