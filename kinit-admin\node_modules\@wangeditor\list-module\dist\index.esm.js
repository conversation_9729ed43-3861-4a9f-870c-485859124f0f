import{i18nAddResources as t,DomEditor as e,t as r}from"@wangeditor/core";import{Path as n,Editor as o,Text as i,Range as u,Transforms as a}from"slate";import{jsx as c}from"snabbdom";import f,{append as l,attr as s,parent as v}from"dom7";t("en",{listModule:{unOrderedList:"Unordered list",orderedList:"Ordered list"}}),t("zh-CN",{listModule:{unOrderedList:"无序列表",orderedList:"有序列表"}});var d="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function p(t){var e={exports:{}};return t(e,e.exports),e.exports}var y,h,g=function(t){return t&&t.Math==Math&&t},b=g("object"==typeof globalThis&&globalThis)||g("object"==typeof window&&window)||g("object"==typeof self&&self)||g("object"==typeof d&&d)||function(){return this}()||Function("return this")(),m=Function.prototype,w=m.bind,x=m.call,S=w&&w.bind(x),O=w?function(t){return t&&S(x,t)}:function(t){return t&&function(){return x.apply(t,arguments)}},E=function(t){try{return!!t()}catch(t){return!0}},T=O({}.toString),j=O("".slice),A=function(t){return j(T(t),8,-1)},I=b.Object,P=O("".split),L=E((function(){return!I("z").propertyIsEnumerable(0)}))?function(t){return"String"==A(t)?P(t,""):I(t)}:I,R=b.TypeError,k=function(t){if(null==t)throw R("Can't call method on "+t);return t},_=function(t){return L(k(t))},N=Object.defineProperty,M=function(t,e){try{N(b,t,{value:e,configurable:!0,writable:!0})}catch(r){b[t]=e}return e},z=b["__core-js_shared__"]||M("__core-js_shared__",{}),C=p((function(t){(t.exports=function(t,e){return z[t]||(z[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),F=b.Object,D=function(t){return F(k(t))},H=O({}.hasOwnProperty),B=Object.hasOwn||function(t,e){return H(D(t),e)},G=0,$=Math.random(),V=O(1..toString),W=function(t){return"Symbol("+(void 0===t?"":t)+")_"+V(++G+$,36)},U=function(t){return"function"==typeof t},K=function(t){return U(t)?t:void 0},Y=function(t,e){return arguments.length<2?K(b[t]):b[t]&&b[t][e]},X=Y("navigator","userAgent")||"",q=b.process,J=b.Deno,Q=q&&q.versions||J&&J.version,Z=Q&&Q.v8;Z&&(h=(y=Z.split("."))[0]>0&&y[0]<4?1:+(y[0]+y[1])),!h&&X&&(!(y=X.match(/Edge\/(\d+)/))||y[1]>=74)&&(y=X.match(/Chrome\/(\d+)/))&&(h=+y[1]);var tt,et=h,rt=!!Object.getOwnPropertySymbols&&!E((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&et&&et<41})),nt=rt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,ot=C("wks"),it=b.Symbol,ut=it&&it.for,at=nt?it:it&&it.withoutSetter||W,ct=function(t){if(!B(ot,t)||!rt&&"string"!=typeof ot[t]){var e="Symbol."+t;rt&&B(it,t)?ot[t]=it[t]:ot[t]=nt&&ut?ut(e):at(e)}return ot[t]},ft=function(t){return"object"==typeof t?null!==t:U(t)},lt=b.String,st=b.TypeError,vt=function(t){if(ft(t))return t;throw st(lt(t)+" is not an object")},dt=!E((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),pt=b.document,yt=ft(pt)&&ft(pt.createElement),ht=function(t){return yt?pt.createElement(t):{}},gt=!dt&&!E((function(){return 7!=Object.defineProperty(ht("div"),"a",{get:function(){return 7}}).a})),bt=Function.prototype.call,mt=bt.bind?bt.bind(bt):function(){return bt.apply(bt,arguments)},wt=O({}.isPrototypeOf),xt=b.Object,St=nt?function(t){return"symbol"==typeof t}:function(t){var e=Y("Symbol");return U(e)&&wt(e.prototype,xt(t))},Ot=b.String,Et=function(t){try{return Ot(t)}catch(t){return"Object"}},Tt=b.TypeError,jt=function(t){if(U(t))return t;throw Tt(Et(t)+" is not a function")},At=function(t,e){var r=t[e];return null==r?void 0:jt(r)},It=b.TypeError,Pt=b.TypeError,Lt=ct("toPrimitive"),Rt=function(t,e){if(!ft(t)||St(t))return t;var r,n=At(t,Lt);if(n){if(void 0===e&&(e="default"),r=mt(n,t,e),!ft(r)||St(r))return r;throw Pt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var r,n;if("string"===e&&U(r=t.toString)&&!ft(n=mt(r,t)))return n;if(U(r=t.valueOf)&&!ft(n=mt(r,t)))return n;if("string"!==e&&U(r=t.toString)&&!ft(n=mt(r,t)))return n;throw It("Can't convert object to primitive value")}(t,e)},kt=function(t){var e=Rt(t,"string");return St(e)?e:e+""},_t=b.TypeError,Nt=Object.defineProperty,Mt={f:dt?Nt:function(t,e,r){if(vt(t),e=kt(e),vt(r),gt)try{return Nt(t,e,r)}catch(t){}if("get"in r||"set"in r)throw _t("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},zt=Math.ceil,Ct=Math.floor,Ft=function(t){var e=+t;return e!=e||0===e?0:(e>0?Ct:zt)(e)},Dt=Math.max,Ht=Math.min,Bt=function(t,e){var r=Ft(t);return r<0?Dt(r+e,0):Ht(r,e)},Gt=Math.min,$t=function(t){return t>0?Gt(Ft(t),9007199254740991):0},Vt=function(t){return $t(t.length)},Wt=function(t){return function(e,r,n){var o,i=_(e),u=Vt(i),a=Bt(n,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},Ut={includes:Wt(!0),indexOf:Wt(!1)},Kt={},Yt=Ut.indexOf,Xt=O([].push),qt=function(t,e){var r,n=_(t),o=0,i=[];for(r in n)!B(Kt,r)&&B(n,r)&&Xt(i,r);for(;e.length>o;)B(n,r=e[o++])&&(~Yt(i,r)||Xt(i,r));return i},Jt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Qt=Object.keys||function(t){return qt(t,Jt)},Zt=dt?Object.defineProperties:function(t,e){vt(t);for(var r,n=_(e),o=Qt(e),i=o.length,u=0;i>u;)Mt.f(t,r=o[u++],n[r]);return t},te=Y("document","documentElement"),ee=C("keys"),re=function(t){return ee[t]||(ee[t]=W(t))},ne=re("IE_PROTO"),oe=function(){},ie=function(t){return"<script>"+t+"<\/script>"},ue=function(t){t.write(ie("")),t.close();var e=t.parentWindow.Object;return t=null,e},ae=function(){try{tt=new ActiveXObject("htmlfile")}catch(t){}var t,e;ae="undefined"!=typeof document?document.domain&&tt?ue(tt):((e=ht("iframe")).style.display="none",te.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(ie("document.F=Object")),t.close(),t.F):ue(tt);for(var r=Jt.length;r--;)delete ae.prototype[Jt[r]];return ae()};Kt[ne]=!0;var ce=Object.create||function(t,e){var r;return null!==t?(oe.prototype=vt(t),r=new oe,oe.prototype=null,r[ne]=t):r=ae(),void 0===e?r:Zt(r,e)},fe=ct("unscopables"),le=Array.prototype;null==le[fe]&&Mt.f(le,fe,{configurable:!0,value:ce(null)});var se=function(t){le[fe][t]=!0},ve={},de=O(Function.toString);U(z.inspectSource)||(z.inspectSource=function(t){return de(t)});var pe,ye,he,ge=z.inspectSource,be=b.WeakMap,me=U(be)&&/native code/.test(ge(be)),we=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},xe=dt?function(t,e,r){return Mt.f(t,e,we(1,r))}:function(t,e,r){return t[e]=r,t},Se=b.TypeError,Oe=b.WeakMap;if(me||z.state){var Ee=z.state||(z.state=new Oe),Te=O(Ee.get),je=O(Ee.has),Ae=O(Ee.set);pe=function(t,e){if(je(Ee,t))throw new Se("Object already initialized");return e.facade=t,Ae(Ee,t,e),e},ye=function(t){return Te(Ee,t)||{}},he=function(t){return je(Ee,t)}}else{var Ie=re("state");Kt[Ie]=!0,pe=function(t,e){if(B(t,Ie))throw new Se("Object already initialized");return e.facade=t,xe(t,Ie,e),e},ye=function(t){return B(t,Ie)?t[Ie]:{}},he=function(t){return B(t,Ie)}}var Pe,Le,Re,ke={set:pe,get:ye,has:he,enforce:function(t){return he(t)?ye(t):pe(t,{})},getterFor:function(t){return function(e){var r;if(!ft(e)||(r=ye(e)).type!==t)throw Se("Incompatible receiver, "+t+" required");return r}}},_e={}.propertyIsEnumerable,Ne=Object.getOwnPropertyDescriptor,Me={f:Ne&&!_e.call({1:2},1)?function(t){var e=Ne(this,t);return!!e&&e.enumerable}:_e},ze=Object.getOwnPropertyDescriptor,Ce={f:dt?ze:function(t,e){if(t=_(t),e=kt(e),gt)try{return ze(t,e)}catch(t){}if(B(t,e))return we(!mt(Me.f,t,e),t[e])}},Fe=Function.prototype,De=dt&&Object.getOwnPropertyDescriptor,He=B(Fe,"name"),Be={EXISTS:He,PROPER:He&&"something"===function(){}.name,CONFIGURABLE:He&&(!dt||dt&&De(Fe,"name").configurable)},Ge=p((function(t){var e=Be.CONFIGURABLE,r=ke.get,n=ke.enforce,o=String(String).split("String");(t.exports=function(t,r,i,u){var a,c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,s=u&&void 0!==u.name?u.name:r;U(i)&&("Symbol("===String(s).slice(0,7)&&(s="["+String(s).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!B(i,"name")||e&&i.name!==s)&&xe(i,"name",s),(a=n(i)).source||(a.source=o.join("string"==typeof s?s:""))),t!==b?(c?!l&&t[r]&&(f=!0):delete t[r],f?t[r]=i:xe(t,r,i)):f?t[r]=i:M(r,i)})(Function.prototype,"toString",(function(){return U(this)&&r(this).source||ge(this)}))})),$e=Jt.concat("length","prototype"),Ve={f:Object.getOwnPropertyNames||function(t){return qt(t,$e)}},We={f:Object.getOwnPropertySymbols},Ue=O([].concat),Ke=Y("Reflect","ownKeys")||function(t){var e=Ve.f(vt(t)),r=We.f;return r?Ue(e,r(t)):e},Ye=function(t,e){for(var r=Ke(e),n=Mt.f,o=Ce.f,i=0;i<r.length;i++){var u=r[i];B(t,u)||n(t,u,o(e,u))}},Xe=/#|\.prototype\./,qe=function(t,e){var r=Qe[Je(t)];return r==tr||r!=Ze&&(U(e)?E(e):!!e)},Je=qe.normalize=function(t){return String(t).replace(Xe,".").toLowerCase()},Qe=qe.data={},Ze=qe.NATIVE="N",tr=qe.POLYFILL="P",er=qe,rr=Ce.f,nr=function(t,e){var r,n,o,i,u,a=t.target,c=t.global,f=t.stat;if(r=c?b:f?b[a]||M(a,{}):(b[a]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(u=rr(r,n))&&u.value:r[n],!er(c?n:a+(f?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ye(i,o)}(t.sham||o&&o.sham)&&xe(i,"sham",!0),Ge(r,n,i,t)}},or=!E((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),ir=re("IE_PROTO"),ur=b.Object,ar=ur.prototype,cr=or?ur.getPrototypeOf:function(t){var e=D(t);if(B(e,ir))return e[ir];var r=e.constructor;return U(r)&&e instanceof r?r.prototype:e instanceof ur?ar:null},fr=ct("iterator"),lr=!1;[].keys&&("next"in(Re=[].keys())?(Le=cr(cr(Re)))!==Object.prototype&&(Pe=Le):lr=!0);var sr=null==Pe||E((function(){var t={};return Pe[fr].call(t)!==t}));sr&&(Pe={}),U(Pe[fr])||Ge(Pe,fr,(function(){return this}));var vr={IteratorPrototype:Pe,BUGGY_SAFARI_ITERATORS:lr},dr=Mt.f,pr=ct("toStringTag"),yr=function(t,e,r){t&&!B(t=r?t:t.prototype,pr)&&dr(t,pr,{configurable:!0,value:e})},hr=vr.IteratorPrototype,gr=function(){return this},br=b.String,mr=b.TypeError,wr=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=O(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return vt(r),function(t){if("object"==typeof t||U(t))return t;throw mr("Can't set "+br(t)+" as a prototype")}(n),e?t(r,n):r.__proto__=n,r}}():void 0),xr=Be.PROPER,Sr=Be.CONFIGURABLE,Or=vr.IteratorPrototype,Er=vr.BUGGY_SAFARI_ITERATORS,Tr=ct("iterator"),jr=function(){return this},Ar=function(t,e,r,n,o,i,u){!function(t,e,r,n){var o=e+" Iterator";t.prototype=ce(hr,{next:we(+!n,r)}),yr(t,o,!1),ve[o]=gr}(r,e,n);var a,c,f,l=function(t){if(t===o&&y)return y;if(!Er&&t in d)return d[t];switch(t){case"keys":case"values":case"entries":return function(){return new r(this,t)}}return function(){return new r(this)}},s=e+" Iterator",v=!1,d=t.prototype,p=d[Tr]||d["@@iterator"]||o&&d[o],y=!Er&&p||l(o),h="Array"==e&&d.entries||p;if(h&&(a=cr(h.call(new t)))!==Object.prototype&&a.next&&(cr(a)!==Or&&(wr?wr(a,Or):U(a[Tr])||Ge(a,Tr,jr)),yr(a,s,!0)),xr&&"values"==o&&p&&"values"!==p.name&&(Sr?xe(d,"name","values"):(v=!0,y=function(){return mt(p,this)})),o)if(c={values:l("values"),keys:i?y:l("keys"),entries:l("entries")},u)for(f in c)(Er||v||!(f in d))&&Ge(d,f,c[f]);else nr({target:e,proto:!0,forced:Er||v},c);return d[Tr]!==y&&Ge(d,Tr,y,{name:o}),ve[e]=y,c},Ir=ke.set,Pr=ke.getterFor("Array Iterator"),Lr=Ar(Array,"Array",(function(t,e){Ir(this,{type:"Array Iterator",target:_(t),index:0,kind:e})}),(function(){var t=Pr(this),e=t.target,r=t.kind,n=t.index++;return!e||n>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==r?{value:n,done:!1}:"values"==r?{value:e[n],done:!1}:{value:[n,e[n]],done:!1}}),"values");ve.Arguments=ve.Array,se("keys"),se("values"),se("entries");var Rr={};Rr[ct("toStringTag")]="z";var kr="[object z]"===String(Rr),_r=ct("toStringTag"),Nr=b.Object,Mr="Arguments"==A(function(){return arguments}()),zr=kr?A:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Nr(t),_r))?r:Mr?A(e):"Object"==(n=A(e))&&U(e.callee)?"Arguments":n},Cr=kr?{}.toString:function(){return"[object "+zr(this)+"]"};kr||Ge(Object.prototype,"toString",Cr,{unsafe:!0});var Fr=b.String,Dr=function(t){if("Symbol"===zr(t))throw TypeError("Cannot convert a Symbol value to a string");return Fr(t)},Hr=O("".charAt),Br=O("".charCodeAt),Gr=O("".slice),$r=function(t){return function(e,r){var n,o,i=Dr(k(e)),u=Ft(r),a=i.length;return u<0||u>=a?t?"":void 0:(n=Br(i,u))<55296||n>56319||u+1===a||(o=Br(i,u+1))<56320||o>57343?t?Hr(i,u):n:t?Gr(i,u,u+2):o-56320+(n-55296<<10)+65536}},Vr={codeAt:$r(!1),charAt:$r(!0)},Wr=Vr.charAt,Ur=ke.set,Kr=ke.getterFor("String Iterator");Ar(String,"String",(function(t){Ur(this,{type:"String Iterator",string:Dr(t),index:0})}),(function(){var t,e=Kr(this),r=e.string,n=e.index;return n>=r.length?{value:void 0,done:!0}:(t=Wr(r,n),e.index+=t.length,{value:t,done:!1})}));var Yr=function(t,e,r){for(var n in e)Ge(t,n,e[n],r);return t},Xr=b.Array,qr=Math.max,Jr=Ve.f,Qr="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Zr=function(t){try{return Jr(t)}catch(t){return function(t,e,r){for(var n,o,i,u,a=Vt(t),c=Bt(e,a),f=Bt(void 0===r?a:r,a),l=Xr(qr(f-c,0)),s=0;c<f;c++,s++)n=l,o=s,i=t[c],u=void 0,(u=kt(o))in n?Mt.f(n,u,we(0,i)):n[u]=i;return l.length=s,l}(Qr)}},tn={f:function(t){return Qr&&"Window"==A(t)?Zr(t):Jr(_(t))}},en=E((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),rn=Object.isExtensible,nn=E((function(){rn(1)}))||en?function(t){return!!ft(t)&&((!en||"ArrayBuffer"!=A(t))&&(!rn||rn(t)))}:rn,on=!E((function(){return Object.isExtensible(Object.preventExtensions({}))})),un=p((function(t){var e=Mt.f,r=!1,n=W("meta"),o=0,i=function(t){e(t,n,{value:{objectID:"O"+o++,weakData:{}}})},u=t.exports={enable:function(){u.enable=function(){},r=!0;var t=Ve.f,e=O([].splice),o={};o[n]=1,t(o).length&&(Ve.f=function(r){for(var o=t(r),i=0,u=o.length;i<u;i++)if(o[i]===n){e(o,i,1);break}return o},nr({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:tn.f}))},fastKey:function(t,e){if(!ft(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!B(t,n)){if(!nn(t))return"F";if(!e)return"E";i(t)}return t[n].objectID},getWeakData:function(t,e){if(!B(t,n)){if(!nn(t))return!0;if(!e)return!1;i(t)}return t[n].weakData},onFreeze:function(t){return on&&r&&nn(t)&&!B(t,n)&&i(t),t}};Kt[n]=!0})),an=O(O.bind),cn=function(t,e){return jt(t),void 0===e?t:an?an(t,e):function(){return t.apply(e,arguments)}},fn=ct("iterator"),ln=Array.prototype,sn=ct("iterator"),vn=function(t){if(null!=t)return At(t,sn)||At(t,"@@iterator")||ve[zr(t)]},dn=b.TypeError,pn=function(t,e,r){var n,o;vt(t);try{if(!(n=At(t,"return"))){if("throw"===e)throw r;return r}n=mt(n,t)}catch(t){o=!0,n=t}if("throw"===e)throw r;if(o)throw n;return vt(n),r},yn=b.TypeError,hn=function(t,e){this.stopped=t,this.result=e},gn=hn.prototype,bn=function(t,e,r){var n,o,i,u,a,c,f,l,s=r&&r.that,v=!(!r||!r.AS_ENTRIES),d=!(!r||!r.IS_ITERATOR),p=!(!r||!r.INTERRUPTED),y=cn(e,s),h=function(t){return n&&pn(n,"normal",t),new hn(!0,t)},g=function(t){return v?(vt(t),p?y(t[0],t[1],h):y(t[0],t[1])):p?y(t,h):y(t)};if(d)n=t;else{if(!(o=vn(t)))throw yn(Et(t)+" is not iterable");if(void 0!==(l=o)&&(ve.Array===l||ln[fn]===l)){for(i=0,u=Vt(t);u>i;i++)if((a=g(t[i]))&&wt(gn,a))return a;return new hn(!1)}n=function(t,e){var r=arguments.length<2?vn(t):e;if(jt(r))return vt(mt(r,t));throw dn(Et(t)+" is not iterable")}(t,o)}for(c=n.next;!(f=mt(c,n)).done;){try{a=g(f.value)}catch(t){pn(n,"throw",t)}if("object"==typeof a&&a&&wt(gn,a))return a}return new hn(!1)},mn=b.TypeError,wn=function(t,e){if(wt(e,t))return t;throw mn("Incorrect invocation")},xn=ct("iterator"),Sn=!1;try{var On=0,En={next:function(){return{done:!!On++}},return:function(){Sn=!0}};En[xn]=function(){return this},Array.from(En,(function(){throw 2}))}catch(t){}var Tn=Array.isArray||function(t){return"Array"==A(t)},jn=function(){},An=[],In=Y("Reflect","construct"),Pn=/^\s*(?:class|function)\b/,Ln=O(Pn.exec),Rn=!Pn.exec(jn),kn=function(t){if(!U(t))return!1;try{return In(jn,An,t),!0}catch(t){return!1}},_n=!In||E((function(){var t;return kn(kn.call)||!kn(Object)||!kn((function(){t=!0}))||t}))?function(t){if(!U(t))return!1;switch(zr(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Rn||!!Ln(Pn,ge(t))}:kn,Nn=ct("species"),Mn=b.Array,zn=function(t,e){return new(function(t){var e;return Tn(t)&&(e=t.constructor,(_n(e)&&(e===Mn||Tn(e.prototype))||ft(e)&&null===(e=e[Nn]))&&(e=void 0)),void 0===e?Mn:e}(t))(0===e?0:e)},Cn=O([].push),Fn=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,f,l,s){for(var v,d,p=D(c),y=L(p),h=cn(f,l),g=Vt(y),b=0,m=s||zn,w=e?m(c,g):r||u?m(c,0):void 0;g>b;b++)if((a||b in y)&&(d=h(v=y[b],b,p),t))if(e)w[b]=d;else if(d)switch(t){case 3:return!0;case 5:return v;case 6:return b;case 2:Cn(w,v)}else switch(t){case 4:return!1;case 7:Cn(w,v)}return i?-1:n||o?o:w}},Dn={forEach:Fn(0),map:Fn(1),filter:Fn(2),some:Fn(3),every:Fn(4),find:Fn(5),findIndex:Fn(6),filterReject:Fn(7)},Hn=un.getWeakData,Bn=ke.set,Gn=ke.getterFor,$n=Dn.find,Vn=Dn.findIndex,Wn=O([].splice),Un=0,Kn=function(t){return t.frozen||(t.frozen=new Yn)},Yn=function(){this.entries=[]},Xn=function(t,e){return $n(t.entries,(function(t){return t[0]===e}))};Yn.prototype={get:function(t){var e=Xn(this,t);if(e)return e[1]},has:function(t){return!!Xn(this,t)},set:function(t,e){var r=Xn(this,t);r?r[1]=e:this.entries.push([t,e])},delete:function(t){var e=Vn(this.entries,(function(e){return e[0]===t}));return~e&&Wn(this.entries,e,1),!!~e}};var qn,Jn={getConstructor:function(t,e,r,n){var o=t((function(t,o){wn(t,i),Bn(t,{type:e,id:Un++,frozen:void 0}),null!=o&&bn(o,t[n],{that:t,AS_ENTRIES:r})})),i=o.prototype,u=Gn(e),a=function(t,e,r){var n=u(t),o=Hn(vt(e),!0);return!0===o?Kn(n).set(e,r):o[n.id]=r,t};return Yr(i,{delete:function(t){var e=u(this);if(!ft(t))return!1;var r=Hn(t);return!0===r?Kn(e).delete(t):r&&B(r,e.id)&&delete r[e.id]},has:function(t){var e=u(this);if(!ft(t))return!1;var r=Hn(t);return!0===r?Kn(e).has(t):r&&B(r,e.id)}}),Yr(i,r?{get:function(t){var e=u(this);if(ft(t)){var r=Hn(t);return!0===r?Kn(e).get(t):r?r[e.id]:void 0}},set:function(t,e){return a(this,t,e)}}:{add:function(t){return a(this,t,!0)}}),o}},Qn=ke.enforce,Zn=!b.ActiveXObject&&"ActiveXObject"in b,to=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},eo=function(t,e,r){var n=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),i=n?"set":"add",u=b[t],a=u&&u.prototype,c=u,f={},l=function(t){var e=O(a[t]);Ge(a,t,"add"==t?function(t){return e(this,0===t?0:t),this}:"delete"==t?function(t){return!(o&&!ft(t))&&e(this,0===t?0:t)}:"get"==t?function(t){return o&&!ft(t)?void 0:e(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!ft(t))&&e(this,0===t?0:t)}:function(t,r){return e(this,0===t?0:t,r),this})};if(er(t,!U(u)||!(o||a.forEach&&!E((function(){(new u).entries().next()})))))c=r.getConstructor(e,t,n,i),un.enable();else if(er(t,!0)){var s=new c,v=s[i](o?{}:-0,1)!=s,d=E((function(){s.has(1)})),p=function(t,e){if(!e&&!Sn)return!1;var r=!1;try{var n={};n[xn]=function(){return{next:function(){return{done:r=!0}}}},t(n)}catch(t){}return r}((function(t){new u(t)})),y=!o&&E((function(){for(var t=new u,e=5;e--;)t[i](e,e);return!t.has(-0)}));p||((c=e((function(t,e){wn(t,a);var r=function(t,e,r){var n,o;return wr&&U(n=e.constructor)&&n!==r&&ft(o=n.prototype)&&o!==r.prototype&&wr(t,o),t}(new u,t,c);return null!=e&&bn(e,r[i],{that:r,AS_ENTRIES:n}),r}))).prototype=a,a.constructor=c),(d||y)&&(l("delete"),l("has"),n&&l("get")),(y||v)&&l(i),o&&a.clear&&delete a.clear}return f[t]=c,nr({global:!0,forced:c!=u},f),yr(c,t),o||r.setStrong(c,t,n),c}("WeakMap",to,Jn);if(me&&Zn){qn=Jn.getConstructor(to,"WeakMap",!0),un.enable();var ro=eo.prototype,no=O(ro.delete),oo=O(ro.has),io=O(ro.get),uo=O(ro.set);Yr(ro,{delete:function(t){if(ft(t)&&!nn(t)){var e=Qn(this);return e.frozen||(e.frozen=new qn),no(this,t)||e.frozen.delete(t)}return no(this,t)},has:function(t){if(ft(t)&&!nn(t)){var e=Qn(this);return e.frozen||(e.frozen=new qn),oo(this,t)||e.frozen.has(t)}return oo(this,t)},get:function(t){if(ft(t)&&!nn(t)){var e=Qn(this);return e.frozen||(e.frozen=new qn),oo(this,t)?io(this,t):e.frozen.get(t)}return io(this,t)},set:function(t,e){if(ft(t)&&!nn(t)){var r=Qn(this);r.frozen||(r.frozen=new qn),oo(this,t)?uo(this,t,e):r.frozen.set(t,e)}else uo(this,t,e);return this}})}var ao={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},co=ht("span").classList,fo=co&&co.constructor&&co.constructor.prototype,lo=fo===Object.prototype?void 0:fo,so=ct("iterator"),vo=ct("toStringTag"),po=Lr.values,yo=function(t,e){if(t){if(t[so]!==po)try{xe(t,so,po)}catch(e){t[so]=po}if(t[vo]||xe(t,vo,e),ao[e])for(var r in Lr)if(t[r]!==Lr[r])try{xe(t,r,Lr[r])}catch(e){t[r]=Lr[r]}}};for(var ho in ao)yo(b[ho]&&b[ho].prototype,ho);yo(lo,"DOMTokenList");var go=new WeakMap;var bo={type:"list-item",renderElem:function(t,r,u){go.set(t,u);var a=t,f=a.level,l=void 0===f?0:f,s=a.ordered,v={margin:"5px 0 5px "+20*l+"px"},d="";if(void 0!==s&&s){var p=function(t,r){var i=r,u=i.type,a=i.level,c=void 0===a?0:a,f=i.ordered,l=void 0!==f&&f;if(!l)return-1;var s=1,v=r,d=e.findPath(t,v);if(0===d[0])return 1;for(;d[0]>0;){var p=n.previous(d),y=o.node(t,p);if(null==y)break;var h=y[0],g=h.level,b=void 0===g?0:g,m=h.type,w=h.ordered;if(m!==u)break;if(b<c)break;if(b===c){if(w!==l)break;s++}v=h,d=p}return s}(u,t);d=p+"."}else d=function(t){void 0===t&&(t=0);var e="";switch(t){case 0:e="•";break;case 1:e="◦";break;default:e="▪"}return e}(l);var y=function(t){var e,r=t.children||[],n=r.length;if(0===n)return"";for(var o=0;o<n&&!e;o++){var u=r[o];i.isText(u)&&(e=u)}return null==e?"":e.color||""}(t);return c("div",{style:v},c("span",{contentEditable:!1,style:{marginRight:"0.5em",color:y},"data-w-e-reserve":!0},d),c("span",null,r))}},mo=function(t,e){return mo=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},mo(t,e)};
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function wo(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}mo(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function xo(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function So(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)u.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return u}function Oo(t){return o.nodes(t,{at:t.selection||void 0,match:function(r){return 1===e.findPath(t,r).length}})}var Eo=Ut.includes;nr({target:"Array",proto:!0},{includes:function(t){return Eo(this,t,arguments.length>1?arguments[1]:void 0)}}),se("includes");var To,jo,Ao=function(){var t=vt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Io=b.RegExp,Po=E((function(){var t=Io("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),Lo=Po||E((function(){return!Io("a","y").sticky})),Ro={BROKEN_CARET:Po||E((function(){var t=Io("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:Lo,UNSUPPORTED_Y:Po},ko=b.RegExp,_o=E((function(){var t=ko(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),No=b.RegExp,Mo=E((function(){var t=No("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),zo=ke.get,Co=C("native-string-replace",String.prototype.replace),Fo=RegExp.prototype.exec,Do=Fo,Ho=O("".charAt),Bo=O("".indexOf),Go=O("".replace),$o=O("".slice),Vo=(jo=/b*/g,mt(Fo,To=/a/,"a"),mt(Fo,jo,"a"),0!==To.lastIndex||0!==jo.lastIndex),Wo=Ro.BROKEN_CARET,Uo=void 0!==/()??/.exec("")[1];(Vo||Uo||Wo||_o||Mo)&&(Do=function(t){var e,r,n,o,i,u,a,c=this,f=zo(c),l=Dr(t),s=f.raw;if(s)return s.lastIndex=c.lastIndex,e=mt(Do,s,l),c.lastIndex=s.lastIndex,e;var v=f.groups,d=Wo&&c.sticky,p=mt(Ao,c),y=c.source,h=0,g=l;if(d&&(p=Go(p,"y",""),-1===Bo(p,"g")&&(p+="g"),g=$o(l,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==Ho(l,c.lastIndex-1))&&(y="(?: "+y+")",g=" "+g,h++),r=new RegExp("^(?:"+y+")",p)),Uo&&(r=new RegExp("^"+y+"$(?!\\s)",p)),Vo&&(n=c.lastIndex),o=mt(Fo,d?r:c,g),d?o?(o.input=$o(o.input,h),o[0]=$o(o[0],h),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Vo&&o&&(c.lastIndex=c.global?o.index+o[0].length:n),Uo&&o&&o.length>1&&mt(Co,o[0],r,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&v)for(o.groups=u=ce(null),i=0;i<v.length;i++)u[(a=v[i])[0]]=o[a[1]];return o});var Ko=Do;nr({target:"RegExp",proto:!0,forced:/./.exec!==Ko},{exec:Ko});var Yo=function(){function t(){this.type="list-item",this.tag="button"}return t.prototype.getListNode=function(t){var r=this.type;return e.getSelectedNodeByType(t,r)},t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){var e=this.getListNode(t);if(null==e)return!1;var r=e.ordered;return(void 0!==r&&r)===this.ordered},t.prototype.isDisabled=function(t){return null==t.selection||!!e.getSelectedElems(t).some((function(e){if(o.isVoid(t,e)&&o.isBlock(t,e))return!0;var r=e.type;return!!["pre","code","table"].includes(r)||void 0}))},t.prototype.exec=function(t,e){this.isActive(t)?a.setNodes(t,{type:"paragraph",ordered:void 0,level:void 0}):a.setNodes(t,{type:"list-item",ordered:this.ordered,indent:void 0})},t}(),Xo='<svg viewBox="0 0 1024 1024"><path d="M384 64h640v128H384V64z m0 384h640v128H384v-128z m0 384h640v128H384v-128zM0 128a128 128 0 1 1 256 0 128 128 0 0 1-256 0z m0 384a128 128 0 1 1 256 0 128 128 0 0 1-256 0z m0 384a128 128 0 1 1 256 0 128 128 0 0 1-256 0z"></path></svg>',qo='<svg viewBox="0 0 1024 1024"><path d="M384 832h640v128H384z m0-384h640v128H384z m0-384h640v128H384zM192 0v256H128V64H64V0zM128 526.016v50.016h128v64H64v-146.016l128-60V384H64v-64h192v146.016zM256 704v320H64v-64h128v-64H64v-64h128v-64H64v-64z"></path></svg>',Jo=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ordered=!1,e.title=r("listModule.unOrderedList"),e.iconSvg=Xo,e}return wo(e,t),e}(Yo),Qo=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ordered=!0,e.title=r("listModule.orderedList"),e.iconSvg=qo,e}return wo(e,t),e}(Yo),Zo={key:"bulletedList",factory:function(){return new Jo}},ti={key:"numberedList",factory:function(){return new Qo}};var ei=[];var ri,ni={type:"list-item",elemToHtml:function(t,r){var i="",u="",a=t.ordered,c=void 0!==a&&a?"ol":"ul",f=function(t){var r=go.get(t);if(null==r)return 0;var i=t,u=i.type,a=i.ordered,c=void 0!==a&&a,f=i.level,l=void 0===f?0:f,s=e.findPath(r,t);if(0===s[0])return l+1;var v=n.previous(s),d=o.node(r,v);if(!d)return 0;var p=So(d,1)[0];if(e.getNodeType(p)!==u)return l+1;var y=p,h=y.ordered,g=void 0!==h&&h,b=y.level,m=void 0===b?0:b;return m<l?l-m:m>l?0:m===l?g===c?0:1:0}(t);if(f>0)for(var l=0;l<f;l++)i+="<"+c+">",ei.push(c);var s=function(t){var r=go.get(t);if(null==r)return 0;var i=t,u=i.type,a=i.ordered,c=void 0!==a&&a,f=i.level,l=void 0===f?0:f,s=e.findPath(r,t);if(s[0]===r.children.length-1)return l+1;var v=n.next(s),d=o.node(r,v);if(!d)return 0;var p=So(d,1)[0];if(e.getNodeType(p)!==u)return l+1;var y=p,h=y.ordered,g=void 0!==h&&h,b=y.level,m=void 0===b?0:b;return m<l?l-m:m>l?0:m===l?g===c?0:1:0}(t);if(s>0)for(l=0;l<s;l++){u+="</"+ei.pop()+">"}return{html:"<li>"+r+"</li>",prefix:i,suffix:u}}},oi=ct("species"),ii=Dn.filter,ui=(ri="filter",et>=51||!E((function(){var t=[];return(t.constructor={})[oi]=function(){return{foo:1}},1!==t[ri](Boolean).foo})));nr({target:"Array",proto:!0,forced:!ui},{filter:function(t){return ii(this,t,arguments.length>1?arguments[1]:void 0)}});var ai=Function.prototype,ci=ai.apply,fi=ai.bind,li=ai.call,si="object"==typeof Reflect&&Reflect.apply||(fi?li.bind(ci):function(){return li.apply(ci,arguments)}),vi=ct("species"),di=RegExp.prototype,pi=Vr.charAt,yi=function(t,e,r){return e+(r?pi(t,e).length:1)},hi=Math.floor,gi=O("".charAt),bi=O("".replace),mi=O("".slice),wi=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,xi=/\$([$&'`]|\d{1,2})/g,Si=function(t,e,r,n,o,i){var u=r+t.length,a=n.length,c=xi;return void 0!==o&&(o=D(o),c=wi),bi(i,c,(function(i,c){var f;switch(gi(c,0)){case"$":return"$";case"&":return t;case"`":return mi(e,0,r);case"'":return mi(e,u);case"<":f=o[mi(c,1,-1)];break;default:var l=+c;if(0===l)return i;if(l>a){var s=hi(l/10);return 0===s?i:s<=a?void 0===n[s-1]?gi(c,1):n[s-1]+gi(c,1):i}f=n[l-1]}return void 0===f?"":f}))},Oi=b.TypeError,Ei=function(t,e){var r=t.exec;if(U(r)){var n=mt(r,t,e);return null!==n&&vt(n),n}if("RegExp"===A(t))return mt(Ko,t,e);throw Oi("RegExp#exec called on incompatible receiver")},Ti=ct("replace"),ji=Math.max,Ai=Math.min,Ii=O([].concat),Pi=O([].push),Li=O("".indexOf),Ri=O("".slice),ki="$0"==="a".replace(/./,"$0"),_i=!!/./[Ti]&&""===/./[Ti]("a","$0");!function(t,e,r,n){var o=ct(t),i=!E((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!E((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[vi]=function(){return r},r.flags="",r[o]=/./[o]),r.exec=function(){return e=!0,null},r[o](""),!e}));if(!i||!u||r){var a=O(/./[o]),c=e(o,""[t],(function(t,e,r,n,o){var u=O(t),c=e.exec;return c===Ko||c===di.exec?i&&!o?{done:!0,value:a(e,r,n)}:{done:!0,value:u(r,e,n)}:{done:!1}}));Ge(String.prototype,t,c[0]),Ge(di,o,c[1])}n&&xe(di[o],"sham",!0)}("replace",(function(t,e,r){var n=_i?"$":"$0";return[function(t,r){var n=k(this),o=null==t?void 0:At(t,Ti);return o?mt(o,t,n,r):mt(e,Dr(n),t,r)},function(t,o){var i=vt(this),u=Dr(t);if("string"==typeof o&&-1===Li(o,n)&&-1===Li(o,"$<")){var a=r(e,i,u,o);if(a.done)return a.value}var c=U(o);c||(o=Dr(o));var f=i.global;if(f){var l=i.unicode;i.lastIndex=0}for(var s=[];;){var v=Ei(i,u);if(null===v)break;if(Pi(s,v),!f)break;""===Dr(v[0])&&(i.lastIndex=yi(u,$t(i.lastIndex),l))}for(var d,p="",y=0,h=0;h<s.length;h++){for(var g=Dr((v=s[h])[0]),b=ji(Ai(Ft(v.index),u.length),0),m=[],w=1;w<v.length;w++)Pi(m,void 0===(d=v[w])?d:String(d));var x=v.groups;if(c){var S=Ii([g],m,b,u);void 0!==x&&Pi(S,x);var O=Dr(si(o,void 0,S))}else O=Si(g,u,b,m,x,o);b>=y&&(p+=Ri(u,y,b)+O,y=b+g.length)}return p+Ri(u,y)}]}),!!E((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!ki||_i);var Ni=b.TypeError,Mi=function(t,e,r,n,o,i,u,a){for(var c,f,l=o,s=0,v=!!u&&cn(u,a);s<n;){if(s in r){if(c=v?v(r[s],s,e):r[s],i>0&&Tn(c))f=Vt(c),l=Mi(t,e,c,f,l,i-1)-1;else{if(l>=9007199254740991)throw Ni("Exceed the acceptable array length");t[l]=c}l++}s++}return l},zi=Mi;function Ci(t){return t.length?t[0].tagName.toLowerCase():""}nr({target:"Array",proto:!0},{flat:function(){var t=arguments.length?arguments[0]:void 0,e=D(this),r=Vt(e),n=zn(e,0);return n.length=zi(n,e,e,r,0,void 0===t?1:Ft(t)),n}}),se("flat"),nr({global:!0},{globalThis:b}),l&&(f.fn.append=l),s&&(f.fn.attr=s),v&&(f.fn.parent=v);var Fi={renderElems:[bo],editorPlugin:function(t){var r=t.deleteBackward,n=t.handleTab,o=t.normalizeNode,i=t;return i.deleteBackward=function(t){var n=i.selection;if(null!=n)if(u.isExpanded(n))r(t);else{var o=e.getSelectedNodeByType(i,"list-item");if(null!=o)if(0!==n.focus.offset)r(t);else{var c=o.level,f=void 0===c?0:c;f>0?a.setNodes(i,{level:f-1}):a.setNodes(i,{type:"paragraph",ordered:void 0,level:void 0})}else r(t)}else r(t)},i.handleTab=function(){var t,r,o,c,f=i.selection;if(null!=f){if(u.isCollapsed(f)){var l=e.getSelectedNodeByType(i,"list-item");if(null==l)return void n();if(0===f.focus.offset){var s=l.level,v=void 0===s?0:s;return void a.setNodes(i,{level:v+1})}}if(u.isExpanded(f)){var d=0,p=!1;try{for(var y=xo(Oo(i)),h=y.next();!h.done;h=y.next()){var g=So(h.value,1)[0];"list-item"===e.getNodeType(g)?d++:p=!0}}catch(e){t={error:e}}finally{try{h&&!h.done&&(r=y.return)&&r.call(y)}finally{if(t)throw t.error}}if(p||d<=1)return void n();try{for(var b=xo(Oo(i)),m=b.next();!m.done;m=b.next()){var w=So(m.value,2),x=(g=w[0],w[1]),S=g.level;v=void 0===S?0:S;a.setNodes(i,{level:v+1},{at:x})}}catch(t){o={error:t}}finally{try{m&&!m.done&&(c=b.return)&&c.call(b)}finally{if(o)throw o.error}}}else n()}else n()},i.normalizeNode=function(t){var r=So(t,2),n=r[0],u=r[1],c=e.getNodeType(n);return"bulleted-list"!==c&&"numbered-list"!==c||a.unwrapNodes(i,{at:u}),o([n,u])},i},menus:[Zo,ti],elemsToHtml:[ni],parseElemsHtml:[{selector:"ul:not([data-w-e-type]),ol:not([data-w-e-type])",parseElemHtml:function(t,e,r){return e.flat(1/0)}},{selector:"li:not([data-w-e-type])",parseElemHtml:function(t,e,r){var n=f(t);0===(e=e.filter((function(t){return!!i.isText(t)||!!r.isInline(t)}))).length&&(e=[{text:n.text().replace(/\s+/gm," ")}]);var o=function(t){return"ol"===Ci(t.parent())}(n),u=function(t){for(var e=0,r=t.parent(),n=Ci(r);"ul"===n||"ol"===n;)n=Ci(r=r.parent()),e++;return e-1}(n);return{type:"list-item",ordered:o,level:u,children:e}}}]};export{Fi as default};
//# sourceMappingURL=index.esm.js.map
