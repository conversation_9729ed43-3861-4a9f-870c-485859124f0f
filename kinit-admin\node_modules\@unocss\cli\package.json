{"name": "@unocss/cli", "version": "0.58.5", "description": "CLI for UnoCSS", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://johannschopplich.com"}, "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unocss/unocss/tree/main/packages/cli#readme", "repository": {"type": "git", "url": "https://github.com/unocss/unocss", "directory": "packages/cli"}, "bugs": {"url": "https://github.com/unocss/unocss/issues"}, "keywords": [], "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "bin": {"unocss": "./bin/unocss.mjs"}, "files": ["bin", "dist"], "engines": {"node": ">=14"}, "dependencies": {"@ampproject/remapping": "^2.2.1", "@rollup/pluginutils": "^5.1.0", "cac": "^6.7.14", "chokidar": "^3.5.3", "colorette": "^2.0.20", "consola": "^3.2.3", "fast-glob": "^3.3.2", "magic-string": "^0.30.6", "pathe": "^1.1.2", "perfect-debounce": "^1.0.0", "@unocss/config": "0.58.5", "@unocss/core": "0.58.5", "@unocss/preset-uno": "0.58.5"}, "scripts": {"build": "unbuild", "stub": "unbuild --stub"}}