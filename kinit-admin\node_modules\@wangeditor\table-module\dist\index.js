!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@wangeditor/core"),require("slate"),require("snabbdom"),require("lodash.throttle"),require("dom7"),require("nanoid"),require("lodash.isequal")):"function"==typeof define&&define.amd?define(["@wangeditor/core","slate","snabbdom","lodash.throttle","dom7","nanoid","lodash.isequal"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).WangEditorTableModule=e(t.core,t.slate,t.snabbdom,t.throttle,t.$,null,t.isEqual)}(this,(function(t,e,n,r,o,i,a){"use strict";function l(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var u=l(r),c=l(o),s=l(a);t.i18nAddResources("en",{tableModule:{deleteCol:"Delete column",deleteRow:"Delete row",deleteTable:"Delete table",widthAuto:"Width auto",insertCol:"Insert column",insertRow:"Insert row",insertTable:"Insert table",header:"Header"}}),t.i18nAddResources("zh-CN",{tableModule:{deleteCol:"删除列",deleteRow:"删除行",deleteTable:"删除表格",widthAuto:"宽度自适应",insertCol:"插入列",insertRow:"插入行",insertTable:"插入表格",header:"表头"}});var f="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function d(t){var e={exports:{}};return t(e,e.exports),e.exports}var p,v,h=function(t){return t&&t.Math==Math&&t},g=h("object"==typeof globalThis&&globalThis)||h("object"==typeof window&&window)||h("object"==typeof self&&self)||h("object"==typeof f&&f)||function(){return this}()||Function("return this")(),y=function(t){try{return!!t()}catch(t){return!0}},m=!y((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),b=Function.prototype.call,E=b.bind?b.bind(b):function(){return b.apply(b,arguments)},w={}.propertyIsEnumerable,x=Object.getOwnPropertyDescriptor,S={f:x&&!w.call({1:2},1)?function(t){var e=x(this,t);return!!e&&e.enumerable}:w},T=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},D=Function.prototype,N=D.bind,P=D.call,M=N&&N.bind(P),R=N?function(t){return t&&M(P,t)}:function(t){return t&&function(){return P.apply(t,arguments)}},O=R({}.toString),H=R("".slice),V=function(t){return H(O(t),8,-1)},z=g.Object,j=R("".split),L=y((function(){return!z("z").propertyIsEnumerable(0)}))?function(t){return"String"==V(t)?j(t,""):z(t)}:z,A=g.TypeError,C=function(t){if(null==t)throw A("Can't call method on "+t);return t},I=function(t){return L(C(t))},k=function(t){return"function"==typeof t},B=function(t){return"object"==typeof t?null!==t:k(t)},$=function(t){return k(t)?t:void 0},F=function(t,e){return arguments.length<2?$(g[t]):g[t]&&g[t][e]},q=R({}.isPrototypeOf),G=F("navigator","userAgent")||"",_=g.process,W=g.Deno,U=_&&_.versions||W&&W.version,X=U&&U.v8;X&&(v=(p=X.split("."))[0]>0&&p[0]<4?1:+(p[0]+p[1])),!v&&G&&(!(p=G.match(/Edge\/(\d+)/))||p[1]>=74)&&(p=G.match(/Chrome\/(\d+)/))&&(v=+p[1]);var Y=v,K=!!Object.getOwnPropertySymbols&&!y((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Y&&Y<41})),J=K&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Q=g.Object,Z=J?function(t){return"symbol"==typeof t}:function(t){var e=F("Symbol");return k(e)&&q(e.prototype,Q(t))},tt=g.String,et=function(t){try{return tt(t)}catch(t){return"Object"}},nt=g.TypeError,rt=function(t){if(k(t))return t;throw nt(et(t)+" is not a function")},ot=function(t,e){var n=t[e];return null==n?void 0:rt(n)},it=g.TypeError,at=Object.defineProperty,lt=function(t,e){try{at(g,t,{value:e,configurable:!0,writable:!0})}catch(n){g[t]=e}return e},ut="__core-js_shared__",ct=g[ut]||lt(ut,{}),st=d((function(t){(t.exports=function(t,e){return ct[t]||(ct[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),ft=g.Object,dt=function(t){return ft(C(t))},pt=R({}.hasOwnProperty),vt=Object.hasOwn||function(t,e){return pt(dt(t),e)},ht=0,gt=Math.random(),yt=R(1..toString),mt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+yt(++ht+gt,36)},bt=st("wks"),Et=g.Symbol,wt=Et&&Et.for,xt=J?Et:Et&&Et.withoutSetter||mt,St=function(t){if(!vt(bt,t)||!K&&"string"!=typeof bt[t]){var e="Symbol."+t;K&&vt(Et,t)?bt[t]=Et[t]:bt[t]=J&&wt?wt(e):xt(e)}return bt[t]},Tt=g.TypeError,Dt=St("toPrimitive"),Nt=function(t,e){if(!B(t)||Z(t))return t;var n,r=ot(t,Dt);if(r){if(void 0===e&&(e="default"),n=E(r,t,e),!B(n)||Z(n))return n;throw Tt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&k(n=t.toString)&&!B(r=E(n,t)))return r;if(k(n=t.valueOf)&&!B(r=E(n,t)))return r;if("string"!==e&&k(n=t.toString)&&!B(r=E(n,t)))return r;throw it("Can't convert object to primitive value")}(t,e)},Pt=function(t){var e=Nt(t,"string");return Z(e)?e:e+""},Mt=g.document,Rt=B(Mt)&&B(Mt.createElement),Ot=function(t){return Rt?Mt.createElement(t):{}},Ht=!m&&!y((function(){return 7!=Object.defineProperty(Ot("div"),"a",{get:function(){return 7}}).a})),Vt=Object.getOwnPropertyDescriptor,zt={f:m?Vt:function(t,e){if(t=I(t),e=Pt(e),Ht)try{return Vt(t,e)}catch(t){}if(vt(t,e))return T(!E(S.f,t,e),t[e])}},jt=g.String,Lt=g.TypeError,At=function(t){if(B(t))return t;throw Lt(jt(t)+" is not an object")},Ct=g.TypeError,It=Object.defineProperty,kt={f:m?It:function(t,e,n){if(At(t),e=Pt(e),At(n),Ht)try{return It(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Ct("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Bt=m?function(t,e,n){return kt.f(t,e,T(1,n))}:function(t,e,n){return t[e]=n,t},$t=R(Function.toString);k(ct.inspectSource)||(ct.inspectSource=function(t){return $t(t)});var Ft,qt,Gt,_t=ct.inspectSource,Wt=g.WeakMap,Ut=k(Wt)&&/native code/.test(_t(Wt)),Xt=st("keys"),Yt=function(t){return Xt[t]||(Xt[t]=mt(t))},Kt={},Jt="Object already initialized",Qt=g.TypeError,Zt=g.WeakMap;if(Ut||ct.state){var te=ct.state||(ct.state=new Zt),ee=R(te.get),ne=R(te.has),re=R(te.set);Ft=function(t,e){if(ne(te,t))throw new Qt(Jt);return e.facade=t,re(te,t,e),e},qt=function(t){return ee(te,t)||{}},Gt=function(t){return ne(te,t)}}else{var oe=Yt("state");Kt[oe]=!0,Ft=function(t,e){if(vt(t,oe))throw new Qt(Jt);return e.facade=t,Bt(t,oe,e),e},qt=function(t){return vt(t,oe)?t[oe]:{}},Gt=function(t){return vt(t,oe)}}var ie={set:Ft,get:qt,has:Gt,enforce:function(t){return Gt(t)?qt(t):Ft(t,{})},getterFor:function(t){return function(e){var n;if(!B(e)||(n=qt(e)).type!==t)throw Qt("Incompatible receiver, "+t+" required");return n}}},ae=Function.prototype,le=m&&Object.getOwnPropertyDescriptor,ue=vt(ae,"name"),ce={EXISTS:ue,PROPER:ue&&"something"===function(){}.name,CONFIGURABLE:ue&&(!m||m&&le(ae,"name").configurable)},se=d((function(t){var e=ce.CONFIGURABLE,n=ie.get,r=ie.enforce,o=String(String).split("String");(t.exports=function(t,n,i,a){var l,u=!!a&&!!a.unsafe,c=!!a&&!!a.enumerable,s=!!a&&!!a.noTargetGet,f=a&&void 0!==a.name?a.name:n;k(i)&&("Symbol("===String(f).slice(0,7)&&(f="["+String(f).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!vt(i,"name")||e&&i.name!==f)&&Bt(i,"name",f),(l=r(i)).source||(l.source=o.join("string"==typeof f?f:""))),t!==g?(u?!s&&t[n]&&(c=!0):delete t[n],c?t[n]=i:Bt(t,n,i)):c?t[n]=i:lt(n,i)})(Function.prototype,"toString",(function(){return k(this)&&n(this).source||_t(this)}))})),fe=Math.ceil,de=Math.floor,pe=function(t){var e=+t;return e!=e||0===e?0:(e>0?de:fe)(e)},ve=Math.max,he=Math.min,ge=function(t,e){var n=pe(t);return n<0?ve(n+e,0):he(n,e)},ye=Math.min,me=function(t){return t>0?ye(pe(t),9007199254740991):0},be=function(t){return me(t.length)},Ee=function(t){return function(e,n,r){var o,i=I(e),a=be(i),l=ge(r,a);if(t&&n!=n){for(;a>l;)if((o=i[l++])!=o)return!0}else for(;a>l;l++)if((t||l in i)&&i[l]===n)return t||l||0;return!t&&-1}},we={includes:Ee(!0),indexOf:Ee(!1)}.indexOf,xe=R([].push),Se=function(t,e){var n,r=I(t),o=0,i=[];for(n in r)!vt(Kt,n)&&vt(r,n)&&xe(i,n);for(;e.length>o;)vt(r,n=e[o++])&&(~we(i,n)||xe(i,n));return i},Te=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],De=Te.concat("length","prototype"),Ne={f:Object.getOwnPropertyNames||function(t){return Se(t,De)}},Pe={f:Object.getOwnPropertySymbols},Me=R([].concat),Re=F("Reflect","ownKeys")||function(t){var e=Ne.f(At(t)),n=Pe.f;return n?Me(e,n(t)):e},Oe=function(t,e){for(var n=Re(e),r=kt.f,o=zt.f,i=0;i<n.length;i++){var a=n[i];vt(t,a)||r(t,a,o(e,a))}},He=/#|\.prototype\./,Ve=function(t,e){var n=je[ze(t)];return n==Ae||n!=Le&&(k(e)?y(e):!!e)},ze=Ve.normalize=function(t){return String(t).replace(He,".").toLowerCase()},je=Ve.data={},Le=Ve.NATIVE="N",Ae=Ve.POLYFILL="P",Ce=Ve,Ie=zt.f,ke=function(t,e){var n,r,o,i,a,l=t.target,u=t.global,c=t.stat;if(n=u?g:c?g[l]||lt(l,{}):(g[l]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(a=Ie(n,r))&&a.value:n[r],!Ce(u?r:l+(c?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Oe(i,o)}(t.sham||o&&o.sham)&&Bt(i,"sham",!0),se(n,r,i,t)}},Be={};Be[St("toStringTag")]="z";var $e,Fe="[object z]"===String(Be),qe=St("toStringTag"),Ge=g.Object,_e="Arguments"==V(function(){return arguments}()),We=Fe?V:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Ge(t),qe))?n:_e?V(e):"Object"==(r=V(e))&&k(e.callee)?"Arguments":r},Ue=g.String,Xe=function(t){if("Symbol"===We(t))throw TypeError("Cannot convert a Symbol value to a string");return Ue(t)},Ye=/"/g,Ke=R("".replace);ke({target:"String",proto:!0,forced:($e="anchor",y((function(){var t=""[$e]('"');return t!==t.toLowerCase()||t.split('"').length>3})))},{anchor:function(t){return e="a",n="name",r=t,o=Xe(C(this)),i="<"+e,""!==n&&(i+=" "+n+'="'+Ke(Xe(r),Ye,"&quot;")+'"'),i+">"+o+"</"+e+">";var e,n,r,o,i}});var Je,Qe=function(){var t=At(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Ze=g.RegExp,tn=y((function(){var t=Ze("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),en=tn||y((function(){return!Ze("a","y").sticky})),nn={BROKEN_CARET:tn||y((function(){var t=Ze("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:en,UNSUPPORTED_Y:tn},rn=Object.keys||function(t){return Se(t,Te)},on=m?Object.defineProperties:function(t,e){At(t);for(var n,r=I(e),o=rn(e),i=o.length,a=0;i>a;)kt.f(t,n=o[a++],r[n]);return t},an=F("document","documentElement"),ln=Yt("IE_PROTO"),un=function(){},cn=function(t){return"<script>"+t+"</"+"script>"},sn=function(t){t.write(cn("")),t.close();var e=t.parentWindow.Object;return t=null,e},fn=function(){try{Je=new ActiveXObject("htmlfile")}catch(t){}var t,e;fn="undefined"!=typeof document?document.domain&&Je?sn(Je):((e=Ot("iframe")).style.display="none",an.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(cn("document.F=Object")),t.close(),t.F):sn(Je);for(var n=Te.length;n--;)delete fn.prototype[Te[n]];return fn()};Kt[ln]=!0;var dn,pn,vn=Object.create||function(t,e){var n;return null!==t?(un.prototype=At(t),n=new un,un.prototype=null,n[ln]=t):n=fn(),void 0===e?n:on(n,e)},hn=g.RegExp,gn=y((function(){var t=hn(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),yn=g.RegExp,mn=y((function(){var t=yn("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),bn=ie.get,En=st("native-string-replace",String.prototype.replace),wn=RegExp.prototype.exec,xn=wn,Sn=R("".charAt),Tn=R("".indexOf),Dn=R("".replace),Nn=R("".slice),Pn=(pn=/b*/g,E(wn,dn=/a/,"a"),E(wn,pn,"a"),0!==dn.lastIndex||0!==pn.lastIndex),Mn=nn.BROKEN_CARET,Rn=void 0!==/()??/.exec("")[1];(Pn||Rn||Mn||gn||mn)&&(xn=function(t){var e,n,r,o,i,a,l,u=this,c=bn(u),s=Xe(t),f=c.raw;if(f)return f.lastIndex=u.lastIndex,e=E(xn,f,s),u.lastIndex=f.lastIndex,e;var d=c.groups,p=Mn&&u.sticky,v=E(Qe,u),h=u.source,g=0,y=s;if(p&&(v=Dn(v,"y",""),-1===Tn(v,"g")&&(v+="g"),y=Nn(s,u.lastIndex),u.lastIndex>0&&(!u.multiline||u.multiline&&"\n"!==Sn(s,u.lastIndex-1))&&(h="(?: "+h+")",y=" "+y,g++),n=new RegExp("^(?:"+h+")",v)),Rn&&(n=new RegExp("^"+h+"$(?!\\s)",v)),Pn&&(r=u.lastIndex),o=E(wn,p?n:u,y),p?o?(o.input=Nn(o.input,g),o[0]=Nn(o[0],g),o.index=u.lastIndex,u.lastIndex+=o[0].length):u.lastIndex=0:Pn&&o&&(u.lastIndex=u.global?o.index+o[0].length:r),Rn&&o&&o.length>1&&E(En,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&d)for(o.groups=a=vn(null),i=0;i<d.length;i++)a[(l=d[i])[0]]=o[l[1]];return o});var On=xn;ke({target:"RegExp",proto:!0,forced:/./.exec!==On},{exec:On});var Hn=Array.isArray||function(t){return"Array"==V(t)},Vn=function(){},zn=[],jn=F("Reflect","construct"),Ln=/^\s*(?:class|function)\b/,An=R(Ln.exec),Cn=!Ln.exec(Vn),In=function(t){if(!k(t))return!1;try{return jn(Vn,zn,t),!0}catch(t){return!1}},kn=!jn||y((function(){var t;return In(In.call)||!In(Object)||!In((function(){t=!0}))||t}))?function(t){if(!k(t))return!1;switch(We(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Cn||!!An(Ln,_t(t))}:In,Bn=function(t,e,n){var r=Pt(e);r in t?kt.f(t,r,T(0,n)):t[r]=n},$n=St("species"),Fn=function(t){return Y>=51||!y((function(){var e=[];return(e.constructor={})[$n]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},qn=R([].slice),Gn=Fn("slice"),_n=St("species"),Wn=g.Array,Un=Math.max;function Xn(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function Yn(n){var r=n.selection;if(null==r)return!1;var o=Xn(e.Editor.nodes(n,{match:function(e){return t.DomEditor.checkNodeType(e,"table-cell")}}),1)[0];if(o){var i=Xn(o,2)[1],a=e.Editor.start(n,i);if(e.Point.equals(r.anchor,a))return!0}return!1}function Kn(n,r){var o,i,a=e.Editor.nodes(n,{at:r,match:function(e){return"table"===t.DomEditor.getNodeType(e)}}),l=!1;try{for(var u=
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */
function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(a),c=u.next();!c.done;c=u.next()){c.value;l=!0}}catch(t){o={error:t}}finally{try{c&&!c.done&&(i=u.return)&&i.call(u)}finally{if(o)throw o.error}}return l}ke({target:"Array",proto:!0,forced:!Gn},{slice:function(t,e){var n,r,o,i=I(this),a=be(i),l=ge(t,a),u=ge(void 0===e?a:e,a);if(Hn(i)&&(n=i.constructor,(kn(n)&&(n===Wn||Hn(n.prototype))||B(n)&&null===(n=n[_n]))&&(n=void 0),n===Wn||void 0===n))return qn(i,l,u);for(r=new(void 0===n?Wn:n)(Un(u-l,0)),o=0;l<u;l++,o++)l in i&&Bn(r,o,i[l]);return r.length=o,r}});var Jn=R(R.bind),Qn=St("species"),Zn=g.Array,tr=function(t,e){return new(function(t){var e;return Hn(t)&&(e=t.constructor,(kn(e)&&(e===Zn||Hn(e.prototype))||B(e)&&null===(e=e[Qn]))&&(e=void 0)),void 0===e?Zn:e}(t))(0===e?0:e)},er=R([].push),nr=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,a=7==t,l=5==t||i;return function(u,c,s,f){for(var d,p,v=dt(u),h=L(v),g=function(t,e){return rt(t),void 0===e?t:Jn?Jn(t,e):function(){return t.apply(e,arguments)}}(c,s),y=be(h),m=0,b=f||tr,E=e?b(u,y):n||a?b(u,0):void 0;y>m;m++)if((l||m in h)&&(p=g(d=h[m],m,v),t))if(e)E[m]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:er(E,d)}else switch(t){case 4:return!1;case 7:er(E,d)}return i?-1:r||o?o:E}},rr={forEach:nr(0),map:nr(1),filter:nr(2),some:nr(3),every:nr(4),find:nr(5),findIndex:nr(6),filterReject:nr(7)},or=rr.map;ke({target:"Array",proto:!0,forced:!Fn("map")},{map:function(t){return or(this,t,arguments.length>1?arguments[1]:void 0)}});var ir=Fe?{}.toString:function(){return"[object "+We(this)+"]"};function ar(t){var e=t.children||[];return 0===e.length?[]:(e[0]||{}).children||[]}function lr(t){return ar(t).every((function(t){return!!t.isHeader}))}Fe||se(Object.prototype,"toString",ir,{unsafe:!0});var ur=ce.PROPER,cr="toString",sr=RegExp.prototype,fr=sr.toString,dr=R(Qe),pr=y((function(){return"/a/b"!=fr.call({source:"a",flags:"b"})})),vr=ur&&fr.name!=cr;(pr||vr)&&se(RegExp.prototype,cr,(function(){var t=At(this),e=Xe(t.source),n=t.flags;return"/"+e+"/"+Xe(void 0===n&&q(sr,t)&&!("flags"in sr)?dr(t):n)}),{unsafe:!0});var hr=St("unscopables"),gr=Array.prototype;null==gr[hr]&&kt.f(gr,hr,{configurable:!0,value:vn(null)});var yr,mr=rr.find,br="find",Er=!0;br in[]&&Array(1).find((function(){Er=!1})),ke({target:"Array",proto:!0,forced:Er},{find:function(t){return mr(this,t,arguments.length>1?arguments[1]:void 0)}}),yr=br,gr[hr][yr]=!0;var wr=Function.prototype,xr=wr.apply,Sr=wr.bind,Tr=wr.call,Dr="object"==typeof Reflect&&Reflect.apply||(Sr?Tr.bind(xr):function(){return Tr.apply(xr,arguments)}),Nr=St("species"),Pr=RegExp.prototype,Mr=function(t,e,n,r){var o=St(t),i=!y((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),a=i&&!y((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[Nr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!a||n){var l=R(/./[o]),u=e(o,""[t],(function(t,e,n,r,o){var a=R(t),u=e.exec;return u===On||u===Pr.exec?i&&!o?{done:!0,value:l(e,n,r)}:{done:!0,value:a(n,e,r)}:{done:!1}}));se(String.prototype,t,u[0]),se(Pr,o,u[1])}r&&Bt(Pr[o],"sham",!0)},Rr=St("match"),Or=g.TypeError,Hr=St("species"),Vr=function(t,e){var n,r=At(t).constructor;return void 0===r||null==(n=At(r)[Hr])?e:function(t){if(kn(t))return t;throw Or(et(t)+" is not a constructor")}(n)},zr=R("".charAt),jr=R("".charCodeAt),Lr=R("".slice),Ar=function(t){return function(e,n){var r,o,i=Xe(C(e)),a=pe(n),l=i.length;return a<0||a>=l?t?"":void 0:(r=jr(i,a))<55296||r>56319||a+1===l||(o=jr(i,a+1))<56320||o>57343?t?zr(i,a):r:t?Lr(i,a,a+2):o-56320+(r-55296<<10)+65536}},Cr={codeAt:Ar(!1),charAt:Ar(!0)}.charAt,Ir=function(t,e,n){return e+(n?Cr(t,e).length:1)},kr=g.Array,Br=Math.max,$r=function(t,e,n){for(var r=be(t),o=ge(e,r),i=ge(void 0===n?r:n,r),a=kr(Br(i-o,0)),l=0;o<i;o++,l++)Bn(a,l,t[o]);return a.length=l,a},Fr=g.TypeError,qr=function(t,e){var n=t.exec;if(k(n)){var r=E(n,t,e);return null!==r&&At(r),r}if("RegExp"===V(t))return E(On,t,e);throw Fr("RegExp#exec called on incompatible receiver")},Gr=nn.UNSUPPORTED_Y,_r=4294967295,Wr=Math.min,Ur=[].push,Xr=R(/./.exec),Yr=R(Ur),Kr=R("".slice),Jr=!y((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]}));Mr("split",(function(t,e,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var r,o,i=Xe(C(this)),a=void 0===n?_r:n>>>0;if(0===a)return[];if(void 0===t)return[i];if(!B(r=t)||!(void 0!==(o=r[Rr])?o:"RegExp"==V(r)))return E(e,i,t,a);for(var l,u,c,s=[],f=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,p=new RegExp(t.source,f+"g");(l=E(On,p,i))&&!((u=p.lastIndex)>d&&(Yr(s,Kr(i,d,l.index)),l.length>1&&l.index<i.length&&Dr(Ur,s,$r(l,1)),c=l[0].length,d=u,s.length>=a));)p.lastIndex===l.index&&p.lastIndex++;return d===i.length?!c&&Xr(p,"")||Yr(s,""):Yr(s,Kr(i,d)),s.length>a?$r(s,0,a):s}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:E(e,this,t,n)}:e,[function(e,n){var o=C(this),i=null==e?void 0:ot(e,t);return i?E(i,e,o,n):E(r,Xe(o),e,n)},function(t,o){var i=At(this),a=Xe(t),l=n(r,i,a,o,r!==e);if(l.done)return l.value;var u=Vr(i,RegExp),c=i.unicode,s=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(Gr?"g":"y"),f=new u(Gr?"^(?:"+i.source+")":i,s),d=void 0===o?_r:o>>>0;if(0===d)return[];if(0===a.length)return null===qr(f,a)?[a]:[];for(var p=0,v=0,h=[];v<a.length;){f.lastIndex=Gr?0:v;var g,y=qr(f,Gr?Kr(a,v):a);if(null===y||(g=Wr(me(f.lastIndex+(Gr?v:0)),a.length))===p)v=Ir(a,v,c);else{if(Yr(h,Kr(a,p,v)),h.length===d)return h;for(var m=1;m<=y.length-1;m++)if(Yr(h,y[m]),h.length===d)return h;v=p=g}}return Yr(h,Kr(a,p)),h}]}),!Jr,Gr);var Qr="\t\n\v\f\r                　\u2028\u2029\ufeff",Zr=R("".replace),to="["+Qr+"]",eo=RegExp("^"+to+to+"*"),no=RegExp(to+to+"*$"),ro=function(t){return function(e){var n=Xe(C(e));return 1&t&&(n=Zr(n,eo,"")),2&t&&(n=Zr(n,no,"")),n}},oo={start:ro(1),end:ro(2),trim:ro(3)},io=ce.PROPER,ao=oo.trim;function lo(t){return t.length?t[0].tagName.toLowerCase():""}ke({target:"String",proto:!0,forced:function(t){return y((function(){return!!Qr[t]()||"​᠎"!=="​᠎"[t]()||io&&Qr[t].name!==t}))}("trim")},{trim:function(){return ao(this)}}),ke({global:!0},{globalThis:g}),o.append&&(c.default.fn.append=o.append),o.on&&(c.default.fn.on=o.on),o.focus&&(c.default.fn.focus=o.focus),o.attr&&(c.default.fn.attr=o.attr),o.val&&(c.default.fn.val=o.val),o.html&&(c.default.fn.html=o.html),o.dataset&&(c.default.fn.dataset=o.dataset),o.addClass&&(c.default.fn.addClass=o.addClass),o.removeClass&&(c.default.fn.removeClass=o.removeClass),o.children&&(c.default.fn.children=o.children),o.each&&(c.default.fn.each=o.each),o.find&&(c.default.fn.find=o.find);var uo=!1,co=0,so=0,fo=null,po=null,vo=c.default("body");function ho(t){uo=!1,po=null,fo=null,vo.off("mousemove",go),vo.off("mouseup",ho)}vo.on("mousedown",(function(t){var e=t.target;if(("TH"===e.tagName||"TD"===e.tagName)&&"col-resize"===e.style.cursor){e.style.cursor="auto",t.preventDefault(),uo=!0;var n=t.clientX;co=n;var r=e.getBoundingClientRect().width;so=r,vo.on("mousemove",go),vo.on("mouseup",ho)}}));var go=u.default((function(t){if(uo&&null!=po&&null!=fo){t.preventDefault();var n=t.clientX,r=so+(n-co);(r=Math.floor(100*r)/100)<30&&(r=30),e.Transforms.setNodes(po,{width:r.toString()},{at:fo})}}),100);var yo={type:"table",renderElem:function(r,o,i){var a=function(n,r){if(n.isDisabled())return!1;var o=n.selection;if(null==o)return!0;if(e.Range.isCollapsed(o))return!0;var i=o.anchor,a=o.focus,l=t.DomEditor.findPath(n,r),u=e.Editor.start(n,l),c=e.Editor.end(n,l),s=e.Point.compare(i,c)<=0&&e.Point.compare(i,u)>=0,f=e.Point.compare(a,c)<=0&&e.Point.compare(a,u)>=0;return!!(s&&f&&e.Path.equals(i.path.slice(0,3),a.path.slice(0,3)))}(i,r),l=r.width,u=void 0===l?"auto":l,c=t.DomEditor.isNodeSelected(i,r),s=ar(r),f=n.jsx("div",{className:"table-container","data-selected":c,on:{mousedown:function(n){if("DIV"===n.target.tagName&&n.preventDefault(),!i.isDisabled()){var o=t.DomEditor.findPath(i,r),a=e.Editor.start(i,o),l=i.selection;if(null!=l)l.anchor.path[0]!==o[0]&&i.select(a);else i.select(a)}}}},n.jsx("table",{width:u,contentEditable:a},n.jsx("colgroup",null,s.map((function(t){var e=t.width,r=void 0===e?"auto":e;return n.jsx("col",{width:r})}))),n.jsx("tbody",null,o)));return f}},mo={type:"table-row",renderElem:function(t,e,r){return n.jsx("tr",null,e)}},bo={type:"table-cell",renderElem:function(e,r,o){var i=function(e,n){var r=t.DomEditor.getParentNode(e,n);if(null==r)return!1;var o=t.DomEditor.getParentNode(e,r);return null!=o&&ar(o).some((function(t){return t===n}))}(o,e),a=e,l=a.colSpan,c=void 0===l?1:l,s=a.rowSpan,f=void 0===s?1:s,d=a.isHeader,p=void 0!==d&&d;if(!i)return n.jsx("td",{colSpan:c,rowSpan:f},r);var v=p?"th":"td",h=n.jsx(v,{colSpan:c,rowSpan:f,style:{borderRightWidth:"3px"},on:{mousemove:u.default((function(n){var r=this.elm;if(null!=r){var i=r.getBoundingClientRect(),a=i.left,l=i.width,u=i.top,c=i.height,s=n.clientX,f=n.clientY;if(!uo)s>a+l-5&&s<a+l&&(f>u&&f<u+c)?(r.style.cursor="col-resize",po=o,fo=t.DomEditor.findPath(o,e)):uo||(r.style.cursor="auto",po=null,fo=null)}}),100)}},r);return h}};var Eo={type:"table",elemToHtml:function(t,e){var n=t.width;return'<table style="width: '+(void 0===n?"auto":n)+';"><tbody>'+e+"</tbody></table>"}},wo={type:"table-row",elemToHtml:function(t,e){return"<tr>"+e+"</tr>"}},xo={type:"table-cell",elemToHtml:function(t,e){var n=t,r=n.colSpan,o=void 0===r?1:r,i=n.rowSpan,a=void 0===i?1:i,l=n.isHeader,u=void 0!==l&&l,c=n.width,s=u?"th":"td";return"<"+s+' colSpan="'+o+'" rowSpan="'+a+'" width="'+(void 0===c?"auto":c)+'">'+e+"</"+s+">"}};var So={selector:"table",preParseHtml:function(t){var e=c.default(t);if("table"!==lo(e))return t;var n=e.find("tbody");if(0===n.length)return t;var r=e.find("tr");return e.append(r),n.remove(),e[0]}},To=rr.filter;ke({target:"Array",proto:!0,forced:!Fn("filter")},{filter:function(t){return To(this,t,arguments.length>1?arguments[1]:void 0)}});var Do=Math.floor,No=R("".charAt),Po=R("".replace),Mo=R("".slice),Ro=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Oo=/\$([$&'`]|\d{1,2})/g,Ho=function(t,e,n,r,o,i){var a=n+t.length,l=r.length,u=Oo;return void 0!==o&&(o=dt(o),u=Ro),Po(i,u,(function(i,u){var c;switch(No(u,0)){case"$":return"$";case"&":return t;case"`":return Mo(e,0,n);case"'":return Mo(e,a);case"<":c=o[Mo(u,1,-1)];break;default:var s=+u;if(0===s)return i;if(s>l){var f=Do(s/10);return 0===f?i:f<=l?void 0===r[f-1]?No(u,1):r[f-1]+No(u,1):i}c=r[s-1]}return void 0===c?"":c}))},Vo=St("replace"),zo=Math.max,jo=Math.min,Lo=R([].concat),Ao=R([].push),Co=R("".indexOf),Io=R("".slice),ko="$0"==="a".replace(/./,"$0"),Bo=!!/./[Vo]&&""===/./[Vo]("a","$0");Mr("replace",(function(t,e,n){var r=Bo?"$":"$0";return[function(t,n){var r=C(this),o=null==t?void 0:ot(t,Vo);return o?E(o,t,r,n):E(e,Xe(r),t,n)},function(t,o){var i=At(this),a=Xe(t);if("string"==typeof o&&-1===Co(o,r)&&-1===Co(o,"$<")){var l=n(e,i,a,o);if(l.done)return l.value}var u=k(o);u||(o=Xe(o));var c=i.global;if(c){var s=i.unicode;i.lastIndex=0}for(var f=[];;){var d=qr(i,a);if(null===d)break;if(Ao(f,d),!c)break;""===Xe(d[0])&&(i.lastIndex=Ir(a,me(i.lastIndex),s))}for(var p,v="",h=0,g=0;g<f.length;g++){for(var y=Xe((d=f[g])[0]),m=zo(jo(pe(d.index),a.length),0),b=[],E=1;E<d.length;E++)Ao(b,void 0===(p=d[E])?p:String(p));var w=d.groups;if(u){var x=Lo([y],b,m,a);void 0!==w&&Ao(x,w);var S=Xe(Dr(o,void 0,x))}else S=Ho(y,a,m,b,w,o);m>=h&&(v+=Io(a,h,m)+S,h=m+y.length)}return v+Io(a,h)}]}),!!y((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!ko||Bo);var $o={selector:"td:not([data-w-e-type]),th:not([data-w-e-type])",parseElemHtml:function(t,n,r){var o=c.default(t);0===(n=n.filter((function(t){return!!e.Text.isText(t)||!!r.isInline(t)}))).length&&(n=[{text:o.text().replace(/\s+/gm," ")}]);var i=parseInt(o.attr("colSpan")||"1"),a=parseInt(o.attr("rowSpan")||"1"),l=o.attr("width")||"auto";return{type:"table-cell",isHeader:"th"===lo(o),colSpan:i,rowSpan:a,width:l,children:n}}};var Fo={selector:"tr:not([data-w-e-type])",parseElemHtml:function(e,n,r){return{type:"table-row",children:n.filter((function(e){return"table-cell"===t.DomEditor.getNodeType(e)}))}}};var qo={selector:"table:not([data-w-e-type])",parseElemHtml:function(e,n,r){var o=c.default(e),i="auto";return"100%"===function(t,e){for(var n="",r=(t.attr("style")||"").split(";"),o=r.length,i=0;i<o;i++){var a=r[i];if(a){var l=a.split(":");l[0].trim()===e&&(n=l[1].trim())}}return n}(o,"width")&&(i="100%"),"100%"===o.attr("width")&&(i="100%"),{type:"table",width:i,children:n.filter((function(e){return"table-row"===t.DomEditor.getNodeType(e)}))}}};var Go=function(){function n(){this.title=t.t("tableModule.insertTable"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M0 64v896h1024V64H0z m384 576v-192h256v192h-256z m256 64v192h-256v-192h256z m0-512v192h-256V192h256zM320 192v192H64V192h256z m-256 256h256v192H64v-192z m640 0h256v192h-256v-192z m0-64V192h256v192h-256zM64 704h256v192H64v-192z m640 192v-192h256v192h-256z"></path></svg>',this.tag="button",this.showDropPanel=!0,this.$content=null}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.exec=function(t,e){},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||!!t.DomEditor.getSelectedElems(n).some((function(e){var r=t.DomEditor.getNodeType(e);return"pre"===r||("table"===r||("list-item"===r||!!n.isVoid(e)))})))},n.prototype.getPanelContentElem=function(t){var e=this;if(this.$content)return this.$content[0];for(var n=c.default('<div class="w-e-panel-content-table"></div>'),r=c.default("<span>0 &times; 0</span>"),o=c.default("<table></table>"),i=0;i<10;i++){for(var a=c.default("<tr></tr>"),l=0;l<10;l++){var u=c.default("<td></td>");u.attr("data-x",l.toString()),u.attr("data-y",i.toString()),a.append(u),u.on("mouseenter",(function(t){var e=t.target;if(null!=e){var n=c.default(e).dataset(),i=n.x,a=n.y;r[0].innerHTML=i+1+" &times; "+(a+1),o.children().each((function(t){c.default(t).children().each((function(t){var e=c.default(t),n=e.dataset(),r=n.x,o=n.y;r<=i&&o<=a?e.addClass("active"):e.removeClass("active")}))}))}})),u.on("click",(function(n){n.preventDefault();var r=n.target;if(null!=r){var o=c.default(r).dataset(),i=o.x,a=o.y;e.insertTable(t,a+1,i+1)}}))}o.append(a)}return n.append(o),n.append(r),this.$content=n,n[0]},n.prototype.insertTable=function(n,r,o){var i=parseInt(r,10),a=parseInt(o,10);if(i&&a&&!(i<=0||a<=0)){t.DomEditor.isSelectedEmptyParagraph(n)&&e.Transforms.removeNodes(n,{mode:"highest"});var l=function(t,e){for(var n=[],r=0;r<t;r++){for(var o=[],i=0;i<e;i++){var a={type:"table-cell",children:[{text:""}]};0===r&&(a.isHeader=!0),o.push(a)}n.push({type:"table-row",children:o})}return{type:"table",width:"auto",children:n}}(i,a);e.Transforms.insertNodes(n,l,{mode:"highest"})}},n}(),_o=function(){function n(){this.title=t.t("tableModule.deleteTable"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M826.8032 356.5312c-19.328 0-36.3776 15.6928-36.3776 35.0464v524.2624c0 19.328-16 34.56-35.328 34.56H264.9344c-19.328 0-35.5072-15.3088-35.5072-34.56V390.0416c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.6928-33.5104 35.0464V915.712c0 57.9328 44.6208 108.288 102.528 108.288H755.2c57.9328 0 108.0832-50.4576 108.0832-108.288V391.4752c-0.1024-19.2512-17.1264-34.944-36.48-34.944z" p-id="9577"></path><path d="M437.1712 775.7568V390.6048c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.616-33.5104 35.0464v385.152c0 19.328 14.1568 35.0464 33.5104 35.0464s33.5104-15.7184 33.5104-35.0464zM649.7024 775.7568V390.6048c0-19.328-17.0496-35.0464-36.3776-35.0464s-36.3776 15.616-36.3776 35.0464v385.152c0 19.328 17.0496 35.0464 36.3776 35.0464s36.3776-15.7184 36.3776-35.0464zM965.0432 217.0368h-174.6176V145.5104c0-57.9328-47.2064-101.76-104.6528-101.76h-350.976c-57.8304 0-105.3952 43.8528-105.3952 101.76v71.5264H54.784c-19.4304 0-35.0464 14.1568-35.0464 33.5104 0 19.328 15.616 33.5104 35.0464 33.5104h910.3616c19.328 0 35.0464-14.1568 35.0464-33.5104 0-19.3536-15.6928-33.5104-35.1488-33.5104z m-247.3728 0H297.3952V145.5104c0-19.328 18.2016-34.7648 37.4272-34.7648h350.976c19.1488 0 31.872 15.1296 31.872 34.7648v71.5264z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.isDisabled=function(e){return null==e.selection||null==t.DomEditor.getSelectedNodeByType(e,"table")},n.prototype.exec=function(t,n){this.isDisabled(t)||e.Transforms.removeNodes(t,{mode:"highest"})},n}(),Wo=function(){function n(){this.title=t.t("tableModule.insertRow"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M707.7888 521.0112h-147.456v-147.456H488.2432v147.456h-147.456v68.8128h147.456v147.456h72.0896v-147.456h147.456zM0 917.504V0h1048.576v917.504H0zM327.68 65.536H65.536v196.608H327.68V65.536z m327.68 0H393.216v196.608h262.144V65.536z m327.68 0h-262.144v196.608h262.144V65.536z m0 258.8672H65.536v462.0288H983.04V324.4032z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||null==t.DomEditor.getSelectedNodeByType(n,"table"))},n.prototype.exec=function(n,r){if(!this.isDisabled(n)){var o=Xn(e.Editor.nodes(n,{match:function(e){return t.DomEditor.checkNodeType(e,"table-cell")},universal:!0}),1),i=Xn(o[0],2),a=i[0],l=i[1],u=t.DomEditor.getParentNode(n,a),c=(null==u?void 0:u.children.length)||0;if(0!==c){for(var s={type:"table-row",children:[]},f=0;f<c;f++){s.children.push({type:"table-cell",children:[{text:""}]})}var d=e.Path.parent(l),p=e.Path.next(d);e.Transforms.insertNodes(n,s,{at:p})}}},n}(),Uo=function(){function n(){this.title=t.t("tableModule.deleteRow"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M907.6736 586.5472L747.1104 425.984l163.84-163.84-78.6432-78.6432-163.84 163.84L507.************ 429.2608 262.144l163.84 163.84-167.1168 167.1168 78.6432 78.6432 167.1168-167.1168 160.5632 160.5632 75.3664-78.6432zM0 917.504V0h1048.576v917.504H0z m983.04-327.68h-22.9376l-65.536-65.536H983.04V327.68h-91.7504l65.536-65.536h26.2144V65.536H65.536v196.608h317.8496l65.536 65.536H65.536v196.608h380.1088l-65.536 65.536H65.536v196.608H983.04v-196.608z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||null==t.DomEditor.getSelectedNodeByType(n,"table-row"))},n.prototype.exec=function(n,r){if(!this.isDisabled(n)){var o=Xn(e.Editor.nodes(n,{match:function(e){return t.DomEditor.checkNodeType(e,"table-row")},universal:!0}),1),i=Xn(o[0],2),a=i[0],l=i[1],u=t.DomEditor.getParentNode(n,a);((null==u?void 0:u.children.length)||0)<=1?e.Transforms.removeNodes(n,{mode:"highest"}):e.Transforms.removeNodes(n,{at:l})}},n}(),Xo={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Yo=Ot("span").classList,Ko=Yo&&Yo.constructor&&Yo.constructor.prototype,Jo=Ko===Object.prototype?void 0:Ko,Qo=rr.forEach,Zo=function(t,e){var n=[][t];return!!n&&y((function(){n.call(null,e||function(){throw 1},1)}))}("forEach"),ti=Zo?[].forEach:function(t){return Qo(this,t,arguments.length>1?arguments[1]:void 0)},ei=function(t){if(t&&t.forEach!==ti)try{Bt(t,"forEach",ti)}catch(e){t.forEach=ti}};for(var ni in Xo)Xo[ni]&&ei(g[ni]&&g[ni].prototype);ei(Jo);var ri=function(){function n(){this.title=t.t("tableModule.insertCol"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M327.68 193.3312v186.7776H140.9024v91.7504H327.68v186.7776h88.4736V471.8592h190.0544V380.1088H416.1536V193.3312zM0 917.504V0h1048.576v917.504H0zM655.36 65.536H65.536v720.896H655.36V65.536z m327.68 0h-262.144v196.608h262.144V65.536z m0 262.144h-262.144v196.608h262.144V327.68z m0 262.144h-262.144v196.608h262.144v-196.608z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||null==t.DomEditor.getSelectedNodeByType(n,"table"))},n.prototype.exec=function(n,r){if(!this.isDisabled(n)){var o=Xn(e.Editor.nodes(n,{match:function(e){return t.DomEditor.checkNodeType(e,"table-cell")},universal:!0}),1),i=Xn(o[0],2),a=i[0],l=i[1],u=t.DomEditor.getParentNode(n,a);if(null!=u){var c=t.DomEditor.getParentNode(n,u);if(null!=c)(c.children||[]).forEach((function(r,o){e.Element.isElement(r)&&(r.children||[]).forEach((function(r){var i=t.DomEditor.findPath(n,r);if(i.length===l.length&&s.default(i.slice(-1),l.slice(-1))){var a={type:"table-cell",children:[{text:""}]};0===o&&lr(c)&&(a.isHeader=!0),e.Transforms.insertNodes(n,a,{at:i})}}))}))}}},n}(),oi=function(){function n(){this.title=t.t("tableModule.deleteCol"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M327.68 510.976L393.216 445.44v-13.1072L327.68 366.7968V510.976z m327.68-78.4384l65.536-65.536V507.904L655.36 442.368v-9.8304z m393.216 484.9664V0H0v917.504h1048.576z m-65.536-131.072h-262.144v-52.4288l-13.1072 13.1072-52.4288-52.4288v91.7504H393.216v-91.7504l-52.4288 52.4288-13.1072-13.1072v52.4288H65.536V65.536H327.68v121.2416l36.0448-36.0448 29.4912 29.4912V62.2592h262.144V180.224l49.152-49.152 16.384 16.384V62.2592h262.144V786.432z m-294.912-108.1344l-160.5632-160.5632-167.1168 167.1168-78.6432-78.6432 167.1168-167.1168L288.3584 278.528l78.6432-78.6432 160.5632 160.5632 163.84-163.84 78.6432 78.6432-163.84 163.84 160.5632 160.5632-78.6432 78.6432z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||null==t.DomEditor.getSelectedNodeByType(n,"table-cell"))},n.prototype.exec=function(n,r){if(!this.isDisabled(n)){var o=Xn(e.Editor.nodes(n,{match:function(e){return t.DomEditor.checkNodeType(e,"table-cell")},universal:!0}),1),i=Xn(o[0],2),a=i[0],l=i[1],u=t.DomEditor.getParentNode(n,a),c=(null==u?void 0:u.children.length)||0;if(!u||c<=1)e.Transforms.removeNodes(n,{mode:"highest"});else{var f=t.DomEditor.getParentNode(n,u);if(null!=f)(f.children||[]).forEach((function(r){e.Element.isElement(r)&&(r.children||[]).forEach((function(r){var o=t.DomEditor.findPath(n,r);o.length===l.length&&s.default(o.slice(-1),l.slice(-1))&&e.Transforms.removeNodes(n,{at:o})}))}))}}},n}(),ii=function(){function n(){this.title=t.t("tableModule.header"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M704 128l-64 0L384 128 320 128 0 128l0 256 0 64 0 192 0 64 0 256 320 0 64 0 256 0 64 0 320 0 0-256 0-64L1024 448 1024 384 1024 128 704 128zM640 640 384 640 384 448l256 0L640 640zM64 448l256 0 0 192L64 640 64 448zM320 896 64 896l0-192 256 0L320 896zM640 896 384 896l0-192 256 0L640 896zM960 896l-256 0 0-192 256 0L960 896zM960 640l-256 0L704 448l256 0L960 640z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(e){var n=t.DomEditor.getSelectedNodeByType(e,"table");return null!=n&&lr(n)},n.prototype.isActive=function(t){return!!this.getValue(t)},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||null==t.DomEditor.getSelectedNodeByType(n,"table"))},n.prototype.exec=function(n,r){if(!this.isDisabled(n)){var o=!r,i=t.DomEditor.getSelectedNodeByType(n,"table");if(null!=i)ar(i).forEach((function(r){return e.Transforms.setNodes(n,{isHeader:o},{at:t.DomEditor.findPath(n,r)})}))}},n}(),ai=function(){function n(){this.title=t.t("tableModule.widthAuto"),this.iconSvg='<svg viewBox="0 0 1228 1024"><path d="M862.514337 563.200461H404.581995v121.753478a13.311987 13.311987 0 0 1-6.655993 11.468789 10.23999 10.23999 0 0 1-12.083188-1.433599l-204.799795-179.199821a13.721586 13.721586 0 0 1 0-20.479979l204.799795-179.302221a10.23999 10.23999 0 0 1 12.185588-1.535998 13.209587 13.209587 0 0 1 6.553593 11.673588v115.097485h457.932342V319.693504a11.571188 11.571188 0 0 1 18.841582-10.239989l204.799795 179.19982a13.721586 13.721586 0 0 1 0 20.47998l-204.799795 179.199821a10.23999 10.23999 0 0 1-12.185588 1.535998 13.311987 13.311987 0 0 1-6.655994-11.571188V563.200461zM136.499064 14.951409v993.893406a15.257585 15.257585 0 0 1-15.155185 15.052785H15.155185A15.155185 15.155185 0 0 1 0 1008.844815V14.951409a15.257585 15.257585 0 0 1 15.155185-15.052785h106.086294a15.155185 15.155185 0 0 1 15.257585 15.155185zM1228.798771 14.951409v993.893406a15.257585 15.257585 0 0 1-15.155185 15.052785h-106.188693a15.155185 15.155185 0 0 1-15.155185-15.052785V14.951409a15.257585 15.257585 0 0 1 15.155185-15.052785h106.086293A15.155185 15.155185 0 0 1 1228.798771 15.053809z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(e){var n=t.DomEditor.getSelectedNodeByType(e,"table");return null!=n&&"100%"===n.width},n.prototype.isActive=function(t){return!!this.getValue(t)},n.prototype.isDisabled=function(n){var r=n.selection;return null==r||(!e.Range.isCollapsed(r)||null==t.DomEditor.getSelectedNodeByType(n,"table"))},n.prototype.exec=function(t,n){if(!this.isDisabled(t)){var r={width:n?"auto":"100%"};e.Transforms.setNodes(t,r,{mode:"highest"})}},n}(),li={renderElems:[yo,mo,bo],elemsToHtml:[Eo,wo,xo],preParseHtml:[So],parseElemsHtml:[$o,Fo,qo],menus:[{key:"insertTable",factory:function(){return new Go}},{key:"deleteTable",factory:function(){return new _o}},{key:"insertTableRow",factory:function(){return new Wo}},{key:"deleteTableRow",factory:function(){return new Uo}},{key:"insertTableCol",factory:function(){return new ri}},{key:"deleteTableCol",factory:function(){return new oi}},{key:"tableHeader",factory:function(){return new ii}},{key:"tableFullWidth",factory:function(){return new ai}}],editorPlugin:function(n){var r=n.insertBreak,o=n.deleteBackward,i=n.deleteForward,a=n.normalizeNode,l=n.insertData,u=n.handleTab,c=n.selectAll,s=n;return s.insertBreak=function(){null==t.DomEditor.getSelectedNodeByType(s,"table")?r():s.insertText("\n")},s.deleteBackward=function(t){if(!Yn(s)){var n=s.selection;if(n){var r=e.Editor.before(s,n);if(r){var i=Kn(s,r),a=Kn(s,n);if(i&&!a)return}}o(t)}},s.handleTab=function(){var r;if(t.DomEditor.getSelectedNodeByType(s,"table")){var o=e.Editor.above(n);t.DomEditor.checkNodeType(o[0],"table-cell")&&e.Transforms.select(n,o[1]);var i=e.Editor.next(n);if(i)i[0]&&i[0].text&&(i=null!==(r=e.Editor.above(n,{at:i[1]}))&&void 0!==r?r:i),e.Transforms.select(n,i[1]);else{var a=s.children||[],l=a.length;if(t.DomEditor.checkNodeType(a[l-1],"table")){var c=t.DomEditor.genEmptyParagraph();e.Transforms.insertNodes(s,c,{at:[l]}),s.handleTab()}}}else u()},s.deleteForward=function(t){Yn(s)||i(t)},s.normalizeNode=function(n){var r=Xn(n,2),o=r[0],i=r[1];if("table"!==t.DomEditor.getNodeType(o))return a([o,i]);if(t.DomEditor.isLastNode(s,o)){var l=t.DomEditor.genEmptyParagraph();e.Transforms.insertNodes(s,l,{at:[i[0]+1]})}},s.insertData=function(n){if(null!=t.DomEditor.getSelectedNodeByType(s,"table")){var r=n.getData("text/plain");"\n"===r||/<img[^>]+>/.test(n.getData("text/html"))?l(n):e.Editor.insertText(s,r)}else l(n)},s.selectAll=function(){var n=s.selection;if(null!=n){var r=t.DomEditor.getSelectedNodeByType(s,"table-cell");if(null!=r){var o=n.anchor,i=n.focus;if(e.Path.equals(o.path.slice(0,3),i.path.slice(0,3)))if(0!==e.Node.string(r).length){var a=t.DomEditor.findPath(s,r),l={anchor:e.Editor.start(s,a),focus:e.Editor.end(s,a)};s.select(l)}else c();else c()}else c()}else c()},s}};return li}));
//# sourceMappingURL=index.js.map
