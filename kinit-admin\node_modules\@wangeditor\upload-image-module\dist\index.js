!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@wangeditor/core"),require("@wangeditor/basic-modules"),require("dom7")):"function"==typeof define&&define.amd?define(["@wangeditor/core","@wangeditor/basic-modules","dom7"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).WangEditorUploadImageModule=e(t.core,t.basicModules,t.$)}(this,(function(t,e,n){"use strict";function r(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var o=r(n);t.i18nAddResources("en",{uploadImgModule:{uploadImage:"Upload Image",uploadError:"{{fileName}} upload error"}}),t.i18nAddResources("zh-CN",{uploadImgModule:{uploadImage:"上传图片",uploadError:"{{fileName}} 上传出错"}});var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function u(t){var e={exports:{}};return t(e,e.exports),e.exports}var a,c,f=function(t){return t&&t.Math==Math&&t},s=f("object"==typeof globalThis&&globalThis)||f("object"==typeof window&&window)||f("object"==typeof self&&self)||f("object"==typeof i&&i)||function(){return this}()||Function("return this")(),l=function(t){try{return!!t()}catch(t){return!0}},p=!l((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),d=Function.prototype.call,v=d.bind?d.bind(d):function(){return d.apply(d,arguments)},h={}.propertyIsEnumerable,y=Object.getOwnPropertyDescriptor,g={f:y&&!h.call({1:2},1)?function(t){var e=y(this,t);return!!e&&e.enumerable}:h},m=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},b=Function.prototype,w=b.bind,x=b.call,S=w&&w.bind(x),O=w?function(t){return t&&S(x,t)}:function(t){return t&&function(){return x.apply(t,arguments)}},E=O({}.toString),j=O("".slice),I=function(t){return j(E(t),8,-1)},T=s.Object,A=O("".split),P=l((function(){return!T("z").propertyIsEnumerable(0)}))?function(t){return"String"==I(t)?A(t,""):T(t)}:T,R=s.TypeError,M=function(t){if(null==t)throw R("Can't call method on "+t);return t},k=function(t){return P(M(t))},L=function(t){return"function"==typeof t},F=function(t){return"object"==typeof t?null!==t:L(t)},C=function(t){return L(t)?t:void 0},N=function(t,e){return arguments.length<2?C(s[t]):s[t]&&s[t][e]},z=O({}.isPrototypeOf),_=N("navigator","userAgent")||"",D=s.process,U=s.Deno,G=D&&D.versions||U&&U.version,B=G&&G.v8;B&&(c=(a=B.split("."))[0]>0&&a[0]<4?1:+(a[0]+a[1])),!c&&_&&(!(a=_.match(/Edge\/(\d+)/))||a[1]>=74)&&(a=_.match(/Chrome\/(\d+)/))&&(c=+a[1]);var W=c,V=!!Object.getOwnPropertySymbols&&!l((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&W&&W<41})),H=V&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,q=s.Object,K=H?function(t){return"symbol"==typeof t}:function(t){var e=N("Symbol");return L(e)&&z(e.prototype,q(t))},Y=s.String,X=function(t){try{return Y(t)}catch(t){return"Object"}},$=s.TypeError,J=function(t){if(L(t))return t;throw $(X(t)+" is not a function")},Q=function(t,e){var n=t[e];return null==n?void 0:J(n)},Z=s.TypeError,tt=Object.defineProperty,et=function(t,e){try{tt(s,t,{value:e,configurable:!0,writable:!0})}catch(n){s[t]=e}return e},nt="__core-js_shared__",rt=s[nt]||et(nt,{}),ot=u((function(t){(t.exports=function(t,e){return rt[t]||(rt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),it=s.Object,ut=function(t){return it(M(t))},at=O({}.hasOwnProperty),ct=Object.hasOwn||function(t,e){return at(ut(t),e)},ft=0,st=Math.random(),lt=O(1..toString),pt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+lt(++ft+st,36)},dt=ot("wks"),vt=s.Symbol,ht=vt&&vt.for,yt=H?vt:vt&&vt.withoutSetter||pt,gt=function(t){if(!ct(dt,t)||!V&&"string"!=typeof dt[t]){var e="Symbol."+t;V&&ct(vt,t)?dt[t]=vt[t]:dt[t]=H&&ht?ht(e):yt(e)}return dt[t]},mt=s.TypeError,bt=gt("toPrimitive"),wt=function(t,e){if(!F(t)||K(t))return t;var n,r=Q(t,bt);if(r){if(void 0===e&&(e="default"),n=v(r,t,e),!F(n)||K(n))return n;throw mt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&L(n=t.toString)&&!F(r=v(n,t)))return r;if(L(n=t.valueOf)&&!F(r=v(n,t)))return r;if("string"!==e&&L(n=t.toString)&&!F(r=v(n,t)))return r;throw Z("Can't convert object to primitive value")}(t,e)},xt=function(t){var e=wt(t,"string");return K(e)?e:e+""},St=s.document,Ot=F(St)&&F(St.createElement),Et=function(t){return Ot?St.createElement(t):{}},jt=!p&&!l((function(){return 7!=Object.defineProperty(Et("div"),"a",{get:function(){return 7}}).a})),It=Object.getOwnPropertyDescriptor,Tt={f:p?It:function(t,e){if(t=k(t),e=xt(e),jt)try{return It(t,e)}catch(t){}if(ct(t,e))return m(!v(g.f,t,e),t[e])}},At=s.String,Pt=s.TypeError,Rt=function(t){if(F(t))return t;throw Pt(At(t)+" is not an object")},Mt=s.TypeError,kt=Object.defineProperty,Lt={f:p?kt:function(t,e,n){if(Rt(t),e=xt(e),Rt(n),jt)try{return kt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Mt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Ft=p?function(t,e,n){return Lt.f(t,e,m(1,n))}:function(t,e,n){return t[e]=n,t},Ct=O(Function.toString);L(rt.inspectSource)||(rt.inspectSource=function(t){return Ct(t)});var Nt,zt,_t,Dt=rt.inspectSource,Ut=s.WeakMap,Gt=L(Ut)&&/native code/.test(Dt(Ut)),Bt=ot("keys"),Wt=function(t){return Bt[t]||(Bt[t]=pt(t))},Vt={},Ht="Object already initialized",qt=s.TypeError,Kt=s.WeakMap;if(Gt||rt.state){var Yt=rt.state||(rt.state=new Kt),Xt=O(Yt.get),$t=O(Yt.has),Jt=O(Yt.set);Nt=function(t,e){if($t(Yt,t))throw new qt(Ht);return e.facade=t,Jt(Yt,t,e),e},zt=function(t){return Xt(Yt,t)||{}},_t=function(t){return $t(Yt,t)}}else{var Qt=Wt("state");Vt[Qt]=!0,Nt=function(t,e){if(ct(t,Qt))throw new qt(Ht);return e.facade=t,Ft(t,Qt,e),e},zt=function(t){return ct(t,Qt)?t[Qt]:{}},_t=function(t){return ct(t,Qt)}}var Zt={set:Nt,get:zt,has:_t,enforce:function(t){return _t(t)?zt(t):Nt(t,{})},getterFor:function(t){return function(e){var n;if(!F(e)||(n=zt(e)).type!==t)throw qt("Incompatible receiver, "+t+" required");return n}}},te=Function.prototype,ee=p&&Object.getOwnPropertyDescriptor,ne=ct(te,"name"),re={EXISTS:ne,PROPER:ne&&"something"===function(){}.name,CONFIGURABLE:ne&&(!p||p&&ee(te,"name").configurable)},oe=u((function(t){var e=re.CONFIGURABLE,n=Zt.get,r=Zt.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var a,c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet,p=u&&void 0!==u.name?u.name:n;L(i)&&("Symbol("===String(p).slice(0,7)&&(p="["+String(p).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!ct(i,"name")||e&&i.name!==p)&&Ft(i,"name",p),(a=r(i)).source||(a.source=o.join("string"==typeof p?p:""))),t!==s?(c?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=i:Ft(t,n,i)):f?t[n]=i:et(n,i)})(Function.prototype,"toString",(function(){return L(this)&&n(this).source||Dt(this)}))})),ie=Math.ceil,ue=Math.floor,ae=function(t){var e=+t;return e!=e||0===e?0:(e>0?ue:ie)(e)},ce=Math.max,fe=Math.min,se=function(t,e){var n=ae(t);return n<0?ce(n+e,0):fe(n,e)},le=Math.min,pe=function(t){return t>0?le(ae(t),9007199254740991):0},de=function(t){return pe(t.length)},ve=function(t){return function(e,n,r){var o,i=k(e),u=de(i),a=se(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},he={includes:ve(!0),indexOf:ve(!1)}.indexOf,ye=O([].push),ge=function(t,e){var n,r=k(t),o=0,i=[];for(n in r)!ct(Vt,n)&&ct(r,n)&&ye(i,n);for(;e.length>o;)ct(r,n=e[o++])&&(~he(i,n)||ye(i,n));return i},me=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],be=me.concat("length","prototype"),we={f:Object.getOwnPropertyNames||function(t){return ge(t,be)}},xe={f:Object.getOwnPropertySymbols},Se=O([].concat),Oe=N("Reflect","ownKeys")||function(t){var e=we.f(Rt(t)),n=xe.f;return n?Se(e,n(t)):e},Ee=function(t,e){for(var n=Oe(e),r=Lt.f,o=Tt.f,i=0;i<n.length;i++){var u=n[i];ct(t,u)||r(t,u,o(e,u))}},je=/#|\.prototype\./,Ie=function(t,e){var n=Ae[Te(t)];return n==Re||n!=Pe&&(L(e)?l(e):!!e)},Te=Ie.normalize=function(t){return String(t).replace(je,".").toLowerCase()},Ae=Ie.data={},Pe=Ie.NATIVE="N",Re=Ie.POLYFILL="P",Me=Ie,ke=Tt.f,Le=function(t,e){var n,r,o,i,u,a=t.target,c=t.global,f=t.stat;if(n=c?s:f?s[a]||et(a,{}):(s[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=ke(n,r))&&u.value:n[r],!Me(c?r:a+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ee(i,o)}(t.sham||o&&o.sham)&&Ft(i,"sham",!0),oe(n,r,i,t)}},Fe=Array.isArray||function(t){return"Array"==I(t)},Ce={};Ce[gt("toStringTag")]="z";var Ne,ze="[object z]"===String(Ce),_e=gt("toStringTag"),De=s.Object,Ue="Arguments"==I(function(){return arguments}()),Ge=ze?I:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=De(t),_e))?n:Ue?I(e):"Object"==(r=I(e))&&L(e.callee)?"Arguments":r},Be=function(){},We=[],Ve=N("Reflect","construct"),He=/^\s*(?:class|function)\b/,qe=O(He.exec),Ke=!He.exec(Be),Ye=function(t){if(!L(t))return!1;try{return Ve(Be,We,t),!0}catch(t){return!1}},Xe=!Ve||l((function(){var t;return Ye(Ye.call)||!Ye(Object)||!Ye((function(){t=!0}))||t}))?function(t){if(!L(t))return!1;switch(Ge(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Ke||!!qe(He,Dt(t))}:Ye,$e=function(t,e,n){var r=xt(e);r in t?Lt.f(t,r,m(0,n)):t[r]=n},Je=gt("species"),Qe=O([].slice),Ze=(Ne="slice",W>=51||!l((function(){var t=[];return(t.constructor={})[Je]=function(){return{foo:1}},1!==t[Ne](Boolean).foo}))),tn=gt("species"),en=s.Array,nn=Math.max;Le({target:"Array",proto:!0,forced:!Ze},{slice:function(t,e){var n,r,o,i=k(this),u=de(i),a=se(t,u),c=se(void 0===e?u:e,u);if(Fe(i)&&(n=i.constructor,(Xe(n)&&(n===en||Fe(n.prototype))||F(n)&&null===(n=n[tn]))&&(n=void 0),n===en||void 0===n))return Qe(i,a,c);for(r=new(void 0===n?en:n)(nn(c-a,0)),o=0;a<c;a++,o++)a in i&&$e(r,o,i[a]);return r.length=o,r}});var rn=ze?{}.toString:function(){return"[object "+Ge(this)+"]"};ze||oe(Object.prototype,"toString",rn,{unsafe:!0});var on,un=s.String,an=function(t){if("Symbol"===Ge(t))throw TypeError("Cannot convert a Symbol value to a string");return un(t)},cn=function(){var t=Rt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},fn=s.RegExp,sn=l((function(){var t=fn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),ln=sn||l((function(){return!fn("a","y").sticky})),pn={BROKEN_CARET:sn||l((function(){var t=fn("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:ln,UNSUPPORTED_Y:sn},dn=Object.keys||function(t){return ge(t,me)},vn=p?Object.defineProperties:function(t,e){Rt(t);for(var n,r=k(e),o=dn(e),i=o.length,u=0;i>u;)Lt.f(t,n=o[u++],r[n]);return t},hn=N("document","documentElement"),yn=Wt("IE_PROTO"),gn=function(){},mn=function(t){return"<script>"+t+"</"+"script>"},bn=function(t){t.write(mn("")),t.close();var e=t.parentWindow.Object;return t=null,e},wn=function(){try{on=new ActiveXObject("htmlfile")}catch(t){}var t,e;wn="undefined"!=typeof document?document.domain&&on?bn(on):((e=Et("iframe")).style.display="none",hn.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(mn("document.F=Object")),t.close(),t.F):bn(on);for(var n=me.length;n--;)delete wn.prototype[me[n]];return wn()};Vt[yn]=!0;var xn,Sn,On=Object.create||function(t,e){var n;return null!==t?(gn.prototype=Rt(t),n=new gn,gn.prototype=null,n[yn]=t):n=wn(),void 0===e?n:vn(n,e)},En=s.RegExp,jn=l((function(){var t=En(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),In=s.RegExp,Tn=l((function(){var t=In("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),An=Zt.get,Pn=ot("native-string-replace",String.prototype.replace),Rn=RegExp.prototype.exec,Mn=Rn,kn=O("".charAt),Ln=O("".indexOf),Fn=O("".replace),Cn=O("".slice),Nn=(Sn=/b*/g,v(Rn,xn=/a/,"a"),v(Rn,Sn,"a"),0!==xn.lastIndex||0!==Sn.lastIndex),zn=pn.BROKEN_CARET,_n=void 0!==/()??/.exec("")[1];(Nn||_n||zn||jn||Tn)&&(Mn=function(t){var e,n,r,o,i,u,a,c=this,f=An(c),s=an(t),l=f.raw;if(l)return l.lastIndex=c.lastIndex,e=v(Mn,l,s),c.lastIndex=l.lastIndex,e;var p=f.groups,d=zn&&c.sticky,h=v(cn,c),y=c.source,g=0,m=s;if(d&&(h=Fn(h,"y",""),-1===Ln(h,"g")&&(h+="g"),m=Cn(s,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==kn(s,c.lastIndex-1))&&(y="(?: "+y+")",m=" "+m,g++),n=new RegExp("^(?:"+y+")",h)),_n&&(n=new RegExp("^"+y+"$(?!\\s)",h)),Nn&&(r=c.lastIndex),o=v(Rn,d?n:c,m),d?o?(o.input=Cn(o.input,g),o[0]=Cn(o[0],g),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Nn&&o&&(c.lastIndex=c.global?o.index+o[0].length:r),_n&&o&&o.length>1&&v(Pn,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&p)for(o.groups=u=On(null),i=0;i<p.length;i++)u[(a=p[i])[0]]=o[a[1]];return o});var Dn=Mn;Le({target:"RegExp",proto:!0,forced:/./.exec!==Dn},{exec:Dn});var Un=Function.prototype,Gn=Un.apply,Bn=Un.bind,Wn=Un.call,Vn="object"==typeof Reflect&&Reflect.apply||(Bn?Wn.bind(Gn):function(){return Wn.apply(Gn,arguments)}),Hn=gt("species"),qn=RegExp.prototype,Kn=gt("match"),Yn=s.TypeError,Xn=gt("species"),$n=function(t,e){var n,r=Rt(t).constructor;return void 0===r||null==(n=Rt(r)[Xn])?e:function(t){if(Xe(t))return t;throw Yn(X(t)+" is not a constructor")}(n)},Jn=O("".charAt),Qn=O("".charCodeAt),Zn=O("".slice),tr=function(t){return function(e,n){var r,o,i=an(M(e)),u=ae(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=Qn(i,u))<55296||r>56319||u+1===a||(o=Qn(i,u+1))<56320||o>57343?t?Jn(i,u):r:t?Zn(i,u,u+2):o-56320+(r-55296<<10)+65536}},er={codeAt:tr(!1),charAt:tr(!0)},nr=er.charAt,rr=function(t,e,n){return e+(n?nr(t,e).length:1)},or=s.Array,ir=Math.max,ur=function(t,e,n){for(var r=de(t),o=se(e,r),i=se(void 0===n?r:n,r),u=or(ir(i-o,0)),a=0;o<i;o++,a++)$e(u,a,t[o]);return u.length=a,u},ar=s.TypeError,cr=function(t,e){var n=t.exec;if(L(n)){var r=v(n,t,e);return null!==r&&Rt(r),r}if("RegExp"===I(t))return v(Dn,t,e);throw ar("RegExp#exec called on incompatible receiver")},fr=pn.UNSUPPORTED_Y,sr=4294967295,lr=Math.min,pr=[].push,dr=O(/./.exec),vr=O(pr),hr=O("".slice),yr=!l((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]}));!function(t,e,n,r){var o=gt(t),i=!l((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!l((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[Hn]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!u||n){var a=O(/./[o]),c=e(o,""[t],(function(t,e,n,r,o){var u=O(t),c=e.exec;return c===Dn||c===qn.exec?i&&!o?{done:!0,value:a(e,n,r)}:{done:!0,value:u(n,e,r)}:{done:!1}}));oe(String.prototype,t,c[0]),oe(qn,o,c[1])}r&&Ft(qn[o],"sham",!0)}("split",(function(t,e,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var r,o,i=an(M(this)),u=void 0===n?sr:n>>>0;if(0===u)return[];if(void 0===t)return[i];if(!F(r=t)||!(void 0!==(o=r[Kn])?o:"RegExp"==I(r)))return v(e,i,t,u);for(var a,c,f,s=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,d=new RegExp(t.source,l+"g");(a=v(Dn,d,i))&&!((c=d.lastIndex)>p&&(vr(s,hr(i,p,a.index)),a.length>1&&a.index<i.length&&Vn(pr,s,ur(a,1)),f=a[0].length,p=c,s.length>=u));)d.lastIndex===a.index&&d.lastIndex++;return p===i.length?!f&&dr(d,"")||vr(s,""):vr(s,hr(i,p)),s.length>u?ur(s,0,u):s}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:v(e,this,t,n)}:e,[function(e,n){var o=M(this),i=null==e?void 0:Q(e,t);return i?v(i,e,o,n):v(r,an(o),e,n)},function(t,o){var i=Rt(this),u=an(t),a=n(r,i,u,o,r!==e);if(a.done)return a.value;var c=$n(i,RegExp),f=i.unicode,s=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(fr?"g":"y"),l=new c(fr?"^(?:"+i.source+")":i,s),p=void 0===o?sr:o>>>0;if(0===p)return[];if(0===u.length)return null===cr(l,u)?[u]:[];for(var d=0,v=0,h=[];v<u.length;){l.lastIndex=fr?0:v;var y,g=cr(l,fr?hr(u,v):u);if(null===g||(y=lr(pe(l.lastIndex+(fr?v:0)),u.length))===d)v=rr(u,v,f);else{if(vr(h,hr(u,d,v)),h.length===p)return h;for(var m=1;m<=g.length-1;m++)if(vr(h,g[m]),h.length===p)return h;v=d=y}}return vr(h,hr(u,d)),h}]}),!yr,fr);
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */
var gr=function(){return gr=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},gr.apply(this,arguments)};function mr(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}c((r=r.apply(t,e||[])).next())}))}function br(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function wr(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function xr(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=wr(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,o){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,o,(e=t[n](e)).done,e.value)}))}}}var Sr=gt("unscopables"),Or=Array.prototype;null==Or[Sr]&&Lt.f(Or,Sr,{configurable:!0,value:On(null)});var Er,jr,Ir,Tr=function(t){Or[Sr][t]=!0},Ar={},Pr=!l((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Rr=Wt("IE_PROTO"),Mr=s.Object,kr=Mr.prototype,Lr=Pr?Mr.getPrototypeOf:function(t){var e=ut(t);if(ct(e,Rr))return e[Rr];var n=e.constructor;return L(n)&&e instanceof n?n.prototype:e instanceof Mr?kr:null},Fr=gt("iterator"),Cr=!1;[].keys&&("next"in(Ir=[].keys())?(jr=Lr(Lr(Ir)))!==Object.prototype&&(Er=jr):Cr=!0);var Nr=null==Er||l((function(){var t={};return Er[Fr].call(t)!==t}));Nr&&(Er={}),L(Er[Fr])||oe(Er,Fr,(function(){return this}));var zr={IteratorPrototype:Er,BUGGY_SAFARI_ITERATORS:Cr},_r=Lt.f,Dr=gt("toStringTag"),Ur=function(t,e,n){t&&!ct(t=n?t:t.prototype,Dr)&&_r(t,Dr,{configurable:!0,value:e})},Gr=zr.IteratorPrototype,Br=function(){return this},Wr=s.String,Vr=s.TypeError,Hr=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=O(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),e=n instanceof Array}catch(t){}return function(n,r){return Rt(n),function(t){if("object"==typeof t||L(t))return t;throw Vr("Can't set "+Wr(t)+" as a prototype")}(r),e?t(n,r):n.__proto__=r,n}}():void 0),qr=re.PROPER,Kr=re.CONFIGURABLE,Yr=zr.IteratorPrototype,Xr=zr.BUGGY_SAFARI_ITERATORS,$r=gt("iterator"),Jr="keys",Qr="values",Zr="entries",to=function(){return this},eo=function(t,e,n,r,o,i,u){!function(t,e,n,r){var o=e+" Iterator";t.prototype=On(Gr,{next:m(+!r,n)}),Ur(t,o,!1),Ar[o]=Br}(n,e,r);var a,c,f,s=function(t){if(t===o&&y)return y;if(!Xr&&t in d)return d[t];switch(t){case Jr:case Qr:case Zr:return function(){return new n(this,t)}}return function(){return new n(this)}},l=e+" Iterator",p=!1,d=t.prototype,h=d[$r]||d["@@iterator"]||o&&d[o],y=!Xr&&h||s(o),g="Array"==e&&d.entries||h;if(g&&(a=Lr(g.call(new t)))!==Object.prototype&&a.next&&(Lr(a)!==Yr&&(Hr?Hr(a,Yr):L(a[$r])||oe(a,$r,to)),Ur(a,l,!0)),qr&&o==Qr&&h&&h.name!==Qr&&(Kr?Ft(d,"name",Qr):(p=!0,y=function(){return v(h,this)})),o)if(c={values:s(Qr),keys:i?y:s(Jr),entries:s(Zr)},u)for(f in c)(Xr||p||!(f in d))&&oe(d,f,c[f]);else Le({target:e,proto:!0,forced:Xr||p},c);return d[$r]!==y&&oe(d,$r,y,{name:o}),Ar[e]=y,c},no="Array Iterator",ro=Zt.set,oo=Zt.getterFor(no),io=eo(Array,"Array",(function(t,e){ro(this,{type:no,target:k(t),index:0,kind:e})}),(function(){var t=oo(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values");Ar.Arguments=Ar.Array,Tr("keys"),Tr("values"),Tr("entries");var uo=er.charAt,ao="String Iterator",co=Zt.set,fo=Zt.getterFor(ao);eo(String,"String",(function(t){co(this,{type:ao,string:an(t),index:0})}),(function(){var t,e=fo(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=uo(n,r),e.index+=t.length,{value:t,done:!1})}));var so=function(t,e,n){for(var r in e)oe(t,r,e[r],n);return t},lo=we.f,po="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],vo={f:function(t){return po&&"Window"==I(t)?function(t){try{return lo(t)}catch(t){return ur(po)}}(t):lo(k(t))}},ho=l((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),yo=Object.isExtensible,go=l((function(){yo(1)}))||ho?function(t){return!!F(t)&&((!ho||"ArrayBuffer"!=I(t))&&(!yo||yo(t)))}:yo,mo=!l((function(){return Object.isExtensible(Object.preventExtensions({}))})),bo=u((function(t){var e=Lt.f,n=!1,r=pt("meta"),o=0,i=function(t){e(t,r,{value:{objectID:"O"+o++,weakData:{}}})},u=t.exports={enable:function(){u.enable=function(){},n=!0;var t=we.f,e=O([].splice),o={};o[r]=1,t(o).length&&(we.f=function(n){for(var o=t(n),i=0,u=o.length;i<u;i++)if(o[i]===r){e(o,i,1);break}return o},Le({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:vo.f}))},fastKey:function(t,e){if(!F(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!ct(t,r)){if(!go(t))return"F";if(!e)return"E";i(t)}return t[r].objectID},getWeakData:function(t,e){if(!ct(t,r)){if(!go(t))return!0;if(!e)return!1;i(t)}return t[r].weakData},onFreeze:function(t){return mo&&n&&go(t)&&!ct(t,r)&&i(t),t}};Vt[r]=!0})),wo=O(O.bind),xo=function(t,e){return J(t),void 0===e?t:wo?wo(t,e):function(){return t.apply(e,arguments)}},So=gt("iterator"),Oo=Array.prototype,Eo=gt("iterator"),jo=function(t){if(null!=t)return Q(t,Eo)||Q(t,"@@iterator")||Ar[Ge(t)]},Io=s.TypeError,To=function(t,e,n){var r,o;Rt(t);try{if(!(r=Q(t,"return"))){if("throw"===e)throw n;return n}r=v(r,t)}catch(t){o=!0,r=t}if("throw"===e)throw n;if(o)throw r;return Rt(r),n},Ao=s.TypeError,Po=function(t,e){this.stopped=t,this.result=e},Ro=Po.prototype,Mo=function(t,e,n){var r,o,i,u,a,c,f,s,l=n&&n.that,p=!(!n||!n.AS_ENTRIES),d=!(!n||!n.IS_ITERATOR),h=!(!n||!n.INTERRUPTED),y=xo(e,l),g=function(t){return r&&To(r,"normal",t),new Po(!0,t)},m=function(t){return p?(Rt(t),h?y(t[0],t[1],g):y(t[0],t[1])):h?y(t,g):y(t)};if(d)r=t;else{if(!(o=jo(t)))throw Ao(X(t)+" is not iterable");if(void 0!==(s=o)&&(Ar.Array===s||Oo[So]===s)){for(i=0,u=de(t);u>i;i++)if((a=m(t[i]))&&z(Ro,a))return a;return new Po(!1)}r=function(t,e){var n=arguments.length<2?jo(t):e;if(J(n))return Rt(v(n,t));throw Io(X(t)+" is not iterable")}(t,o)}for(c=r.next;!(f=v(c,r)).done;){try{a=m(f.value)}catch(t){To(r,"throw",t)}if("object"==typeof a&&a&&z(Ro,a))return a}return new Po(!1)},ko=s.TypeError,Lo=function(t,e){if(z(e,t))return t;throw ko("Incorrect invocation")},Fo=gt("iterator"),Co=!1;try{var No=0,zo={next:function(){return{done:!!No++}},return:function(){Co=!0}};zo[Fo]=function(){return this},Array.from(zo,(function(){throw 2}))}catch(t){}var _o=function(t,e){if(!e&&!Co)return!1;var n=!1;try{var r={};r[Fo]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(t){}return n},Do=gt("species"),Uo=s.Array,Go=function(t,e){return new(function(t){var e;return Fe(t)&&(e=t.constructor,(Xe(e)&&(e===Uo||Fe(e.prototype))||F(e)&&null===(e=e[Do]))&&(e=void 0)),void 0===e?Uo:e}(t))(0===e?0:e)},Bo=O([].push),Wo=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,f,s,l){for(var p,d,v=ut(c),h=P(v),y=xo(f,s),g=de(h),m=0,b=l||Go,w=e?b(c,g):n||u?b(c,0):void 0;g>m;m++)if((a||m in h)&&(d=y(p=h[m],m,v),t))if(e)w[m]=d;else if(d)switch(t){case 3:return!0;case 5:return p;case 6:return m;case 2:Bo(w,p)}else switch(t){case 4:return!1;case 7:Bo(w,p)}return i?-1:r||o?o:w}},Vo={forEach:Wo(0),map:Wo(1),filter:Wo(2),some:Wo(3),every:Wo(4),find:Wo(5),findIndex:Wo(6),filterReject:Wo(7)},Ho=bo.getWeakData,qo=Zt.set,Ko=Zt.getterFor,Yo=Vo.find,Xo=Vo.findIndex,$o=O([].splice),Jo=0,Qo=function(t){return t.frozen||(t.frozen=new Zo)},Zo=function(){this.entries=[]},ti=function(t,e){return Yo(t.entries,(function(t){return t[0]===e}))};Zo.prototype={get:function(t){var e=ti(this,t);if(e)return e[1]},has:function(t){return!!ti(this,t)},set:function(t,e){var n=ti(this,t);n?n[1]=e:this.entries.push([t,e])},delete:function(t){var e=Xo(this.entries,(function(e){return e[0]===t}));return~e&&$o(this.entries,e,1),!!~e}};var ei,ni={getConstructor:function(t,e,n,r){var o=t((function(t,o){Lo(t,i),qo(t,{type:e,id:Jo++,frozen:void 0}),null!=o&&Mo(o,t[r],{that:t,AS_ENTRIES:n})})),i=o.prototype,u=Ko(e),a=function(t,e,n){var r=u(t),o=Ho(Rt(e),!0);return!0===o?Qo(r).set(e,n):o[r.id]=n,t};return so(i,{delete:function(t){var e=u(this);if(!F(t))return!1;var n=Ho(t);return!0===n?Qo(e).delete(t):n&&ct(n,e.id)&&delete n[e.id]},has:function(t){var e=u(this);if(!F(t))return!1;var n=Ho(t);return!0===n?Qo(e).has(t):n&&ct(n,e.id)}}),so(i,n?{get:function(t){var e=u(this);if(F(t)){var n=Ho(t);return!0===n?Qo(e).get(t):n?n[e.id]:void 0}},set:function(t,e){return a(this,t,e)}}:{add:function(t){return a(this,t,!0)}}),o}},ri=Zt.enforce,oi=!s.ActiveXObject&&"ActiveXObject"in s,ii=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},ui=function(t,e,n){var r=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),i=r?"set":"add",u=s[t],a=u&&u.prototype,c=u,f={},p=function(t){var e=O(a[t]);oe(a,t,"add"==t?function(t){return e(this,0===t?0:t),this}:"delete"==t?function(t){return!(o&&!F(t))&&e(this,0===t?0:t)}:"get"==t?function(t){return o&&!F(t)?void 0:e(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!F(t))&&e(this,0===t?0:t)}:function(t,n){return e(this,0===t?0:t,n),this})};if(Me(t,!L(u)||!(o||a.forEach&&!l((function(){(new u).entries().next()})))))c=n.getConstructor(e,t,r,i),bo.enable();else if(Me(t,!0)){var d=new c,v=d[i](o?{}:-0,1)!=d,h=l((function(){d.has(1)})),y=_o((function(t){new u(t)})),g=!o&&l((function(){for(var t=new u,e=5;e--;)t[i](e,e);return!t.has(-0)}));y||((c=e((function(t,e){Lo(t,a);var n=function(t,e,n){var r,o;return Hr&&L(r=e.constructor)&&r!==n&&F(o=r.prototype)&&o!==n.prototype&&Hr(t,o),t}(new u,t,c);return null!=e&&Mo(e,n[i],{that:n,AS_ENTRIES:r}),n}))).prototype=a,a.constructor=c),(h||g)&&(p("delete"),p("has"),r&&p("get")),(g||v)&&p(i),o&&a.clear&&delete a.clear}return f[t]=c,Le({global:!0,forced:c!=u},f),Ur(c,t),o||n.setStrong(c,t,r),c}("WeakMap",ii,ni);if(Gt&&oi){ei=ni.getConstructor(ii,"WeakMap",!0),bo.enable();var ai=ui.prototype,ci=O(ai.delete),fi=O(ai.has),si=O(ai.get),li=O(ai.set);so(ai,{delete:function(t){if(F(t)&&!go(t)){var e=ri(this);return e.frozen||(e.frozen=new ei),ci(this,t)||e.frozen.delete(t)}return ci(this,t)},has:function(t){if(F(t)&&!go(t)){var e=ri(this);return e.frozen||(e.frozen=new ei),fi(this,t)||e.frozen.has(t)}return fi(this,t)},get:function(t){if(F(t)&&!go(t)){var e=ri(this);return e.frozen||(e.frozen=new ei),fi(this,t)?si(this,t):e.frozen.get(t)}return si(this,t)},set:function(t,e){if(F(t)&&!go(t)){var n=ri(this);n.frozen||(n.frozen=new ei),fi(this,t)?li(this,t,e):n.frozen.set(t,e)}else li(this,t,e);return this}})}var pi={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},di=Et("span").classList,vi=di&&di.constructor&&di.constructor.prototype,hi=vi===Object.prototype?void 0:vi,yi=gt("iterator"),gi=gt("toStringTag"),mi=io.values,bi=function(t,e){if(t){if(t[yi]!==mi)try{Ft(t,yi,mi)}catch(e){t[yi]=mi}if(t[gi]||Ft(t,gi,e),pi[e])for(var n in io)if(t[n]!==io[n])try{Ft(t,n,io[n])}catch(e){t[n]=io[n]}}};for(var wi in pi)bi(s[wi]&&s[wi].prototype,wi);bi(hi,"DOMTokenList");var xi=function(t,e){var n=[][t];return!!n&&l((function(){n.call(null,e||function(){throw 1},1)}))},Si=Vo.forEach,Oi=xi("forEach")?[].forEach:function(t){return Si(this,t,arguments.length>1?arguments[1]:void 0)},Ei=function(t){if(t&&t.forEach!==Oi)try{Ft(t,"forEach",Oi)}catch(e){t.forEach=Oi}};for(var ji in pi)pi[ji]&&Ei(s[ji]&&s[ji].prototype);Ei(hi);var Ii,Ti,Ai,Pi,Ri=s.Promise,Mi=gt("species"),ki=/(?:ipad|iphone|ipod).*applewebkit/i.test(_),Li="process"==I(s.process),Fi=s.setImmediate,Ci=s.clearImmediate,Ni=s.process,zi=s.Dispatch,_i=s.Function,Di=s.MessageChannel,Ui=s.String,Gi=0,Bi={},Wi="onreadystatechange";try{Ii=s.location}catch(t){}var Vi=function(t){if(ct(Bi,t)){var e=Bi[t];delete Bi[t],e()}},Hi=function(t){return function(){Vi(t)}},qi=function(t){Vi(t.data)},Ki=function(t){s.postMessage(Ui(t),Ii.protocol+"//"+Ii.host)};Fi&&Ci||(Fi=function(t){var e=Qe(arguments,1);return Bi[++Gi]=function(){Vn(L(t)?t:_i(t),void 0,e)},Ti(Gi),Gi},Ci=function(t){delete Bi[t]},Li?Ti=function(t){Ni.nextTick(Hi(t))}:zi&&zi.now?Ti=function(t){zi.now(Hi(t))}:Di&&!ki?(Pi=(Ai=new Di).port2,Ai.port1.onmessage=qi,Ti=xo(Pi.postMessage,Pi)):s.addEventListener&&L(s.postMessage)&&!s.importScripts&&Ii&&"file:"!==Ii.protocol&&!l(Ki)?(Ti=Ki,s.addEventListener("message",qi,!1)):Ti=Wi in Et("script")?function(t){hn.appendChild(Et("script")).onreadystatechange=function(){hn.removeChild(this),Vi(t)}}:function(t){setTimeout(Hi(t),0)});var Yi,Xi,$i,Ji,Qi,Zi,tu,eu,nu={set:Fi,clear:Ci},ru=/ipad|iphone|ipod/i.test(_)&&void 0!==s.Pebble,ou=/web0s(?!.*chrome)/i.test(_),iu=Tt.f,uu=nu.set,au=s.MutationObserver||s.WebKitMutationObserver,cu=s.document,fu=s.process,su=s.Promise,lu=iu(s,"queueMicrotask"),pu=lu&&lu.value;pu||(Yi=function(){var t,e;for(Li&&(t=fu.domain)&&t.exit();Xi;){e=Xi.fn,Xi=Xi.next;try{e()}catch(t){throw Xi?Ji():$i=void 0,t}}$i=void 0,t&&t.enter()},ki||Li||ou||!au||!cu?!ru&&su&&su.resolve?((tu=su.resolve(void 0)).constructor=su,eu=xo(tu.then,tu),Ji=function(){eu(Yi)}):Li?Ji=function(){fu.nextTick(Yi)}:(uu=xo(uu,s),Ji=function(){uu(Yi)}):(Qi=!0,Zi=cu.createTextNode(""),new au(Yi).observe(Zi,{characterData:!0}),Ji=function(){Zi.data=Qi=!Qi}));var du,vu,hu,yu,gu=pu||function(t){var e={fn:t,next:void 0};$i&&($i.next=e),Xi||(Xi=e,Ji()),$i=e},mu=function(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=r})),this.resolve=J(e),this.reject=J(n)},bu={f:function(t){return new mu(t)}},wu=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},xu="object"==typeof window,Su=nu.set,Ou=gt("species"),Eu="Promise",ju=Zt.getterFor(Eu),Iu=Zt.set,Tu=Zt.getterFor(Eu),Au=Ri&&Ri.prototype,Pu=Ri,Ru=Au,Mu=s.TypeError,ku=s.document,Lu=s.process,Fu=bu.f,Cu=Fu,Nu=!!(ku&&ku.createEvent&&s.dispatchEvent),zu=L(s.PromiseRejectionEvent),_u="unhandledrejection",Du=!1,Uu=Me(Eu,(function(){var t=Dt(Pu),e=t!==String(Pu);if(!e&&66===W)return!0;if(W>=51&&/native code/.test(t))return!1;var n=new Pu((function(t){t(1)})),r=function(t){t((function(){}),(function(){}))};return(n.constructor={})[Ou]=r,!(Du=n.then((function(){}))instanceof r)||!e&&xu&&!zu})),Gu=Uu||!_o((function(t){Pu.all(t).catch((function(){}))})),Bu=function(t){var e;return!(!F(t)||!L(e=t.then))&&e},Wu=function(t,e){if(!t.notified){t.notified=!0;var n=t.reactions;gu((function(){for(var r=t.value,o=1==t.state,i=0;n.length>i;){var u,a,c,f=n[i++],s=o?f.ok:f.fail,l=f.resolve,p=f.reject,d=f.domain;try{s?(o||(2===t.rejection&&Ku(t),t.rejection=1),!0===s?u=r:(d&&d.enter(),u=s(r),d&&(d.exit(),c=!0)),u===f.promise?p(Mu("Promise-chain cycle")):(a=Bu(u))?v(a,u,l,p):l(u)):p(r)}catch(t){d&&!c&&d.exit(),p(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&Hu(t)}))}},Vu=function(t,e,n){var r,o;Nu?((r=ku.createEvent("Event")).promise=e,r.reason=n,r.initEvent(t,!1,!0),s.dispatchEvent(r)):r={promise:e,reason:n},!zu&&(o=s["on"+t])?o(r):t===_u&&function(t,e){var n=s.console;n&&n.error&&(1==arguments.length?n.error(t):n.error(t,e))}("Unhandled promise rejection",n)},Hu=function(t){v(Su,s,(function(){var e,n=t.facade,r=t.value;if(qu(t)&&(e=wu((function(){Li?Lu.emit("unhandledRejection",r,n):Vu(_u,n,r)})),t.rejection=Li||qu(t)?2:1,e.error))throw e.value}))},qu=function(t){return 1!==t.rejection&&!t.parent},Ku=function(t){v(Su,s,(function(){var e=t.facade;Li?Lu.emit("rejectionHandled",e):Vu("rejectionhandled",e,t.value)}))},Yu=function(t,e,n){return function(r){t(e,r,n)}},Xu=function(t,e,n){t.done||(t.done=!0,n&&(t=n),t.value=e,t.state=2,Wu(t,!0))},$u=function(t,e,n){if(!t.done){t.done=!0,n&&(t=n);try{if(t.facade===e)throw Mu("Promise can't be resolved itself");var r=Bu(e);r?gu((function(){var n={done:!1};try{v(r,e,Yu($u,n,t),Yu(Xu,n,t))}catch(e){Xu(n,e,t)}})):(t.value=e,t.state=1,Wu(t,!1))}catch(e){Xu({done:!1},e,t)}}};if(Uu&&(Ru=(Pu=function(t){Lo(this,Ru),J(t),v(du,this);var e=ju(this);try{t(Yu($u,e),Yu(Xu,e))}catch(t){Xu(e,t)}}).prototype,(du=function(t){Iu(this,{type:Eu,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=so(Ru,{then:function(t,e){var n=Tu(this),r=n.reactions,o=Fu($n(this,Pu));return o.ok=!L(t)||t,o.fail=L(e)&&e,o.domain=Li?Lu.domain:void 0,n.parent=!0,r[r.length]=o,0!=n.state&&Wu(n,!1),o.promise},catch:function(t){return this.then(void 0,t)}}),vu=function(){var t=new du,e=ju(t);this.promise=t,this.resolve=Yu($u,e),this.reject=Yu(Xu,e)},bu.f=Fu=function(t){return t===Pu||t===hu?new vu(t):Cu(t)},L(Ri)&&Au!==Object.prototype)){yu=Au.then,Du||(oe(Au,"then",(function(t,e){var n=this;return new Pu((function(t,e){v(yu,n,t,e)})).then(t,e)}),{unsafe:!0}),oe(Au,"catch",Ru.catch,{unsafe:!0}));try{delete Au.constructor}catch(t){}Hr&&Hr(Au,Ru)}Le({global:!0,wrap:!0,forced:Uu},{Promise:Pu}),Ur(Pu,Eu,!1),function(t){var e=N(t),n=Lt.f;p&&e&&!e[Mi]&&n(e,Mi,{configurable:!0,get:function(){return this}})}(Eu),hu=N(Eu),Le({target:Eu,stat:!0,forced:Uu},{reject:function(t){var e=Fu(this);return v(e.reject,void 0,t),e.promise}}),Le({target:Eu,stat:!0,forced:Uu},{resolve:function(t){return function(t,e){if(Rt(t),F(e)&&e.constructor===t)return e;var n=bu.f(t);return(0,n.resolve)(e),n.promise}(this,t)}}),Le({target:Eu,stat:!0,forced:Gu},{all:function(t){var e=this,n=Fu(e),r=n.resolve,o=n.reject,i=wu((function(){var n=J(e.resolve),i=[],u=0,a=1;Mo(t,(function(t){var c=u++,f=!1;a++,v(n,e,t).then((function(t){f||(f=!0,i[c]=t,--a||r(i))}),o)})),--a||r(i)}));return i.error&&o(i.value),n.promise},race:function(t){var e=this,n=Fu(e),r=n.reject,o=wu((function(){var o=J(e.resolve);Mo(t,(function(t){v(o,e,t).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}});var Ju=re.PROPER,Qu="toString",Zu=RegExp.prototype,ta=Zu.toString,ea=O(cn),na=l((function(){return"/a/b"!=ta.call({source:"a",flags:"b"})})),ra=Ju&&ta.name!=Qu;(na||ra)&&oe(RegExp.prototype,Qu,(function(){var t=Rt(this),e=an(t.source),n=t.flags;return"/"+e+"/"+an(void 0===n&&z(Zu,t)&&!("flags"in Zu)?ea(t):n)}),{unsafe:!0});var oa=re.EXISTS,ia=Lt.f,ua=Function.prototype,aa=O(ua.toString),ca=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,fa=O(ca.exec);p&&!oa&&ia(ua,"name",{configurable:!0,get:function(){try{return fa(ca,aa(this))[1]}catch(t){return""}}});var sa=new WeakMap;function la(t){return t.getMenuConfig("uploadImage")}function pa(t,n){return mr(this,void 0,void 0,(function(){return br(this,(function(r){return[2,new Promise((function(r){var o=new FileReader;o.readAsDataURL(n),o.onload=function(){var i=o.result;if(i){var u=i.toString(),a=0===u.indexOf("data:image")?"":u;e.insertImageNode(t,u,n.name,a),r("ok")}}}))]}))}))}function da(n,r){return mr(this,void 0,void 0,(function(){var o,i,u,a;return br(this,(function(c){switch(c.label){case 0:return o=function(n){var r=sa.get(n);if(null!=r)return r;var o=la(n),i=o.onSuccess,u=o.onProgress,a=o.onFailed,c=o.customInsert,f=o.onError;return r=t.createUploader(gr(gr({},o),{onProgress:function(t){n.showProgressBar(t),u&&u(t)},onSuccess:function(t,r){if(c)return c(r,(function(t,r,o){return e.insertImageNode(n,t,r,o)})),void i(t,r);var o=r.errno,u=void 0===o?1:o,f=r.data,s=void 0===f?{}:f;if(0===u){if(Array.isArray(s))s.forEach((function(t){var r=t.url,o=void 0===r?"":r,i=t.alt,u=void 0===i?"":i,a=t.href,c=void 0===a?"":a;e.insertImageNode(n,o,u,c)}));else{var l=s.url,p=void 0===l?"":l,d=s.alt,v=void 0===d?"":d,h=s.href,y=void 0===h?"":h;e.insertImageNode(n,p,v,y)}i(t,r)}else a(t,r)},onError:function(t,e,n){f(t,e,n)}})),sa.set(n,r),r}(n),i=r.name,u=r.type,a=r.size,o.addFile({name:i,type:u,size:a,data:r}),[4,o.upload()];case 1:return c.sent(),[2]}}))}))}function va(t,n){var r,o;return mr(this,void 0,void 0,(function(){var i,u,a,c,f,s,l,p,d;return br(this,(function(v){switch(v.label){case 0:if(null==n)return[2];i=Array.prototype.slice.call(n),u=la(t),a=u.customUpload,c=u.base64LimitSize,v.label=1;case 1:v.trys.push([1,11,12,17]),f=xr(i),v.label=2;case 2:return[4,f.next()];case 3:return(s=v.sent()).done?[3,10]:(l=s.value,p=l.size,c&&p<=c?[4,pa(t,l)]:[3,5]);case 4:return v.sent(),[3,9];case 5:return a?[4,a(l,(function(n,r,o){return e.insertImageNode(t,n,r,o)}))]:[3,7];case 6:return v.sent(),[3,9];case 7:return[4,da(t,l)];case 8:v.sent(),v.label=9;case 9:return[3,2];case 10:return[3,17];case 11:return d=v.sent(),r={error:d},[3,17];case 12:return v.trys.push([12,,15,16]),s&&!s.done&&(o=f.return)?[4,o.call(f)]:[3,14];case 13:v.sent(),v.label=14;case 14:return[3,16];case 15:if(r)throw r.error;return[7];case 16:return[7];case 17:return[2]}}))}))}var ha=O([].join),ya=P!=Object,ga=xi("join",",");Le({target:"Array",proto:!0,forced:ya||!ga},{join:function(t){return ha(k(this),void 0===t?",":t)}});n.append&&(o.default.fn.append=n.append),n.on&&(o.default.fn.on=n.on),n.remove&&(o.default.fn.remove=n.remove),n.val&&(o.default.fn.val=n.val),n.click&&(o.default.fn.click=n.click),n.hide&&(o.default.fn.hide=n.hide);var ma=function(){function n(){this.title=t.t("uploadImgModule.uploadImage"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M828.708571 585.045333a48.761905 48.761905 0 0 0-48.737523 48.761905v18.529524l-72.143238-72.167619a135.972571 135.972571 0 0 0-191.585524 0l-34.133334 34.133333-120.880762-120.953905a138.898286 138.898286 0 0 0-191.585523 0l-72.167619 72.167619V292.400762a48.786286 48.786286 0 0 1 48.761904-48.761905h341.23581a48.737524 48.737524 0 0 0 34.474667-83.285333 48.737524 48.737524 0 0 0-34.474667-14.287238H146.236952A146.212571 146.212571 0 0 0 0 292.400762v585.289143A146.358857 146.358857 0 0 0 146.236952 1024h584.996572a146.212571 146.212571 0 0 0 146.236952-146.310095V633.807238a48.786286 48.786286 0 0 0-48.761905-48.761905zM146.261333 926.45181a48.737524 48.737524 0 0 1-48.761904-48.761905v-174.128762l141.409523-141.458286a38.497524 38.497524 0 0 1 53.126096 0l154.526476 154.624 209.627428 209.724953H146.236952z m633.734096-48.761905c-0.073143 9.337905-3.145143 18.383238-8.777143 25.843809l-219.843048-220.94019 34.133333-34.133334a37.546667 37.546667 0 0 1 53.613715 0l140.873143 141.897143V877.714286zM1009.615238 160.231619L863.329524 13.897143a48.737524 48.737524 0 0 0-16.091429-10.24c-11.849143-4.87619-25.161143-4.87619-37.059047 0a48.761905 48.761905 0 0 0-16.067048 10.24l-146.236952 146.334476a49.005714 49.005714 0 0 0 69.217523 69.241905l62.902858-63.390476v272.627809a48.761905 48.761905 0 1 0 97.475047 0V166.083048l62.902857 63.390476a48.737524 48.737524 0 0 0 69.217524 0 48.761905 48.761905 0 0 0 0-69.241905z"></path></svg>',this.tag="button"}return n.prototype.getValue=function(t){return""},n.prototype.isActive=function(t){return!1},n.prototype.isDisabled=function(t){return e.isInsertImageMenuDisabled(t)},n.prototype.getMenuConfig=function(t){return t.getMenuConfig("uploadImage")},n.prototype.exec=function(t,n){var r=this.getMenuConfig(t),i=r.allowedFileTypes,u=void 0===i?[]:i,a=r.customBrowseAndUpload;if(a)a((function(n,r,o){return e.insertImageNode(t,n,r,o)}));else{var c="";u.length>0&&(c='accept="'+u.join(", ")+'"');var f=o.default("body"),s=o.default('<input type="file" '+c+" multiple/>");s.hide(),f.append(s),s.click(),s.on("change",(function(){var e=s[0].files;va(t,e)}))}},n}();var ba={menus:[{key:"uploadImage",factory:function(){return new ma},config:{server:"",fieldName:"wangeditor-uploaded-image",maxFileSize:2097152,maxNumberOfFiles:100,allowedFileTypes:["image/*"],meta:{},metaWithUrl:!1,withCredentials:!1,timeout:1e4,onBeforeUpload:function(t){return t},onProgress:function(t){},onSuccess:function(t,e){},onFailed:function(t,e){console.error("'"+t.name+"' upload failed",e)},onError:function(t,e,n){console.error("'"+t.name+"' upload error",n)},base64LimitSize:0}}],editorPlugin:function(t){var n=t.insertData,r=t;return r.insertData=function(o){if(e.isInsertImageMenuDisabled(r))n(o);else if(o.getData("text/plain"))n(o);else{var i=o.files;if(i.length<=0)n(o);else Array.prototype.slice.call(i).some((function(t){return"image"===function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}(t.type.split("/"),1)[0]}))?va(t,i):n(o)}},r}};return ba}));
//# sourceMappingURL=index.js.map
