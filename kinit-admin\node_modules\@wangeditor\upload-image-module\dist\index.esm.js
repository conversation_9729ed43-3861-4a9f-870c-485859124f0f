import{i18nAddResources as t,createUploader as e,t as n}from"@wangeditor/core";import{insertImageNode as r,isInsertImageMenuDisabled as o}from"@wangeditor/basic-modules";import i,{append as u,on as a,remove as c,val as f,click as s,hide as l}from"dom7";t("en",{uploadImgModule:{uploadImage:"Upload Image",uploadError:"{{fileName}} upload error"}}),t("zh-CN",{uploadImgModule:{uploadImage:"上传图片",uploadError:"{{fileName}} 上传出错"}});var p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function v(t){var e={exports:{}};return t(e,e.exports),e.exports}var d,h,y=function(t){return t&&t.Math==Math&&t},g=y("object"==typeof globalThis&&globalThis)||y("object"==typeof window&&window)||y("object"==typeof self&&self)||y("object"==typeof p&&p)||function(){return this}()||Function("return this")(),m=function(t){try{return!!t()}catch(t){return!0}},b=!m((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),w=Function.prototype.call,S=w.bind?w.bind(w):function(){return w.apply(w,arguments)},x={}.propertyIsEnumerable,O=Object.getOwnPropertyDescriptor,E={f:O&&!x.call({1:2},1)?function(t){var e=O(this,t);return!!e&&e.enumerable}:x},j=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},I=Function.prototype,T=I.bind,A=I.call,P=T&&T.bind(A),R=T?function(t){return t&&P(A,t)}:function(t){return t&&function(){return A.apply(t,arguments)}},k=R({}.toString),M=R("".slice),L=function(t){return M(k(t),8,-1)},F=g.Object,_=R("".split),C=m((function(){return!F("z").propertyIsEnumerable(0)}))?function(t){return"String"==L(t)?_(t,""):F(t)}:F,z=g.TypeError,D=function(t){if(null==t)throw z("Can't call method on "+t);return t},N=function(t){return C(D(t))},G=function(t){return"function"==typeof t},U=function(t){return"object"==typeof t?null!==t:G(t)},B=function(t){return G(t)?t:void 0},V=function(t,e){return arguments.length<2?B(g[t]):g[t]&&g[t][e]},W=R({}.isPrototypeOf),H=V("navigator","userAgent")||"",K=g.process,Y=g.Deno,X=K&&K.versions||Y&&Y.version,q=X&&X.v8;q&&(h=(d=q.split("."))[0]>0&&d[0]<4?1:+(d[0]+d[1])),!h&&H&&(!(d=H.match(/Edge\/(\d+)/))||d[1]>=74)&&(d=H.match(/Chrome\/(\d+)/))&&(h=+d[1]);var $=h,J=!!Object.getOwnPropertySymbols&&!m((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&$&&$<41})),Q=J&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=g.Object,tt=Q?function(t){return"symbol"==typeof t}:function(t){var e=V("Symbol");return G(e)&&W(e.prototype,Z(t))},et=g.String,nt=function(t){try{return et(t)}catch(t){return"Object"}},rt=g.TypeError,ot=function(t){if(G(t))return t;throw rt(nt(t)+" is not a function")},it=function(t,e){var n=t[e];return null==n?void 0:ot(n)},ut=g.TypeError,at=Object.defineProperty,ct=function(t,e){try{at(g,t,{value:e,configurable:!0,writable:!0})}catch(n){g[t]=e}return e},ft=g["__core-js_shared__"]||ct("__core-js_shared__",{}),st=v((function(t){(t.exports=function(t,e){return ft[t]||(ft[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),lt=g.Object,pt=function(t){return lt(D(t))},vt=R({}.hasOwnProperty),dt=Object.hasOwn||function(t,e){return vt(pt(t),e)},ht=0,yt=Math.random(),gt=R(1..toString),mt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+gt(++ht+yt,36)},bt=st("wks"),wt=g.Symbol,St=wt&&wt.for,xt=Q?wt:wt&&wt.withoutSetter||mt,Ot=function(t){if(!dt(bt,t)||!J&&"string"!=typeof bt[t]){var e="Symbol."+t;J&&dt(wt,t)?bt[t]=wt[t]:bt[t]=Q&&St?St(e):xt(e)}return bt[t]},Et=g.TypeError,jt=Ot("toPrimitive"),It=function(t,e){if(!U(t)||tt(t))return t;var n,r=it(t,jt);if(r){if(void 0===e&&(e="default"),n=S(r,t,e),!U(n)||tt(n))return n;throw Et("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&G(n=t.toString)&&!U(r=S(n,t)))return r;if(G(n=t.valueOf)&&!U(r=S(n,t)))return r;if("string"!==e&&G(n=t.toString)&&!U(r=S(n,t)))return r;throw ut("Can't convert object to primitive value")}(t,e)},Tt=function(t){var e=It(t,"string");return tt(e)?e:e+""},At=g.document,Pt=U(At)&&U(At.createElement),Rt=function(t){return Pt?At.createElement(t):{}},kt=!b&&!m((function(){return 7!=Object.defineProperty(Rt("div"),"a",{get:function(){return 7}}).a})),Mt=Object.getOwnPropertyDescriptor,Lt={f:b?Mt:function(t,e){if(t=N(t),e=Tt(e),kt)try{return Mt(t,e)}catch(t){}if(dt(t,e))return j(!S(E.f,t,e),t[e])}},Ft=g.String,_t=g.TypeError,Ct=function(t){if(U(t))return t;throw _t(Ft(t)+" is not an object")},zt=g.TypeError,Dt=Object.defineProperty,Nt={f:b?Dt:function(t,e,n){if(Ct(t),e=Tt(e),Ct(n),kt)try{return Dt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw zt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},Gt=b?function(t,e,n){return Nt.f(t,e,j(1,n))}:function(t,e,n){return t[e]=n,t},Ut=R(Function.toString);G(ft.inspectSource)||(ft.inspectSource=function(t){return Ut(t)});var Bt,Vt,Wt,Ht=ft.inspectSource,Kt=g.WeakMap,Yt=G(Kt)&&/native code/.test(Ht(Kt)),Xt=st("keys"),qt=function(t){return Xt[t]||(Xt[t]=mt(t))},$t={},Jt=g.TypeError,Qt=g.WeakMap;if(Yt||ft.state){var Zt=ft.state||(ft.state=new Qt),te=R(Zt.get),ee=R(Zt.has),ne=R(Zt.set);Bt=function(t,e){if(ee(Zt,t))throw new Jt("Object already initialized");return e.facade=t,ne(Zt,t,e),e},Vt=function(t){return te(Zt,t)||{}},Wt=function(t){return ee(Zt,t)}}else{var re=qt("state");$t[re]=!0,Bt=function(t,e){if(dt(t,re))throw new Jt("Object already initialized");return e.facade=t,Gt(t,re,e),e},Vt=function(t){return dt(t,re)?t[re]:{}},Wt=function(t){return dt(t,re)}}var oe={set:Bt,get:Vt,has:Wt,enforce:function(t){return Wt(t)?Vt(t):Bt(t,{})},getterFor:function(t){return function(e){var n;if(!U(e)||(n=Vt(e)).type!==t)throw Jt("Incompatible receiver, "+t+" required");return n}}},ie=Function.prototype,ue=b&&Object.getOwnPropertyDescriptor,ae=dt(ie,"name"),ce={EXISTS:ae,PROPER:ae&&"something"===function(){}.name,CONFIGURABLE:ae&&(!b||b&&ue(ie,"name").configurable)},fe=v((function(t){var e=ce.CONFIGURABLE,n=oe.get,r=oe.enforce,o=String(String).split("String");(t.exports=function(t,n,i,u){var a,c=!!u&&!!u.unsafe,f=!!u&&!!u.enumerable,s=!!u&&!!u.noTargetGet,l=u&&void 0!==u.name?u.name:n;G(i)&&("Symbol("===String(l).slice(0,7)&&(l="["+String(l).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!dt(i,"name")||e&&i.name!==l)&&Gt(i,"name",l),(a=r(i)).source||(a.source=o.join("string"==typeof l?l:""))),t!==g?(c?!s&&t[n]&&(f=!0):delete t[n],f?t[n]=i:Gt(t,n,i)):f?t[n]=i:ct(n,i)})(Function.prototype,"toString",(function(){return G(this)&&n(this).source||Ht(this)}))})),se=Math.ceil,le=Math.floor,pe=function(t){var e=+t;return e!=e||0===e?0:(e>0?le:se)(e)},ve=Math.max,de=Math.min,he=function(t,e){var n=pe(t);return n<0?ve(n+e,0):de(n,e)},ye=Math.min,ge=function(t){return t>0?ye(pe(t),9007199254740991):0},me=function(t){return ge(t.length)},be=function(t){return function(e,n,r){var o,i=N(e),u=me(i),a=he(r,u);if(t&&n!=n){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===n)return t||a||0;return!t&&-1}},we={includes:be(!0),indexOf:be(!1)}.indexOf,Se=R([].push),xe=function(t,e){var n,r=N(t),o=0,i=[];for(n in r)!dt($t,n)&&dt(r,n)&&Se(i,n);for(;e.length>o;)dt(r,n=e[o++])&&(~we(i,n)||Se(i,n));return i},Oe=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ee=Oe.concat("length","prototype"),je={f:Object.getOwnPropertyNames||function(t){return xe(t,Ee)}},Ie={f:Object.getOwnPropertySymbols},Te=R([].concat),Ae=V("Reflect","ownKeys")||function(t){var e=je.f(Ct(t)),n=Ie.f;return n?Te(e,n(t)):e},Pe=function(t,e){for(var n=Ae(e),r=Nt.f,o=Lt.f,i=0;i<n.length;i++){var u=n[i];dt(t,u)||r(t,u,o(e,u))}},Re=/#|\.prototype\./,ke=function(t,e){var n=Le[Me(t)];return n==_e||n!=Fe&&(G(e)?m(e):!!e)},Me=ke.normalize=function(t){return String(t).replace(Re,".").toLowerCase()},Le=ke.data={},Fe=ke.NATIVE="N",_e=ke.POLYFILL="P",Ce=ke,ze=Lt.f,De=function(t,e){var n,r,o,i,u,a=t.target,c=t.global,f=t.stat;if(n=c?g:f?g[a]||ct(a,{}):(g[a]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(u=ze(n,r))&&u.value:n[r],!Ce(c?r:a+(f?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Pe(i,o)}(t.sham||o&&o.sham)&&Gt(i,"sham",!0),fe(n,r,i,t)}},Ne=Array.isArray||function(t){return"Array"==L(t)},Ge={};Ge[Ot("toStringTag")]="z";var Ue,Be="[object z]"===String(Ge),Ve=Ot("toStringTag"),We=g.Object,He="Arguments"==L(function(){return arguments}()),Ke=Be?L:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=We(t),Ve))?n:He?L(e):"Object"==(r=L(e))&&G(e.callee)?"Arguments":r},Ye=function(){},Xe=[],qe=V("Reflect","construct"),$e=/^\s*(?:class|function)\b/,Je=R($e.exec),Qe=!$e.exec(Ye),Ze=function(t){if(!G(t))return!1;try{return qe(Ye,Xe,t),!0}catch(t){return!1}},tn=!qe||m((function(){var t;return Ze(Ze.call)||!Ze(Object)||!Ze((function(){t=!0}))||t}))?function(t){if(!G(t))return!1;switch(Ke(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Qe||!!Je($e,Ht(t))}:Ze,en=function(t,e,n){var r=Tt(e);r in t?Nt.f(t,r,j(0,n)):t[r]=n},nn=Ot("species"),rn=R([].slice),on=(Ue="slice",$>=51||!m((function(){var t=[];return(t.constructor={})[nn]=function(){return{foo:1}},1!==t[Ue](Boolean).foo}))),un=Ot("species"),an=g.Array,cn=Math.max;De({target:"Array",proto:!0,forced:!on},{slice:function(t,e){var n,r,o,i=N(this),u=me(i),a=he(t,u),c=he(void 0===e?u:e,u);if(Ne(i)&&(n=i.constructor,(tn(n)&&(n===an||Ne(n.prototype))||U(n)&&null===(n=n[un]))&&(n=void 0),n===an||void 0===n))return rn(i,a,c);for(r=new(void 0===n?an:n)(cn(c-a,0)),o=0;a<c;a++,o++)a in i&&en(r,o,i[a]);return r.length=o,r}});var fn=Be?{}.toString:function(){return"[object "+Ke(this)+"]"};Be||fe(Object.prototype,"toString",fn,{unsafe:!0});var sn,ln=g.String,pn=function(t){if("Symbol"===Ke(t))throw TypeError("Cannot convert a Symbol value to a string");return ln(t)},vn=function(){var t=Ct(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},dn=g.RegExp,hn=m((function(){var t=dn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),yn=hn||m((function(){return!dn("a","y").sticky})),gn={BROKEN_CARET:hn||m((function(){var t=dn("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:yn,UNSUPPORTED_Y:hn},mn=Object.keys||function(t){return xe(t,Oe)},bn=b?Object.defineProperties:function(t,e){Ct(t);for(var n,r=N(e),o=mn(e),i=o.length,u=0;i>u;)Nt.f(t,n=o[u++],r[n]);return t},wn=V("document","documentElement"),Sn=qt("IE_PROTO"),xn=function(){},On=function(t){return"<script>"+t+"<\/script>"},En=function(t){t.write(On("")),t.close();var e=t.parentWindow.Object;return t=null,e},jn=function(){try{sn=new ActiveXObject("htmlfile")}catch(t){}var t,e;jn="undefined"!=typeof document?document.domain&&sn?En(sn):((e=Rt("iframe")).style.display="none",wn.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(On("document.F=Object")),t.close(),t.F):En(sn);for(var n=Oe.length;n--;)delete jn.prototype[Oe[n]];return jn()};$t[Sn]=!0;var In,Tn,An=Object.create||function(t,e){var n;return null!==t?(xn.prototype=Ct(t),n=new xn,xn.prototype=null,n[Sn]=t):n=jn(),void 0===e?n:bn(n,e)},Pn=g.RegExp,Rn=m((function(){var t=Pn(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),kn=g.RegExp,Mn=m((function(){var t=kn("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Ln=oe.get,Fn=st("native-string-replace",String.prototype.replace),_n=RegExp.prototype.exec,Cn=_n,zn=R("".charAt),Dn=R("".indexOf),Nn=R("".replace),Gn=R("".slice),Un=(Tn=/b*/g,S(_n,In=/a/,"a"),S(_n,Tn,"a"),0!==In.lastIndex||0!==Tn.lastIndex),Bn=gn.BROKEN_CARET,Vn=void 0!==/()??/.exec("")[1];(Un||Vn||Bn||Rn||Mn)&&(Cn=function(t){var e,n,r,o,i,u,a,c=this,f=Ln(c),s=pn(t),l=f.raw;if(l)return l.lastIndex=c.lastIndex,e=S(Cn,l,s),c.lastIndex=l.lastIndex,e;var p=f.groups,v=Bn&&c.sticky,d=S(vn,c),h=c.source,y=0,g=s;if(v&&(d=Nn(d,"y",""),-1===Dn(d,"g")&&(d+="g"),g=Gn(s,c.lastIndex),c.lastIndex>0&&(!c.multiline||c.multiline&&"\n"!==zn(s,c.lastIndex-1))&&(h="(?: "+h+")",g=" "+g,y++),n=new RegExp("^(?:"+h+")",d)),Vn&&(n=new RegExp("^"+h+"$(?!\\s)",d)),Un&&(r=c.lastIndex),o=S(_n,v?n:c,g),v?o?(o.input=Gn(o.input,y),o[0]=Gn(o[0],y),o.index=c.lastIndex,c.lastIndex+=o[0].length):c.lastIndex=0:Un&&o&&(c.lastIndex=c.global?o.index+o[0].length:r),Vn&&o&&o.length>1&&S(Fn,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&p)for(o.groups=u=An(null),i=0;i<p.length;i++)u[(a=p[i])[0]]=o[a[1]];return o});var Wn=Cn;De({target:"RegExp",proto:!0,forced:/./.exec!==Wn},{exec:Wn});var Hn=Function.prototype,Kn=Hn.apply,Yn=Hn.bind,Xn=Hn.call,qn="object"==typeof Reflect&&Reflect.apply||(Yn?Xn.bind(Kn):function(){return Xn.apply(Kn,arguments)}),$n=Ot("species"),Jn=RegExp.prototype,Qn=Ot("match"),Zn=g.TypeError,tr=Ot("species"),er=function(t,e){var n,r=Ct(t).constructor;return void 0===r||null==(n=Ct(r)[tr])?e:function(t){if(tn(t))return t;throw Zn(nt(t)+" is not a constructor")}(n)},nr=R("".charAt),rr=R("".charCodeAt),or=R("".slice),ir=function(t){return function(e,n){var r,o,i=pn(D(e)),u=pe(n),a=i.length;return u<0||u>=a?t?"":void 0:(r=rr(i,u))<55296||r>56319||u+1===a||(o=rr(i,u+1))<56320||o>57343?t?nr(i,u):r:t?or(i,u,u+2):o-56320+(r-55296<<10)+65536}},ur={codeAt:ir(!1),charAt:ir(!0)},ar=ur.charAt,cr=function(t,e,n){return e+(n?ar(t,e).length:1)},fr=g.Array,sr=Math.max,lr=function(t,e,n){for(var r=me(t),o=he(e,r),i=he(void 0===n?r:n,r),u=fr(sr(i-o,0)),a=0;o<i;o++,a++)en(u,a,t[o]);return u.length=a,u},pr=g.TypeError,vr=function(t,e){var n=t.exec;if(G(n)){var r=S(n,t,e);return null!==r&&Ct(r),r}if("RegExp"===L(t))return S(Wn,t,e);throw pr("RegExp#exec called on incompatible receiver")},dr=gn.UNSUPPORTED_Y,hr=Math.min,yr=[].push,gr=R(/./.exec),mr=R(yr),br=R("".slice);!function(t,e,n,r){var o=Ot(t),i=!m((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),u=i&&!m((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[$n]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!u||n){var a=R(/./[o]),c=e(o,""[t],(function(t,e,n,r,o){var u=R(t),c=e.exec;return c===Wn||c===Jn.exec?i&&!o?{done:!0,value:a(e,n,r)}:{done:!0,value:u(n,e,r)}:{done:!1}}));fe(String.prototype,t,c[0]),fe(Jn,o,c[1])}r&&Gt(Jn[o],"sham",!0)}("split",(function(t,e,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var r,o,i=pn(D(this)),u=void 0===n?4294967295:n>>>0;if(0===u)return[];if(void 0===t)return[i];if(!U(r=t)||!(void 0!==(o=r[Qn])?o:"RegExp"==L(r)))return S(e,i,t,u);for(var a,c,f,s=[],l=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,v=new RegExp(t.source,l+"g");(a=S(Wn,v,i))&&!((c=v.lastIndex)>p&&(mr(s,br(i,p,a.index)),a.length>1&&a.index<i.length&&qn(yr,s,lr(a,1)),f=a[0].length,p=c,s.length>=u));)v.lastIndex===a.index&&v.lastIndex++;return p===i.length?!f&&gr(v,"")||mr(s,""):mr(s,br(i,p)),s.length>u?lr(s,0,u):s}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:S(e,this,t,n)}:e,[function(e,n){var o=D(this),i=null==e?void 0:it(e,t);return i?S(i,e,o,n):S(r,pn(o),e,n)},function(t,o){var i=Ct(this),u=pn(t),a=n(r,i,u,o,r!==e);if(a.done)return a.value;var c=er(i,RegExp),f=i.unicode,s=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(dr?"g":"y"),l=new c(dr?"^(?:"+i.source+")":i,s),p=void 0===o?4294967295:o>>>0;if(0===p)return[];if(0===u.length)return null===vr(l,u)?[u]:[];for(var v=0,d=0,h=[];d<u.length;){l.lastIndex=dr?0:d;var y,g=vr(l,dr?br(u,d):u);if(null===g||(y=hr(ge(l.lastIndex+(dr?d:0)),u.length))===v)d=cr(u,d,f);else{if(mr(h,br(u,v,d)),h.length===p)return h;for(var m=1;m<=g.length-1;m++)if(mr(h,g[m]),h.length===p)return h;d=v=y}}return mr(h,br(u,v)),h}]}),!!m((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),dr);
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var wr=function(){return wr=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},wr.apply(this,arguments)};function Sr(t,e,n,r){return new(n||(n=Promise))((function(o,i){function u(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(u,a)}c((r=r.apply(t,e||[])).next())}))}function xr(t,e){var n,r,o,i,u={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;u;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,r=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(o=u.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){u=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){u.label=i[1];break}if(6===i[0]&&u.label<o[1]){u.label=o[1],o=i;break}if(o&&u.label<o[2]){u.label=o[2],u.ops.push(i);break}o[2]&&u.ops.pop(),u.trys.pop();continue}i=e.call(t,u)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function Or(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function Er(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=Or(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,o){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,o,(e=t[n](e)).done,e.value)}))}}}var jr=Ot("unscopables"),Ir=Array.prototype;null==Ir[jr]&&Nt.f(Ir,jr,{configurable:!0,value:An(null)});var Tr,Ar,Pr,Rr=function(t){Ir[jr][t]=!0},kr={},Mr=!m((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Lr=qt("IE_PROTO"),Fr=g.Object,_r=Fr.prototype,Cr=Mr?Fr.getPrototypeOf:function(t){var e=pt(t);if(dt(e,Lr))return e[Lr];var n=e.constructor;return G(n)&&e instanceof n?n.prototype:e instanceof Fr?_r:null},zr=Ot("iterator"),Dr=!1;[].keys&&("next"in(Pr=[].keys())?(Ar=Cr(Cr(Pr)))!==Object.prototype&&(Tr=Ar):Dr=!0);var Nr=null==Tr||m((function(){var t={};return Tr[zr].call(t)!==t}));Nr&&(Tr={}),G(Tr[zr])||fe(Tr,zr,(function(){return this}));var Gr={IteratorPrototype:Tr,BUGGY_SAFARI_ITERATORS:Dr},Ur=Nt.f,Br=Ot("toStringTag"),Vr=function(t,e,n){t&&!dt(t=n?t:t.prototype,Br)&&Ur(t,Br,{configurable:!0,value:e})},Wr=Gr.IteratorPrototype,Hr=function(){return this},Kr=g.String,Yr=g.TypeError,Xr=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=R(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),e=n instanceof Array}catch(t){}return function(n,r){return Ct(n),function(t){if("object"==typeof t||G(t))return t;throw Yr("Can't set "+Kr(t)+" as a prototype")}(r),e?t(n,r):n.__proto__=r,n}}():void 0),qr=ce.PROPER,$r=ce.CONFIGURABLE,Jr=Gr.IteratorPrototype,Qr=Gr.BUGGY_SAFARI_ITERATORS,Zr=Ot("iterator"),to=function(){return this},eo=function(t,e,n,r,o,i,u){!function(t,e,n,r){var o=e+" Iterator";t.prototype=An(Wr,{next:j(+!r,n)}),Vr(t,o,!1),kr[o]=Hr}(n,e,r);var a,c,f,s=function(t){if(t===o&&h)return h;if(!Qr&&t in v)return v[t];switch(t){case"keys":case"values":case"entries":return function(){return new n(this,t)}}return function(){return new n(this)}},l=e+" Iterator",p=!1,v=t.prototype,d=v[Zr]||v["@@iterator"]||o&&v[o],h=!Qr&&d||s(o),y="Array"==e&&v.entries||d;if(y&&(a=Cr(y.call(new t)))!==Object.prototype&&a.next&&(Cr(a)!==Jr&&(Xr?Xr(a,Jr):G(a[Zr])||fe(a,Zr,to)),Vr(a,l,!0)),qr&&"values"==o&&d&&"values"!==d.name&&($r?Gt(v,"name","values"):(p=!0,h=function(){return S(d,this)})),o)if(c={values:s("values"),keys:i?h:s("keys"),entries:s("entries")},u)for(f in c)(Qr||p||!(f in v))&&fe(v,f,c[f]);else De({target:e,proto:!0,forced:Qr||p},c);return v[Zr]!==h&&fe(v,Zr,h,{name:o}),kr[e]=h,c},no=oe.set,ro=oe.getterFor("Array Iterator"),oo=eo(Array,"Array",(function(t,e){no(this,{type:"Array Iterator",target:N(t),index:0,kind:e})}),(function(){var t=ro(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values");kr.Arguments=kr.Array,Rr("keys"),Rr("values"),Rr("entries");var io=ur.charAt,uo=oe.set,ao=oe.getterFor("String Iterator");eo(String,"String",(function(t){uo(this,{type:"String Iterator",string:pn(t),index:0})}),(function(){var t,e=ao(this),n=e.string,r=e.index;return r>=n.length?{value:void 0,done:!0}:(t=io(n,r),e.index+=t.length,{value:t,done:!1})}));var co=function(t,e,n){for(var r in e)fe(t,r,e[r],n);return t},fo=je.f,so="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],lo={f:function(t){return so&&"Window"==L(t)?function(t){try{return fo(t)}catch(t){return lr(so)}}(t):fo(N(t))}},po=m((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),vo=Object.isExtensible,ho=m((function(){vo(1)}))||po?function(t){return!!U(t)&&((!po||"ArrayBuffer"!=L(t))&&(!vo||vo(t)))}:vo,yo=!m((function(){return Object.isExtensible(Object.preventExtensions({}))})),go=v((function(t){var e=Nt.f,n=!1,r=mt("meta"),o=0,i=function(t){e(t,r,{value:{objectID:"O"+o++,weakData:{}}})},u=t.exports={enable:function(){u.enable=function(){},n=!0;var t=je.f,e=R([].splice),o={};o[r]=1,t(o).length&&(je.f=function(n){for(var o=t(n),i=0,u=o.length;i<u;i++)if(o[i]===r){e(o,i,1);break}return o},De({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:lo.f}))},fastKey:function(t,e){if(!U(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!dt(t,r)){if(!ho(t))return"F";if(!e)return"E";i(t)}return t[r].objectID},getWeakData:function(t,e){if(!dt(t,r)){if(!ho(t))return!0;if(!e)return!1;i(t)}return t[r].weakData},onFreeze:function(t){return yo&&n&&ho(t)&&!dt(t,r)&&i(t),t}};$t[r]=!0})),mo=R(R.bind),bo=function(t,e){return ot(t),void 0===e?t:mo?mo(t,e):function(){return t.apply(e,arguments)}},wo=Ot("iterator"),So=Array.prototype,xo=Ot("iterator"),Oo=function(t){if(null!=t)return it(t,xo)||it(t,"@@iterator")||kr[Ke(t)]},Eo=g.TypeError,jo=function(t,e,n){var r,o;Ct(t);try{if(!(r=it(t,"return"))){if("throw"===e)throw n;return n}r=S(r,t)}catch(t){o=!0,r=t}if("throw"===e)throw n;if(o)throw r;return Ct(r),n},Io=g.TypeError,To=function(t,e){this.stopped=t,this.result=e},Ao=To.prototype,Po=function(t,e,n){var r,o,i,u,a,c,f,s,l=n&&n.that,p=!(!n||!n.AS_ENTRIES),v=!(!n||!n.IS_ITERATOR),d=!(!n||!n.INTERRUPTED),h=bo(e,l),y=function(t){return r&&jo(r,"normal",t),new To(!0,t)},g=function(t){return p?(Ct(t),d?h(t[0],t[1],y):h(t[0],t[1])):d?h(t,y):h(t)};if(v)r=t;else{if(!(o=Oo(t)))throw Io(nt(t)+" is not iterable");if(void 0!==(s=o)&&(kr.Array===s||So[wo]===s)){for(i=0,u=me(t);u>i;i++)if((a=g(t[i]))&&W(Ao,a))return a;return new To(!1)}r=function(t,e){var n=arguments.length<2?Oo(t):e;if(ot(n))return Ct(S(n,t));throw Eo(nt(t)+" is not iterable")}(t,o)}for(c=r.next;!(f=S(c,r)).done;){try{a=g(f.value)}catch(t){jo(r,"throw",t)}if("object"==typeof a&&a&&W(Ao,a))return a}return new To(!1)},Ro=g.TypeError,ko=function(t,e){if(W(e,t))return t;throw Ro("Incorrect invocation")},Mo=Ot("iterator"),Lo=!1;try{var Fo=0,_o={next:function(){return{done:!!Fo++}},return:function(){Lo=!0}};_o[Mo]=function(){return this},Array.from(_o,(function(){throw 2}))}catch(t){}var Co=function(t,e){if(!e&&!Lo)return!1;var n=!1;try{var r={};r[Mo]=function(){return{next:function(){return{done:n=!0}}}},t(r)}catch(t){}return n},zo=Ot("species"),Do=g.Array,No=function(t,e){return new(function(t){var e;return Ne(t)&&(e=t.constructor,(tn(e)&&(e===Do||Ne(e.prototype))||U(e)&&null===(e=e[zo]))&&(e=void 0)),void 0===e?Do:e}(t))(0===e?0:e)},Go=R([].push),Uo=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,u=7==t,a=5==t||i;return function(c,f,s,l){for(var p,v,d=pt(c),h=C(d),y=bo(f,s),g=me(h),m=0,b=l||No,w=e?b(c,g):n||u?b(c,0):void 0;g>m;m++)if((a||m in h)&&(v=y(p=h[m],m,d),t))if(e)w[m]=v;else if(v)switch(t){case 3:return!0;case 5:return p;case 6:return m;case 2:Go(w,p)}else switch(t){case 4:return!1;case 7:Go(w,p)}return i?-1:r||o?o:w}},Bo={forEach:Uo(0),map:Uo(1),filter:Uo(2),some:Uo(3),every:Uo(4),find:Uo(5),findIndex:Uo(6),filterReject:Uo(7)},Vo=go.getWeakData,Wo=oe.set,Ho=oe.getterFor,Ko=Bo.find,Yo=Bo.findIndex,Xo=R([].splice),qo=0,$o=function(t){return t.frozen||(t.frozen=new Jo)},Jo=function(){this.entries=[]},Qo=function(t,e){return Ko(t.entries,(function(t){return t[0]===e}))};Jo.prototype={get:function(t){var e=Qo(this,t);if(e)return e[1]},has:function(t){return!!Qo(this,t)},set:function(t,e){var n=Qo(this,t);n?n[1]=e:this.entries.push([t,e])},delete:function(t){var e=Yo(this.entries,(function(e){return e[0]===t}));return~e&&Xo(this.entries,e,1),!!~e}};var Zo,ti={getConstructor:function(t,e,n,r){var o=t((function(t,o){ko(t,i),Wo(t,{type:e,id:qo++,frozen:void 0}),null!=o&&Po(o,t[r],{that:t,AS_ENTRIES:n})})),i=o.prototype,u=Ho(e),a=function(t,e,n){var r=u(t),o=Vo(Ct(e),!0);return!0===o?$o(r).set(e,n):o[r.id]=n,t};return co(i,{delete:function(t){var e=u(this);if(!U(t))return!1;var n=Vo(t);return!0===n?$o(e).delete(t):n&&dt(n,e.id)&&delete n[e.id]},has:function(t){var e=u(this);if(!U(t))return!1;var n=Vo(t);return!0===n?$o(e).has(t):n&&dt(n,e.id)}}),co(i,n?{get:function(t){var e=u(this);if(U(t)){var n=Vo(t);return!0===n?$o(e).get(t):n?n[e.id]:void 0}},set:function(t,e){return a(this,t,e)}}:{add:function(t){return a(this,t,!0)}}),o}},ei=oe.enforce,ni=!g.ActiveXObject&&"ActiveXObject"in g,ri=function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}},oi=function(t,e,n){var r=-1!==t.indexOf("Map"),o=-1!==t.indexOf("Weak"),i=r?"set":"add",u=g[t],a=u&&u.prototype,c=u,f={},s=function(t){var e=R(a[t]);fe(a,t,"add"==t?function(t){return e(this,0===t?0:t),this}:"delete"==t?function(t){return!(o&&!U(t))&&e(this,0===t?0:t)}:"get"==t?function(t){return o&&!U(t)?void 0:e(this,0===t?0:t)}:"has"==t?function(t){return!(o&&!U(t))&&e(this,0===t?0:t)}:function(t,n){return e(this,0===t?0:t,n),this})};if(Ce(t,!G(u)||!(o||a.forEach&&!m((function(){(new u).entries().next()})))))c=n.getConstructor(e,t,r,i),go.enable();else if(Ce(t,!0)){var l=new c,p=l[i](o?{}:-0,1)!=l,v=m((function(){l.has(1)})),d=Co((function(t){new u(t)})),h=!o&&m((function(){for(var t=new u,e=5;e--;)t[i](e,e);return!t.has(-0)}));d||((c=e((function(t,e){ko(t,a);var n=function(t,e,n){var r,o;return Xr&&G(r=e.constructor)&&r!==n&&U(o=r.prototype)&&o!==n.prototype&&Xr(t,o),t}(new u,t,c);return null!=e&&Po(e,n[i],{that:n,AS_ENTRIES:r}),n}))).prototype=a,a.constructor=c),(v||h)&&(s("delete"),s("has"),r&&s("get")),(h||p)&&s(i),o&&a.clear&&delete a.clear}return f[t]=c,De({global:!0,forced:c!=u},f),Vr(c,t),o||n.setStrong(c,t,r),c}("WeakMap",ri,ti);if(Yt&&ni){Zo=ti.getConstructor(ri,"WeakMap",!0),go.enable();var ii=oi.prototype,ui=R(ii.delete),ai=R(ii.has),ci=R(ii.get),fi=R(ii.set);co(ii,{delete:function(t){if(U(t)&&!ho(t)){var e=ei(this);return e.frozen||(e.frozen=new Zo),ui(this,t)||e.frozen.delete(t)}return ui(this,t)},has:function(t){if(U(t)&&!ho(t)){var e=ei(this);return e.frozen||(e.frozen=new Zo),ai(this,t)||e.frozen.has(t)}return ai(this,t)},get:function(t){if(U(t)&&!ho(t)){var e=ei(this);return e.frozen||(e.frozen=new Zo),ai(this,t)?ci(this,t):e.frozen.get(t)}return ci(this,t)},set:function(t,e){if(U(t)&&!ho(t)){var n=ei(this);n.frozen||(n.frozen=new Zo),ai(this,t)?fi(this,t,e):n.frozen.set(t,e)}else fi(this,t,e);return this}})}var si={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},li=Rt("span").classList,pi=li&&li.constructor&&li.constructor.prototype,vi=pi===Object.prototype?void 0:pi,di=Ot("iterator"),hi=Ot("toStringTag"),yi=oo.values,gi=function(t,e){if(t){if(t[di]!==yi)try{Gt(t,di,yi)}catch(e){t[di]=yi}if(t[hi]||Gt(t,hi,e),si[e])for(var n in oo)if(t[n]!==oo[n])try{Gt(t,n,oo[n])}catch(e){t[n]=oo[n]}}};for(var mi in si)gi(g[mi]&&g[mi].prototype,mi);gi(vi,"DOMTokenList");var bi=function(t,e){var n=[][t];return!!n&&m((function(){n.call(null,e||function(){throw 1},1)}))},wi=Bo.forEach,Si=bi("forEach")?[].forEach:function(t){return wi(this,t,arguments.length>1?arguments[1]:void 0)},xi=function(t){if(t&&t.forEach!==Si)try{Gt(t,"forEach",Si)}catch(e){t.forEach=Si}};for(var Oi in si)si[Oi]&&xi(g[Oi]&&g[Oi].prototype);xi(vi);var Ei,ji,Ii,Ti,Ai=g.Promise,Pi=Ot("species"),Ri=/(?:ipad|iphone|ipod).*applewebkit/i.test(H),ki="process"==L(g.process),Mi=g.setImmediate,Li=g.clearImmediate,Fi=g.process,_i=g.Dispatch,Ci=g.Function,zi=g.MessageChannel,Di=g.String,Ni=0,Gi={};try{Ei=g.location}catch(t){}var Ui=function(t){if(dt(Gi,t)){var e=Gi[t];delete Gi[t],e()}},Bi=function(t){return function(){Ui(t)}},Vi=function(t){Ui(t.data)},Wi=function(t){g.postMessage(Di(t),Ei.protocol+"//"+Ei.host)};Mi&&Li||(Mi=function(t){var e=rn(arguments,1);return Gi[++Ni]=function(){qn(G(t)?t:Ci(t),void 0,e)},ji(Ni),Ni},Li=function(t){delete Gi[t]},ki?ji=function(t){Fi.nextTick(Bi(t))}:_i&&_i.now?ji=function(t){_i.now(Bi(t))}:zi&&!Ri?(Ti=(Ii=new zi).port2,Ii.port1.onmessage=Vi,ji=bo(Ti.postMessage,Ti)):g.addEventListener&&G(g.postMessage)&&!g.importScripts&&Ei&&"file:"!==Ei.protocol&&!m(Wi)?(ji=Wi,g.addEventListener("message",Vi,!1)):ji="onreadystatechange"in Rt("script")?function(t){wn.appendChild(Rt("script")).onreadystatechange=function(){wn.removeChild(this),Ui(t)}}:function(t){setTimeout(Bi(t),0)});var Hi,Ki,Yi,Xi,qi,$i,Ji,Qi,Zi={set:Mi,clear:Li},tu=/ipad|iphone|ipod/i.test(H)&&void 0!==g.Pebble,eu=/web0s(?!.*chrome)/i.test(H),nu=Lt.f,ru=Zi.set,ou=g.MutationObserver||g.WebKitMutationObserver,iu=g.document,uu=g.process,au=g.Promise,cu=nu(g,"queueMicrotask"),fu=cu&&cu.value;fu||(Hi=function(){var t,e;for(ki&&(t=uu.domain)&&t.exit();Ki;){e=Ki.fn,Ki=Ki.next;try{e()}catch(t){throw Ki?Xi():Yi=void 0,t}}Yi=void 0,t&&t.enter()},Ri||ki||eu||!ou||!iu?!tu&&au&&au.resolve?((Ji=au.resolve(void 0)).constructor=au,Qi=bo(Ji.then,Ji),Xi=function(){Qi(Hi)}):ki?Xi=function(){uu.nextTick(Hi)}:(ru=bo(ru,g),Xi=function(){ru(Hi)}):(qi=!0,$i=iu.createTextNode(""),new ou(Hi).observe($i,{characterData:!0}),Xi=function(){$i.data=qi=!qi}));var su,lu,pu,vu,du=fu||function(t){var e={fn:t,next:void 0};Yi&&(Yi.next=e),Ki||(Ki=e,Xi()),Yi=e},hu=function(t){var e,n;this.promise=new t((function(t,r){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=r})),this.resolve=ot(e),this.reject=ot(n)},yu={f:function(t){return new hu(t)}},gu=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},mu="object"==typeof window,bu=Zi.set,wu=Ot("species"),Su="Promise",xu=oe.getterFor(Su),Ou=oe.set,Eu=oe.getterFor(Su),ju=Ai&&Ai.prototype,Iu=Ai,Tu=ju,Au=g.TypeError,Pu=g.document,Ru=g.process,ku=yu.f,Mu=ku,Lu=!!(Pu&&Pu.createEvent&&g.dispatchEvent),Fu=G(g.PromiseRejectionEvent),_u=!1,Cu=Ce(Su,(function(){var t=Ht(Iu),e=t!==String(Iu);if(!e&&66===$)return!0;if($>=51&&/native code/.test(t))return!1;var n=new Iu((function(t){t(1)})),r=function(t){t((function(){}),(function(){}))};return(n.constructor={})[wu]=r,!(_u=n.then((function(){}))instanceof r)||!e&&mu&&!Fu})),zu=Cu||!Co((function(t){Iu.all(t).catch((function(){}))})),Du=function(t){var e;return!(!U(t)||!G(e=t.then))&&e},Nu=function(t,e){if(!t.notified){t.notified=!0;var n=t.reactions;du((function(){for(var r=t.value,o=1==t.state,i=0;n.length>i;){var u,a,c,f=n[i++],s=o?f.ok:f.fail,l=f.resolve,p=f.reject,v=f.domain;try{s?(o||(2===t.rejection&&Vu(t),t.rejection=1),!0===s?u=r:(v&&v.enter(),u=s(r),v&&(v.exit(),c=!0)),u===f.promise?p(Au("Promise-chain cycle")):(a=Du(u))?S(a,u,l,p):l(u)):p(r)}catch(t){v&&!c&&v.exit(),p(t)}}t.reactions=[],t.notified=!1,e&&!t.rejection&&Uu(t)}))}},Gu=function(t,e,n){var r,o;Lu?((r=Pu.createEvent("Event")).promise=e,r.reason=n,r.initEvent(t,!1,!0),g.dispatchEvent(r)):r={promise:e,reason:n},!Fu&&(o=g["on"+t])?o(r):"unhandledrejection"===t&&function(t,e){var n=g.console;n&&n.error&&(1==arguments.length?n.error(t):n.error(t,e))}("Unhandled promise rejection",n)},Uu=function(t){S(bu,g,(function(){var e,n=t.facade,r=t.value;if(Bu(t)&&(e=gu((function(){ki?Ru.emit("unhandledRejection",r,n):Gu("unhandledrejection",n,r)})),t.rejection=ki||Bu(t)?2:1,e.error))throw e.value}))},Bu=function(t){return 1!==t.rejection&&!t.parent},Vu=function(t){S(bu,g,(function(){var e=t.facade;ki?Ru.emit("rejectionHandled",e):Gu("rejectionhandled",e,t.value)}))},Wu=function(t,e,n){return function(r){t(e,r,n)}},Hu=function(t,e,n){t.done||(t.done=!0,n&&(t=n),t.value=e,t.state=2,Nu(t,!0))},Ku=function(t,e,n){if(!t.done){t.done=!0,n&&(t=n);try{if(t.facade===e)throw Au("Promise can't be resolved itself");var r=Du(e);r?du((function(){var n={done:!1};try{S(r,e,Wu(Ku,n,t),Wu(Hu,n,t))}catch(e){Hu(n,e,t)}})):(t.value=e,t.state=1,Nu(t,!1))}catch(e){Hu({done:!1},e,t)}}};if(Cu&&(Tu=(Iu=function(t){ko(this,Tu),ot(t),S(su,this);var e=xu(this);try{t(Wu(Ku,e),Wu(Hu,e))}catch(t){Hu(e,t)}}).prototype,(su=function(t){Ou(this,{type:Su,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=co(Tu,{then:function(t,e){var n=Eu(this),r=n.reactions,o=ku(er(this,Iu));return o.ok=!G(t)||t,o.fail=G(e)&&e,o.domain=ki?Ru.domain:void 0,n.parent=!0,r[r.length]=o,0!=n.state&&Nu(n,!1),o.promise},catch:function(t){return this.then(void 0,t)}}),lu=function(){var t=new su,e=xu(t);this.promise=t,this.resolve=Wu(Ku,e),this.reject=Wu(Hu,e)},yu.f=ku=function(t){return t===Iu||t===pu?new lu(t):Mu(t)},G(Ai)&&ju!==Object.prototype)){vu=ju.then,_u||(fe(ju,"then",(function(t,e){var n=this;return new Iu((function(t,e){S(vu,n,t,e)})).then(t,e)}),{unsafe:!0}),fe(ju,"catch",Tu.catch,{unsafe:!0}));try{delete ju.constructor}catch(t){}Xr&&Xr(ju,Tu)}De({global:!0,wrap:!0,forced:Cu},{Promise:Iu}),Vr(Iu,Su,!1),function(t){var e=V(t),n=Nt.f;b&&e&&!e[Pi]&&n(e,Pi,{configurable:!0,get:function(){return this}})}(Su),pu=V(Su),De({target:Su,stat:!0,forced:Cu},{reject:function(t){var e=ku(this);return S(e.reject,void 0,t),e.promise}}),De({target:Su,stat:!0,forced:Cu},{resolve:function(t){return function(t,e){if(Ct(t),U(e)&&e.constructor===t)return e;var n=yu.f(t);return(0,n.resolve)(e),n.promise}(this,t)}}),De({target:Su,stat:!0,forced:zu},{all:function(t){var e=this,n=ku(e),r=n.resolve,o=n.reject,i=gu((function(){var n=ot(e.resolve),i=[],u=0,a=1;Po(t,(function(t){var c=u++,f=!1;a++,S(n,e,t).then((function(t){f||(f=!0,i[c]=t,--a||r(i))}),o)})),--a||r(i)}));return i.error&&o(i.value),n.promise},race:function(t){var e=this,n=ku(e),r=n.reject,o=gu((function(){var o=ot(e.resolve);Po(t,(function(t){S(o,e,t).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}});var Yu=ce.PROPER,Xu=RegExp.prototype,qu=Xu.toString,$u=R(vn),Ju=m((function(){return"/a/b"!=qu.call({source:"a",flags:"b"})})),Qu=Yu&&"toString"!=qu.name;(Ju||Qu)&&fe(RegExp.prototype,"toString",(function(){var t=Ct(this),e=pn(t.source),n=t.flags;return"/"+e+"/"+pn(void 0===n&&W(Xu,t)&&!("flags"in Xu)?$u(t):n)}),{unsafe:!0});var Zu=ce.EXISTS,ta=Nt.f,ea=Function.prototype,na=R(ea.toString),ra=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,oa=R(ra.exec);b&&!Zu&&ta(ea,"name",{configurable:!0,get:function(){try{return oa(ra,na(this))[1]}catch(t){return""}}});var ia=new WeakMap;function ua(t){return t.getMenuConfig("uploadImage")}function aa(t,e){return Sr(this,void 0,void 0,(function(){return xr(this,(function(n){return[2,new Promise((function(n){var o=new FileReader;o.readAsDataURL(e),o.onload=function(){var i=o.result;if(i){var u=i.toString(),a=0===u.indexOf("data:image")?"":u;r(t,u,e.name,a),n("ok")}}}))]}))}))}function ca(t,n){return Sr(this,void 0,void 0,(function(){var o,i,u,a;return xr(this,(function(c){switch(c.label){case 0:return o=function(t){var n=ia.get(t);if(null!=n)return n;var o=ua(t),i=o.onSuccess,u=o.onProgress,a=o.onFailed,c=o.customInsert,f=o.onError;return n=e(wr(wr({},o),{onProgress:function(e){t.showProgressBar(e),u&&u(e)},onSuccess:function(e,n){if(c)return c(n,(function(e,n,o){return r(t,e,n,o)})),void i(e,n);var o=n.errno,u=void 0===o?1:o,f=n.data,s=void 0===f?{}:f;if(0===u){if(Array.isArray(s))s.forEach((function(e){var n=e.url,o=void 0===n?"":n,i=e.alt,u=void 0===i?"":i,a=e.href;r(t,o,u,void 0===a?"":a)}));else{var l=s.url,p=void 0===l?"":l,v=s.alt,d=void 0===v?"":v,h=s.href;r(t,p,d,void 0===h?"":h)}i(e,n)}else a(e,n)},onError:function(t,e,n){f(t,e,n)}})),ia.set(t,n),n}(t),i=n.name,u=n.type,a=n.size,o.addFile({name:i,type:u,size:a,data:n}),[4,o.upload()];case 1:return c.sent(),[2]}}))}))}function fa(t,e){var n,o;return Sr(this,void 0,void 0,(function(){var i,u,a,c,f,s,l,p,v;return xr(this,(function(d){switch(d.label){case 0:if(null==e)return[2];i=Array.prototype.slice.call(e),u=ua(t),a=u.customUpload,c=u.base64LimitSize,d.label=1;case 1:d.trys.push([1,11,12,17]),f=Er(i),d.label=2;case 2:return[4,f.next()];case 3:return(s=d.sent()).done?[3,10]:(l=s.value,p=l.size,c&&p<=c?[4,aa(t,l)]:[3,5]);case 4:return d.sent(),[3,9];case 5:return a?[4,a(l,(function(e,n,o){return r(t,e,n,o)}))]:[3,7];case 6:return d.sent(),[3,9];case 7:return[4,ca(t,l)];case 8:d.sent(),d.label=9;case 9:return[3,2];case 10:return[3,17];case 11:return v=d.sent(),n={error:v},[3,17];case 12:return d.trys.push([12,,15,16]),s&&!s.done&&(o=f.return)?[4,o.call(f)]:[3,14];case 13:d.sent(),d.label=14;case 14:return[3,16];case 15:if(n)throw n.error;return[7];case 16:return[7];case 17:return[2]}}))}))}var sa=R([].join),la=C!=Object,pa=bi("join",",");De({target:"Array",proto:!0,forced:la||!pa},{join:function(t){return sa(N(this),void 0===t?",":t)}});u&&(i.fn.append=u),a&&(i.fn.on=a),c&&(i.fn.remove=c),f&&(i.fn.val=f),s&&(i.fn.click=s),l&&(i.fn.hide=l);var va=function(){function t(){this.title=n("uploadImgModule.uploadImage"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M828.708571 585.045333a48.761905 48.761905 0 0 0-48.737523 48.761905v18.529524l-72.143238-72.167619a135.972571 135.972571 0 0 0-191.585524 0l-34.133334 34.133333-120.880762-120.953905a138.898286 138.898286 0 0 0-191.585523 0l-72.167619 72.167619V292.400762a48.786286 48.786286 0 0 1 48.761904-48.761905h341.23581a48.737524 48.737524 0 0 0 34.474667-83.285333 48.737524 48.737524 0 0 0-34.474667-14.287238H146.236952A146.212571 146.212571 0 0 0 0 292.400762v585.289143A146.358857 146.358857 0 0 0 146.236952 1024h584.996572a146.212571 146.212571 0 0 0 146.236952-146.310095V633.807238a48.786286 48.786286 0 0 0-48.761905-48.761905zM146.261333 926.45181a48.737524 48.737524 0 0 1-48.761904-48.761905v-174.128762l141.409523-141.458286a38.497524 38.497524 0 0 1 53.126096 0l154.526476 154.624 209.627428 209.724953H146.236952z m633.734096-48.761905c-0.073143 9.337905-3.145143 18.383238-8.777143 25.843809l-219.843048-220.94019 34.133333-34.133334a37.546667 37.546667 0 0 1 53.613715 0l140.873143 141.897143V877.714286zM1009.615238 160.231619L863.329524 13.897143a48.737524 48.737524 0 0 0-16.091429-10.24c-11.849143-4.87619-25.161143-4.87619-37.059047 0a48.761905 48.761905 0 0 0-16.067048 10.24l-146.236952 146.334476a49.005714 49.005714 0 0 0 69.217523 69.241905l62.902858-63.390476v272.627809a48.761905 48.761905 0 1 0 97.475047 0V166.083048l62.902857 63.390476a48.737524 48.737524 0 0 0 69.217524 0 48.761905 48.761905 0 0 0 0-69.241905z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return o(t)},t.prototype.getMenuConfig=function(t){return t.getMenuConfig("uploadImage")},t.prototype.exec=function(t,e){var n=this.getMenuConfig(t),o=n.allowedFileTypes,u=void 0===o?[]:o,a=n.customBrowseAndUpload;if(a)a((function(e,n,o){return r(t,e,n,o)}));else{var c="";u.length>0&&(c='accept="'+u.join(", ")+'"');var f=i("body"),s=i('<input type="file" '+c+" multiple/>");s.hide(),f.append(s),s.click(),s.on("change",(function(){var e=s[0].files;fa(t,e)}))}},t}();var da={menus:[{key:"uploadImage",factory:function(){return new va},config:{server:"",fieldName:"wangeditor-uploaded-image",maxFileSize:2097152,maxNumberOfFiles:100,allowedFileTypes:["image/*"],meta:{},metaWithUrl:!1,withCredentials:!1,timeout:1e4,onBeforeUpload:function(t){return t},onProgress:function(t){},onSuccess:function(t,e){},onFailed:function(t,e){console.error("'"+t.name+"' upload failed",e)},onError:function(t,e,n){console.error("'"+t.name+"' upload error",n)},base64LimitSize:0}}],editorPlugin:function(t){var e=t.insertData,n=t;return n.insertData=function(r){if(o(n))e(r);else if(r.getData("text/plain"))e(r);else{var i=r.files;if(i.length<=0)e(r);else Array.prototype.slice.call(i).some((function(t){return"image"===function(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return u}(t.type.split("/"),1)[0]}))?fa(t,i):e(r)}},n}};export{da as default};
//# sourceMappingURL=index.esm.js.map
