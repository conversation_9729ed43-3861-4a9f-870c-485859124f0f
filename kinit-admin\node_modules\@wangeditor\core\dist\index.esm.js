import{Editor as e,Operation as t,Path as n,Node as r,Range as o,Element as i,Text as a,Transforms as s,Point as l,createEditor as u}from"slate";import c from"lodash.toarray";import f,{css as d,append as h,addClass as p,removeClass as g,hasClass as v,on as y,focus as m,attr as b,removeAttr as w,hide as x,show as S,offset as k,width as E,height as O,parent as C,parents as N,is as T,dataset as L,val as M,text as P,html as D,children as R,remove as j,find as A,each as F,empty as I}from"dom7";import _ from"lodash.foreach";import"nanoid";import B from"lodash.throttle";import{init as $,classModule as W,propsModule as V,styleModule as z,datasetModule as H,eventListenersModule as U,attributesModule as K,jsx as q,h as G}from"snabbdom";import J from"lodash.camelcase";import{isKeyHotkey as Y,isHotkey as X}from"is-hotkey";import Q from"lodash.debounce";import Z from"lodash.clonedeep";import ee from"@uppy/core";import te from"@uppy/xhr-upload";var ne="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function re(e){var t={exports:{}};return e(t,t.exports),t.exports}var oe,ie,ae=function(e){return e&&e.Math==Math&&e},se=ae("object"==typeof globalThis&&globalThis)||ae("object"==typeof window&&window)||ae("object"==typeof self&&self)||ae("object"==typeof ne&&ne)||function(){return this}()||Function("return this")(),le=Object.defineProperty,ue=function(e,t){try{le(se,e,{value:t,configurable:!0,writable:!0})}catch(n){se[e]=t}return t},ce=se["__core-js_shared__"]||ue("__core-js_shared__",{}),fe=re((function(e){(e.exports=function(e,t){return ce[e]||(ce[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),de=Function.prototype,he=de.bind,pe=de.call,ge=he&&he.bind(pe),ve=he?function(e){return e&&ge(pe,e)}:function(e){return e&&function(){return pe.apply(e,arguments)}},ye=se.TypeError,me=function(e){if(null==e)throw ye("Can't call method on "+e);return e},be=se.Object,we=function(e){return be(me(e))},xe=ve({}.hasOwnProperty),Se=Object.hasOwn||function(e,t){return xe(we(e),t)},ke=0,Ee=Math.random(),Oe=ve(1..toString),Ce=function(e){return"Symbol("+(void 0===e?"":e)+")_"+Oe(++ke+Ee,36)},Ne=function(e){return"function"==typeof e},Te=function(e){return Ne(e)?e:void 0},Le=function(e,t){return arguments.length<2?Te(se[e]):se[e]&&se[e][t]},Me=Le("navigator","userAgent")||"",Pe=se.process,De=se.Deno,Re=Pe&&Pe.versions||De&&De.version,je=Re&&Re.v8;je&&(ie=(oe=je.split("."))[0]>0&&oe[0]<4?1:+(oe[0]+oe[1])),!ie&&Me&&(!(oe=Me.match(/Edge\/(\d+)/))||oe[1]>=74)&&(oe=Me.match(/Chrome\/(\d+)/))&&(ie=+oe[1]);var Ae=ie,Fe=function(e){try{return!!e()}catch(e){return!0}},Ie=!!Object.getOwnPropertySymbols&&!Fe((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&Ae&&Ae<41})),_e=Ie&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Be=fe("wks"),$e=se.Symbol,We=$e&&$e.for,Ve=_e?$e:$e&&$e.withoutSetter||Ce,ze=function(e){if(!Se(Be,e)||!Ie&&"string"!=typeof Be[e]){var t="Symbol."+e;Ie&&Se($e,e)?Be[e]=$e[e]:Be[e]=_e&&We?We(t):Ve(t)}return Be[e]},He={};He[ze("toStringTag")]="z";var Ue="[object z]"===String(He),Ke=!Fe((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),qe=function(e){return"object"==typeof e?null!==e:Ne(e)},Ge=se.document,Je=qe(Ge)&&qe(Ge.createElement),Ye=function(e){return Je?Ge.createElement(e):{}},Xe=!Ke&&!Fe((function(){return 7!=Object.defineProperty(Ye("div"),"a",{get:function(){return 7}}).a})),Qe=se.String,Ze=se.TypeError,et=function(e){if(qe(e))return e;throw Ze(Qe(e)+" is not an object")},tt=Function.prototype.call,nt=tt.bind?tt.bind(tt):function(){return tt.apply(tt,arguments)},rt=ve({}.isPrototypeOf),ot=se.Object,it=_e?function(e){return"symbol"==typeof e}:function(e){var t=Le("Symbol");return Ne(t)&&rt(t.prototype,ot(e))},at=se.String,st=function(e){try{return at(e)}catch(e){return"Object"}},lt=se.TypeError,ut=function(e){if(Ne(e))return e;throw lt(st(e)+" is not a function")},ct=function(e,t){var n=e[t];return null==n?void 0:ut(n)},ft=se.TypeError,dt=se.TypeError,ht=ze("toPrimitive"),pt=function(e,t){if(!qe(e)||it(e))return e;var n,r=ct(e,ht);if(r){if(void 0===t&&(t="default"),n=nt(r,e,t),!qe(n)||it(n))return n;throw dt("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var n,r;if("string"===t&&Ne(n=e.toString)&&!qe(r=nt(n,e)))return r;if(Ne(n=e.valueOf)&&!qe(r=nt(n,e)))return r;if("string"!==t&&Ne(n=e.toString)&&!qe(r=nt(n,e)))return r;throw ft("Can't convert object to primitive value")}(e,t)},gt=function(e){var t=pt(e,"string");return it(t)?t:t+""},vt=se.TypeError,yt=Object.defineProperty,mt={f:Ke?yt:function(e,t,n){if(et(e),t=gt(t),et(n),Xe)try{return yt(e,t,n)}catch(e){}if("get"in n||"set"in n)throw vt("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},bt=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},wt=Ke?function(e,t,n){return mt.f(e,t,bt(1,n))}:function(e,t,n){return e[t]=n,e},xt=ve(Function.toString);Ne(ce.inspectSource)||(ce.inspectSource=function(e){return xt(e)});var St,kt,Et,Ot=ce.inspectSource,Ct=se.WeakMap,Nt=Ne(Ct)&&/native code/.test(Ot(Ct)),Tt=fe("keys"),Lt=function(e){return Tt[e]||(Tt[e]=Ce(e))},Mt={},Pt=se.TypeError,Dt=se.WeakMap;if(Nt||ce.state){var Rt=ce.state||(ce.state=new Dt),jt=ve(Rt.get),At=ve(Rt.has),Ft=ve(Rt.set);St=function(e,t){if(At(Rt,e))throw new Pt("Object already initialized");return t.facade=e,Ft(Rt,e,t),t},kt=function(e){return jt(Rt,e)||{}},Et=function(e){return At(Rt,e)}}else{var It=Lt("state");Mt[It]=!0,St=function(e,t){if(Se(e,It))throw new Pt("Object already initialized");return t.facade=e,wt(e,It,t),t},kt=function(e){return Se(e,It)?e[It]:{}},Et=function(e){return Se(e,It)}}var _t={set:St,get:kt,has:Et,enforce:function(e){return Et(e)?kt(e):St(e,{})},getterFor:function(e){return function(t){var n;if(!qe(t)||(n=kt(t)).type!==e)throw Pt("Incompatible receiver, "+e+" required");return n}}},Bt=Function.prototype,$t=Ke&&Object.getOwnPropertyDescriptor,Wt=Se(Bt,"name"),Vt={EXISTS:Wt,PROPER:Wt&&"something"===function(){}.name,CONFIGURABLE:Wt&&(!Ke||Ke&&$t(Bt,"name").configurable)},zt=re((function(e){var t=Vt.CONFIGURABLE,n=_t.get,r=_t.enforce,o=String(String).split("String");(e.exports=function(e,n,i,a){var s,l=!!a&&!!a.unsafe,u=!!a&&!!a.enumerable,c=!!a&&!!a.noTargetGet,f=a&&void 0!==a.name?a.name:n;Ne(i)&&("Symbol("===String(f).slice(0,7)&&(f="["+String(f).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Se(i,"name")||t&&i.name!==f)&&wt(i,"name",f),(s=r(i)).source||(s.source=o.join("string"==typeof f?f:""))),e!==se?(l?!c&&e[n]&&(u=!0):delete e[n],u?e[n]=i:wt(e,n,i)):u?e[n]=i:ue(n,i)})(Function.prototype,"toString",(function(){return Ne(this)&&n(this).source||Ot(this)}))})),Ht=ve({}.toString),Ut=ve("".slice),Kt=function(e){return Ut(Ht(e),8,-1)},qt=ze("toStringTag"),Gt=se.Object,Jt="Arguments"==Kt(function(){return arguments}()),Yt=Ue?Kt:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Gt(e),qt))?n:Jt?Kt(t):"Object"==(r=Kt(t))&&Ne(t.callee)?"Arguments":r},Xt=Ue?{}.toString:function(){return"[object "+Yt(this)+"]"};Ue||zt(Object.prototype,"toString",Xt,{unsafe:!0});var Qt={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Zt=Ye("span").classList,en=Zt&&Zt.constructor&&Zt.constructor.prototype,tn=en===Object.prototype?void 0:en,nn=ve(ve.bind),rn=function(e,t){return ut(e),void 0===t?e:nn?nn(e,t):function(){return e.apply(t,arguments)}},on=se.Object,an=ve("".split),sn=Fe((function(){return!on("z").propertyIsEnumerable(0)}))?function(e){return"String"==Kt(e)?an(e,""):on(e)}:on,ln=Math.ceil,un=Math.floor,cn=function(e){var t=+e;return t!=t||0===t?0:(t>0?un:ln)(t)},fn=Math.min,dn=function(e){return e>0?fn(cn(e),9007199254740991):0},hn=function(e){return dn(e.length)},pn=Array.isArray||function(e){return"Array"==Kt(e)},gn=function(){},vn=[],yn=Le("Reflect","construct"),mn=/^\s*(?:class|function)\b/,bn=ve(mn.exec),wn=!mn.exec(gn),xn=function(e){if(!Ne(e))return!1;try{return yn(gn,vn,e),!0}catch(e){return!1}},Sn=!yn||Fe((function(){var e;return xn(xn.call)||!xn(Object)||!xn((function(){e=!0}))||e}))?function(e){if(!Ne(e))return!1;switch(Yt(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return wn||!!bn(mn,Ot(e))}:xn,kn=ze("species"),En=se.Array,On=function(e,t){return new(function(e){var t;return pn(e)&&(t=e.constructor,(Sn(t)&&(t===En||pn(t.prototype))||qe(t)&&null===(t=t[kn]))&&(t=void 0)),void 0===t?En:t}(e))(0===t?0:t)},Cn=ve([].push),Nn=function(e){var t=1==e,n=2==e,r=3==e,o=4==e,i=6==e,a=7==e,s=5==e||i;return function(l,u,c,f){for(var d,h,p=we(l),g=sn(p),v=rn(u,c),y=hn(g),m=0,b=f||On,w=t?b(l,y):n||a?b(l,0):void 0;y>m;m++)if((s||m in g)&&(h=v(d=g[m],m,p),e))if(t)w[m]=h;else if(h)switch(e){case 3:return!0;case 5:return d;case 6:return m;case 2:Cn(w,d)}else switch(e){case 4:return!1;case 7:Cn(w,d)}return i?-1:r||o?o:w}},Tn={forEach:Nn(0),map:Nn(1),filter:Nn(2),some:Nn(3),every:Nn(4),find:Nn(5),findIndex:Nn(6),filterReject:Nn(7)},Ln=function(e,t){var n=[][e];return!!n&&Fe((function(){n.call(null,t||function(){throw 1},1)}))},Mn=Tn.forEach,Pn=Ln("forEach")?[].forEach:function(e){return Mn(this,e,arguments.length>1?arguments[1]:void 0)},Dn=function(e){if(e&&e.forEach!==Pn)try{wt(e,"forEach",Pn)}catch(t){e.forEach=Pn}};for(var Rn in Qt)Qt[Rn]&&Dn(se[Rn]&&se[Rn].prototype);Dn(tn);var jn={}.propertyIsEnumerable,An=Object.getOwnPropertyDescriptor,Fn=An&&!jn.call({1:2},1)?function(e){var t=An(this,e);return!!t&&t.enumerable}:jn,In={f:Fn},_n=function(e){return sn(me(e))},Bn=Object.getOwnPropertyDescriptor,$n={f:Ke?Bn:function(e,t){if(e=_n(e),t=gt(t),Xe)try{return Bn(e,t)}catch(e){}if(Se(e,t))return bt(!nt(In.f,e,t),e[t])}},Wn=Math.max,Vn=Math.min,zn=function(e,t){var n=cn(e);return n<0?Wn(n+t,0):Vn(n,t)},Hn=function(e){return function(t,n,r){var o,i=_n(t),a=hn(i),s=zn(r,a);if(e&&n!=n){for(;a>s;)if((o=i[s++])!=o)return!0}else for(;a>s;s++)if((e||s in i)&&i[s]===n)return e||s||0;return!e&&-1}},Un={includes:Hn(!0),indexOf:Hn(!1)},Kn=Un.indexOf,qn=ve([].push),Gn=function(e,t){var n,r=_n(e),o=0,i=[];for(n in r)!Se(Mt,n)&&Se(r,n)&&qn(i,n);for(;t.length>o;)Se(r,n=t[o++])&&(~Kn(i,n)||qn(i,n));return i},Jn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Yn=Jn.concat("length","prototype"),Xn={f:Object.getOwnPropertyNames||function(e){return Gn(e,Yn)}},Qn={f:Object.getOwnPropertySymbols},Zn=ve([].concat),er=Le("Reflect","ownKeys")||function(e){var t=Xn.f(et(e)),n=Qn.f;return n?Zn(t,n(e)):t},tr=function(e,t){for(var n=er(t),r=mt.f,o=$n.f,i=0;i<n.length;i++){var a=n[i];Se(e,a)||r(e,a,o(t,a))}},nr=/#|\.prototype\./,rr=function(e,t){var n=ir[or(e)];return n==sr||n!=ar&&(Ne(t)?Fe(t):!!t)},or=rr.normalize=function(e){return String(e).replace(nr,".").toLowerCase()},ir=rr.data={},ar=rr.NATIVE="N",sr=rr.POLYFILL="P",lr=rr,ur=$n.f,cr=function(e,t){var n,r,o,i,a,s=e.target,l=e.global,u=e.stat;if(n=l?se:u?se[s]||ue(s,{}):(se[s]||{}).prototype)for(r in t){if(i=t[r],o=e.noTargetGet?(a=ur(n,r))&&a.value:n[r],!lr(l?r:s+(u?".":"#")+r,e.forced)&&void 0!==o){if(typeof i==typeof o)continue;tr(i,o)}(e.sham||o&&o.sham)&&wt(i,"sham",!0),zt(n,r,i,e)}},fr=Object.keys||function(e){return Gn(e,Jn)},dr=Fe((function(){fr(1)}));
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function hr(e){return"[object Object]"===Object.prototype.toString.call(e)}cr({target:"Object",stat:!0,forced:dr},{keys:function(e){return fr(we(e))}});var pr={isHistory(e){return!1!==hr(n=e)&&(void 0===(r=n.constructor)||!1!==hr(o=r.prototype)&&!1!==o.hasOwnProperty("isPrototypeOf"))&&Array.isArray(e.redos)&&Array.isArray(e.undos)&&(0===e.redos.length||t.isOperationList(e.redos[0]))&&(0===e.undos.length||t.isOperationList(e.undos[0]));var n,r,o}},gr=new WeakMap,vr=new WeakMap,yr={isHistoryEditor:t=>pr.isHistory(t.history)&&e.isEditor(t),isMerging:e=>vr.get(e),isSaving:e=>gr.get(e),redo(e){e.redo()},undo(e){e.undo()},withoutMerging(e,t){var n=yr.isMerging(e);vr.set(e,!1),t(),vr.set(e,n)},withoutSaving(e,t){var n=yr.isSaving(e);gr.set(e,!1),t(),gr.set(e,n)}},mr=(e,t)=>"set_selection"===e.type||(!(!t||"insert_text"!==e.type||"insert_text"!==t.type||e.offset!==t.offset+t.text.length||!n.equals(e.path,t.path))||!(!t||"remove_text"!==e.type||"remove_text"!==t.type||e.offset+e.text.length!==t.offset||!n.equals(e.path,t.path))),br=(e,t)=>"set_selection"!==e.type||null!=e.properties&&null!=e.newProperties,wr=(e,t)=>!(!t||"set_selection"!==e.type||"set_selection"!==t.type),xr=e=>"set_selection"!==e.type,Sr=se.String,kr=function(e){if("Symbol"===Yt(e))throw TypeError("Cannot convert a Symbol value to a string");return Sr(e)},Er=function(){var e=et(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t},Or=Vt.PROPER,Cr=RegExp.prototype,Nr=Cr.toString,Tr=ve(Er),Lr=Fe((function(){return"/a/b"!=Nr.call({source:"a",flags:"b"})})),Mr=Or&&"toString"!=Nr.name;(Lr||Mr)&&zt(RegExp.prototype,"toString",(function(){var e=et(this),t=kr(e.source),n=e.flags;return"/"+t+"/"+kr(void 0===n&&rt(Cr,e)&&!("flags"in Cr)?Tr(e):n)}),{unsafe:!0});var Pr,Dr=Ke?Object.defineProperties:function(e,t){et(e);for(var n,r=_n(t),o=fr(t),i=o.length,a=0;i>a;)mt.f(e,n=o[a++],r[n]);return e},Rr=Le("document","documentElement"),jr=Lt("IE_PROTO"),Ar=function(){},Fr=function(e){return"<script>"+e+"<\/script>"},Ir=function(e){e.write(Fr("")),e.close();var t=e.parentWindow.Object;return e=null,t},_r=function(){try{Pr=new ActiveXObject("htmlfile")}catch(e){}var e,t;_r="undefined"!=typeof document?document.domain&&Pr?Ir(Pr):((t=Ye("iframe")).style.display="none",Rr.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Fr("document.F=Object")),e.close(),e.F):Ir(Pr);for(var n=Jn.length;n--;)delete _r.prototype[Jn[n]];return _r()};Mt[jr]=!0;var Br=Object.create||function(e,t){var n;return null!==e?(Ar.prototype=et(e),n=new Ar,Ar.prototype=null,n[jr]=e):n=_r(),void 0===t?n:Dr(n,t)},$r=ze("unscopables"),Wr=Array.prototype;null==Wr[$r]&&mt.f(Wr,$r,{configurable:!0,value:Br(null)});var Vr=function(e){Wr[$r][e]=!0},zr=Un.includes;cr({target:"Array",proto:!0},{includes:function(e){return zr(this,e,arguments.length>1?arguments[1]:void 0)}}),Vr("includes");var Hr=ze("match"),Ur=function(e){var t;return qe(e)&&(void 0!==(t=e[Hr])?!!t:"RegExp"==Kt(e))},Kr=se.TypeError,qr=function(e){if(Ur(e))throw Kr("The method doesn't accept regular expressions");return e},Gr=ze("match"),Jr=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[Gr]=!1,"/./"[e](t)}catch(e){}}return!1},Yr=ve("".indexOf);cr({target:"String",proto:!0,forced:!Jr("includes")},{includes:function(e){return!!~Yr(kr(me(this)),kr(qr(e)),arguments.length>1?arguments[1]:void 0)}});var Xr,Qr=/"/g,Zr=ve("".replace);cr({target:"String",proto:!0,forced:(Xr="anchor",Fe((function(){var e=""[Xr]('"');return e!==e.toLowerCase()||e.split('"').length>3})))},{anchor:function(e){return t="a",n="name",r=e,o=kr(me(this)),i="<"+t,""!==n&&(i+=" "+n+'="'+Zr(kr(r),Qr,"&quot;")+'"'),i+">"+o+"</"+t+">";var t,n,r,o,i}});var eo=function(e,t,n){var r,o;et(e);try{if(!(r=ct(e,"return"))){if("throw"===t)throw n;return n}r=nt(r,e)}catch(e){o=!0,r=e}if("throw"===t)throw n;if(o)throw r;return et(r),n},to=function(e,t,n,r){try{return r?t(et(n)[0],n[1]):t(n)}catch(t){eo(e,"throw",t)}},no={},ro=ze("iterator"),oo=Array.prototype,io=function(e){return void 0!==e&&(no.Array===e||oo[ro]===e)},ao=function(e,t,n){var r=gt(t);r in e?mt.f(e,r,bt(0,n)):e[r]=n},so=ze("iterator"),lo=function(e){if(null!=e)return ct(e,so)||ct(e,"@@iterator")||no[Yt(e)]},uo=se.TypeError,co=function(e,t){var n=arguments.length<2?lo(e):t;if(ut(n))return et(nt(n,e));throw uo(st(e)+" is not iterable")},fo=se.Array,ho=ze("iterator"),po=!1;try{var go=0,vo={next:function(){return{done:!!go++}},return:function(){po=!0}};vo[ho]=function(){return this},Array.from(vo,(function(){throw 2}))}catch(e){}var yo=function(e,t){if(!t&&!po)return!1;var n=!1;try{var r={};r[ho]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(e){}return n},mo=!yo((function(e){Array.from(e)}));cr({target:"Array",stat:!0,forced:mo},{from:function(e){var t=we(e),n=Sn(this),r=arguments.length,o=r>1?arguments[1]:void 0,i=void 0!==o;i&&(o=rn(o,r>2?arguments[2]:void 0));var a,s,l,u,c,f,d=lo(t),h=0;if(!d||this==fo&&io(d))for(a=hn(t),s=n?new this(a):fo(a);a>h;h++)f=i?o(t[h],h):t[h],ao(s,h,f);else for(c=(u=co(t,d)).next,s=n?new this:[];!(l=nt(c,u)).done;h++)f=i?to(u,o,[l.value,h],!0):l.value,ao(s,h,f);return s.length=h,s}});var bo,wo,xo,So=ve("".charAt),ko=ve("".charCodeAt),Eo=ve("".slice),Oo=function(e){return function(t,n){var r,o,i=kr(me(t)),a=cn(n),s=i.length;return a<0||a>=s?e?"":void 0:(r=ko(i,a))<55296||r>56319||a+1===s||(o=ko(i,a+1))<56320||o>57343?e?So(i,a):r:e?Eo(i,a,a+2):o-56320+(r-55296<<10)+65536}},Co={codeAt:Oo(!1),charAt:Oo(!0)},No=!Fe((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),To=Lt("IE_PROTO"),Lo=se.Object,Mo=Lo.prototype,Po=No?Lo.getPrototypeOf:function(e){var t=we(e);if(Se(t,To))return t[To];var n=t.constructor;return Ne(n)&&t instanceof n?n.prototype:t instanceof Lo?Mo:null},Do=ze("iterator"),Ro=!1;[].keys&&("next"in(xo=[].keys())?(wo=Po(Po(xo)))!==Object.prototype&&(bo=wo):Ro=!0);var jo=null==bo||Fe((function(){var e={};return bo[Do].call(e)!==e}));jo&&(bo={}),Ne(bo[Do])||zt(bo,Do,(function(){return this}));var Ao={IteratorPrototype:bo,BUGGY_SAFARI_ITERATORS:Ro},Fo=mt.f,Io=ze("toStringTag"),_o=function(e,t,n){e&&!Se(e=n?e:e.prototype,Io)&&Fo(e,Io,{configurable:!0,value:t})},Bo=Ao.IteratorPrototype,$o=function(){return this},Wo=se.String,Vo=se.TypeError,zo=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=ve(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return et(n),function(e){if("object"==typeof e||Ne(e))return e;throw Vo("Can't set "+Wo(e)+" as a prototype")}(r),t?e(n,r):n.__proto__=r,n}}():void 0),Ho=Vt.PROPER,Uo=Vt.CONFIGURABLE,Ko=Ao.IteratorPrototype,qo=Ao.BUGGY_SAFARI_ITERATORS,Go=ze("iterator"),Jo=function(){return this},Yo=function(e,t,n,r,o,i,a){!function(e,t,n,r){var o=t+" Iterator";e.prototype=Br(Bo,{next:bt(+!r,n)}),_o(e,o,!1),no[o]=$o}(n,t,r);var s,l,u,c=function(e){if(e===o&&g)return g;if(!qo&&e in h)return h[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},f=t+" Iterator",d=!1,h=e.prototype,p=h[Go]||h["@@iterator"]||o&&h[o],g=!qo&&p||c(o),v="Array"==t&&h.entries||p;if(v&&(s=Po(v.call(new e)))!==Object.prototype&&s.next&&(Po(s)!==Ko&&(zo?zo(s,Ko):Ne(s[Go])||zt(s,Go,Jo)),_o(s,f,!0)),Ho&&"values"==o&&p&&"values"!==p.name&&(Uo?wt(h,"name","values"):(d=!0,g=function(){return nt(p,this)})),o)if(l={values:c("values"),keys:i?g:c("keys"),entries:c("entries")},a)for(u in l)(qo||d||!(u in h))&&zt(h,u,l[u]);else cr({target:t,proto:!0,forced:qo||d},l);return h[Go]!==g&&zt(h,Go,g,{name:o}),no[t]=g,l},Xo=Co.charAt,Qo=_t.set,Zo=_t.getterFor("String Iterator");Yo(String,"String",(function(e){Qo(this,{type:"String Iterator",string:kr(e),index:0})}),(function(){var e,t=Zo(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=Xo(n,r),t.index+=e.length,{value:e,done:!1})}));var ei,ti=$n.f,ni=ve("".endsWith),ri=ve("".slice),oi=Math.min,ii=Jr("endsWith"),ai=!(ii||(ei=ti(String.prototype,"endsWith"),!ei||ei.writable));cr({target:"String",proto:!0,forced:!ai&&!ii},{endsWith:function(e){var t=kr(me(this));qr(e);var n=arguments.length>1?arguments[1]:void 0,r=t.length,o=void 0===n?r:oi(dn(n),r),i=kr(e);return ni?ni(t,i,o):ri(t,o-i.length,o)===i}});var si=ve([].join),li=sn!=Object,ui=Ln("join",",");cr({target:"Array",proto:!0,forced:li||!ui},{join:function(e){return si(_n(this),void 0===e?",":e)}});var ci=ze("species"),fi=function(e){return Ae>=51||!Fe((function(){var t=[];return(t.constructor={})[ci]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},di=Tn.map,hi=fi("map");cr({target:"Array",proto:!0,forced:!hi},{map:function(e){return di(this,e,arguments.length>1?arguments[1]:void 0)}});var pi=se.RegExp,gi=Fe((function(){var e=pi("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),vi=gi||Fe((function(){return!pi("a","y").sticky})),yi={BROKEN_CARET:gi||Fe((function(){var e=pi("^r","gy");return e.lastIndex=2,null!=e.exec("str")})),MISSED_STICKY:vi,UNSUPPORTED_Y:gi},mi=se.RegExp,bi=Fe((function(){var e=mi(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)})),wi=se.RegExp,xi=Fe((function(){var e=wi("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")})),Si=_t.get,ki=fe("native-string-replace",String.prototype.replace),Ei=RegExp.prototype.exec,Oi=Ei,Ci=ve("".charAt),Ni=ve("".indexOf),Ti=ve("".replace),Li=ve("".slice),Mi=function(){var e=/a/,t=/b*/g;return nt(Ei,e,"a"),nt(Ei,t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),Pi=yi.BROKEN_CARET,Di=void 0!==/()??/.exec("")[1];(Mi||Di||Pi||bi||xi)&&(Oi=function(e){var t,n,r,o,i,a,s,l=this,u=Si(l),c=kr(e),f=u.raw;if(f)return f.lastIndex=l.lastIndex,t=nt(Oi,f,c),l.lastIndex=f.lastIndex,t;var d=u.groups,h=Pi&&l.sticky,p=nt(Er,l),g=l.source,v=0,y=c;if(h&&(p=Ti(p,"y",""),-1===Ni(p,"g")&&(p+="g"),y=Li(c,l.lastIndex),l.lastIndex>0&&(!l.multiline||l.multiline&&"\n"!==Ci(c,l.lastIndex-1))&&(g="(?: "+g+")",y=" "+y,v++),n=new RegExp("^(?:"+g+")",p)),Di&&(n=new RegExp("^"+g+"$(?!\\s)",p)),Mi&&(r=l.lastIndex),o=nt(Ei,h?n:l,y),h?o?(o.input=Li(o.input,v),o[0]=Li(o[0],v),o.index=l.lastIndex,l.lastIndex+=o[0].length):l.lastIndex=0:Mi&&o&&(l.lastIndex=l.global?o.index+o[0].length:r),Di&&o&&o.length>1&&nt(ki,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&d)for(o.groups=a=Br(null),i=0;i<d.length;i++)a[(s=d[i])[0]]=o[s[1]];return o});var Ri=Oi;cr({target:"RegExp",proto:!0,forced:/./.exec!==Ri},{exec:Ri});var ji=Function.prototype,Ai=ji.apply,Fi=ji.bind,Ii=ji.call,_i="object"==typeof Reflect&&Reflect.apply||(Fi?Ii.bind(Ai):function(){return Ii.apply(Ai,arguments)}),Bi=ze("species"),$i=RegExp.prototype,Wi=function(e,t,n,r){var o=ze(e),i=!Fe((function(){var t={};return t[o]=function(){return 7},7!=""[e](t)})),a=i&&!Fe((function(){var t=!1,n=/a/;return"split"===e&&((n={}).constructor={},n.constructor[Bi]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return t=!0,null},n[o](""),!t}));if(!i||!a||n){var s=ve(/./[o]),l=t(o,""[e],(function(e,t,n,r,o){var a=ve(e),l=t.exec;return l===Ri||l===$i.exec?i&&!o?{done:!0,value:s(t,n,r)}:{done:!0,value:a(n,t,r)}:{done:!1}}));zt(String.prototype,e,l[0]),zt($i,o,l[1])}r&&wt($i[o],"sham",!0)},Vi=Co.charAt,zi=function(e,t,n){return t+(n?Vi(e,t).length:1)},Hi=Math.floor,Ui=ve("".charAt),Ki=ve("".replace),qi=ve("".slice),Gi=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Ji=/\$([$&'`]|\d{1,2})/g,Yi=function(e,t,n,r,o,i){var a=n+e.length,s=r.length,l=Ji;return void 0!==o&&(o=we(o),l=Gi),Ki(i,l,(function(i,l){var u;switch(Ui(l,0)){case"$":return"$";case"&":return e;case"`":return qi(t,0,n);case"'":return qi(t,a);case"<":u=o[qi(l,1,-1)];break;default:var c=+l;if(0===c)return i;if(c>s){var f=Hi(c/10);return 0===f?i:f<=s?void 0===r[f-1]?Ui(l,1):r[f-1]+Ui(l,1):i}u=r[c-1]}return void 0===u?"":u}))},Xi=se.TypeError,Qi=function(e,t){var n=e.exec;if(Ne(n)){var r=nt(n,e,t);return null!==r&&et(r),r}if("RegExp"===Kt(e))return nt(Ri,e,t);throw Xi("RegExp#exec called on incompatible receiver")},Zi=ze("replace"),ea=Math.max,ta=Math.min,na=ve([].concat),ra=ve([].push),oa=ve("".indexOf),ia=ve("".slice),aa="$0"==="a".replace(/./,"$0"),sa=!!/./[Zi]&&""===/./[Zi]("a","$0");Wi("replace",(function(e,t,n){var r=sa?"$":"$0";return[function(e,n){var r=me(this),o=null==e?void 0:ct(e,Zi);return o?nt(o,e,r,n):nt(t,kr(r),e,n)},function(e,o){var i=et(this),a=kr(e);if("string"==typeof o&&-1===oa(o,r)&&-1===oa(o,"$<")){var s=n(t,i,a,o);if(s.done)return s.value}var l=Ne(o);l||(o=kr(o));var u=i.global;if(u){var c=i.unicode;i.lastIndex=0}for(var f=[];;){var d=Qi(i,a);if(null===d)break;if(ra(f,d),!u)break;""===kr(d[0])&&(i.lastIndex=zi(a,dn(i.lastIndex),c))}for(var h,p="",g=0,v=0;v<f.length;v++){for(var y=kr((d=f[v])[0]),m=ea(ta(cn(d.index),a.length),0),b=[],w=1;w<d.length;w++)ra(b,void 0===(h=d[w])?h:String(h));var x=d.groups;if(l){var S=na([y],b,m,a);void 0!==x&&ra(S,x);var k=kr(_i(o,void 0,S))}else k=Yi(y,a,m,b,x,o);m>=g&&(p+=ia(a,g,m)+k,g=m+y.length)}return p+ia(a,g)}]}),!!Fe((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}))||!aa||sa);
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var la=function(e,t){return la=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},la(e,t)};function ua(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}la(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var ca=function(){return ca=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ca.apply(this,arguments)};function fa(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function da(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function ha(e,t){for(var n=0,r=t.length,o=e.length;n<r;n++,o++)e[o]=t[n];return e}var pa=0,ga=function(){this.id=""+pa++},va=_t.set,ya=_t.getterFor("Array Iterator"),ma=Yo(Array,"Array",(function(e,t){va(this,{type:"Array Iterator",target:_n(e),index:0,kind:t})}),(function(){var e=ya(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");no.Arguments=no.Array,Vr("keys"),Vr("values"),Vr("entries");var ba=function(e,t,n){for(var r in t)zt(e,r,t[r],n);return e},wa=se.Array,xa=Math.max,Sa=function(e,t,n){for(var r=hn(e),o=zn(t,r),i=zn(void 0===n?r:n,r),a=wa(xa(i-o,0)),s=0;o<i;o++,s++)ao(a,s,e[o]);return a.length=s,a},ka=Xn.f,Ea="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Oa={f:function(e){return Ea&&"Window"==Kt(e)?function(e){try{return ka(e)}catch(e){return Sa(Ea)}}(e):ka(_n(e))}},Ca=Fe((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),Na=Object.isExtensible,Ta=Fe((function(){Na(1)}))||Ca?function(e){return!!qe(e)&&((!Ca||"ArrayBuffer"!=Kt(e))&&(!Na||Na(e)))}:Na,La=!Fe((function(){return Object.isExtensible(Object.preventExtensions({}))})),Ma=re((function(e){var t=mt.f,n=!1,r=Ce("meta"),o=0,i=function(e){t(e,r,{value:{objectID:"O"+o++,weakData:{}}})},a=e.exports={enable:function(){a.enable=function(){},n=!0;var e=Xn.f,t=ve([].splice),o={};o[r]=1,e(o).length&&(Xn.f=function(n){for(var o=e(n),i=0,a=o.length;i<a;i++)if(o[i]===r){t(o,i,1);break}return o},cr({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Oa.f}))},fastKey:function(e,t){if(!qe(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!Se(e,r)){if(!Ta(e))return"F";if(!t)return"E";i(e)}return e[r].objectID},getWeakData:function(e,t){if(!Se(e,r)){if(!Ta(e))return!0;if(!t)return!1;i(e)}return e[r].weakData},onFreeze:function(e){return La&&n&&Ta(e)&&!Se(e,r)&&i(e),e}};Mt[r]=!0})),Pa=se.TypeError,Da=function(e,t){this.stopped=e,this.result=t},Ra=Da.prototype,ja=function(e,t,n){var r,o,i,a,s,l,u,c=n&&n.that,f=!(!n||!n.AS_ENTRIES),d=!(!n||!n.IS_ITERATOR),h=!(!n||!n.INTERRUPTED),p=rn(t,c),g=function(e){return r&&eo(r,"normal",e),new Da(!0,e)},v=function(e){return f?(et(e),h?p(e[0],e[1],g):p(e[0],e[1])):h?p(e,g):p(e)};if(d)r=e;else{if(!(o=lo(e)))throw Pa(st(e)+" is not iterable");if(io(o)){for(i=0,a=hn(e);a>i;i++)if((s=v(e[i]))&&rt(Ra,s))return s;return new Da(!1)}r=co(e,o)}for(l=r.next;!(u=nt(l,r)).done;){try{s=v(u.value)}catch(e){eo(r,"throw",e)}if("object"==typeof s&&s&&rt(Ra,s))return s}return new Da(!1)},Aa=se.TypeError,Fa=function(e,t){if(rt(t,e))return e;throw Aa("Incorrect invocation")},Ia=function(e,t,n){var r,o;return zo&&Ne(r=t.constructor)&&r!==n&&qe(o=r.prototype)&&o!==n.prototype&&zo(e,o),e},_a=function(e,t,n){var r=-1!==e.indexOf("Map"),o=-1!==e.indexOf("Weak"),i=r?"set":"add",a=se[e],s=a&&a.prototype,l=a,u={},c=function(e){var t=ve(s[e]);zt(s,e,"add"==e?function(e){return t(this,0===e?0:e),this}:"delete"==e?function(e){return!(o&&!qe(e))&&t(this,0===e?0:e)}:"get"==e?function(e){return o&&!qe(e)?void 0:t(this,0===e?0:e)}:"has"==e?function(e){return!(o&&!qe(e))&&t(this,0===e?0:e)}:function(e,n){return t(this,0===e?0:e,n),this})};if(lr(e,!Ne(a)||!(o||s.forEach&&!Fe((function(){(new a).entries().next()})))))l=n.getConstructor(t,e,r,i),Ma.enable();else if(lr(e,!0)){var f=new l,d=f[i](o?{}:-0,1)!=f,h=Fe((function(){f.has(1)})),p=yo((function(e){new a(e)})),g=!o&&Fe((function(){for(var e=new a,t=5;t--;)e[i](t,t);return!e.has(-0)}));p||((l=t((function(e,t){Fa(e,s);var n=Ia(new a,e,l);return null!=t&&ja(t,n[i],{that:n,AS_ENTRIES:r}),n}))).prototype=s,s.constructor=l),(h||g)&&(c("delete"),c("has"),r&&c("get")),(g||d)&&c(i),o&&s.clear&&delete s.clear}return u[e]=l,cr({global:!0,forced:l!=a},u),_o(l,e),o||n.setStrong(l,e,r),l},Ba=Ma.getWeakData,$a=_t.set,Wa=_t.getterFor,Va=Tn.find,za=Tn.findIndex,Ha=ve([].splice),Ua=0,Ka=function(e){return e.frozen||(e.frozen=new qa)},qa=function(){this.entries=[]},Ga=function(e,t){return Va(e.entries,(function(e){return e[0]===t}))};qa.prototype={get:function(e){var t=Ga(this,e);if(t)return t[1]},has:function(e){return!!Ga(this,e)},set:function(e,t){var n=Ga(this,e);n?n[1]=t:this.entries.push([e,t])},delete:function(e){var t=za(this.entries,(function(t){return t[0]===e}));return~t&&Ha(this.entries,t,1),!!~t}};var Ja,Ya={getConstructor:function(e,t,n,r){var o=e((function(e,o){Fa(e,i),$a(e,{type:t,id:Ua++,frozen:void 0}),null!=o&&ja(o,e[r],{that:e,AS_ENTRIES:n})})),i=o.prototype,a=Wa(t),s=function(e,t,n){var r=a(e),o=Ba(et(t),!0);return!0===o?Ka(r).set(t,n):o[r.id]=n,e};return ba(i,{delete:function(e){var t=a(this);if(!qe(e))return!1;var n=Ba(e);return!0===n?Ka(t).delete(e):n&&Se(n,t.id)&&delete n[t.id]},has:function(e){var t=a(this);if(!qe(e))return!1;var n=Ba(e);return!0===n?Ka(t).has(e):n&&Se(n,t.id)}}),ba(i,n?{get:function(e){var t=a(this);if(qe(e)){var n=Ba(e);return!0===n?Ka(t).get(e):n?n[t.id]:void 0}},set:function(e,t){return s(this,e,t)}}:{add:function(e){return s(this,e,!0)}}),o}},Xa=_t.enforce,Qa=!se.ActiveXObject&&"ActiveXObject"in se,Za=function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}},es=_a("WeakMap",Za,Ya);if(Nt&&Qa){Ja=Ya.getConstructor(Za,"WeakMap",!0),Ma.enable();var ts=es.prototype,ns=ve(ts.delete),rs=ve(ts.has),os=ve(ts.get),is=ve(ts.set);ba(ts,{delete:function(e){if(qe(e)&&!Ta(e)){var t=Xa(this);return t.frozen||(t.frozen=new Ja),ns(this,e)||t.frozen.delete(e)}return ns(this,e)},has:function(e){if(qe(e)&&!Ta(e)){var t=Xa(this);return t.frozen||(t.frozen=new Ja),rs(this,e)||t.frozen.has(e)}return rs(this,e)},get:function(e){if(qe(e)&&!Ta(e)){var t=Xa(this);return t.frozen||(t.frozen=new Ja),rs(this,e)?os(this,e):t.frozen.get(e)}return os(this,e)},set:function(e,t){if(qe(e)&&!Ta(e)){var n=Xa(this);n.frozen||(n.frozen=new Ja),rs(this,e)?is(this,e,t):n.frozen.set(e,t)}else is(this,e,t);return this}})}var as=ze("iterator"),ss=ze("toStringTag"),ls=ma.values,us=function(e,t){if(e){if(e[as]!==ls)try{wt(e,as,ls)}catch(t){e[as]=ls}if(e[ss]||wt(e,ss,t),Qt[t])for(var n in ma)if(e[n]!==ma[n])try{wt(e,n,ma[n])}catch(t){e[n]=ma[n]}}};for(var cs in Qt)us(se[cs]&&se[cs].prototype,cs);us(tn,"DOMTokenList");var fs=new WeakMap,ds=new WeakMap,hs=new WeakMap,ps=new WeakMap,gs=new WeakMap,vs=new WeakMap,ys=new WeakMap,ms=new WeakMap,bs=new WeakMap,ws=new WeakMap,xs=new WeakMap,Ss=new WeakMap,ks=new WeakMap,Es=new WeakMap,Os=new WeakMap,Cs=new WeakMap,Ns=new WeakMap,Ts=new WeakMap,Ls=new WeakMap,Ms=new WeakMap,Ps=new WeakMap,Ds=new WeakMap,Rs=new WeakMap,js=new WeakMap,As=new WeakMap,Fs=Tn.find,Is=!0;"find"in[]&&Array(1).find((function(){Is=!1})),cr({target:"Array",proto:!0,forced:Is},{find:function(e){return Fs(this,e,arguments.length>1?arguments[1]:void 0)}}),Vr("find"),cr({global:!0},{globalThis:se});const _s=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","isindex","keygen","link","menuitem","meta","nextid","param","source","track","wbr"];d&&(f.fn.css=d),h&&(f.fn.append=h),p&&(f.fn.addClass=p),g&&(f.fn.removeClass=g),v&&(f.fn.hasClass=v),y&&(f.fn.on=y),m&&(f.fn.focus=m),b&&(f.fn.attr=b),w&&(f.fn.removeAttr=w),x&&(f.fn.hide=x),S&&(f.fn.show=S),k&&(f.fn.offset=k),E&&(f.fn.width=E),O&&(f.fn.height=O),C&&(f.fn.parent=C),N&&(f.fn.parents=N),T&&(f.fn.is=T),L&&(f.fn.dataset=L),M&&(f.fn.val=M),P&&(f.fn.text=P),D&&(f.fn.html=D),R&&(f.fn.children=R),j&&(f.fn.remove=j),A&&(f.fn.find=A),F&&(f.fn.each=F),I&&(f.fn.empty=I);var Bs,$s=function(e){return e&&e.ownerDocument&&e.ownerDocument.defaultView||null},Ws=function(e){return Vs(e)&&1===e.nodeType},Vs=function(e){var t=$s(e);return!!t&&e instanceof t.Node},zs=function(e){var t=e&&e.anchorNode&&$s(e.anchorNode);return!!t&&e instanceof t.Selection},Hs=function(e){return Vs(e)&&3===e.nodeType},Us=function(e){var t,n,r;return null!==(t=window.document.getElementById(e))&&void 0!==t?t:(null===(r=null===(n=window.document.activeElement)||void 0===n?void 0:n.shadowRoot)||void 0===r?void 0:r.getElementById(e))||null},Ks=function(e,t,n){for(var r,o=e.childNodes,i=o[t],a=t,s=!1,l=!1;(Vs(r=i)&&8===r.nodeType||Ws(i)&&0===i.childNodes.length||Ws(i)&&"false"===i.getAttribute("contenteditable"))&&(!s||!l);)a>=o.length?(s=!0,a=t-1,n="backward"):a<0?(l=!0,a=t+1,n="forward"):(i=o[a],t=a,a+="forward"===n?1:-1);return[i,t]},qs=function(e,t,n){return da(Ks(e,t,n),1)[0]},Gs=function e(t){var n,r,o="";if(Hs(t)&&t.nodeValue)return t.nodeValue;if(Ws(t)){try{for(var i=fa(Array.from(t.childNodes)),a=i.next();!a.done;a=i.next()){o+=e(a.value)}}catch(e){n={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}var s=getComputedStyle(t).getPropertyValue("display");"block"!==s&&"list"!==s&&"table-row"!==s&&"BR"!==t.tagName||(o+="\n")}return o};function Js(e,t){if(!(e instanceof HTMLElement&&"true"===e.dataset.slateVoid))for(var n=e.childNodes,r=n.length;r--;){var o=n[r],i=o.nodeType;3==i?t(o,e):1!=i&&9!=i&&11!=i||Js(o,t)}}function Ys(e){if(0===e.length)return"";var t=e[0];return t.nodeType!==Bs.ELEMENT_NODE?"":t.tagName.toLowerCase()}!function(e){e[e.ELEMENT_NODE=1]="ELEMENT_NODE",e[e.TEXT_NODE=3]="TEXT_NODE",e[e.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",e[e.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",e[e.COMMENT_NODE=8]="COMMENT_NODE",e[e.DOCUMENT_NODE=9]="DOCUMENT_NODE",e[e.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",e[e.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"}(Bs||(Bs={})),void 0!==globalThis.navigator&&void 0!==globalThis.window&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&globalThis.window.MSStream;var Xs="undefined"!=typeof navigator&&/Mac OS X/.test(navigator.userAgent),Qs="undefined"!=typeof navigator&&/^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);"undefined"!=typeof navigator&&/^(?!.*Seamonkey)(?=.*Firefox\/(?:[0-7][0-9]|[0-8][0-6])(?:\.)).*/i.test(navigator.userAgent);var Zs="undefined"!=typeof navigator&&/Version\/[\d\.]+.*Safari/.test(navigator.userAgent),el="undefined"!=typeof navigator&&/Edge?\/(?:[0-6][0-9]|[0-7][0-8])(?:\.)/i.test(navigator.userAgent),tl="undefined"!=typeof navigator&&/Chrome?\/(?:[0-7][0-5]|[0-6][0-9])(?:\.)/i.test(navigator.userAgent),nl="undefined"!=typeof navigator&&/Chrome/i.test(navigator.userAgent);"undefined"!=typeof navigator&&/.*QQBrowser/.test(navigator.userAgent);var rl=!tl&&!el&&"undefined"!=typeof globalThis&&globalThis.InputEvent&&"function"==typeof globalThis.InputEvent.prototype.getTargetRanges,ol={getWindow:function(e){var t=Ps.get(e);if(!t)throw new Error("Unable to find a host window element for this editor");return t},findKey:function(e,t){var n=Ms.get(t);return n||(n=new ga,Ms.set(t,n)),n},setNewKey:function(e){var t=new ga;Ms.set(e,t)},findPath:function(t,n){for(var r=[],o=n;;){var i=Os.get(o);if(null==i){if(e.isEditor(o))return r;break}var a=Es.get(o);if(null==a)break;r.unshift(a),o=i}throw new Error("Unable to find the path for Slate node: "+JSON.stringify(n))},findDocumentOrShadowRoot:function(e){if(e.isDestroyed)return window.document;var t=ol.toDOMNode(e,e),n=t.getRootNode();return(n instanceof Document||n instanceof ShadowRoot)&&null!=n.getSelection?n:t.ownerDocument},getParentNode:function(e,t){return Os.get(t)||null},getParentsNodes:function(e,t){for(var n=[],r=t;r!==e&&null!=r;){var o=ol.getParentNode(e,r);if(null==o)break;n.push(o),r=o}return n},getTopNode:function(e,t){var n=[ol.findPath(e,t)[0]];return r.get(e,n)},toDOMNode:function(t,n){var r;if(e.isEditor(n))r=Cs.get(t);else{var o=ol.findKey(t,n);r=Ts.get(o)}if(!r)throw new Error("Cannot resolve a DOM node from Slate node: "+JSON.stringify(n));return r},hasDOMNode:function(e,t,n){void 0===n&&(n={});var r,o=n.editable,i=void 0!==o&&o,a=ol.toDOMNode(e,e);try{r=Ws(t)?t:t.parentElement}catch(e){if(!e.message.includes('Permission denied to access property "nodeType"'))throw e}return!!r&&(r.closest("[data-slate-editor]")===a&&(!i||r.isContentEditable||!!r.getAttribute("data-slate-zero-width")))},toDOMRange:function(e,t){var n=t.anchor,r=t.focus,i=o.isBackward(t),a=ol.toDOMPoint(e,n),s=o.isCollapsed(t)?a:ol.toDOMPoint(e,r),l=ol.getWindow(e).document.createRange(),u=da(i?s:a,2),c=u[0],f=u[1],d=da(i?a:s,2),h=d[0],p=d[1],g=!!(Ws(c)?c:c.parentElement).getAttribute("data-slate-zero-width"),v=!!(Ws(h)?h:h.parentElement).getAttribute("data-slate-zero-width");return l.setStart(c,g?1:f),l.setEnd(h,v?1:p),l},toDOMPoint:function(t,n){var r,o,i,a=da(e.node(t,n.path),1)[0],s=ol.toDOMNode(t,a);e.void(t,{at:n})&&(n={path:n.path,offset:0});var l=Array.from(s.querySelectorAll("[data-slate-string], [data-slate-zero-width]")),u=0;try{for(var c=fa(l),f=c.next();!f.done;f=c.next()){var d=f.value,h=d.childNodes[0];if(null!=h&&null!=h.textContent){var p=h.textContent.length,g=d.getAttribute("data-slate-length"),v=u+(null==g?p:parseInt(g,10));if(n.offset<=v){i=[h,Math.min(p,Math.max(0,n.offset-u))];break}u=v}}}catch(e){r={error:e}}finally{try{f&&!f.done&&(o=c.return)&&o.call(c)}finally{if(r)throw r.error}}if(!i)throw new Error("Cannot resolve a DOM point from Slate point: "+JSON.stringify(n));return i},toSlateNode:function(e,t){var n=Ws(t)?t:t.parentElement;n&&!n.hasAttribute("data-slate-node")&&(n=n.closest("[data-slate-node]"));var r=n?Ns.get(n):null;if(!r)throw new Error("Cannot resolve a Slate node from DOM node: "+n);return r},findEventRange:function(t,n){"nativeEvent"in n&&(n=n.nativeEvent);var r=n.clientX,o=n.clientY,i=n.target;if(null==r||null==o)throw new Error("Cannot resolve a Slate range from a DOM event: "+n);var a,s=ol.toSlateNode(t,n.target),l=ol.findPath(t,s);if(e.isVoid(t,s)){var u=i.getBoundingClientRect(),c=t.isInline(s)?r-u.left<u.left+u.width-r:o-u.top<u.top+u.height-o,f=e.point(t,l,{edge:c?"start":"end"}),d=c?e.before(t,f):e.after(t,f);if(d)return e.range(t,d)}var h=this.getWindow(t).document;if(h.caretRangeFromPoint)a=h.caretRangeFromPoint(r,o);else{var p=h.caretPositionFromPoint(r,o);p&&((a=h.createRange()).setStart(p.offsetNode,p.offset),a.setEnd(p.offsetNode,p.offset))}if(!a)throw new Error("Cannot resolve a Slate range from a DOM event: "+n);return ol.toSlateRange(t,a,{exactMatch:!1,suppressThrow:!1})},toSlateRange:function(t,n,r){var i,a,s,l,u,c=r.exactMatch,f=r.suppressThrow;if((zs(n)?n.anchorNode:n.startContainer)&&(zs(n)?(i=n.anchorNode,a=n.anchorOffset,s=n.focusNode,l=n.focusOffset,u=nl&&window.document.activeElement&&window.document.activeElement.shadowRoot?n.anchorNode===n.focusNode&&n.anchorOffset===n.focusOffset:n.isCollapsed):(i=n.startContainer,a=n.startOffset,s=n.endContainer,l=n.endOffset,u=n.collapsed)),null==i||null==s||null==a||null==l)throw new Error("Cannot resolve a Slate range from DOM range: "+n);var d=ol.toSlatePoint(t,[i,a],{exactMatch:c,suppressThrow:f});if(!d)return null;var h=u?d:ol.toSlatePoint(t,[s,l],{exactMatch:c,suppressThrow:f});if(!h)return null;var p={anchor:d,focus:h};return o.isExpanded(p)&&o.isForward(p)&&Ws(s)&&e.void(t,{at:p.focus,mode:"highest"})&&(p=e.unhangRange(t,p,{voids:!0})),p},toSlatePoint:function(e,t,n){var r,o=n.exactMatch,i=n.suppressThrow,a=da(o?t:function(e){var t,n=da(e,2),r=n[0],o=n[1];if(Ws(r)&&r.childNodes.length){var i=o===r.childNodes.length,a=i?o-1:o;for(r=(t=da(Ks(r,a,i?"backward":"forward"),2))[0],i=(a=t[1])<o;Ws(r)&&r.childNodes.length;){var s=i?r.childNodes.length-1:0;r=qs(r,s,i?"backward":"forward")}o=i&&null!=r.textContent?r.textContent.length:0}return[r,o]}(t),2),s=a[0],l=a[1],u=s.parentNode,f=null,d=0;if(u){var h=u.closest('[data-slate-void="true"]'),p=u.closest("[data-slate-leaf]"),g=null;if(p){f=p.closest('[data-slate-node="text"]');var v=ol.getWindow(e).document.createRange();v.setStart(f,0),v.setEnd(s,l);var y=v.cloneContents();ha(ha([],da(c(y.querySelectorAll("[data-slate-zero-width]")))),da(c(y.querySelectorAll("[contenteditable=false]")))).forEach((function(e){e.parentNode.removeChild(e)})),d=y.textContent.length,g=f}else h&&((p=h.querySelector("[data-slate-leaf]"))?(f=p.closest('[data-slate-node="text"]'),d=(g=p).textContent.length,g.querySelectorAll("[data-slate-zero-width]").forEach((function(e){d-=e.textContent.length}))):d=1);g&&d===g.textContent.length&&(u.hasAttribute("data-slate-zero-width")||Qs&&(null===(r=g.textContent)||void 0===r?void 0:r.endsWith("\n")))&&d--}if(!f){if(i)return null;throw new Error("Cannot resolve a Slate point from DOM point: "+t)}var m=ol.toSlateNode(e,f);return{path:ol.findPath(e,m),offset:d}},hasRange:function(t,n){var r=n.anchor,o=n.focus;return e.hasPath(t,r.path)&&e.hasPath(t,o.path)},getNodeType:function(e){return i.isElement(e)?e.type:""},checkNodeType:function(e,t){return this.getNodeType(e)===t},getNodesStr:function(e){return e.map((function(e){return r.string(e)})).join("")},getSelectedElems:function(t){var n,r,o=[],a=e.nodes(t,{universal:!0});try{for(var s=fa(a),l=s.next();!l.done;l=s.next()){var u=da(l.value,1)[0];i.isElement(u)&&o.push(u)}}catch(e){n={error:e}}finally{try{l&&!l.done&&(r=s.return)&&r.call(s)}finally{if(n)throw n.error}}return o},getSelectedNodeByType:function(t,n){var r=this,o=da(e.nodes(t,{match:function(e){return r.checkNodeType(e,n)},universal:!0}),1),i=o[0];return null==i?null:i[0]},getSelectedTextNode:function(t){var n=da(e.nodes(t,{match:function(e){return a.isText(e)},universal:!0}),1),r=n[0];return null==r?null:r[0]},isNodeSelected:function(t,n){var r=da(e.nodes(t,{match:function(e){return e===n},universal:!0}),1),o=r[0];return null!=o&&da(o,1)[0]===n},isSelectionAtLineEnd:function(t,n){var r=t.selection;return!!r&&(e.isEnd(t,r.anchor,n)||e.isEnd(t,r.focus,n))},getTextarea:function(e){var t=fs.get(e);if(null==t)throw new Error("Cannot find textarea instance by editor");return t},getToolbar:function(e){return ps.get(e)||null},getHoverbar:function(e){return vs.get(e)||null},normalizeContent:function(e){e.children.forEach((function(t,n){e.normalizeNode([t,[n]])}))},getLeftLengthOfMaxLength:function(e){var t=e.getConfig(),n=t.maxLength,r=t.onMaxLength;if("number"!=typeof n||n<=0)return 1/0;var o=n-e.getText().replace(/\r|\n|(\r\n)/g,"").length;return o<=0&&r&&r(e),o},cleanExposedTexNodeInSelectionBlock:function(t){var n,r,o,a,s=ol.getTextarea(t).$textArea,l=null==s?void 0:s[0].childNodes;if(l)try{for(var u=fa(Array.from(l)),c=u.next();!c.done;c=u.next()){var d=c.value;if(3!==d.nodeType)break;d.remove()}}catch(e){n={error:e}}finally{try{c&&!c.done&&(r=u.return)&&r.call(u)}finally{if(n)throw n.error}}var h=e.nodes(t,{match:function(e){return!(!i.isElement(e)||t.isInline(e))},universal:!0});try{for(var p=fa(h),g=p.next();!g.done;g=p.next()){var v=g.value;if(null!=v){var y=v[0];Js(ol.toDOMNode(t,y),(function(e,t){var n=f(t);n.attr("data-slate-string")||n.attr("data-slate-zero-width")||n.attr("data-w-e-reserve")||t.removeChild(e)}))}}}catch(e){o={error:e}}finally{try{g&&!g.done&&(a=p.return)&&a.call(p)}finally{if(o)throw o.error}}},isLastNode:function(e,t){var n=e.children||[];return n[n.length-1]===t},genEmptyParagraph:function(){return{type:"paragraph",children:[{text:""}]}},isSelectedVoidNode:function(t){var n,r,o=e.nodes(t,{match:function(e){return t.isVoid(e)}}),i=0;try{for(var a=fa(o),s=a.next();!s.done;s=a.next()){s.value;i++}}catch(e){n={error:e}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return i>0},isSelectedEmptyParagraph:function(e){var t=e.selection;if(null==t)return!1;if(o.isExpanded(t))return!1;var n=ol.getSelectedNodeByType(e,"paragraph");if(null===n)return!1;var r=n.children;return 1===r.length&&(""===r[0].text||void 0)},isEmptyPath:function(t,n){var r=e.node(t,n);if(null==r)return!1;var o=da(r,1)[0].children;if(1===o.length&&""===o[0].text)return!0;return!1}},il=1,al={};var sl={};function ll(e,t){var n=e.key,r=e.factory,o=e.config,i=ca(ca({},o),t||{});if(null!=sl[n])throw new Error("Duplicated key '"+n+"' in menu items");sl[n]=r,function(e,t){null!=t&&(al[e]=t)}(n,i)}var ul=Tn.filter,cl=fi("filter");cr({target:"Array",proto:!0,forced:!cl},{filter:function(e){return ul(this,e,arguments.length>1?arguments[1]:void 0)}});var fl="\t\n\v\f\r                　\u2028\u2029\ufeff",dl=ve("".replace),hl="["+fl+"]",pl=RegExp("^"+hl+hl+"*"),gl=RegExp(hl+hl+"*$"),vl=function(e){return function(t){var n=kr(me(t));return 1&e&&(n=dl(n,pl,"")),2&e&&(n=dl(n,gl,"")),n}},yl={start:vl(1),end:vl(2),trim:vl(3)},ml=Vt.PROPER,bl=yl.trim;cr({target:"String",proto:!0,forced:function(e){return Fe((function(){return!!fl[e]()||"​᠎"!=="​᠎"[e]()||ml&&fl[e].name!==e}))}("trim")},{trim:function(){return bl(this)}});var wl=[];function xl(e){wl.push(e)}var Sl={};function kl(e){var t=e.type,n=e.elemToHtml;Sl[t||""]=n}function El(e,t,n){var r=n.isInline(e)?"span":"div";return"<"+r+">"+t+"</"+r+">"}function Ol(t,n){var r=t.type,o=void 0===r?"":r,i=t.children,a=void 0===i?[]:i,s=e.isVoid(n,t),l="";s||(l=a.map((function(e){return sc(e,n)})).join(""));var u=function(e){return Sl[e]||El}(o),c=u(t,l,n),f="";if(f="string"==typeof c?c:c.html||"",s||wl.forEach((function(e){return f=e(t,f)})),"string"==typeof c)return f;var d=c.prefix,h=void 0===d?"":d,p=c.suffix,g=void 0===p?"":p;return h&&(f=h+f),g&&(f+=g),f}var Cl,Nl,Tl,Ll,Ml=se.Promise,Pl=ze("species"),Dl=function(e){var t=Le(e),n=mt.f;Ke&&t&&!t[Pl]&&n(t,Pl,{configurable:!0,get:function(){return this}})},Rl=se.TypeError,jl=ze("species"),Al=function(e,t){var n,r=et(e).constructor;return void 0===r||null==(n=et(r)[jl])?t:function(e){if(Sn(e))return e;throw Rl(st(e)+" is not a constructor")}(n)},Fl=ve([].slice),Il=/(?:ipad|iphone|ipod).*applewebkit/i.test(Me),_l="process"==Kt(se.process),Bl=se.setImmediate,$l=se.clearImmediate,Wl=se.process,Vl=se.Dispatch,zl=se.Function,Hl=se.MessageChannel,Ul=se.String,Kl=0,ql={};try{Cl=se.location}catch(e){}var Gl=function(e){if(Se(ql,e)){var t=ql[e];delete ql[e],t()}},Jl=function(e){return function(){Gl(e)}},Yl=function(e){Gl(e.data)},Xl=function(e){se.postMessage(Ul(e),Cl.protocol+"//"+Cl.host)};Bl&&$l||(Bl=function(e){var t=Fl(arguments,1);return ql[++Kl]=function(){_i(Ne(e)?e:zl(e),void 0,t)},Nl(Kl),Kl},$l=function(e){delete ql[e]},_l?Nl=function(e){Wl.nextTick(Jl(e))}:Vl&&Vl.now?Nl=function(e){Vl.now(Jl(e))}:Hl&&!Il?(Ll=(Tl=new Hl).port2,Tl.port1.onmessage=Yl,Nl=rn(Ll.postMessage,Ll)):se.addEventListener&&Ne(se.postMessage)&&!se.importScripts&&Cl&&"file:"!==Cl.protocol&&!Fe(Xl)?(Nl=Xl,se.addEventListener("message",Yl,!1)):Nl="onreadystatechange"in Ye("script")?function(e){Rr.appendChild(Ye("script")).onreadystatechange=function(){Rr.removeChild(this),Gl(e)}}:function(e){setTimeout(Jl(e),0)});var Ql,Zl,eu,tu,nu,ru,ou,iu,au={set:Bl,clear:$l},su=/ipad|iphone|ipod/i.test(Me)&&void 0!==se.Pebble,lu=/web0s(?!.*chrome)/i.test(Me),uu=$n.f,cu=au.set,fu=se.MutationObserver||se.WebKitMutationObserver,du=se.document,hu=se.process,pu=se.Promise,gu=uu(se,"queueMicrotask"),vu=gu&&gu.value;vu||(Ql=function(){var e,t;for(_l&&(e=hu.domain)&&e.exit();Zl;){t=Zl.fn,Zl=Zl.next;try{t()}catch(e){throw Zl?tu():eu=void 0,e}}eu=void 0,e&&e.enter()},Il||_l||lu||!fu||!du?!su&&pu&&pu.resolve?((ou=pu.resolve(void 0)).constructor=pu,iu=rn(ou.then,ou),tu=function(){iu(Ql)}):_l?tu=function(){hu.nextTick(Ql)}:(cu=rn(cu,se),tu=function(){cu(Ql)}):(nu=!0,ru=du.createTextNode(""),new fu(Ql).observe(ru,{characterData:!0}),tu=function(){ru.data=nu=!nu}));var yu,mu,bu,wu,xu=vu||function(e){var t={fn:e,next:void 0};eu&&(eu.next=t),Zl||(Zl=t,tu()),eu=t},Su=function(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=ut(t),this.reject=ut(n)},ku={f:function(e){return new Su(e)}},Eu=function(e){try{return{error:!1,value:e()}}catch(e){return{error:!0,value:e}}},Ou="object"==typeof window,Cu=au.set,Nu=ze("species"),Tu="Promise",Lu=_t.getterFor(Tu),Mu=_t.set,Pu=_t.getterFor(Tu),Du=Ml&&Ml.prototype,Ru=Ml,ju=Du,Au=se.TypeError,Fu=se.document,Iu=se.process,_u=ku.f,Bu=_u,$u=!!(Fu&&Fu.createEvent&&se.dispatchEvent),Wu=Ne(se.PromiseRejectionEvent),Vu=!1,zu=lr(Tu,(function(){var e=Ot(Ru),t=e!==String(Ru);if(!t&&66===Ae)return!0;if(Ae>=51&&/native code/.test(e))return!1;var n=new Ru((function(e){e(1)})),r=function(e){e((function(){}),(function(){}))};return(n.constructor={})[Nu]=r,!(Vu=n.then((function(){}))instanceof r)||!t&&Ou&&!Wu})),Hu=zu||!yo((function(e){Ru.all(e).catch((function(){}))})),Uu=function(e){var t;return!(!qe(e)||!Ne(t=e.then))&&t},Ku=function(e,t){if(!e.notified){e.notified=!0;var n=e.reactions;xu((function(){for(var r=e.value,o=1==e.state,i=0;n.length>i;){var a,s,l,u=n[i++],c=o?u.ok:u.fail,f=u.resolve,d=u.reject,h=u.domain;try{c?(o||(2===e.rejection&&Yu(e),e.rejection=1),!0===c?a=r:(h&&h.enter(),a=c(r),h&&(h.exit(),l=!0)),a===u.promise?d(Au("Promise-chain cycle")):(s=Uu(a))?nt(s,a,f,d):f(a)):d(r)}catch(e){h&&!l&&h.exit(),d(e)}}e.reactions=[],e.notified=!1,t&&!e.rejection&&Gu(e)}))}},qu=function(e,t,n){var r,o;$u?((r=Fu.createEvent("Event")).promise=t,r.reason=n,r.initEvent(e,!1,!0),se.dispatchEvent(r)):r={promise:t,reason:n},!Wu&&(o=se["on"+e])?o(r):"unhandledrejection"===e&&function(e,t){var n=se.console;n&&n.error&&(1==arguments.length?n.error(e):n.error(e,t))}("Unhandled promise rejection",n)},Gu=function(e){nt(Cu,se,(function(){var t,n=e.facade,r=e.value;if(Ju(e)&&(t=Eu((function(){_l?Iu.emit("unhandledRejection",r,n):qu("unhandledrejection",n,r)})),e.rejection=_l||Ju(e)?2:1,t.error))throw t.value}))},Ju=function(e){return 1!==e.rejection&&!e.parent},Yu=function(e){nt(Cu,se,(function(){var t=e.facade;_l?Iu.emit("rejectionHandled",t):qu("rejectionhandled",t,e.value)}))},Xu=function(e,t,n){return function(r){e(t,r,n)}},Qu=function(e,t,n){e.done||(e.done=!0,n&&(e=n),e.value=t,e.state=2,Ku(e,!0))},Zu=function(e,t,n){if(!e.done){e.done=!0,n&&(e=n);try{if(e.facade===t)throw Au("Promise can't be resolved itself");var r=Uu(t);r?xu((function(){var n={done:!1};try{nt(r,t,Xu(Zu,n,e),Xu(Qu,n,e))}catch(t){Qu(n,t,e)}})):(e.value=t,e.state=1,Ku(e,!1))}catch(t){Qu({done:!1},t,e)}}};if(zu&&(ju=(Ru=function(e){Fa(this,ju),ut(e),nt(yu,this);var t=Lu(this);try{e(Xu(Zu,t),Xu(Qu,t))}catch(e){Qu(t,e)}}).prototype,(yu=function(e){Mu(this,{type:Tu,done:!1,notified:!1,parent:!1,reactions:[],rejection:!1,state:0,value:void 0})}).prototype=ba(ju,{then:function(e,t){var n=Pu(this),r=n.reactions,o=_u(Al(this,Ru));return o.ok=!Ne(e)||e,o.fail=Ne(t)&&t,o.domain=_l?Iu.domain:void 0,n.parent=!0,r[r.length]=o,0!=n.state&&Ku(n,!1),o.promise},catch:function(e){return this.then(void 0,e)}}),mu=function(){var e=new yu,t=Lu(e);this.promise=e,this.resolve=Xu(Zu,t),this.reject=Xu(Qu,t)},ku.f=_u=function(e){return e===Ru||e===bu?new mu(e):Bu(e)},Ne(Ml)&&Du!==Object.prototype)){wu=Du.then,Vu||(zt(Du,"then",(function(e,t){var n=this;return new Ru((function(e,t){nt(wu,n,e,t)})).then(e,t)}),{unsafe:!0}),zt(Du,"catch",ju.catch,{unsafe:!0}));try{delete Du.constructor}catch(e){}zo&&zo(Du,ju)}cr({global:!0,wrap:!0,forced:zu},{Promise:Ru}),_o(Ru,Tu,!1),Dl(Tu),bu=Le(Tu),cr({target:Tu,stat:!0,forced:zu},{reject:function(e){var t=_u(this);return nt(t.reject,void 0,e),t.promise}}),cr({target:Tu,stat:!0,forced:zu},{resolve:function(e){return function(e,t){if(et(e),qe(t)&&t.constructor===e)return t;var n=ku.f(e);return(0,n.resolve)(t),n.promise}(this,e)}}),cr({target:Tu,stat:!0,forced:Hu},{all:function(e){var t=this,n=_u(t),r=n.resolve,o=n.reject,i=Eu((function(){var n=ut(t.resolve),i=[],a=0,s=1;ja(e,(function(e){var l=a++,u=!1;s++,nt(n,t,e).then((function(e){u||(u=!0,i[l]=e,--s||r(i))}),o)})),--s||r(i)}));return i.error&&o(i.value),n.promise},race:function(e){var t=this,n=_u(t),r=n.reject,o=Eu((function(){var o=ut(t.resolve);ja(e,(function(e){nt(o,t,e).then(n.resolve,r)}))}));return o.error&&r(o.value),n.promise}});var ec=yi.UNSUPPORTED_Y,tc=Math.min,nc=[].push,rc=ve(/./.exec),oc=ve(nc),ic=ve("".slice);function ac(e){Promise.resolve().then(e)}function sc(e,t){return i.isElement(e)?Ol(e,t):function(e,t){var n=e.text;if(null==n)throw new Error("Current node is not slate Text "+JSON.stringify(e));var r=n;r=function(e){return e.replace(/ {2}/g," &nbsp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/®/g,"&reg;").replace(/©/g,"&copy;").replace(/™/g,"&trade;")}(r);var o=ol.getParentsNodes(t,e).some((function(e){return"pre"===ol.getNodeType(e)}));if(o||(r=r.replace(/\r\n|\r|\n/g,"<br>")),o&&(r=r.replace(/&nbsp;/g," ")),""===r){var i=ol.getParentNode(null,e);if(!i||0!==i.children.length)return r;r="<br>"}return wl.forEach((function(t){return r=t(e,r)})),r}(e,t)}function lc(e){return"w-e-element-"+e}Wi("split",(function(e,t,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var r=kr(me(this)),o=void 0===n?4294967295:n>>>0;if(0===o)return[];if(void 0===e)return[r];if(!Ur(e))return nt(t,r,e,o);for(var i,a,s,l=[],u=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),c=0,f=new RegExp(e.source,u+"g");(i=nt(Ri,f,r))&&!((a=f.lastIndex)>c&&(oc(l,ic(r,c,i.index)),i.length>1&&i.index<r.length&&_i(nc,l,Sa(i,1)),s=i[0].length,c=a,l.length>=o));)f.lastIndex===i.index&&f.lastIndex++;return c===r.length?!s&&rc(f,"")||oc(l,""):oc(l,ic(r,c)),l.length>o?Sa(l,0,o):l}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:nt(t,this,e,n)}:t,[function(t,n){var o=me(this),i=null==t?void 0:ct(t,e);return i?nt(i,t,o,n):nt(r,kr(o),t,n)},function(e,o){var i=et(this),a=kr(e),s=n(r,i,a,o,r!==t);if(s.done)return s.value;var l=Al(i,RegExp),u=i.unicode,c=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(ec?"g":"y"),f=new l(ec?"^(?:"+i.source+")":i,c),d=void 0===o?4294967295:o>>>0;if(0===d)return[];if(0===a.length)return null===Qi(f,a)?[a]:[];for(var h=0,p=0,g=[];p<a.length;){f.lastIndex=ec?0:p;var v,y=Qi(f,ec?ic(a,p):a);if(null===y||(v=tc(dn(f.lastIndex+(ec?p:0)),a.length))===h)p=zi(a,p,u);else{if(oc(g,ic(a,h,p)),g.length===d)return g;for(var m=1;m<=y.length-1;m++)if(oc(g,y[m]),g.length===d)return g;p=h=v}}return oc(g,ic(a,h)),g}]}),!!Fe((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),ec);var uc=function(e,t){var n=(t.top+t.bottom)/2;return e.top<=n&&e.bottom>=n},cc=function(e,t,n){var r=ol.toDOMRange(e,t).getBoundingClientRect(),o=ol.toDOMRange(e,n).getBoundingClientRect();return uc(r,o)&&uc(o,r)},fc=["span","b","strong","i","em","s","strike","u","font","sub","sup"],dc=[];function hc(e){dc.push(e)}var pc=[];function gc(e){pc.push(e)}var vc={};function yc(e){var t=e.selector,n=e.parseElemHtml;vc[t]=n}var mc=mt.f,bc=Xn.f,wc=_t.enforce,xc=ze("match"),Sc=se.RegExp,kc=Sc.prototype,Ec=se.SyntaxError,Oc=ve(Er),Cc=ve(kc.exec),Nc=ve("".charAt),Tc=ve("".replace),Lc=ve("".indexOf),Mc=ve("".slice),Pc=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,Dc=/a/g,Rc=/a/g,jc=new Sc(Dc)!==Dc,Ac=yi.MISSED_STICKY,Fc=yi.UNSUPPORTED_Y,Ic=Ke&&(!jc||Ac||bi||xi||Fe((function(){return Rc[xc]=!1,Sc(Dc)!=Dc||Sc(Rc)==Rc||"/a/i"!=Sc(Dc,"i")})));if(lr("RegExp",Ic)){for(var _c=function(e,t){var n,r,o,i,a,s,l=rt(kc,this),u=Ur(e),c=void 0===t,f=[],d=e;if(!l&&u&&c&&e.constructor===_c)return e;if((u||rt(kc,e))&&(e=e.source,c&&(t="flags"in d?d.flags:Oc(d))),e=void 0===e?"":kr(e),t=void 0===t?"":kr(t),d=e,bi&&"dotAll"in Dc&&(r=!!t&&Lc(t,"s")>-1)&&(t=Tc(t,/s/g,"")),n=t,Ac&&"sticky"in Dc&&(o=!!t&&Lc(t,"y")>-1)&&Fc&&(t=Tc(t,/y/g,"")),xi&&(i=function(e){for(var t,n=e.length,r=0,o="",i=[],a={},s=!1,l=!1,u=0,c="";r<=n;r++){if("\\"===(t=Nc(e,r)))t+=Nc(e,++r);else if("]"===t)s=!1;else if(!s)switch(!0){case"["===t:s=!0;break;case"("===t:Cc(Pc,Mc(e,r+1))&&(r+=2,l=!0),o+=t,u++;continue;case">"===t&&l:if(""===c||Se(a,c))throw new Ec("Invalid capture group name");a[c]=!0,i[i.length]=[c,u],l=!1,c="";continue}l?c+=t:o+=t}return[o,i]}(e),e=i[0],f=i[1]),a=Ia(Sc(e,t),l?this:kc,_c),(r||o||f.length)&&(s=wc(a),r&&(s.dotAll=!0,s.raw=_c(function(e){for(var t,n=e.length,r=0,o="",i=!1;r<=n;r++)"\\"!==(t=Nc(e,r))?i||"."!==t?("["===t?i=!0:"]"===t&&(i=!1),o+=t):o+="[\\s\\S]":o+=t+Nc(e,++r);return o}(e),n)),o&&(s.sticky=!0),f.length&&(s.groups=f)),e!==d)try{wt(a,"source",""===d?"(?:)":d)}catch(e){}return a},Bc=function(e){e in _c||mc(_c,e,{configurable:!0,get:function(){return Sc[e]},set:function(t){Sc[e]=t}})},$c=bc(Sc),Wc=0;$c.length>Wc;)Bc($c[Wc++]);kc.constructor=_c,_c.prototype=kc,zt(se,"RegExp",_c)}Dl("RegExp");var Vc=new RegExp(String.fromCharCode(160),"g");function zc(e){return e.replace(Vc," ")}function Hc(e,t){var n=e.length;if(n){var r=e[n-1];if(a.isText(r)){var o=Object.keys(r);if(1===o.length&&"text"===o[0])return r.text=r.text+t,!0}}return!1}function Uc(e,t,n){return{type:"paragraph",children:[{text:f(e).text().replace(/\s+/gm," ")}]}}function Kc(t,n){var r=function(e,t){var n=[];if(null!=e.attr("data-w-e-is-void"))return n;var r=e[0].childNodes;return 1===r.length&&"BR"===r[0].nodeName?(n.push({text:""}),n):(r.forEach((function(e){if(e.nodeType!==Bs.ELEMENT_NODE)if(e.nodeType!==Bs.TEXT_NODE);else{var r=e.textContent||"";if(""===r.trim()&&r.indexOf("\n")>=0)return;r&&(r=zc(r),Hc(n,r)||n.push({text:r}))}else{if("BR"===e.nodeName)return void(Hc(n,"\n")||n.push({text:"\n"}));var o=Gc(f(e),t);Array.isArray(o)?o.forEach((function(e){return n.push(e)})):n.push(o)}})),n)}(t,n),o=function(e){for(var t in vc)if(e[0].matches(t))return vc[t];return Uc}(t),i=o(t[0],r,n);return Array.isArray(i)||(i=[i]),i.forEach((function(o){e.isVoid(n,o)||(0===r.length&&(o.children=[{text:t.text().replace(/\s+/gm," ")}]),pc.forEach((function(e){o=e(t[0],o,n)})))})),i}function qc(e,t){0===e.parents("pre").length&&(e[0].innerHTML=e[0].innerHTML.replace(/\s+/gm," ").replace(/<br>/g,"\n"));var n=e[0].textContent||"";n=function(e){return e.replace(/&nbsp;/g," ").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&reg;/g,"®").replace(/&copy;/g,"©").replace(/&trade;/g,"™").replace(/&quot;/g,'"')}(n);var r={text:n=zc(n)};return pc.forEach((function(n){r=n(e[0],r,t)})),r}function Gc(e,t){dc.forEach((function(t){var n=t.selector,r=t.preParseHtml;e[0].matches(n)&&(e=f(r(e[0])))}));var n=Ys(e);return"span"===n?e.attr("data-w-e-type")?Kc(e,t):qc(e,t):"code"===n?"pre"===Ys(e.parent())?Kc(e,t):qc(e,t):fc.includes(n)?qc(e,t):Kc(e,t)}function Jc(e,t,n){var r=f(n);return!!r.attr(t)||(r.attr(t,"true"),e.on("destroyed",(function(){r.removeAttr(t)})),!1)}function Yc(e,t){void 0===t&&(t="");var n=[];""===t&&(t="<p><br></p>"),0!==t.indexOf("<")&&(t=t.split(/\n/).map((function(e){return"<p>"+e+"</p>"})).join(""));var r=f("<div>"+t+"</div>");return Array.from(r.children()).forEach((function(t){var r=Gc(f(t),e);Array.isArray(r)?r.forEach((function(e){return n.push(e)})):n.push(r)})),n}var Xc=mt.f,Qc=Ma.fastKey,Zc=_t.set,ef=_t.getterFor,tf={getConstructor:function(e,t,n,r){var o=e((function(e,o){Fa(e,i),Zc(e,{type:t,index:Br(null),first:void 0,last:void 0,size:0}),Ke||(e.size=0),null!=o&&ja(o,e[r],{that:e,AS_ENTRIES:n})})),i=o.prototype,a=ef(t),s=function(e,t,n){var r,o,i=a(e),s=l(e,t);return s?s.value=n:(i.last=s={index:o=Qc(t,!0),key:t,value:n,previous:r=i.last,next:void 0,removed:!1},i.first||(i.first=s),r&&(r.next=s),Ke?i.size++:e.size++,"F"!==o&&(i.index[o]=s)),e},l=function(e,t){var n,r=a(e),o=Qc(t);if("F"!==o)return r.index[o];for(n=r.first;n;n=n.next)if(n.key==t)return n};return ba(i,{clear:function(){for(var e=a(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,Ke?e.size=0:this.size=0},delete:function(e){var t=this,n=a(t),r=l(t,e);if(r){var o=r.next,i=r.previous;delete n.index[r.index],r.removed=!0,i&&(i.next=o),o&&(o.previous=i),n.first==r&&(n.first=o),n.last==r&&(n.last=i),Ke?n.size--:t.size--}return!!r},forEach:function(e){for(var t,n=a(this),r=rn(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!l(this,e)}}),ba(i,n?{get:function(e){var t=l(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),Ke&&Xc(i,"size",{get:function(){return a(this).size}}),o},setStrong:function(e,t,n){var r=t+" Iterator",o=ef(t),i=ef(r);Yo(e,t,(function(e,t){Zc(this,{type:r,target:e,state:o(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),Dl(t)}};_a("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),tf);var nf=new Set(["doctype","!doctype","meta","script","style","link","frame","iframe","title","svg"]);function rf(e,t){e.isInline(t)?(e.insertNode(t),"link"===t.type&&e.insertFragment([{text:""}])):s.insertNodes(e,t,{mode:"highest"})}var of=function(t){var l=t,u=l.onChange,c=l.insertText,d=l.apply,h=l.deleteBackward;return l.insertText=function(e){l.getConfig().readOnly||c(e)},l.apply=function(t){var r,o,i,a,s,u,c,f,h=[];switch(t.type){case"insert_text":case"remove_text":case"set_node":try{for(var p=fa(e.levels(l,{at:t.path})),g=p.next();!g.done;g=p.next()){var v=da(g.value,2),y=v[0],m=v[1],b=ol.findKey(l,y);h.push([m,b])}}catch(e){r={error:e}}finally{try{g&&!g.done&&(o=p.return)&&o.call(p)}finally{if(r)throw r.error}}break;case"insert_node":case"remove_node":case"merge_node":case"split_node":try{for(var w=fa(e.levels(l,{at:n.parent(t.path)})),x=w.next();!x.done;x=w.next()){var S=da(x.value,2);y=S[0],m=S[1],b=ol.findKey(l,y);h.push([m,b])}}catch(e){i={error:e}}finally{try{x&&!x.done&&(a=w.return)&&a.call(w)}finally{if(i)throw i.error}}break;case"move_node":try{for(var k=fa(e.levels(l,{at:n.common(n.parent(t.path),n.parent(t.newPath))})),E=k.next();!E.done;E=k.next()){var O=da(E.value,2);y=O[0],m=O[1],b=ol.findKey(l,y);h.push([m,b])}}catch(e){s={error:e}}finally{try{E&&!E.done&&(u=k.return)&&u.call(k)}finally{if(s)throw s.error}}}d(t);try{for(var C=fa(h),N=C.next();!N.done;N=C.next()){var T=da(N.value,2);m=T[0],b=T[1],y=da(e.node(l,m),1)[0];Ms.set(y,b)}}catch(e){c={error:e}}finally{try{N&&!N.done&&(f=C.return)&&f.call(C)}finally{if(c)throw c.error}}},l.deleteBackward=function(n){if("line"!==n)return h(n);if(t.selection&&o.isCollapsed(t.selection)){var r=e.above(t,{match:function(n){return e.isBlock(t,n)},at:t.selection});if(r){var i=da(r,2)[1],a=e.range(t,i,t.selection.anchor),u=function(t,n){var r=e.range(t,o.end(n)),i=Array.from(e.positions(t,{at:n})),a=0,s=i.length,l=Math.floor(s/2);if(cc(t,e.range(t,i[a]),r))return e.range(t,i[a],r);if(i.length<2)return e.range(t,i[i.length-1],r);for(;l!==i.length&&l!==a;)cc(t,e.range(t,i[l]),r)?s=l:a=l,l=Math.floor((a+s)/2);return e.range(t,i[s],r)}(l,a);o.isCollapsed(u)||s.delete(t,{at:u})}}},l.onChange=function(){var e=l.selection;null!=e&&Rs.set(l,e),l.emit("change"),u()},l.handleTab=function(){l.insertText("    ")},l.getHtml=function(){var e=l.children;return(void 0===e?[]:e).map((function(e){return sc(e,l)})).join("")},l.getText=function(){var e=l.children;return(void 0===e?[]:e).map((function(e){return r.string(e)})).join("\n")},l.getSelectionText=function(){var n=l.selection;return null==n?"":e.string(t,n)},l.getElemsByType=function(t,n){var r,o;void 0===n&&(n=!1);var a=[],s=e.nodes(l,{at:[],universal:!0});try{for(var u=fa(s),c=u.next();!c.done;c=u.next()){var f=da(c.value,1)[0];if(i.isElement(f))if(n?f.type.indexOf(t)>=0:f.type===t){var d=lc(ol.findKey(l,f).id);a.push(ca(ca({},f),{id:d}))}}}catch(e){r={error:e}}finally{try{c&&!c.done&&(o=u.return)&&o.call(u)}finally{if(r)throw r.error}}return a},l.getElemsByTypePrefix=function(e){return l.getElemsByType(e,!0)},l.isEmpty=function(){var e=l.children,t=void 0===e?[]:e;if(t.length>1)return!1;var n=t[0];if(null==n)return!0;if(i.isElement(n)&&"paragraph"===n.type){var r=n.children,o=void 0===r?[]:r;if(o.length>1)return!1;var s=o[0];if(null==s)return!0;if(a.isText(s)&&""===s.text)return!0}return!1},l.clear=function(){s.delete(l,{at:{anchor:e.start(l,[]),focus:e.end(l,[])}}),0===l.children.length&&s.insertNodes(l,[{type:"paragraph",children:[{text:""}]}])},l.getParentNode=function(e){return ol.getParentNode(l,e)},l.dangerouslyInsertHtml=function(e,t){if(void 0===e&&(e=""),void 0===t&&(t=!1),e){var n=document.createElement("div");n.innerHTML=e;var r=Array.from(n.childNodes);if(r=r.filter((function(e){var t=e.nodeType,n=e.nodeName;return t===Bs.TEXT_NODE||t===Bs.ELEMENT_NODE&&!nf.has(n.toLowerCase())})),0!==r.length){var o=l.selection;if(null!=o){var i=null;if(ol.isSelectedEmptyParagraph(l)&&!t)i=[o.focus.path[0]];n.setAttribute("hidden","true"),document.body.appendChild(n);var a=0;r.forEach((function(e){var t=e.nodeType,n=e.nodeName,r=e.textContent,o=void 0===r?"":r;if(t!==Bs.TEXT_NODE)if("BR"!==n){var i=e,s=!1;if(fc.includes(n.toLowerCase()))s=!0;else for(var u in vc)if(i.matches(u)){s=!0;break}if(s){var c=Gc(f(i),l);return Array.isArray(c)?(c.forEach((function(e){return rf(l,e)})),a++):(rf(l,c),a++),void(ol.isSelectedVoidNode(l)&&l.move(1))}var d=window.getComputedStyle(i).display;ol.isSelectedEmptyParagraph(l)||d.indexOf("inline")<0&&l.insertBreak(),l.dangerouslyInsertHtml(i.innerHTML,!0)}else l.insertText("\n");else{if(!o||!o.trim())return;l.insertNode({text:o})}})),a&&i&&ol.isEmptyPath(l,i)&&s.removeNodes(l,{at:i}),n.remove()}}}},l.setHtml=function(t){void 0===t&&(t="");var n=l.isDisabled(),r=l.isFocused(),o=JSON.stringify(l.selection);l.enable(),l.focus(),l.clear();var i=Yc(l,t);if(s.insertFragment(l,i),r||(l.deselect(),l.blur()),n&&(l.deselect(),l.disable()),l.isFocused())try{l.select(JSON.parse(o))}catch(t){l.select(e.start(l,[]))}},l},af=function(t){var n=t,r=n.insertText;return n.insertFragment,n.setFragmentData=function(t){var r=n.selection;if(r){var i=da(o.edges(r),2),a=i[0],s=i[1],l=e.void(n,{at:a.path}),u=e.void(n,{at:s.path});if(!o.isCollapsed(r)||l){var c=ol.toDOMRange(n,r),f=c.cloneContents(),d=f.childNodes[0];if(f.childNodes.forEach((function(e){e.textContent&&""!==e.textContent.trim()&&(d=e)})),u){var h=da(u,1)[0],p=c.cloneRange(),g=ol.toDOMNode(n,h);p.setEndAfter(g),f=p.cloneContents()}if(l&&(d=f.querySelector("[data-slate-spacer]")),Array.from(f.querySelectorAll("[data-slate-zero-width]")).forEach((function(e){var t="n"===e.getAttribute("data-slate-zero-width");e.textContent=t?"\n":""})),Hs(d)){var v=d.ownerDocument.createElement("span");v.style.whiteSpace="pre",v.appendChild(d),f.appendChild(v),d=v}var y=n.getFragment(),m=JSON.stringify(y),b=window.btoa(encodeURIComponent(m));d.setAttribute("data-slate-fragment",b),t.setData("application/x-slate-fragment",b);var w=f.ownerDocument.createElement("div");return w.appendChild(f),w.setAttribute("hidden","true"),f.ownerDocument.body.appendChild(w),t.setData("text/html",w.innerHTML),t.setData("text/plain",Gs(w)),f.ownerDocument.body.removeChild(w),t}}},n.insertData=function(e){var t,o,i=e.getData("application/x-slate-fragment");if(i){var a=decodeURIComponent(window.atob(i)),l=JSON.parse(a);n.insertFragment(l)}else{var u=e.getData("text/plain"),c=e.getData("text/html");if(c)n.dangerouslyInsertHtml(c);else if(u){var f=u.split(/\r\n|\r|\n/),d=!1;try{for(var h=fa(f),p=h.next();!p.done;p=h.next()){var g=p.value;d&&s.splitNodes(n,{always:!0}),r(g),d=!0}}catch(e){t={error:e}}finally{try{p&&!p.done&&(o=h.return)&&o.call(h)}finally{if(t)throw t.error}}}else;}},n},sf=function(e){return null!=e},lf={object:!0,function:!0,undefined:!0},uf=function(e){if(!function(e){return!!sf(e)&&hasOwnProperty.call(lf,typeof e)}(e))return!1;try{return!!e.constructor&&e.constructor.prototype===e}catch(e){return!1}},cf=/^\s*class[\s{/}]/,ff=Function.prototype.toString,df=function(e){return!!function(e){if("function"!=typeof e)return!1;if(!hasOwnProperty.call(e,"length"))return!1;try{if("number"!=typeof e.length)return!1;if("function"!=typeof e.call)return!1;if("function"!=typeof e.apply)return!1}catch(e){return!1}return!uf(e)}(e)&&!cf.test(ff.call(e))},hf=function(e){return null!=e},pf=Object.keys,gf=function(){try{return Object.keys("primitive"),!0}catch(e){return!1}}()?Object.keys:function(e){return pf(hf(e)?Object(e):e)},vf=function(e){if(!hf(e))throw new TypeError("Cannot use null or undefined");return e},yf=Math.max,mf=function(){var e,t=Object.assign;return"function"==typeof t&&(t(e={foo:"raz"},{bar:"dwa"},{trzy:"trzy"}),e.foo+e.bar+e.trzy==="razdwatrzy")}()?Object.assign:function(e,t){var n,r,o,i=yf(arguments.length,2);for(e=Object(vf(e)),o=function(r){try{e[r]=t[r]}catch(e){n||(n=e)}},r=1;r<i;++r)gf(t=arguments[r]).forEach(o);if(void 0!==n)throw n;return e},bf=Array.prototype.forEach,wf=Object.create,xf=function(e,t){var n;for(n in e)t[n]=e[n]},Sf=function(e){var t=wf(null);return bf.call(arguments,(function(e){hf(e)&&xf(Object(e),t)})),t},kf="razdwatrzy",Ef=String.prototype.indexOf,Of="function"==typeof kf.contains&&!0===kf.contains("dwa")&&!1===kf.contains("foo")?String.prototype.contains:function(e){return Ef.call(this,e,arguments[1])>-1},Cf=re((function(e){var t=e.exports=function(e,t){var n,r,o,i,a;return arguments.length<2||"string"!=typeof e?(i=t,t=e,e=null):i=arguments[2],sf(e)?(n=Of.call(e,"c"),r=Of.call(e,"e"),o=Of.call(e,"w")):(n=o=!0,r=!1),a={value:t,configurable:n,enumerable:r,writable:o},i?mf(Sf(i),a):a};t.gs=function(e,t,n){var r,o,i,a;return"string"!=typeof e?(i=n,n=t,t=e,e=null):i=arguments[3],sf(t)?df(t)?sf(n)?df(n)||(i=n,n=void 0):n=void 0:(i=t,t=n=void 0):t=void 0,sf(e)?(r=Of.call(e,"c"),o=Of.call(e,"e")):(r=!0,o=!1),a={get:t,set:n,configurable:r,enumerable:o},i?mf(Sf(i),a):a}})),Nf=function(e){if("function"!=typeof e)throw new TypeError(e+" is not a function");return e},Tf=re((function(e,t){var n,r,o,i,a,s,l,u=Function.prototype.apply,c=Function.prototype.call,f=Object.create,d=Object.defineProperty,h=Object.defineProperties,p=Object.prototype.hasOwnProperty,g={configurable:!0,enumerable:!1,writable:!0};n=function(e,t){var n;return Nf(t),p.call(this,"__ee__")?n=this.__ee__:(n=g.value=f(null),d(this,"__ee__",g),g.value=null),n[e]?"object"==typeof n[e]?n[e].push(t):n[e]=[n[e],t]:n[e]=t,this},r=function(e,t){var r,i;return Nf(t),i=this,n.call(this,e,r=function(){o.call(i,e,r),u.call(t,this,arguments)}),r.__eeOnceListener__=t,this},o=function(e,t){var n,r,o,i;if(Nf(t),!p.call(this,"__ee__"))return this;if(!(n=this.__ee__)[e])return this;if("object"==typeof(r=n[e]))for(i=0;o=r[i];++i)o!==t&&o.__eeOnceListener__!==t||(2===r.length?n[e]=r[i?0:1]:r.splice(i,1));else r!==t&&r.__eeOnceListener__!==t||delete n[e];return this},i=function(e){var t,n,r,o,i;if(p.call(this,"__ee__")&&(o=this.__ee__[e]))if("object"==typeof o){for(n=arguments.length,i=new Array(n-1),t=1;t<n;++t)i[t-1]=arguments[t];for(o=o.slice(),t=0;r=o[t];++t)u.call(r,this,i)}else switch(arguments.length){case 1:c.call(o,this);break;case 2:c.call(o,this,arguments[1]);break;case 3:c.call(o,this,arguments[1],arguments[2]);break;default:for(n=arguments.length,i=new Array(n-1),t=1;t<n;++t)i[t-1]=arguments[t];u.call(o,this,i)}},a={on:n,once:r,off:o,emit:i},s={on:Cf(n),once:Cf(r),off:Cf(o),emit:Cf(i)},l=h({},s),e.exports=t=function(e){return null==e?f(l):h(Object(e),s)},t.methods=a}));function Lf(e){var t=js.get(e);return null==t&&(t=Tf(),js.set(e,t)),t}var Mf=new WeakMap;function Pf(e,t){var n=Mf.get(e);null==n&&(n=new Set,Mf.set(e,n)),n.add(t)}function Df(e){return Mf.get(e)||new Set}function Rf(e){Mf.set(e,new Set)}function jf(e){var t=ol.getTextarea(e).$textAreaContainer,n=t.width(),r=t.height(),o=t.offset();return{top:o.top,left:o.left,width:n,height:r}}function Af(e){var t={top:"0",left:"0"},n=e.selection;if(null==n)return t;var r=jf(e);if(null==r)return t;var o=r.top,i=r.left,a=r.width,s=r.height,l=ol.toDOMRange(e,n).getClientRects()[0];if(null==l)return t;l.width;var u=l.height,c={},f=l.top-o,d=l.left-i;if(d>a/2){var h=a-d;c.right=h+5+"px"}else c.left=d+5+"px";if(f>s/2){var p=s-f;c.bottom=p+5+"px"}else{var g=f+u;g<0&&(g=0),c.top=g+5+"px"}return c}function Ff(e,t,n){void 0===n&&(n="modal");var r={top:"0",left:"0"};if(null==e.selection)return r;var o=i.isElement(t)&&e.isVoid(t),a=i.isElement(t)&&e.isInline(t),s=Ls.get(t);if(null==s)return r;var l=s.getBoundingClientRect(),u=l.top,c=l.left,f=l.height,d=l.width;if(o){var h=function(e){var t=[];t.push(e);for(var n=0;t.length>0;){var r=t.pop();if(null==r)break;if(++n>1e4)break;var o=r.nodeName;if(1===r.nodeType){var i=o.toLowerCase();if(_s.includes(i)||"iframe"===i||"video"===i)return r;var a=r.children||[],s=a.length;if(s)for(var l=s-1;l>=0;l--)t.push(a[l])}}return null}(s);if(null!=h){var p=h.getBoundingClientRect();u=p.top,f=p.height}}var g=jf(e);if(null==g)return r;var v=g.top,y=g.left,m=g.width,b=g.height,w={},x=u-v,S=c-y;if("bar"===n)return w.left=S+"px",x>40?w.bottom=b-x+5+"px":w.top=x+f+5+"px",w;if("modal"===n){var k;if(o?a?S>(m-d)/2?w.right=m-S+5+"px":w.left=S+d+5+"px":w.left="20px":w.left=S+"px",o)(k=x)<0&&(k=0),w.top=k+"px";else if(x>(b-f)/2)w.bottom=b-x+5+"px";else(k=x+f)<0&&(k=0),w.top=k+5+"px";return w}throw new Error("type '"+n+"' is invalid")}function If(e,t){ac((function(){var n=jf(e);if(null!=n){var r,o=n.top,i=n.left,a=n.width,s=n.height,l=t.offset(),u=l.top,c=l.left,f=t.width(),d=t.height(),h=u-o,p=c-i,g=t.attr("style");if(g.indexOf("top")>=0)if((r=h+d-s)>0){var v=t.css("top"),y=parseInt(v.toString())-r;y<0&&(y=0),t.css("top",y+"px")}if(g.indexOf("bottom")>=0&&u<0){var m=t.css("bottom"),b=parseInt(m.toString())-Math.abs(u);t.css("bottom",b+"px")}if(g.indexOf("left")>=0)if((r=p+f-a)>0){var w=t.css("left"),x=parseInt(w.toString())-r;x<0&&(x=0),t.css("left",x+"px")}if(g.indexOf("right")>=0&&c<0){var S=t.css("right"),k=parseInt(S.toString())-Math.abs(c);t.css("right",k+"px")}}}))}var _f=fi("slice"),Bf=ze("species"),$f=se.Array,Wf=Math.max;cr({target:"Array",proto:!0,forced:!_f},{slice:function(e,t){var n,r,o,i=_n(this),a=hn(i),s=zn(e,a),l=zn(void 0===t?a:t,a);if(pn(i)&&(n=i.constructor,(Sn(n)&&(n===$f||pn(n.prototype))||qe(n)&&null===(n=n[Bf]))&&(n=void 0),n===$f||void 0===n))return Fl(i,s,l);for(r=new(void 0===n?$f:n)(Wf(l-s,0)),o=0;s<l;s++,o++)s in i&&ao(r,o,i[s]);return r.length=o,r}});var Vf=$n.f,zf=ve("".startsWith),Hf=ve("".slice),Uf=Math.min,Kf=Jr("startsWith"),qf=!Kf&&!!function(){var e=Vf(String.prototype,"startsWith");return e&&!e.writable}();cr({target:"String",proto:!0,forced:!qf&&!Kf},{startsWith:function(e){var t=kr(me(this));qr(e);var n=dn(Uf(arguments.length>1?arguments[1]:void 0,t.length)),r=kr(e);return zf?zf(t,r,n):Hf(t,n,n+r.length)===r}});var Gf=Object.assign,Jf=Object.defineProperty,Yf=ve([].concat),Xf=!Gf||Fe((function(){if(Ke&&1!==Gf({b:1},Gf(Jf({},"a",{enumerable:!0,get:function(){Jf(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach((function(e){t[e]=e})),7!=Gf({},e)[n]||fr(Gf({},t)).join("")!=r}))?function(e,t){for(var n=we(e),r=arguments.length,o=1,i=Qn.f,a=In.f;r>o;)for(var s,l=sn(arguments[o++]),u=i?Yf(fr(l),i(l)):fr(l),c=u.length,f=0;c>f;)s=u[f++],Ke&&!nt(a,l,s)||(n[s]=l[s]);return n}:Gf;cr({target:"Object",stat:!0,forced:Object.assign!==Xf},{assign:Xf});var Qf=["props","attrs","style","dataset","on","hook"];function Zf(e){var t=e.data,n=void 0===t?{}:t,r=e.children,o=void 0===r?[]:r;Object.keys(n).forEach((function(t){var r,o,i=n[t];if("key"!==t){if(!Qf.includes(t)){if(t.startsWith("data-")){var a=t.slice(5);return a=J(a),function(e,t){null==e.data&&(e.data={});var n=e.data;null==n.dataset&&(n.dataset={});Object.assign(n.dataset,t)}(e,((r={})[a]=i,r)),void delete n[t]}!function(e,t){null==e.data&&(e.data={});var n=e.data;null==n.props&&(n.props={});Object.assign(n.props,t)}(e,(o={},o[t]=i,o)),delete n[t]}}else e.key=i})),o.length>0&&o.forEach((function(e){"string"!=typeof e&&Zf(e)}))}var ed=[];function td(e){ed.push(e)}var nd={};function rd(e){var t=e.type,n=e.renderElem;nd[t||""]=n}function od(e,t,n){var r=n.isInline(e)?"span":"div";return q(r,null,t)}function id(t,n){var o,i=ol.findKey(n,t),a=n.isInline(t),s=e.isVoid(n,t),l=lc(i.id),u={id:l,key:i.id,"data-slate-node":"element","data-slate-inline":a},c=t.type,f=t.children,d=void 0===f?[]:f,h=function(e){return nd[e]||od}(c);o=s?null:d.map((function(e,r){return ud(e,r,t,n)}));var p=h(t,o,n);if(s){u["data-slate-void"]=!0;var g=a?"span":"div",v=da(r.texts(t),1),y=da(v[0],1)[0],m=ud(y,0,t,n),b=q(g,{"data-slate-spacer":!0,style:{height:"0",color:"transparent",outline:"none",position:"absolute"}},m);p=q(g,{style:{position:"relative"}},p,b),Es.set(y,0),Os.set(y,t)}return null==p.data&&(p.data={}),Object.assign(p.data,u),s||a||(p=function(e,t){var n=t;return ed.forEach((function(r){n=r(e,t)})),n}(t,p)),ac((function(){var e=Us(l);null!=e&&(Ts.set(i,e),Ls.set(t,e),Ns.set(e,t))})),p}function ad(e,t){return void 0===t&&(t=!1),q("span",{"data-slate-string":!0},t?e+"\n":e)}function sd(e,t){return void 0===e&&(e=0),void 0===t&&(t=!1),q("span",{"data-slate-zero-width":t?"n":"z","data-slate-length":e},"\ufeff",t?q("br",null):null)}function ld(t,o,i){if(null==t.text)throw new Error("Current node is not slate Text "+JSON.stringify(t));var s=ol.findKey(i,t),l=i.getConfig().decorate;if(null==l)throw new Error("Can not get config.decorate");var u=ol.findPath(i,t),c=l([t,u]),f=a.decorations(t,c),d=f.map((function(a,s){var l=function(t,o,i,a,s){void 0===o&&(o=!1);var l=t.text,u=ol.findPath(s,i),c=n.parent(u);if(e.isEditor(a))throw new Error("Text node "+JSON.stringify(i)+" parent is Editor");return s.isVoid(a)?sd(r.string(a).length):""!==l||a.children[a.children.length-1]!==i||s.isInline(a)||""!==e.string(s,c)?""===l?sd():o&&"\n"===l.slice(-1)?ad(l,!0):ad(l):sd(0,!0)}(a,s===f.length-1,t,o,i);return l=function(e,t){var n=t;return ed.forEach((function(t){n=t(e,n)})),n}(a,l),q("span",{"data-slate-leaf":!0},l)})),h=function(e){return"w-e-text-"+e}(s.id),p=q("span",{"data-slate-node":"text",id:h,key:s.id},d);return ac((function(){var e=Us(h);null!=e&&(Ts.set(s,e),Ls.set(t,e),Ns.set(e,t))})),p}function ud(e,t,n,r){return Es.set(e,t),Os.set(e,n),i.isElement(e)?id(e,r):ld(e,n,r)}function cd(e,t){var n,r=e.$scroll,o=function(e){return"w-e-textarea-"+e}(e.id),i=t.getConfig(),a=i.readOnly,s=i.autoFocus,l=function(e,t){return void 0===t&&(t=!1),G("div#"+e,{props:{contentEditable:!t}})}(o,a),u=t.children||[];l.children=u.map((function(e,n){var r=ud(e,n,t,t);return Zf(r),r}));var c=xs.get(e);if(null==c&&(c=!0),c){var d=function(e,t){return f('<div\n        id="'+e+'"\n        data-slate-editor\n        data-slate-node="value"\n        suppressContentEditableWarning\n        role="textarea"\n        spellCheck="true"\n        autoCorrect="true"\n        autoCapitalize="true"\n    ></div>')}(o);r.append(d),e.$textArea=d,n=d[0],(p=$([W,V,z,H,U,K]))(n,l),xs.set(e,!1),Ss.set(e,p)}else{var h=ks.get(e),p=Ss.get(e);if(null==h||null==p)return;n=h.elm,p(h,l)}if(null!=n||null!=(n=Us(o))){if((c?s:t.isFocused())&&n.focus({preventScroll:!0}),c){var g=$s(n);g&&Ps.set(t,g)}Cs.set(t,n),Ls.set(t,n),Ns.set(n,t),ks.set(e,l)}}function fd(e){return"object"==typeof e&&null!=e&&1===e.nodeType}function dd(e,t){return(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e}function hd(e,t){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var n=getComputedStyle(e,null);return dd(n.overflowY,t)||dd(n.overflowX,t)||function(e){var t=function(e){if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}}(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)}(e)}return!1}function pd(e,t,n,r,o,i,a,s){return i<e&&a>t||i>e&&a<t?0:i<=e&&s<=n||a>=t&&s>=n?i-e-r:a>t&&s<n||i<e&&s>n?a-t+o:0}function gd(e,t){var n=window,r=t.scrollMode,o=t.block,i=t.inline,a=t.boundary,s=t.skipOverflowHiddenElements,l="function"==typeof a?a:function(e){return e!==a};if(!fd(e))throw new TypeError("Invalid target");for(var u=document.scrollingElement||document.documentElement,c=[],f=e;fd(f)&&l(f);){if((f=f.parentElement)===u){c.push(f);break}null!=f&&f===document.body&&hd(f)&&!hd(document.documentElement)||null!=f&&hd(f,s)&&c.push(f)}for(var d=n.visualViewport?n.visualViewport.width:innerWidth,h=n.visualViewport?n.visualViewport.height:innerHeight,p=window.scrollX||pageXOffset,g=window.scrollY||pageYOffset,v=e.getBoundingClientRect(),y=v.height,m=v.width,b=v.top,w=v.right,x=v.bottom,S=v.left,k="start"===o||"nearest"===o?b:"end"===o?x:b+y/2,E="center"===i?S+m/2:"end"===i?w:S,O=[],C=0;C<c.length;C++){var N=c[C],T=N.getBoundingClientRect(),L=T.height,M=T.width,P=T.top,D=T.right,R=T.bottom,j=T.left;if("if-needed"===r&&b>=0&&S>=0&&x<=h&&w<=d&&b>=P&&x<=R&&S>=j&&w<=D)return O;var A=getComputedStyle(N),F=parseInt(A.borderLeftWidth,10),I=parseInt(A.borderTopWidth,10),_=parseInt(A.borderRightWidth,10),B=parseInt(A.borderBottomWidth,10),$=0,W=0,V="offsetWidth"in N?N.offsetWidth-N.clientWidth-F-_:0,z="offsetHeight"in N?N.offsetHeight-N.clientHeight-I-B:0;if(u===N)$="start"===o?k:"end"===o?k-h:"nearest"===o?pd(g,g+h,h,I,B,g+k,g+k+y,y):k-h/2,W="start"===i?E:"center"===i?E-d/2:"end"===i?E-d:pd(p,p+d,d,F,_,p+E,p+E+m,m),$=Math.max(0,$+g),W=Math.max(0,W+p);else{$="start"===o?k-P-I:"end"===o?k-R+B+z:"nearest"===o?pd(P,R,L,I,B+z,k,k+y,y):k-(P+L/2)+z/2,W="start"===i?E-j-F:"center"===i?E-(j+M/2)+V/2:"end"===i?E-D+_+V:pd(j,D,M,F,_+V,E,E+m,m);var H=N.scrollLeft,U=N.scrollTop;k+=U-($=Math.max(0,Math.min(U+$,N.scrollHeight-L+z))),E+=H-(W=Math.max(0,Math.min(H+W,N.scrollWidth-M+V)))}O.push({el:N,top:$,left:W})}return O}function vd(e){return e===Object(e)&&0!==Object.keys(e).length}function yd(e,t){var n=!e.ownerDocument.documentElement.contains(e);if(vd(t)&&"function"==typeof t.behavior)return t.behavior(n?[]:gd(e,t));if(!n){var r=function(e){return!1===e?{block:"end",inline:"nearest"}:vd(e)?e:{block:"start",inline:"nearest"}}(t);return function(e,t){void 0===t&&(t="auto");var n="scrollBehavior"in document.body.style;e.forEach((function(e){var r=e.el,o=e.top,i=e.left;r.scroll&&n?r.scroll({top:o,left:i,behavior:t}):(r.scrollTop=o,r.scrollLeft=i)}))}(gd(e,r),r.behavior)}}function md(e,t){return Vs(t)&&ol.hasDOMNode(e,t,{editable:!0})}function bd(t,n){if(t.getConfig().readOnly)return!1;var r=wd(t,n)&&ol.toSlateNode(t,n);return e.isVoid(t,r)}function wd(e,t){return Vs(t)&&ol.hasDOMNode(e,t)}function xd(e,t,n){void 0===n&&(n=!1);var r=t.selection,i=t.getConfig(),a=ol.findDocumentOrShadowRoot(t).getSelection();if(a&&(!e.isComposing||n)&&t.isFocused()){var s="None"!==a.type;if(r||s){var l=Cs.get(t),u=!1;if(l.contains(a.anchorNode)&&l.contains(a.focusNode)&&(u=!0),s&&u&&r){var c=ol.toSlateRange(t,a,{exactMatch:!0,suppressThrow:!0});if(c&&o.equals(c,r)){var f=!0;if(o.isCollapsed(r)){var d=a.anchorNode,h=a.anchorOffset;if(d===l){var p=l.childNodes,g=void 0;(g=p[h])&&g.matches("table")&&(f=!1),(g=p[h-1])&&g.matches("table")&&(f=!1)}}if(f)return}}if(!r||ol.hasRange(t,r)){e.isUpdatingSelection=!0;var v=r&&ol.toDOMRange(t,r);if(v){o.isBackward(r)?a.setBaseAndExtent(v.endContainer,v.endOffset,v.startContainer,v.startOffset):a.setBaseAndExtent(v.startContainer,v.startOffset,v.endContainer,v.endOffset);var y=v.startContainer.parentElement;if(!y.closest("[data-slate-spacer]")){y.getBoundingClientRect=v.getBoundingClientRect.bind(v);var m=document.body;yd(y,{scrollMode:"if-needed",boundary:i.scroll?l.parentElement:m,block:"end",behavior:"smooth"}),delete y.getBoundingClientRect}}else a.removeAllRanges();setTimeout((function(){v&&Qs&&l.focus(),e.isUpdatingSelection=!1}))}else t.selection=ol.toSlateRange(t,a,{exactMatch:!1,suppressThrow:!1})}}}var Sd=new WeakMap,kd=new WeakMap;var Ed={bold:"mod+b",compose:["down","left","right","up","backspace","enter"],moveBackward:"left",moveForward:"right",moveWordBackward:"ctrl+left",moveWordForward:"ctrl+right",deleteBackward:"shift?+backspace",deleteForward:"shift?+delete",extendBackward:"shift+left",extendForward:"shift+right",italic:"mod+i",splitBlock:"shift?+enter",undo:"mod+z",tab:"tab",selectAll:"mod+a"},Od={moveLineBackward:"opt+up",moveLineForward:"opt+down",moveWordBackward:"opt+left",moveWordForward:"opt+right",deleteBackward:["ctrl+backspace","ctrl+h"],deleteForward:["ctrl+delete","ctrl+d"],deleteLineBackward:"cmd+shift?+backspace",deleteLineForward:["cmd+shift?+delete","ctrl+k"],deleteWordBackward:"opt+shift?+backspace",deleteWordForward:"opt+shift?+delete",extendLineBackward:"opt+shift+up",extendLineForward:"opt+shift+down",redo:"cmd+shift+z",transposeCharacter:"ctrl+t"},Cd={deleteWordBackward:"ctrl+shift?+backspace",deleteWordForward:"ctrl+shift?+delete",redo:["ctrl+y","ctrl+shift+z"]},Nd=function(e){var t=Ed[e],n=Od[e],r=Cd[e],o=t&&Y(t),i=n&&Y(n),a=r&&Y(r);return function(e){return!(!o||!o(e))||(!!(Xs&&i&&i(e))||!(Xs||!a||!a(e)))}},Td={isBold:Nd("bold"),isCompose:Nd("compose"),isMoveBackward:Nd("moveBackward"),isMoveForward:Nd("moveForward"),isDeleteBackward:Nd("deleteBackward"),isDeleteForward:Nd("deleteForward"),isDeleteLineBackward:Nd("deleteLineBackward"),isDeleteLineForward:Nd("deleteLineForward"),isDeleteWordBackward:Nd("deleteWordBackward"),isDeleteWordForward:Nd("deleteWordForward"),isExtendBackward:Nd("extendBackward"),isExtendForward:Nd("extendForward"),isExtendLineBackward:Nd("extendLineBackward"),isExtendLineForward:Nd("extendLineForward"),isItalic:Nd("italic"),isMoveLineBackward:Nd("moveLineBackward"),isMoveLineForward:Nd("moveLineForward"),isMoveWordBackward:Nd("moveWordBackward"),isMoveWordForward:Nd("moveWordForward"),isRedo:Nd("redo"),isSplitBlock:Nd("splitBlock"),isTransposeCharacter:Nd("transposeCharacter"),isUndo:Nd("undo"),isTab:Nd("tab"),isSelectAll:Nd("selectAll")};function Ld(e){e.preventDefault()}var Md={beforeinput:function(t,n,r){var i=t,a=r.getConfig().readOnly;if(rl&&!a&&md(r,i.target)){var l=r.selection,u=i.inputType,c=i.dataTransfer||i.data||void 0;if("insertCompositionText"!==u&&"deleteCompositionText"!==u){if(i.preventDefault(),!u.startsWith("delete")||u.startsWith("deleteBy")){var f=da(i.getTargetRanges(),1)[0];if(f){var d=ol.toSlateRange(r,f,{exactMatch:!1,suppressThrow:!1});l&&o.equals(l,d)||s.select(r,d)}}if(l&&o.isExpanded(l)&&u.startsWith("delete")){var h=u.endsWith("Backward")?"backward":"forward";e.deleteFragment(r,{direction:h})}else switch(u){case"deleteByComposition":case"deleteByCut":case"deleteByDrag":e.deleteFragment(r);break;case"deleteContent":case"deleteContentForward":e.deleteForward(r);break;case"deleteContentBackward":e.deleteBackward(r);break;case"deleteEntireSoftLine":e.deleteBackward(r,{unit:"line"}),e.deleteForward(r,{unit:"line"});break;case"deleteHardLineBackward":e.deleteBackward(r,{unit:"block"});break;case"deleteSoftLineBackward":e.deleteBackward(r,{unit:"line"});break;case"deleteHardLineForward":e.deleteForward(r,{unit:"block"});break;case"deleteSoftLineForward":e.deleteForward(r,{unit:"line"});break;case"deleteWordBackward":e.deleteBackward(r,{unit:"word"});break;case"deleteWordForward":e.deleteForward(r,{unit:"word"});break;case"insertLineBreak":case"insertParagraph":e.insertBreak(r);break;case"insertFromDrop":case"insertFromPaste":case"insertFromYank":case"insertReplacementText":case"insertText":if("insertFromPaste"===u&&!As.get(r))break;c instanceof DataTransfer?r.insertData(c):"string"==typeof c&&e.insertText(r,c)}}}},blur:function(e,t,n){var r=e,o=t.isUpdatingSelection,a=t.latestElement;if(!n.getConfig().readOnly&&!o&&md(n,r.target)){var s=ol.findDocumentOrShadowRoot(n);if(a!==s.activeElement){var l=r.relatedTarget;if(!(l===ol.toDOMNode(n,n)||Ws(l)&&l.hasAttribute("data-slate-spacer"))){if(null!=l&&Vs(l)&&ol.hasDOMNode(n,l)){var u=ol.toSlateNode(n,l);if(i.isElement(u)&&!n.isVoid(u))return}if(Zs){var c=s.getSelection();null==c||c.removeAllRanges()}Ds.delete(n)}}}},focus:function(e,t,n){var r=ol.toDOMNode(n,n),o=ol.findDocumentOrShadowRoot(n);t.latestElement=o.activeElement,Qs&&e.target!==r?r.focus():Ds.set(n,!0)},click:function(t,o,i){if(!i.getConfig().readOnly&&wd(i,t.target)&&Vs(t.target)){var a=ol.toSlateNode(i,t.target),l=ol.findPath(i,a);if(e.hasPath(i,l))if(r.get(i,l)===a){var u=e.start(i,l),c=e.end(i,l),f=e.void(i,{at:u}),d=e.void(i,{at:c});if(f&&d&&n.equals(f[1],d[1])){var h=e.range(i,u);s.select(i,h)}}}},compositionstart:function(t,n,r){if(md(r,t.target)){var i=r.selection;if(i&&o.isExpanded(i)&&(e.deleteFragment(r),Promise.resolve().then((function(){xd(n,r,!0)}))),i&&o.isCollapsed(i)){var a=ol.toDOMRange(r,i).startContainer,s=a.textContent||"";Sd.set(r,s),kd.set(r,a)}n.isComposing=!0,function(e,t){var n;t.getConfig().placeholder&&t.isEmpty()&&e.showPlaceholder&&(null===(n=e.$placeholder)||void 0===n||n.hide(),e.showPlaceholder=!1)}(n,r)}},compositionend:function(t,n,r){var a=t;if(md(r,a.target)){n.isComposing=!1;var s=r.selection;if(null!=s){(nl||Qs)&&ol.cleanExposedTexNodeInSelectionBlock(r);for(var l=o.isBackward(s)?s.focus:s.anchor,u=da(e.node(r,[l.path[0]]),1)[0],c=0;c<l.path.length;c++){var f=da(e.node(r,l.path.slice(0,c+1)),1)[0];if(i.isElement(f)&&((Zs||Qs)&&"link"===f.type||"code"===f.type)){ol.setNewKey(u);break}}var d=a.data;if(d){if(r.getConfig().maxLength){var h=ol.getLeftLengthOfMaxLength(r);if(h<d.length)ol.toDOMRange(r,s).startContainer.textContent=Sd.get(r)||"",h>0&&e.insertText(r,d.slice(0,h)),n.changeViewState();else e.insertText(r,d)}else e.insertText(r,d);Zs||setTimeout((function(){var e=r.selection;if(null!=e){var t=kd.get(r);if(null!=t)ol.toDOMRange(r,e).startContainer!==t&&(t.textContent=Sd.get(r)||"")}}))}}}},compositionupdate:function(e,t,n){md(n,e.target)&&(t.isComposing=!0)},keydown:function(t,n,a){var l=t,u=a.selection;if(!a.getConfig().readOnly&&!n.isComposing&&md(a,l.target)){if(function(e,t){var n=ps.get(e),r=n&&n.getMenus(),o=vs.get(e),i=o&&o.getMenus(),a=ca(ca({},r),i);for(var s in a){var l=a[s],u=l.hotkey;if(u&&X(u,t)&&!l.isDisabled(e)){var c=l.getValue(e);l.exec(e,c)}}}(a,l),Td.isTab(l))return Ld(l),void a.handleTab();if(Td.isRedo(l))return Ld(l),void("function"==typeof a.redo&&a.redo());if(Td.isUndo(l))return Ld(l),void("function"==typeof a.undo&&a.undo());if(Td.isMoveLineBackward(l))return Ld(l),void s.move(a,{unit:"line",reverse:!0});if(Td.isMoveLineForward(l))return Ld(l),void s.move(a,{unit:"line"});if(Td.isExtendLineBackward(l))return Ld(l),void s.move(a,{unit:"line",edge:"focus",reverse:!0});if(Td.isExtendLineForward(l))return Ld(l),void s.move(a,{unit:"line",edge:"focus"});if(Td.isMoveBackward(l))return Ld(l),void(u&&o.isCollapsed(u)?s.move(a,{reverse:!0}):s.collapse(a,{edge:"start"}));if(Td.isMoveForward(l))return Ld(l),void(u&&o.isCollapsed(u)?s.move(a):s.collapse(a,{edge:"end"}));if(Td.isMoveWordBackward(l))return Ld(l),u&&o.isExpanded(u)&&s.collapse(a,{edge:"focus"}),void s.move(a,{unit:"word",reverse:!0});if(Td.isMoveWordForward(l))return Ld(l),u&&o.isExpanded(u)&&s.collapse(a,{edge:"focus"}),void s.move(a,{unit:"word"});if(Td.isSelectAll(l))return Ld(l),void a.selectAll();if(rl){if((nl||Zs)&&u&&(Td.isDeleteBackward(l)||Td.isDeleteForward(l))&&o.isCollapsed(u)){var c=r.parent(a,u.anchor.path);if(i.isElement(c)&&e.isVoid(a,c)&&e.isInline(a,c))return l.preventDefault(),void s.delete(a,{unit:"block"})}}else{if(Td.isBold(l)||Td.isItalic(l)||Td.isTransposeCharacter(l))return void Ld(l);if(Td.isSplitBlock(l))return Ld(l),void e.insertBreak(a);if(Td.isDeleteBackward(l))return Ld(l),void(u&&o.isExpanded(u)?e.deleteFragment(a,{direction:"backward"}):e.deleteBackward(a));if(Td.isDeleteForward(l))return Ld(l),void(u&&o.isExpanded(u)?e.deleteFragment(a,{direction:"forward"}):e.deleteForward(a));if(Td.isDeleteLineBackward(l))return Ld(l),void(u&&o.isExpanded(u)?e.deleteFragment(a,{direction:"backward"}):e.deleteBackward(a,{unit:"line"}));if(Td.isDeleteLineForward(l))return Ld(l),void(u&&o.isExpanded(u)?e.deleteFragment(a,{direction:"forward"}):e.deleteForward(a,{unit:"line"}));if(Td.isDeleteWordBackward(l))return Ld(l),void(u&&o.isExpanded(u)?e.deleteFragment(a,{direction:"backward"}):e.deleteBackward(a,{unit:"word"}));if(Td.isDeleteWordForward(l))return Ld(l),void(u&&o.isExpanded(u)?e.deleteFragment(a,{direction:"forward"}):e.deleteForward(a,{unit:"word"}))}}},keypress:function(t,n,r){if(!rl&&!r.getConfig().readOnly&&md(r,t.target)){t.preventDefault();var o=t.key;e.insertText(r,o)}},copy:function(e,t,n){var r=e;if(md(n,r.target)){r.preventDefault();var o=r.clipboardData;null!=o&&n.setFragmentData(o)}},cut:function(t,n,i){var a=t,l=i.selection;if(!i.getConfig().readOnly&&md(i,a.target)){a.preventDefault();var u=a.clipboardData;if(null!=u&&(i.setFragmentData(u),l))if(o.isExpanded(l))e.deleteFragment(i);else{var c=r.parent(i,l.anchor.path);e.isVoid(i,c)&&s.delete(i)}}},paste:function(e,t,n){As.set(n,!0);var r=e;if(!n.getConfig().readOnly&&md(n,r.target)){var o=n.getConfig().customPaste;if(o)if(!1===o(n,r))return void As.set(n,!1);if(!rl||function(e){return e.clipboardData&&""!==e.clipboardData.getData("text/plain")&&1===e.clipboardData.types.length}(r)){r.preventDefault();var i=r.clipboardData;null!=i&&n.insertData(i)}}},dragover:function(t,n,r){if(wd(r,t.target)){var o=ol.toSlateNode(r,t.target);e.isVoid(r,o)&&t.preventDefault()}},dragstart:function(t,n,r){var o=t;if(wd(r,o.target)&&!r.getConfig().readOnly){var i=ol.toSlateNode(r,o.target),a=ol.findPath(r,i);if(e.isVoid(r,i)||e.void(r,{at:a,voids:!0})){var l=e.range(r,a);s.select(r,l)}var u=o.dataTransfer;null!=u&&(n.isDraggingInternally=!0,r.setFragmentData(u))}},dragend:function(e,t,n){var r=e;n.getConfig().readOnly||t.isDraggingInternally&&wd(n,r.target)&&(t.isDraggingInternally=!1)},drop:function(e,t,n){var r=e,o=r.dataTransfer;if(!n.getConfig().readOnly&&wd(n,r.target)&&null!=o&&!(rl&&Zs&&o.files.length>0)){r.preventDefault();var i=n.selection,a=ol.findEventRange(n,r);s.select(n,a),t.isDraggingInternally&&(i&&s.delete(n,{at:i}),t.isDraggingInternally=!1),n.insertData(o),n.isFocused()||n.focus()}}},Pd=1,Dd=function(){function e(e){var t=this;this.id=Pd++,this.$textArea=null,this.$progressBar=f('<div class="w-e-progress-bar"></div>'),this.$maxLengthInfo=f('<div class="w-e-max-length-info"></div>'),this.isComposing=!1,this.isUpdatingSelection=!1,this.isDraggingInternally=!1,this.latestElement=null,this.showPlaceholder=!1,this.$placeholder=null,this.latestEditorSelection=null,this.onDOMSelectionChange=B((function(){var e=t.editorInstance;!function(e,t){var n=e.isComposing,r=e.isUpdatingSelection,o=e.isDraggingInternally;if(!(t.getConfig().readOnly||n||r||o)){var i=ol.findDocumentOrShadowRoot(t),a=i.activeElement,l=ol.toDOMNode(t,t),u=i.getSelection();if(a===l?(e.latestElement=a,Ds.set(t,!0)):Ds.delete(t),!u)return s.deselect(t);var c=u.anchorNode,f=u.focusNode,d=md(t,c)||bd(t,c),h=md(t,f)||bd(t,f);if(d&&h){var p=ol.toSlateRange(t,u,{exactMatch:!1,suppressThrow:!1});s.select(t,p)}else s.deselect(t)}}(t,e)}),100);var n=f(e);if(0===n.length)throw new Error("Cannot find textarea DOM by selector '"+e+"'");this.$box=n;var r=f('<div class="w-e-text-container"></div>');r.append(this.$progressBar),r.append(this.$maxLengthInfo),n.append(r);var o=f('<div class="w-e-scroll"></div>');r.append(o),this.$scroll=o,this.$textAreaContainer=r,ac((function(){var e=t.editorInstance,n=ol.getWindow(e);n.document.addEventListener("selectionchange",t.onDOMSelectionChange),e.on("destroyed",(function(){n.document.removeEventListener("selectionchange",t.onDOMSelectionChange)})),r.on("click",(function(){return e.hidePanelOrModal()})),e.on("change",t.changeViewState.bind(t));var o=e.getConfig().onChange;o&&e.on("change",(function(){return o(e)})),t.onFocusAndOnBlur(),e.on("change",t.changeMaxLengthInfo.bind(t)),t.bindEvent()}))}return Object.defineProperty(e.prototype,"editorInstance",{get:function(){var e=ds.get(this);if(null==e)throw new Error("Can not get editor instance");return e},enumerable:!1,configurable:!0}),e.prototype.bindEvent=function(){var e=this,t=this.$textArea,n=this.$scroll,r=this.editorInstance;null!=t&&(_(Md,(function(n,o){t.on(o,(function(t){n(t,e,r)}))})),r.getConfig().scroll&&(n.css("overflow-y","auto"),n.on("scroll",B((function(){r.emit("scroll")}),100))))},e.prototype.onFocusAndOnBlur=function(){var e=this,t=this.editorInstance,n=t.getConfig(),r=n.onBlur,o=n.onFocus;this.latestEditorSelection=t.selection,t.on("change",(function(){null==e.latestEditorSelection&&null!=t.selection?setTimeout((function(){return o&&o(t)})):null!=e.latestEditorSelection&&null==t.selection&&setTimeout((function(){return r&&r(t)})),e.latestEditorSelection=t.selection}))},e.prototype.changeMaxLengthInfo=function(){var e=this.editorInstance,t=e.getConfig().maxLength;if(t){var n=t-ol.getLeftLengthOfMaxLength(e);this.$maxLengthInfo[0].innerHTML=n+"/"+t}},e.prototype.changeProgress=function(e){var t=this.$progressBar;t.css("width",e+"%"),e>=100&&setTimeout((function(){t.hide(),t.css("width","0"),t.show()}),1e3)},e.prototype.changeViewState=function(){var e=this,t=this.editorInstance;cd(this,t),function(e,t){var n,r=t.getConfig().placeholder;if(r){var o=t.isEmpty();if(o&&!e.showPlaceholder&&!e.isComposing){if(null==e.$placeholder){var i=f('<div class="w-e-text-placeholder">'+r+"</div>");e.$textAreaContainer.append(i),e.$placeholder=i}return e.$placeholder.show(),void(e.showPlaceholder=!0)}!o&&e.showPlaceholder&&(null===(n=e.$placeholder)||void 0===n||n.hide(),e.showPlaceholder=!1)}}(this,t),ac((function(){xd(e,t)}))},e.prototype.destroy=function(){this.$textAreaContainer.remove()},e}();Wi("match",(function(e,t,n){return[function(t){var n=me(this),r=null==t?void 0:ct(t,e);return r?nt(r,t,n):new RegExp(t)[e](kr(n))},function(e){var r=et(this),o=kr(e),i=n(t,r,o);if(i.done)return i.value;if(!r.global)return Qi(r,o);var a=r.unicode;r.lastIndex=0;for(var s,l=[],u=0;null!==(s=Qi(r,o));){var c=kr(s[0]);l[u]=c,""===c&&(r.lastIndex=zi(o,dn(r.lastIndex),a)),u++}return 0===u?null:l}]}));function Rd(e){e.removeAttr("width"),e.removeAttr("height"),e.removeAttr("fill"),e.removeAttr("class"),e.removeAttr("t"),e.removeAttr("p-id");var t=e.children();t.length&&Rd(t)}function jd(){return f('<svg viewBox="0 0 1024 1024"><path d="M498.7 655.8l-197.6-268c-8.1-10.9-0.3-26.4 13.3-26.4h395.2c13.6 0 21.4 15.4 13.3 26.4l-197.6 268c-6.6 9-20 9-26.6 0z"></path></svg>')}function Ad(){return f('<div class="w-e-bar-divider"></div>')}function Fd(e,t,n,r,o){if(void 0===o&&(o=!1),t){if(r){var i=Xs?"cmd":"ctrl";r=r.replace("mod",i)}if(o)r&&(e.attr("data-tooltip",r),e.addClass("w-e-menu-tooltip-v5"),e.addClass("tooltip-right"));else{var a=r?n+"\n"+r:n;e.attr("data-tooltip",a),e.addClass("w-e-menu-tooltip-v5")}}}var Id=function(){function e(e,t,n){var r=this;void 0===n&&(n=!1),this.$elem=f('<div class="w-e-bar-item"></div>'),this.$button=f('<button type="button"></button>'),this.disabled=!1,this.menu=t;var o=t.tag,i=t.width;if("button"!==o)throw new Error("Invalid tag '"+o+"', expected 'button'");var a=t.title,s=t.hotkey,l=void 0===s?"":s,u=t.iconSvg,c=void 0===u?"":u,d=this.$button;if(c){var h=f(c);Rd(h),d.append(h)}else d.text(a);Fd(d,c,a,l,n),n&&c&&d.append(f('<span class="title">'+a+"</span>")),i&&d.css("width",i+"px"),d.attr("data-menu-key",e),this.$elem.append(d),ac((function(){return r.init()}))}return e.prototype.init=function(){var e=this;this.setActive(),this.setDisabled(),this.$button.on("click",(function(t){t.preventDefault(),Xd(e).hidePanelOrModal(),e.disabled||(e.exec(),e.onButtonClick())}))},e.prototype.exec=function(){var e=Xd(this),t=this.menu,n=t.getValue(e);t.exec(e,n)},e.prototype.setActive=function(){var e=Xd(this),t=this.$button,n="active";this.menu.isActive(e)?t.addClass(n):t.removeClass(n)},e.prototype.setDisabled=function(){var e=Xd(this),t=this.$button,n=this.menu.isDisabled(e);(null==e.selection||e.isDisabled())&&(n=!0),this.menu.alwaysEnable&&(n=!1);var r="disabled";n?t.addClass(r):t.removeClass(r),this.disabled=n},e.prototype.changeMenuState=function(){this.setActive(),this.setDisabled()},e}(),_d=function(e){function t(t,n,r){return void 0===r&&(r=!1),e.call(this,t,n,r)||this}return ua(t,e),t.prototype.onButtonClick=function(){},t}(Id),Bd=function(){function e(e){this.isShow=!1,this.showTime=0,this.record(e)}return e.prototype.record=function(e){var t=ms.get(e);null==t&&(t=new Set,ms.set(e,t)),t.add(this),bs.set(this,e)},e.prototype.renderContent=function(e){var t=this.$elem;t.empty(),t.append(e);var n=this.genSelfElem();n&&t.append(n)},e.prototype.appendTo=function(e){var t=this.$elem;e.append(t)},e.prototype.show=function(){if(!this.isShow){this.showTime=Date.now(),this.$elem.show(),this.isShow=!0;var e=bs.get(this);e&&e.emit("modalOrPanelShow",this)}},e.prototype.hide=function(){if(this.isShow&&!(Date.now()-this.showTime<200)){this.$elem.hide(),this.isShow=!1;var e=bs.get(this);e&&e.emit("modalOrPanelHide")}},e}(),$d=function(e){function t(t){var n=e.call(this,t)||this;return n.type="dropPanel",n.$elem=f('<div class="w-e-drop-panel"></div>'),n}return ua(t,e),t.prototype.genSelfElem=function(){return null},t}(Bd),Wd=function(e){function t(t,n,r){void 0===r&&(r=!1);var o=e.call(this,t,n,r)||this;if(o.dropPanel=null,o.menu=n,n.showDropPanel){var i=jd();o.$button.append(i)}return o}return ua(t,e),t.prototype.onButtonClick=function(){this.menu.showDropPanel&&this.handleDropPanel()},t.prototype.handleDropPanel=function(){var e=this.menu;if(null!=e.getPanelContentElem){var t=Xd(this);if(null==this.dropPanel){var n=new $d(t),r=e.getPanelContentElem(t);n.renderContent(r),n.appendTo(this.$elem),n.show(),this.dropPanel=n}else{var o=this.dropPanel;if(o.isShow)o.hide();else{r=e.getPanelContentElem(t);o.renderContent(r),o.show()}}var i=this.dropPanel;if(i.isShow){var a=this.$elem,s=a.offset().left,l=a.parents(".w-e-bar");s-l.offset().left>=l.width()/2?i.$elem.css({left:"none",right:"0"}):i.$elem.css({left:"0",right:"none"})}}},t}(Id),Vd=function(e){function t(t,n){void 0===n&&(n=0);var r=e.call(this,t)||this;r.type="modal",r.$elem=f('<div class="w-e-modal"></div>'),r.width=0,n&&(r.width=n);var o=r.$elem;return o.on("click",(function(e){return e.stopPropagation()})),o.on("keyup",(function(e){"Escape"===e.code&&(r.hide(),t.restoreSelection())})),r}return ua(t,e),t.prototype.genSelfElem=function(){var e=this,t=f('<span class="btn-close"><svg viewBox="0 0 1024 1024"><path d="M1024 896.1024l-128 128L512 ************ 0 896 384 512 0 128 128 0 512 ************ 0l128 128L640 512z"></path></svg></span>'),n=bs.get(this);return t.on("click",(function(){e.hide(),null==n||n.restoreSelection()})),t},t.prototype.setStyle=function(e){var t=this.width,n=this.$elem;n.attr("style",""),t&&n.css("width",t+"px"),n.css(e)},t}(Bd);function zd(e,t,n){var r=f('<label class="babel-container"></label>');r.append("<span>"+e+"</span>");var o=f('<input type="text" id="'+t+'" placeholder="'+(n||"")+'">');return r.append(o),[r[0],o[0]]}function Hd(e,t,n){var r=f('<label class="babel-container"></label>');r.append("<span>"+e+"</span>");var o=f('<textarea type="text" id="'+t+'" placeholder="'+(n||"")+'"></textarea>');return r.append(o),[r[0],o[0]]}function Ud(e,t){var n=f('<div class="button-container"></div>'),r=f('<button type="button" id="'+e+'">'+t+"</button>");return n.append(r),[n[0],r[0]]}var Kd=function(e){function t(t,n,r){void 0===r&&(r=!1);var o=e.call(this,t,n,r)||this;return o.$body=f("body"),o.modal=null,o.menu=n,o}return ua(t,e),t.prototype.onButtonClick=function(){this.menu.showModal&&this.handleModal()},t.prototype.getPosition=function(){var e=Xd(this),t=this.menu.getModalPositionNode(e);return i.isElement(t)?Ff(e,t,"modal"):Af(e)},t.prototype.handleModal=function(){var e=Xd(this),t=this.menu;if(null==this.modal){var n=new Vd(e,t.modalWidth);this.renderAndShowModal(n,!0),this.modal=n}else{(n=this.modal).isShow?n.hide():this.renderAndShowModal(n,!1)}},t.prototype.renderAndShowModal=function(e,t){void 0===t&&(t=!1);var n=Xd(this),r=this.menu;if(null!=r.getModalContentElem){var o=ol.getTextarea(n),i=ol.getToolbar(n),a=((null==i?void 0:i.getConfig())||{}).modalAppendToBody,s=r.getModalContentElem(n);if(e.renderContent(s),a)e.setStyle({left:"0",right:"0"});else{var l=this.getPosition();e.setStyle(l)}t&&(a?e.appendTo(this.$body):e.appendTo(o.$textAreaContainer)),e.show(),a||If(n,e.$elem),setTimeout((function(){n.blur()}))}},t}(Id);var qd=function(e){function t(t,n){var r=e.call(this,t)||this;return r.type="selectList",r.$elem=f('<div class="w-e-select-list"></div>'),n&&r.$elem.css("width",n+"px"),r.$elem.on("click",(function(e){e.stopPropagation()})),r}return ua(t,e),t.prototype.renderList=function(e){var t=this.$elem;t.empty();var n=f("<ul></ul>");e.forEach((function(e){var t=e.value,r=e.text,o=e.selected,i=e.styleForRenderMenuList,a=f('<li data-value="'+t+'"></li>');if(i&&a.css(i),o){var s=f('<svg viewBox="0 0 1446 1024"><path d="M574.116299 786.736392 1238.811249 48.517862C1272.390222 11.224635 1329.414799 7.827718 1366.75664 41.450462 1403.840015 74.840484 1406.731043 132.084741 1373.10189 169.433699L655.118888 966.834607C653.072421 969.716875 650.835807 972.514337 648.407938 975.210759 615.017957 1012.29409 558.292155 1015.652019 521.195664 982.250188L72.778218 578.493306C35.910826 545.297758 32.859041 488.584019 66.481825 451.242134 99.871807 414.158803 156.597563 410.800834 193.694055 444.202665L574.116299 786.736392Z"></path></svg>');a.append(s),a.addClass("selected")}a.append(f('<span data-value="'+t+'">'+r+"</span>")),a.attr("title",r),n.append(a)})),t.append(n)},t.prototype.genSelfElem=function(){return null},t}(Bd);var Gd=function(){function e(e,t,n){var r=this;void 0===n&&(n=!1),this.$elem=f('<div class="w-e-bar-item"></div>'),this.$button=f('<button type="button" class="select-button"></button>'),this.disabled=!1,this.selectList=null;var o=t.tag,i=t.title,a=t.width,s=t.iconSvg,l=void 0===s?"":s,u=t.hotkey,c=void 0===u?"":u;if("select"!==o)throw new Error("Invalid tag '"+o+"', expected 'select'");var d=this.$button;a&&d.css("width",a+"px"),d.attr("data-menu-key",e),Fd(d,l,i,c,n),this.$elem.append(d),this.menu=t,ac((function(){return r.init()}))}return e.prototype.init=function(){var e=this;this.setSelectedValue(),this.$button.on("click",(function(t){t.preventDefault(),Xd(e).hidePanelOrModal(),e.trigger()}))},e.prototype.trigger=function(){var e=this,t=Xd(this);if(!t.isDisabled()&&!this.disabled){var n=this.menu;if(null==this.selectList){this.selectList=new qd(t,n.selectPanelWidth);var r=this.selectList,o=n.getOptions(t);r.renderList(o),r.appendTo(this.$elem),r.show(),r.$elem.on("click","li",(function(t){var n=t.target;if(null!=n){t.preventDefault();var r=f(n).attr("data-value");e.onChange(r)}}))}else{if((r=this.selectList).isShow)r.hide();else{o=n.getOptions(t);r.renderList(o),r.show()}}}},e.prototype.onChange=function(e){var t=Xd(this),n=this.menu;n.exec&&n.exec(t,e)},e.prototype.setSelectedValue=function(){var e=Xd(this),t=this.menu,n=t.getValue(e),r=function(e,t){for(var n=e.length,r="",o=0;o<n;o++){var i=e[o];if(i.value===t){r=i.text;break}}return r}(t.getOptions(e),n.toString()),o=this.$button,i=jd();o.empty(),o.text(r),o.append(i)},e.prototype.setDisabled=function(){var e=Xd(this),t=this.menu.isDisabled(e),n=this.$button;(null==e.selection||e.isDisabled())&&(t=!0);var r="disabled";t?n.addClass(r):n.removeClass(r),this.disabled=t},e.prototype.changeMenuState=function(){this.setSelectedValue(),this.setDisabled()},e}(),Jd=function(){function e(e){this.$elem=f('<div class="w-e-bar-item w-e-bar-item-group"></div>'),this.$container=f('<div class="w-e-bar-item-menus-container"></div>'),this.$button=f('<button type="button"></button>');var t=e.key,n=e.iconSvg,r=e.title,o=this.$elem,i=this.$button;if(n){var a=f(n);Rd(a),i.append(a)}else i.text(r);i.attr("data-menu-key",t);var s=jd();i.append(s),o.append(i);var l=this.$container;o.append(l);var u=this.createObserver();this.observe(u)}return e.prototype.appendBarItem=function(e){var t=e.$elem;this.$container.append(t)},e.prototype.observe=function(e){var t=this.$container;e.observe(t[0],{childList:!0,subtree:!0,attributes:!0})},e.prototype.createObserver=function(){var e=this,t=this.$container,n=this.$button,r=new MutationObserver((function(){var o=t.find("button"),i=o.length;if(0!==i){var a=0;o.each((function(e){f(e).hasClass("disabled")&&a++})),r.disconnect(),a===i?n.addClass("disabled"):n.removeClass("disabled"),e.observe(r)}}));return r},e}(),Yd=new WeakMap;function Xd(e){var t=ys.get(e);if(null==t)throw new Error("Can not get editor instance");return t}function Qd(e,t,n){void 0===n&&(n=!1);var r=Yd.get(t);if(r)return r;var o=t.tag;if("button"===o){var i=t.showDropPanel,a=t.showModal;r=i?new Wd(e,t,n):a?new Kd(e,t,n):new _d(e,t,n)}if("select"===o&&(r=new Gd(e,t,n)),null==r)throw new Error("Invalid tag in menu "+JSON.stringify(t));return Yd.set(t,r),r}function Zd(e,t){var n=e.selection;return null!=n&&(!o.isCollapsed(n)&&(!ol.getSelectedElems(e).some((function(t){if(e.isVoid(t))return!0;var n=t.type;return!!["pre","code","table"].includes(n)||void 0}))&&!!a.isText(t)))}var eh=function(){function t(){var e=this;this.$elem=f('<div class="w-e-bar w-e-bar-hidden w-e-hover-bar"></div>'),this.menus={},this.hoverbarItems=[],this.prevSelectedNode=null,this.isShow=!1,this.changeHoverbarState=Q((function(){var t=e.isShow,n=e.getSelectedNodeAndMenuKeys()||{},r=n.node,o=void 0===r?null:r,a=n.menuKeys,s=void 0===a?[]:a;if((null!=o&&e.changeItemsState(),o&&i.isElement(o))&&(t&&e.isSamePath(o,e.prevSelectedNode)))return;e.hideAndClean(),null!=o&&(e.registerItems(s),e.setPosition(o),e.show()),e.prevSelectedNode=o}),200),ac((function(){var t=e.getEditorInstance(),n=e.$elem;n.on("mousedown",(function(e){return e.preventDefault()}),{passive:!1}),ol.getTextarea(t).$textAreaContainer.append(n),t.on("change",e.changeHoverbarState);var r=e.hideAndClean.bind(e);t.on("scroll",r),t.on("fullScreen",r),t.on("unFullScreen",r)}))}return t.prototype.getMenus=function(){return this.menus},t.prototype.hideAndClean=function(){var e=this.$elem;e.removeClass("w-e-bar-show").addClass("w-e-bar-hidden"),this.hoverbarItems=[],e.empty(),this.isShow=!1},t.prototype.checkPositionBottom=function(){var e=this.$elem,t=!1,n=window.innerHeight;n&&n>=360&&(n-e[0].getBoundingClientRect().bottom<360&&(t=!0));t?e.addClass("w-e-bar-bottom"):e.removeClass("w-e-bar-bottom")},t.prototype.show=function(){this.$elem.removeClass("w-e-bar-hidden").addClass("w-e-bar-show"),this.isShow=!0,this.checkPositionBottom()},t.prototype.changeItemsState=function(){var e=this;ac((function(){e.hoverbarItems.forEach((function(e){e.changeMenuState()}))}))},t.prototype.registerItems=function(e){var t=this,n=this.$elem;e.forEach((function(e){if("|"!==e)t.registerSingleItem(e);else{var r=Ad();n.append(r)}}))},t.prototype.registerSingleItem=function(e){var t=this.getEditorInstance(),n=this.menus,r=n[e];if(null==r){var o=sl[e];if(null==o)throw new Error("Not found menu item factory by key '"+e+"'");if("function"!=typeof o)throw new Error("Menu item factory (key='"+e+"') is not a function");r=o(),n[e]=r}var i=Qd(e,r);this.hoverbarItems.push(i),ys.set(i,t),this.$elem.append(i.$elem)},t.prototype.setPosition=function(e){var t=this.getEditorInstance(),n=this.$elem;if(n.attr("style",""),i.isElement(e)){var r=Ff(t,e,"bar");return n.css(r),void If(t,n)}if(a.isText(e)){r=Af(t);return n.css(r),void If(t,n)}throw new Error("hoverbar.setPosition error, current selected node is not elem nor text")},t.prototype.getSelectedNodeAndMenuKeys=function(){var t=this.getEditorInstance();if(null==t.selection)return null;var n=this.getHoverbarKeysConf(),r=null,o=[],i=function(i){var a=n[i],s=a.match,l=a.menuKeys,u=void 0===l?[]:l,c=s||function(e,t){return ol.checkNodeType(t,i)},f=da(e.nodes(t,{match:function(e){return c(t,e)},universal:!0}),1),d=f[0];if(null!=d)return r=d[0],o=u,"break"};for(var a in n){if("break"===i(a))break}return null==r||0===o.length?null:{node:r,menuKeys:o}},t.prototype.getEditorInstance=function(){var e=gs.get(this);if(null==e)throw new Error("Can not get editor instance");return e},t.prototype.getHoverbarKeysConf=function(){var e=this.getEditorInstance().getConfig().hoverbarKeys,t=void 0===e?{}:e,n=t.text;return n&&null==n.match&&(n.match=Zd),t},t.prototype.isSamePath=function(e,t){if(null==e||null==t)return!1;var r=ol.findPath(null,e),o=ol.findPath(null,t);return n.equals(r,o)},t.prototype.destroy=function(){this.changeHoverbarState.cancel(),this.$elem.remove(),this.menus={},this.hoverbarItems=[],this.prevSelectedNode=null},t}();function th(t,n,o,a){if(Es.set(t,n),Os.set(t,o),i.isElement(t)){var s=t.children;if((void 0===s?[]:s).forEach((function(e,n){return th(e,n,t,a)})),e.isVoid(a,t)){var l=da(r.texts(t),1),u=da(l[0],1)[0];Es.set(u,0),Os.set(u,t)}}}function nh(n){var i=n.selector,a=void 0===i?"":i,c=n.config,d=void 0===c?{}:c,h=n.content,p=n.html,g=n.plugins,v=void 0===g?[]:g,y=(n=>{var r=n,{apply:o}=r;return r.history={undos:[],redos:[]},r.redo=()=>{var{history:t}=r,{redos:n}=t;if(n.length>0){var o=n[n.length-1];yr.withoutSaving(r,(()=>{e.withoutNormalizing(r,(()=>{for(var e of o)r.apply(e)}))})),t.redos.pop(),t.undos.push(o)}},r.undo=()=>{var{history:n}=r,{undos:o}=n;if(o.length>0){var i=o[o.length-1];yr.withoutSaving(r,(()=>{e.withoutNormalizing(r,(()=>{var e=i.map(t.inverse).reverse();for(var n of e)r.apply(n)}))})),n.redos.push(i),n.undos.pop()}},r.apply=e=>{var{operations:t,history:n}=r,{undos:i}=n,a=i[i.length-1],s=a&&a[a.length-1],l=wr(e,s),u=yr.isSaving(r),c=yr.isMerging(r);if(null==u&&(u=br(e)),u){if(null==c&&(c=null!=a&&(0!==t.length||mr(e,s)||l)),a&&c)l&&a.pop(),a.push(e);else{var f=[e];i.push(f)}for(;i.length>100;)i.shift();xr(e)&&(n.redos=[])}o(e)},r})(function(e){var t=e,n=t.insertText,o=t.insertNode,i=t.insertFragment,a=t.dangerouslyInsertHtml;return t.insertText=function(e){if(t.getConfig().maxLength){var r=ol.getLeftLengthOfMaxLength(t);r<=0||(r<e.length?n(e.slice(0,r)):n(e))}else n(e)},t.insertNode=function(e){if(t.getConfig().maxLength){var n=ol.getLeftLengthOfMaxLength(t);n<=0||n<r.string(e).length||o(e)}else o(e)},t.insertFragment=function(e){if(t.getConfig().maxLength)if(1!==e.length)e.forEach((function(e){t.insertNode(e)}));else{var n=e[0];if(ol.getLeftLengthOfMaxLength(t)<r.string(n).length)return;i(e)}else i(e)},t.dangerouslyInsertHtml=function(e,n){if(void 0===e&&(e=""),void 0===n&&(n=!1),e)if(t.getConfig().maxLength){var r=ol.getLeftLengthOfMaxLength(t);if(!(r<=0)){var o=document.createElement("div");o.innerHTML=e;var i=Array.from(o.childNodes).reduce((function(e,t){var n=t.nodeType,r=t.nodeName;return t?n===Bs.TEXT_NODE?e+(t.textContent||""):n===Bs.ELEMENT_NODE?nf.has(r.toLowerCase())?e:e+(t.textContent||""):e:e}),"");r<i.length||a(e,n)}}else a(e,n)},t}(function(e){var t=e;return t.on=function(e,n){var r=Lf(t);if(r.on(e,n),"destroyed"===e&&Pf(t,n),"destroyed"!==e){var o=function(){return r.off(e,n)};r.on("destroyed",o),Pf(t,o)}},t.once=function(e,n){Lf(t).once(e,n)},t.off=function(e,n){Lf(t).off(e,n)},t.emit=function(e){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];var o=Lf(t);(o.emit.apply(o,ha([e],da(n))),"destroyed"===e)&&(Df(t).forEach((function(e){return o.off("destroyed",e)})),Rf(t))},t}(function(t){var n=t;return n.select=function(e){s.select(n,e)},n.deselect=function(){var e=n.selection,r=ol.findDocumentOrShadowRoot(n).getSelection();r&&r.rangeCount>0&&r.removeAllRanges(),e&&s.deselect(t)},n.move=function(e,n){void 0===n&&(n=!1),e&&(e<0||s.move(t,{distance:e,unit:"character",reverse:n}))},n.moveReverse=function(e){n.move(e,!0)},n.restoreSelection=function(){var e=Rs.get(n);null!=e&&(n.focus(),s.select(n,e))},n.getSelectionPosition=function(){return Af(n)},n.getNodePosition=function(e){return Ff(n,e)},n.isSelectedAll=function(){var t=n.selection;if(null==t)return!1;var r=da(o.edges(t),2),i=r[0],a=r[1],s=da(e.edges(n,[]),2),u=s[0],c=s[1];return!(!l.equals(i,u)||!l.equals(a,c))},n.selectAll=function(){var t=e.start(n,[]),r=e.end(n,[]);s.select(n,{anchor:t,focus:r})},n}(of(function(e){var t=e;return t.getAllMenuKeys=function(){var e=[];for(var t in sl)e.push(t);return e},t.getConfig=function(){var e=ws.get(t);if(null==e)throw new Error("Can not get editor config");return e},t.getMenuConfig=function(e){var n=t.getConfig().MENU_CONF;return(void 0===n?{}:n)[e]||{}},t.alert=function(e,n){void 0===n&&(n="info");var r=t.getConfig().customAlert;r&&r(e,n)},t}(function(t){var n=t;return n.id="wangEditor-"+il++,n.isDestroyed=!1,n.isFullScreen=!1,n.focus=function(t){if(ol.toDOMNode(n,n).focus({preventScroll:!0}),Ds.set(n,!0),t){var r=e.end(n,[]);s.select(n,r)}else{var o=Rs.get(n);o?s.select(n,o):s.select(n,e.start(n,[]))}},n.isFocused=function(){return!!Ds.get(n)},n.blur=function(){ol.toDOMNode(n,n).blur(),s.deselect(n),Ds.set(n,!1)},n.updateView=function(){ol.getTextarea(n).changeViewState();var e=ol.getToolbar(n);e&&e.changeToolbarState();var t=ol.getHoverbar(n);t&&t.changeHoverbarState()},n.destroy=function(){if(!n.isDestroyed){var e=ol.getTextarea(n);e.destroy(),fs.delete(n),ds.delete(e);var t=ol.getToolbar(n);t&&(t.destroy(),ps.delete(n),hs.delete(t));var r=ol.getHoverbar(n);r&&(r.destroy(),vs.delete(n),gs.delete(r)),n.isDestroyed=!0,n.emit("destroyed")}},n.scrollToElem=function(e){if(!n.getConfig().scroll){var t="编辑器禁用了 scroll ，编辑器内容无法滚动，请自行实现该功能";return t+="\nYou has disabled editor scroll, please do this yourself",void console.warn(t)}var r=f("#"+e);if(0!==r.length){var o=r[0];if(!ol.hasDOMNode(n,o))return t="Element (found by id is '"+e+"') is not in editor DOM",t+="\n 通过 id '"+e+"' 找到的 element 不在 editor DOM 之内",void console.error(t,o);var i=ol.getTextarea(n),a=i.$textAreaContainer,s=i.$scroll,l=r.offset().top,u=a.offset().top;s[0].scrollBy({top:l-u,behavior:"smooth"})}},n.showProgressBar=function(e){e<1||ol.getTextarea(n).changeProgress(e)},n.hidePanelOrModal=function(){var e=ms.get(n);null!=e&&e.forEach((function(e){return e.hide()}))},n.enable=function(){n.getConfig().readOnly=!1,n.updateView()},n.disable=function(){n.getConfig().readOnly=!0,n.updateView()},n.isDisabled=function(){return n.getConfig().readOnly},n.toDOMNode=function(e){return ol.toDOMNode(n,e)},n.fullScreen=function(){if(!n.isFullScreen){var e=null,t=ol.getToolbar(n);t&&(e=t.$box);var r=ol.getTextarea(n).$box.parent();if(e&&e.parent()[0]!==r[0])throw new Error("Can not set full screen, cause toolbar DOM parent is not equal to textarea DOM parent\n不能设置全屏，因为 toolbar DOM 父节点和 textarea DOM 父节点不一致");r.addClass("w-e-full-screen-container");var o=r.css("z-index");r.attr("data-z-index",o.toString()),n.isFullScreen=!0,n.emit("fullScreen")}},n.unFullScreen=function(){if(n.isFullScreen){var e=ol.getTextarea(n).$box.parent();setTimeout((function(){e.removeClass("w-e-full-screen-container"),n.isFullScreen=!1,n.emit("unFullScreen")}),200)}},n.getEditableContainer=function(){return ol.getTextarea(n).$textAreaContainer[0]},n}(af(u()))))))));if(a&&function(e,t){return Jc(e,"data-w-e-textarea",t)}(y,a))throw new Error("Repeated create editor by selector '"+a+"'");var m=function(e){void 0===e&&(e={});var t=Z(al),n={},r=e.MENU_CONF,o=void 0===r?{}:r;return _(t,(function(e,t){n[t]=ca(ca({},e),o[t]||{})})),delete e.MENU_CONF,ca({scroll:!0,readOnly:!1,autoFocus:!0,decorate:function(){return[]},maxLength:0,MENU_CONF:n,hoverbarKeys:{},customAlert:function(e,t){window.alert(t+":\n"+e)}},e)}(d);ws.set(y,m);var b=m.hoverbarKeys,w=void 0===b?{}:b;if(v.forEach((function(e){y=e(y)})),null!=p&&(y.children=Yc(y,p)),h&&h.length&&(y.children=h),0===y.children.length&&(y.children=[{type:"paragraph",children:[{text:""}]}]),ol.normalizeContent(y),a){var x=new Dd(a);fs.set(y,x),ds.set(x,y),x.changeViewState(),ac((function(){var e=x.$scroll;if(null!=e&&e.height()<300){"\nTextarea height < 300px . This may be cause modal and hoverbar position error",console.warn("编辑区域高度 < 300px 这可能会导致 modal hoverbar 定位异常\nTextarea height < 300px . This may be cause modal and hoverbar position error",e)}}));var S=void 0;Object.keys(w).length>0&&(S=new eh,gs.set(S,y),vs.set(y,S)),y.on("change",(function(){y.hidePanelOrModal()})),y.on("scroll",(function(){y.hidePanelOrModal()}))}else y.children.forEach((function(e,t){return th(e,t,y,y)}));var k=m.onCreated,E=m.onDestroyed;return k&&y.on("created",(function(){return k(y)})),E&&y.on("destroyed",(function(){return E(y)})),ac((function(){return y.emit("created")})),y}var rh=fi("splice"),oh=se.TypeError,ih=Math.max,ah=Math.min;cr({target:"Array",proto:!0,forced:!rh},{splice:function(e,t){var n,r,o,i,a,s,l=we(this),u=hn(l),c=zn(e,u),f=arguments.length;if(0===f?n=r=0:1===f?(n=0,r=u-c):(n=f-2,r=ah(ih(cn(t),0),u-c)),u+n-r>9007199254740991)throw oh("Maximum allowed length exceeded");for(o=On(l,r),i=0;i<r;i++)(a=c+i)in l&&ao(o,i,l[a]);if(o.length=r,n<r){for(i=c;i<u-r;i++)s=i+n,(a=i+r)in l?l[s]=l[a]:delete l[s];for(i=u;i>u-r+n;i--)delete l[i-1]}else if(n>r)for(i=u-r;i>c;i--)s=i+n-1,(a=i+r-1)in l?l[s]=l[a]:delete l[s];for(i=0;i<n;i++)l[i+c]=arguments[i+2];return l.length=u-r+n,o}});var sh=function(){function e(e,t){var n=this;this.$toolbar=f('<div class="w-e-bar w-e-bar-show w-e-toolbar"></div>'),this.menus={},this.toolbarItems=[],this.config={},this.changeToolbarState=Q((function(){n.toolbarItems.forEach((function(e){e.changeMenuState()}))}),200),this.config=t;var r=f(e);if(0===r.length)throw new Error("Cannot find toolbar DOM by selector '"+e+"'");this.$box=r;var o=this.$toolbar;o.on("mousedown",(function(e){return e.preventDefault()}),{passive:!1}),r.append(o),ac((function(){n.registerItems(),n.changeToolbarState(),n.getEditorInstance().on("change",n.changeToolbarState)}))}return e.prototype.getMenus=function(){return this.menus},e.prototype.getConfig=function(){return this.config},e.prototype.registerItems=function(){var e=this,t="",n=this.$toolbar,r=this.config,o=r.toolbarKeys,i=void 0===o?[]:o,a=r.insertKeys,s=void 0===a?{index:0,keys:[]}:a,l=r.excludeKeys,u=void 0===l?[]:l,c=Z(i);s.keys.length>0&&("string"==typeof s.keys&&(s.keys=[s.keys]),s.keys.forEach((function(e,t){c.splice(s.index+t,0,e)})));var f=c.filter((function(e){if("string"==typeof e){if(u.includes(e))return!1}else if(u.includes(e.key))return!1;return!0})),d=f.length;f.forEach((function(r,o){if("|"===r){if(0===o)return;if(o+1===d)return;if("|"===t)return;var i=Ad();return n.append(i),void(t=r)}if("string"==typeof r)return e.registerSingleItem(r,e),void(t=r);e.registerGroup(r),t="group"}))},e.prototype.registerGroup=function(e){var t=this,n=this.$toolbar,r=function(e){return new Jd(e)}(e),o=e.menuKeys,i=void 0===o?[]:o,a=this.config.excludeKeys,s=void 0===a?[]:a;i.forEach((function(e){s.includes(e)||t.registerSingleItem(e,r)})),n.append(r.$elem)},e.prototype.registerSingleItem=function(e,t){var n=this.getEditorInstance(),r=t instanceof Jd,o=this.menus,i=o[e];if(null==i){var a=sl[e];if(null==a)throw new Error("Not found menu item factory by key '"+e+"'");if("function"!=typeof a)throw new Error("Menu item factory (key='"+e+"') is not a function");i=a(),o[e]=i}else console.warn("Duplicated toolbar menu key '"+e+"'\n重复注册了菜单栏 menu '"+e+"'");var s=Qd(e,i,r);(this.toolbarItems.push(s),ys.set(s,n),r)?t.appendBarItem(s):t.$toolbar.append(s.$elem)},e.prototype.getEditorInstance=function(){var e=hs.get(this);if(null==e)throw new Error("Can not get editor instance");return e},e.prototype.destroy=function(){this.$toolbar.remove(),this.menus={},this.toolbarItems=[]},e}();function lh(e,t){if(null==e)throw new Error("Cannot create toolbar, because editor is null");var n=t.selector,r=t.config,o=void 0===r?{}:r;if(function(e,t){return Jc(e,"data-w-e-toolbar",t)}(e,n))throw new Error("Repeated create toolbar by selector '"+n+"'");var i=ca({toolbarKeys:[],excludeKeys:[],insertKeys:{index:0,keys:[]},modalAppendToBody:!1},o||{}),a=new sh(n,i);return hs.set(a,e),ps.set(e,a),a}var uh=Vt.EXISTS,ch=mt.f,fh=Function.prototype,dh=ve(fh.toString),hh=/function\b(?:\s|\/\*[\S\s]*?\*\/|\/\/[^\n\r]*[\n\r]+)*([^\s(/]*)/,ph=ve(hh.exec);function gh(e){var t=e.server,n=void 0===t?"":t,r=e.fieldName,o=void 0===r?"":r,i=e.maxFileSize,a=void 0===i?10485760:i,s=e.maxNumberOfFiles,l=void 0===s?100:s,u=e.meta,c=void 0===u?{}:u,f=e.metaWithUrl,d=void 0!==f&&f,h=e.headers,p=void 0===h?{}:h,g=e.withCredentials,v=void 0!==g&&g,y=e.timeout,m=void 0===y?1e4:y,b=e.onBeforeUpload,w=void 0===b?function(e){return e}:b,x=e.onSuccess,S=void 0===x?function(e,t){}:x,k=e.onError,E=void 0===k?function(e,t,n){console.error(e.name+" upload error",t,n)}:k,O=e.onProgress,C=void 0===O?function(e){}:O;if(!n)throw new Error("Cannot get upload server address\n没有配置上传地址");if(!o)throw new Error("Cannot get fieldName\n没有配置 fieldName");var N=n;d&&(N=function(e,t){var n=da(e.split("#"),2),r=n[0],o=n[1],i=[];_(t,(function(e,t){i.push(t+"="+e)}));var a=i.join("&");return r=r.indexOf("?")>0?r+"&"+a:r+"?"+a,o?r+"#"+o:r}(N,c));var T=new ee({onBeforeUpload:w,restrictions:{maxFileSize:a,maxNumberOfFiles:l},meta:c}).use(te,{endpoint:N,headers:p,formData:!0,fieldName:o,bundle:!0,withCredentials:v,timeout:m});return T.on("upload-success",(function(e,t){var n=t.body,r=void 0===n?{}:n;try{S(e,r)}catch(e){console.error("wangEditor upload file - onSuccess error",e)}T.removeFile(e.id)})),T.on("progress",(function(e){e<1||C(e)})),T.on("upload-error",(function(e,t,n){try{E(e,t,n)}catch(e){console.error("wangEditor upload file - onError error",e)}T.removeFile(e.id)})),T.on("restriction-failed",(function(e,t){try{E(e,t)}catch(e){console.error("wangEditor upload file - onError error",e)}T.removeFile(e.id)})),T}function vh(e){return vh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vh(e)}function yh(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function mh(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&r.push.apply(r,Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach((function(t){yh(e,t,n[t])}))}return e}function bh(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function wh(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function xh(e,t,n){return t&&wh(e.prototype,t),n&&wh(e,n),e}function Sh(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function kh(e,t){if(t&&("object"===vh(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Sh(e)}function Eh(e){return Eh=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},Eh(e)}function Oh(e,t){return Oh=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},Oh(e,t)}function Ch(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Oh(e,t)}Ke&&!uh&&ch(fh,"name",{configurable:!0,get:function(){try{return ph(hh,dh(this))[1]}catch(e){return""}}});var Nh={type:"logger",log:function(e){this.output("log",e)},warn:function(e){this.output("warn",e)},error:function(e){this.output("error",e)},output:function(e,t){console&&console[e]&&console[e].apply(console,t)}},Th=new(function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};bh(this,e),this.init(t,n)}return xh(e,[{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||Nh,this.options=t,this.debug=t.debug}},{key:"setDebug",value:function(e){this.debug=e}},{key:"log",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}},{key:"warn",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}},{key:"error",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}},{key:"deprecate",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}},{key:"forward",value:function(e,t,n,r){return r&&!this.debug?null:("string"==typeof e[0]&&(e[0]="".concat(n).concat(this.prefix," ").concat(e[0])),this.logger[t](e))}},{key:"create",value:function(t){return new e(this.logger,mh({},{prefix:"".concat(this.prefix,":").concat(t,":")},this.options))}}]),e}()),Lh=function(){function e(){bh(this,e),this.observers={}}return xh(e,[{key:"on",value:function(e,t){var n=this;return e.split(" ").forEach((function(e){n.observers[e]=n.observers[e]||[],n.observers[e].push(t)})),this}},{key:"off",value:function(e,t){this.observers[e]&&(t?this.observers[e]=this.observers[e].filter((function(e){return e!==t})):delete this.observers[e])}},{key:"emit",value:function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(this.observers[e]){var o=[].concat(this.observers[e]);o.forEach((function(e){e.apply(void 0,n)}))}if(this.observers["*"]){var i=[].concat(this.observers["*"]);i.forEach((function(t){t.apply(t,[e].concat(n))}))}}}]),e}();function Mh(){var e,t,n=new Promise((function(n,r){e=n,t=r}));return n.resolve=e,n.reject=t,n}function Ph(e){return null==e?"":""+e}function Dh(e,t,n){e.forEach((function(e){t[e]&&(n[e]=t[e])}))}function Rh(e,t,n){function r(e){return e&&e.indexOf("###")>-1?e.replace(/###/g,"."):e}function o(){return!e||"string"==typeof e}for(var i="string"!=typeof t?[].concat(t):t.split(".");i.length>1;){if(o())return{};var a=r(i.shift());!e[a]&&n&&(e[a]=new n),e=Object.prototype.hasOwnProperty.call(e,a)?e[a]:{}}return o()?{}:{obj:e,k:r(i.shift())}}function jh(e,t,n){var r=Rh(e,t,Object);r.obj[r.k]=n}function Ah(e,t){var n=Rh(e,t),r=n.obj,o=n.k;if(r)return r[o]}function Fh(e,t,n){var r=Ah(e,n);return void 0!==r?r:Ah(t,n)}function Ih(e,t,n){for(var r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?"string"==typeof e[r]||e[r]instanceof String||"string"==typeof t[r]||t[r]instanceof String?n&&(e[r]=t[r]):Ih(e[r],t[r],n):e[r]=t[r]);return e}function _h(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var Bh={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function $h(e){return"string"==typeof e?e.replace(/[&<>"'\/]/g,(function(e){return Bh[e]})):e}var Wh="undefined"!=typeof window&&window.navigator&&window.navigator.userAgent&&window.navigator.userAgent.indexOf("MSIE")>-1;function Vh(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(e){if(e[t])return e[t];for(var r=t.split(n),o=e,i=0;i<r.length;++i){if(!o)return;if("string"==typeof o[r[i]]&&i+1<r.length)return;if(void 0===o[r[i]]){for(var a=2,s=r.slice(i,i+a).join(n),l=o[s];void 0===l&&r.length>i+a;)a++,l=o[s=r.slice(i,i+a).join(n)];if(void 0===l)return;if("string"==typeof l)return l;if(s&&"string"==typeof l[s])return l[s];var u=r.slice(i+a).join(n);return u?Vh(l,u,n):void 0}o=o[r[i]]}return o}}var zh=function(e){function t(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};return bh(this,t),n=kh(this,Eh(t).call(this)),Wh&&Lh.call(Sh(n)),n.data=e||{},n.options=r,void 0===n.options.keySeparator&&(n.options.keySeparator="."),void 0===n.options.ignoreJSONStructure&&(n.options.ignoreJSONStructure=!0),n}return Ch(t,Lh),xh(t,[{key:"addNamespaces",value:function(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}},{key:"removeNamespaces",value:function(e){var t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}},{key:"getResource",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,i=void 0!==r.ignoreJSONStructure?r.ignoreJSONStructure:this.options.ignoreJSONStructure,a=[e,t];n&&"string"!=typeof n&&(a=a.concat(n)),n&&"string"==typeof n&&(a=a.concat(o?n.split(o):n)),e.indexOf(".")>-1&&(a=e.split("."));var s=Ah(this.data,a);return s||!i||"string"!=typeof n?s:Vh(this.data&&this.data[e]&&this.data[e][t],n,o)}},{key:"addResource",value:function(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},i=this.options.keySeparator;void 0===i&&(i=".");var a=[e,t];n&&(a=a.concat(i?n.split(i):n)),e.indexOf(".")>-1&&(r=t,t=(a=e.split("."))[1]),this.addNamespaces(t),jh(this.data,a,r),o.silent||this.emit("added",e,t,n,r)}},{key:"addResources",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(var o in n)"string"!=typeof n[o]&&"[object Array]"!==Object.prototype.toString.apply(n[o])||this.addResource(e,t,o,n[o],{silent:!0});r.silent||this.emit("added",e,t,n)}},{key:"addResourceBundle",value:function(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1},a=[e,t];e.indexOf(".")>-1&&(r=n,n=t,t=(a=e.split("."))[1]),this.addNamespaces(t);var s=Ah(this.data,a)||{};r?Ih(s,n,o):s=mh({},s,n),jh(this.data,a,s),i.silent||this.emit("added",e,t,n)}},{key:"removeResourceBundle",value:function(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}},{key:"hasResourceBundle",value:function(e,t){return void 0!==this.getResource(e,t)}},{key:"getResourceBundle",value:function(e,t){return t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI?mh({},{},this.getResource(e,t)):this.getResource(e,t)}},{key:"getDataByLanguage",value:function(e){return this.data[e]}},{key:"toJSON",value:function(){return this.data}}]),t}(),Hh={processors:{},addPostProcessor:function(e){this.processors[e.name]=e},handle:function(e,t,n,r,o){var i=this;return e.forEach((function(e){i.processors[e]&&(t=i.processors[e].process(t,n,r,o))})),t}},Uh={},Kh=function(e){function t(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return bh(this,t),n=kh(this,Eh(t).call(this)),Wh&&Lh.call(Sh(n)),Dh(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,Sh(n)),n.options=r,void 0===n.options.keySeparator&&(n.options.keySeparator="."),n.logger=Th.create("translator"),n}return Ch(t,Lh),xh(t,[{key:"changeLanguage",value:function(e){e&&(this.language=e)}},{key:"exists",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;var n=this.resolve(e,t);return n&&void 0!==n.res}},{key:"extractFromKey",value:function(e,t){var n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");var r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,o=t.ns||this.options.defaultNS;if(n&&e.indexOf(n)>-1){var i=e.match(this.interpolator.nestingRegexp);if(i&&i.length>0)return{key:e,namespaces:o};var a=e.split(n);(n!==r||n===r&&this.options.ns.indexOf(a[0])>-1)&&(o=a.shift()),e=a.join(r)}return"string"==typeof o&&(o=[o]),{key:e,namespaces:o}}},{key:"translate",value:function(e,n,r){var o=this;if("object"!==vh(n)&&this.options.overloadTranslationOptionHandler&&(n=this.options.overloadTranslationOptionHandler(arguments)),n||(n={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);var i=void 0!==n.keySeparator?n.keySeparator:this.options.keySeparator,a=this.extractFromKey(e[e.length-1],n),s=a.key,l=a.namespaces,u=l[l.length-1],c=n.lng||this.language,f=n.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(c&&"cimode"===c.toLowerCase()){if(f){var d=n.nsSeparator||this.options.nsSeparator;return u+d+s}return s}var h=this.resolve(e,n),p=h&&h.res,g=h&&h.usedKey||s,v=h&&h.exactUsedKey||s,y=Object.prototype.toString.apply(p),m=["[object Number]","[object Function]","[object RegExp]"],b=void 0!==n.joinArrays?n.joinArrays:this.options.joinArrays,w=!this.i18nFormat||this.i18nFormat.handleAsObject,x="string"!=typeof p&&"boolean"!=typeof p&&"number"!=typeof p;if(w&&p&&x&&m.indexOf(y)<0&&("string"!=typeof b||"[object Array]"!==y)){if(!n.returnObjects&&!this.options.returnObjects)return this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!"),this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,p,mh({},n,{ns:l})):"key '".concat(s," (").concat(this.language,")' returned an object instead of string.");if(i){var S="[object Array]"===y,k=S?[]:{},E=S?v:g;for(var O in p)if(Object.prototype.hasOwnProperty.call(p,O)){var C="".concat(E).concat(i).concat(O);k[O]=this.translate(C,mh({},n,{joinArrays:!1,ns:l})),k[O]===C&&(k[O]=p[O])}p=k}}else if(w&&"string"==typeof b&&"[object Array]"===y)(p=p.join(b))&&(p=this.extendTranslation(p,e,n,r));else{var N=!1,T=!1,L=void 0!==n.count&&"string"!=typeof n.count,M=t.hasDefaultValue(n),P=L?this.pluralResolver.getSuffix(c,n.count):"",D=n["defaultValue".concat(P)]||n.defaultValue;!this.isValidLookup(p)&&M&&(N=!0,p=D),this.isValidLookup(p)||(T=!0,p=s);var R=n.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey,j=R&&T?void 0:p,A=M&&D!==p&&this.options.updateMissing;if(T||N||A){if(this.logger.log(A?"updateKey":"missingKey",c,u,s,A?D:p),i){var F=this.resolve(s,mh({},n,{keySeparator:!1}));F&&F.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}var I=[],_=this.languageUtils.getFallbackCodes(this.options.fallbackLng,n.lng||this.language);if("fallback"===this.options.saveMissingTo&&_&&_[0])for(var B=0;B<_.length;B++)I.push(_[B]);else"all"===this.options.saveMissingTo?I=this.languageUtils.toResolveHierarchy(n.lng||this.language):I.push(n.lng||this.language);var $=function(e,t,r){o.options.missingKeyHandler?o.options.missingKeyHandler(e,u,t,A?r:j,A,n):o.backendConnector&&o.backendConnector.saveMissing&&o.backendConnector.saveMissing(e,u,t,A?r:j,A,n),o.emit("missingKey",e,u,t,p)};this.options.saveMissing&&(this.options.saveMissingPlurals&&L?I.forEach((function(e){o.pluralResolver.getSuffixes(e).forEach((function(t){$([e],s+t,n["defaultValue".concat(t)]||D)}))})):$(I,s,D))}p=this.extendTranslation(p,e,n,h,r),T&&p===s&&this.options.appendNamespaceToMissingKey&&(p="".concat(u,":").concat(s)),(T||N)&&this.options.parseMissingKeyHandler&&(p=this.options.parseMissingKeyHandler(p))}return p}},{key:"extendTranslation",value:function(e,t,n,r,o){var i=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,n,r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init(mh({},n,{interpolation:mh({},this.options.interpolation,n.interpolation)}));var a,s=n.interpolation&&n.interpolation.skipOnVariables||this.options.interpolation.skipOnVariables;if(s){var l=e.match(this.interpolator.nestingRegexp);a=l&&l.length}var u=n.replace&&"string"!=typeof n.replace?n.replace:n;if(this.options.interpolation.defaultVariables&&(u=mh({},this.options.interpolation.defaultVariables,u)),e=this.interpolator.interpolate(e,u,n.lng||this.language,n),s){var c=e.match(this.interpolator.nestingRegexp);a<(c&&c.length)&&(n.nest=!1)}!1!==n.nest&&(e=this.interpolator.nest(e,(function(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return o&&o[0]===r[0]&&!n.context?(i.logger.warn("It seems you are nesting recursively key: ".concat(r[0]," in key: ").concat(t[0])),null):i.translate.apply(i,r.concat([t]))}),n)),n.interpolation&&this.interpolator.reset()}var f=n.postProcess||this.options.postProcess,d="string"==typeof f?[f]:f;return null!=e&&d&&d.length&&!1!==n.applyPostProcessor&&(e=Hh.handle(d,e,t,this.options&&this.options.postProcessPassResolved?mh({i18nResolved:r},n):n,this)),e}},{key:"resolve",value:function(e){var t,n,r,o,i,a=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"string"==typeof e&&(e=[e]),e.forEach((function(e){if(!a.isValidLookup(t)){var l=a.extractFromKey(e,s),u=l.key;n=u;var c=l.namespaces;a.options.fallbackNS&&(c=c.concat(a.options.fallbackNS));var f=void 0!==s.count&&"string"!=typeof s.count,d=void 0!==s.context&&("string"==typeof s.context||"number"==typeof s.context)&&""!==s.context,h=s.lngs?s.lngs:a.languageUtils.toResolveHierarchy(s.lng||a.language,s.fallbackLng);c.forEach((function(e){a.isValidLookup(t)||(i=e,!Uh["".concat(h[0],"-").concat(e)]&&a.utils&&a.utils.hasLoadedNamespace&&!a.utils.hasLoadedNamespace(i)&&(Uh["".concat(h[0],"-").concat(e)]=!0,a.logger.warn('key "'.concat(n,'" for languages "').concat(h.join(", "),'" won\'t get resolved as namespace "').concat(i,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),h.forEach((function(n){if(!a.isValidLookup(t)){o=n;var i,l,c=u,h=[c];if(a.i18nFormat&&a.i18nFormat.addLookupKeys)a.i18nFormat.addLookupKeys(h,u,n,e,s);else f&&(i=a.pluralResolver.getSuffix(n,s.count)),f&&d&&h.push(c+i),d&&h.push(c+="".concat(a.options.contextSeparator).concat(s.context)),f&&h.push(c+=i);for(;l=h.pop();)a.isValidLookup(t)||(r=l,t=a.getResource(n,e,l,s))}})))}))}})),{res:t,usedKey:n,exactUsedKey:r,usedLng:o,usedNS:i}}},{key:"isValidLookup",value:function(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}},{key:"getResource",value:function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}}],[{key:"hasDefaultValue",value:function(e){var t="defaultValue";for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,t.length)&&void 0!==e[n])return!0;return!1}}]),t}();function qh(e){return e.charAt(0).toUpperCase()+e.slice(1)}var Gh=function(){function e(t){bh(this,e),this.options=t,this.whitelist=this.options.supportedLngs||!1,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Th.create("languageUtils")}return xh(e,[{key:"getScriptPartFromCode",value:function(e){if(!e||e.indexOf("-")<0)return null;var t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}},{key:"getLanguagePartFromCode",value:function(e){if(!e||e.indexOf("-")<0)return e;var t=e.split("-");return this.formatLanguageCode(t[0])}},{key:"formatLanguageCode",value:function(e){if("string"==typeof e&&e.indexOf("-")>-1){var t=["hans","hant","latn","cyrl","cans","mong","arab"],n=e.split("-");return this.options.lowerCaseLng?n=n.map((function(e){return e.toLowerCase()})):2===n.length?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=qh(n[1].toLowerCase()))):3===n.length&&(n[0]=n[0].toLowerCase(),2===n[1].length&&(n[1]=n[1].toUpperCase()),"sgn"!==n[0]&&2===n[2].length&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=qh(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=qh(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}},{key:"isWhitelisted",value:function(e){return this.logger.deprecate("languageUtils.isWhitelisted",'function "isWhitelisted" will be renamed to "isSupportedCode" in the next major - please make sure to rename it\'s usage asap.'),this.isSupportedCode(e)}},{key:"isSupportedCode",value:function(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}},{key:"getBestMatchFromCodes",value:function(e){var t,n=this;return e?(e.forEach((function(e){if(!t){var r=n.formatLanguageCode(e);n.options.supportedLngs&&!n.isSupportedCode(r)||(t=r)}})),!t&&this.options.supportedLngs&&e.forEach((function(e){if(!t){var r=n.getLanguagePartFromCode(e);if(n.isSupportedCode(r))return t=r;t=n.options.supportedLngs.find((function(e){if(0===e.indexOf(r))return e}))}})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}},{key:"getFallbackCodes",value:function(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),"string"==typeof e&&(e=[e]),"[object Array]"===Object.prototype.toString.apply(e))return e;if(!t)return e.default||[];var n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}},{key:"toResolveHierarchy",value:function(e,t){var n=this,r=this.getFallbackCodes(t||this.options.fallbackLng||[],e),o=[],i=function(e){e&&(n.isSupportedCode(e)?o.push(e):n.logger.warn("rejecting language code not found in supportedLngs: ".concat(e)))};return"string"==typeof e&&e.indexOf("-")>-1?("languageOnly"!==this.options.load&&i(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&i(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&i(this.getLanguagePartFromCode(e))):"string"==typeof e&&i(this.formatLanguageCode(e)),r.forEach((function(e){o.indexOf(e)<0&&i(n.formatLanguageCode(e))})),o}}]),e}(),Jh=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],Yh={1:function(e){return Number(e>1)},2:function(e){return Number(1!=e)},3:function(e){return 0},4:function(e){return Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2)},5:function(e){return Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5)},6:function(e){return Number(1==e?0:e>=2&&e<=4?1:2)},7:function(e){return Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2)},8:function(e){return Number(1==e?0:2==e?1:8!=e&&11!=e?2:3)},9:function(e){return Number(e>=2)},10:function(e){return Number(1==e?0:2==e?1:e<7?2:e<11?3:4)},11:function(e){return Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3)},12:function(e){return Number(e%10!=1||e%100==11)},13:function(e){return Number(0!==e)},14:function(e){return Number(1==e?0:2==e?1:3==e?2:3)},15:function(e){return Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2)},16:function(e){return Number(e%10==1&&e%100!=11?0:0!==e?1:2)},17:function(e){return Number(1==e||e%10==1&&e%100!=11?0:1)},18:function(e){return Number(0==e?0:1==e?1:2)},19:function(e){return Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3)},20:function(e){return Number(1==e?0:0==e||e%100>0&&e%100<20?1:2)},21:function(e){return Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0)},22:function(e){return Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)}};function Xh(){var e={};return Jh.forEach((function(t){t.lngs.forEach((function(n){e[n]={numbers:t.nr,plurals:Yh[t.fc]}}))})),e}var Qh=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};bh(this,e),this.languageUtils=t,this.options=n,this.logger=Th.create("pluralResolver"),this.rules=Xh()}return xh(e,[{key:"addRule",value:function(e,t){this.rules[e]=t}},{key:"getRule",value:function(e){return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}},{key:"needsPlural",value:function(e){var t=this.getRule(e);return t&&t.numbers.length>1}},{key:"getPluralFormsOfKey",value:function(e,t){return this.getSuffixes(e).map((function(e){return t+e}))}},{key:"getSuffixes",value:function(e){var t=this,n=this.getRule(e);return n?n.numbers.map((function(n){return t.getSuffix(e,n)})):[]}},{key:"getSuffix",value:function(e,t){var n=this,r=this.getRule(e);if(r){var o=r.noAbs?r.plurals(t):r.plurals(Math.abs(t)),i=r.numbers[o];this.options.simplifyPluralSuffix&&2===r.numbers.length&&1===r.numbers[0]&&(2===i?i="plural":1===i&&(i=""));var a=function(){return n.options.prepend&&i.toString()?n.options.prepend+i.toString():i.toString()};return"v1"===this.options.compatibilityJSON?1===i?"":"number"==typeof i?"_plural_".concat(i.toString()):a():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===r.numbers.length&&1===r.numbers[0]?a():this.options.prepend&&o.toString()?this.options.prepend+o.toString():o.toString()}return this.logger.warn("no plural rule found for: ".concat(e)),""}}]),e}(),Zh=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};bh(this,e),this.logger=Th.create("interpolator"),this.options=t,this.format=t.interpolation&&t.interpolation.format||function(e){return e},this.init(t)}return xh(e,[{key:"init",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});var t=e.interpolation;this.escape=void 0!==t.escape?t.escape:$h,this.escapeValue=void 0===t.escapeValue||t.escapeValue,this.useRawValueToEscape=void 0!==t.useRawValueToEscape&&t.useRawValueToEscape,this.prefix=t.prefix?_h(t.prefix):t.prefixEscaped||"{{",this.suffix=t.suffix?_h(t.suffix):t.suffixEscaped||"}}",this.formatSeparator=t.formatSeparator?t.formatSeparator:t.formatSeparator||",",this.unescapePrefix=t.unescapeSuffix?"":t.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":t.unescapeSuffix||"",this.nestingPrefix=t.nestingPrefix?_h(t.nestingPrefix):t.nestingPrefixEscaped||_h("$t("),this.nestingSuffix=t.nestingSuffix?_h(t.nestingSuffix):t.nestingSuffixEscaped||_h(")"),this.nestingOptionsSeparator=t.nestingOptionsSeparator?t.nestingOptionsSeparator:t.nestingOptionsSeparator||",",this.maxReplaces=t.maxReplaces?t.maxReplaces:1e3,this.alwaysFormat=void 0!==t.alwaysFormat&&t.alwaysFormat,this.resetRegExp()}},{key:"reset",value:function(){this.options&&this.init(this.options)}},{key:"resetRegExp",value:function(){var e="".concat(this.prefix,"(.+?)").concat(this.suffix);this.regexp=new RegExp(e,"g");var t="".concat(this.prefix).concat(this.unescapePrefix,"(.+?)").concat(this.unescapeSuffix).concat(this.suffix);this.regexpUnescape=new RegExp(t,"g");var n="".concat(this.nestingPrefix,"(.+?)").concat(this.nestingSuffix);this.nestingRegexp=new RegExp(n,"g")}},{key:"interpolate",value:function(e,t,n,r){var o,i,a,s=this,l=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function u(e){return e.replace(/\$/g,"$$$$")}var c=function(e){if(e.indexOf(s.formatSeparator)<0){var o=Fh(t,l,e);return s.alwaysFormat?s.format(o,void 0,n,mh({},r,t,{interpolationkey:e})):o}var i=e.split(s.formatSeparator),a=i.shift().trim(),u=i.join(s.formatSeparator).trim();return s.format(Fh(t,l,a),u,n,mh({},r,t,{interpolationkey:a}))};this.resetRegExp();var f=r&&r.missingInterpolationHandler||this.options.missingInterpolationHandler,d=r&&r.interpolation&&r.interpolation.skipOnVariables||this.options.interpolation.skipOnVariables,h=[{regex:this.regexpUnescape,safeValue:function(e){return u(e)}},{regex:this.regexp,safeValue:function(e){return s.escapeValue?u(s.escape(e)):u(e)}}];return h.forEach((function(t){for(a=0;o=t.regex.exec(e);){if(void 0===(i=c(o[1].trim())))if("function"==typeof f){var n=f(e,o,r);i="string"==typeof n?n:""}else{if(d){i=o[0];continue}s.logger.warn("missed to pass in variable ".concat(o[1]," for interpolating ").concat(e)),i=""}else"string"==typeof i||s.useRawValueToEscape||(i=Ph(i));var l=t.safeValue(i);if(e=e.replace(o[0],l),d?(t.regex.lastIndex+=l.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,++a>=s.maxReplaces)break}})),e}},{key:"nest",value:function(e,t){var n,r,o=this,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=mh({},i);function s(e,t){var n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;var r=e.split(new RegExp("".concat(n,"[ ]*{"))),o="{".concat(r[1]);e=r[0],o=(o=this.interpolate(o,a)).replace(/'/g,'"');try{a=JSON.parse(o),t&&(a=mh({},t,a))}catch(t){return this.logger.warn("failed parsing options string in nesting for key ".concat(e),t),"".concat(e).concat(n).concat(o)}return delete a.defaultValue,e}for(a.applyPostProcessor=!1,delete a.defaultValue;n=this.nestingRegexp.exec(e);){var l=[],u=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){var c=n[1].split(this.formatSeparator).map((function(e){return e.trim()}));n[1]=c.shift(),l=c,u=!0}if((r=t(s.call(this,n[1].trim(),a),a))&&n[0]===e&&"string"!=typeof r)return r;"string"!=typeof r&&(r=Ph(r)),r||(this.logger.warn("missed to resolve ".concat(n[1]," for nesting ").concat(e)),r=""),u&&(r=l.reduce((function(e,t){return o.format(e,t,i.lng,mh({},i,{interpolationkey:n[1].trim()}))}),r.trim())),e=e.replace(n[0],r),this.regexp.lastIndex=0}return e}}]),e}();var ep=function(e){function t(e,n,r){var o,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return bh(this,t),o=kh(this,Eh(t).call(this)),Wh&&Lh.call(Sh(o)),o.backend=e,o.store=n,o.services=r,o.languageUtils=r.languageUtils,o.options=i,o.logger=Th.create("backendConnector"),o.state={},o.queue=[],o.backend&&o.backend.init&&o.backend.init(r,i.backend,i),o}return Ch(t,Lh),xh(t,[{key:"queueLoad",value:function(e,t,n,r){var o=this,i=[],a=[],s=[],l=[];return e.forEach((function(e){var r=!0;t.forEach((function(t){var s="".concat(e,"|").concat(t);!n.reload&&o.store.hasResourceBundle(e,t)?o.state[s]=2:o.state[s]<0||(1===o.state[s]?a.indexOf(s)<0&&a.push(s):(o.state[s]=1,r=!1,a.indexOf(s)<0&&a.push(s),i.indexOf(s)<0&&i.push(s),l.indexOf(t)<0&&l.push(t)))})),r||s.push(e)})),(i.length||a.length)&&this.queue.push({pending:a,loaded:{},errors:[],callback:r}),{toLoad:i,pending:a,toLoadLanguages:s,toLoadNamespaces:l}}},{key:"loaded",value:function(e,t,n){var r=e.split("|"),o=r[0],i=r[1];t&&this.emit("failedLoading",o,i,t),n&&this.store.addResourceBundle(o,i,n),this.state[e]=t?-1:2;var a={};this.queue.forEach((function(n){!function(e,t,n,r){var o=Rh(e,t,Object),i=o.obj,a=o.k;i[a]=i[a]||[],r&&(i[a]=i[a].concat(n)),r||i[a].push(n)}(n.loaded,[o],i),function(e,t){for(var n=e.indexOf(t);-1!==n;)e.splice(n,1),n=e.indexOf(t)}(n.pending,e),t&&n.errors.push(t),0!==n.pending.length||n.done||(Object.keys(n.loaded).forEach((function(e){a[e]||(a[e]=[]),n.loaded[e].length&&n.loaded[e].forEach((function(t){a[e].indexOf(t)<0&&a[e].push(t)}))})),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())})),this.emit("loaded",a),this.queue=this.queue.filter((function(e){return!e.done}))}},{key:"read",value:function(e,t,n){var r=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:350,a=arguments.length>5?arguments[5]:void 0;return e.length?this.backend[n](e,t,(function(s,l){s&&l&&o<5?setTimeout((function(){r.read.call(r,e,t,n,o+1,2*i,a)}),i):a(s,l)})):a(null,{})}},{key:"prepareLoading",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();"string"==typeof e&&(e=this.languageUtils.toResolveHierarchy(e)),"string"==typeof t&&(t=[t]);var i=this.queueLoad(e,t,r,o);if(!i.toLoad.length)return i.pending.length||o(),null;i.toLoad.forEach((function(e){n.loadOne(e)}))}},{key:"load",value:function(e,t,n){this.prepareLoading(e,t,{},n)}},{key:"reload",value:function(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}},{key:"loadOne",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=e.split("|"),o=r[0],i=r[1];this.read(o,i,"read",void 0,void 0,(function(r,a){r&&t.logger.warn("".concat(n,"loading namespace ").concat(i," for language ").concat(o," failed"),r),!r&&a&&t.logger.log("".concat(n,"loaded namespace ").concat(i," for language ").concat(o),a),t.loaded(e,r,a)}))}},{key:"saveMissing",value:function(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)?this.logger.warn('did not save key "'.concat(n,'" as the namespace "').concat(t,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!"):null!=n&&""!==n&&(this.backend&&this.backend.create&&this.backend.create(e,t,n,r,null,mh({},i,{isUpdate:o})),e&&e[0]&&this.store.addResource(e[0],t,n,r))}}]),t}();function tp(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,whitelist:!1,nonExplicitWhitelist:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!0,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(e){var t={};if("object"===vh(e[1])&&(t=e[1]),"string"==typeof e[1]&&(t.defaultValue=e[1]),"string"==typeof e[2]&&(t.tDescription=e[2]),"object"===vh(e[2])||"object"===vh(e[3])){var n=e[3]||e[2];Object.keys(n).forEach((function(e){t[e]=n[e]}))}return t},interpolation:{escapeValue:!0,format:function(e,t,n,r){return e},prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!1}}}function np(e){return"string"==typeof e.ns&&(e.ns=[e.ns]),"string"==typeof e.fallbackLng&&(e.fallbackLng=[e.fallbackLng]),"string"==typeof e.fallbackNS&&(e.fallbackNS=[e.fallbackNS]),e.whitelist&&(e.whitelist&&e.whitelist.indexOf("cimode")<0&&(e.whitelist=e.whitelist.concat(["cimode"])),e.supportedLngs=e.whitelist),e.nonExplicitWhitelist&&(e.nonExplicitSupportedLngs=e.nonExplicitWhitelist),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e}function rp(){}var op=function(e){function t(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;if(bh(this,t),e=kh(this,Eh(t).call(this)),Wh&&Lh.call(Sh(e)),e.options=np(n),e.services={},e.logger=Th,e.modules={external:[]},r&&!e.isInitialized&&!n.isClone){if(!e.options.initImmediate)return e.init(n,r),kh(e,Sh(e));setTimeout((function(){e.init(n,r)}),0)}return e}return Ch(t,Lh),xh(t,[{key:"init",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;function r(e){return e?"function"==typeof e?new e:e:null}if("function"==typeof t&&(n=t,t={}),t.whitelist&&!t.supportedLngs&&this.logger.deprecate("whitelist",'option "whitelist" will be renamed to "supportedLngs" in the next major - please make sure to rename this option asap.'),t.nonExplicitWhitelist&&!t.nonExplicitSupportedLngs&&this.logger.deprecate("whitelist",'options "nonExplicitWhitelist" will be renamed to "nonExplicitSupportedLngs" in the next major - please make sure to rename this option asap.'),this.options=mh({},tp(),this.options,np(t)),this.format=this.options.interpolation.format,n||(n=rp),!this.options.isClone){this.modules.logger?Th.init(r(this.modules.logger),this.options):Th.init(null,this.options);var o=new Gh(this.options);this.store=new zh(this.options.resources,this.options);var i=this.services;i.logger=Th,i.resourceStore=this.store,i.languageUtils=o,i.pluralResolver=new Qh(o,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),i.interpolator=new Zh(this.options),i.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},i.backendConnector=new ep(r(this.modules.backend),i.resourceStore,i,this.options),i.backendConnector.on("*",(function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];e.emit.apply(e,[t].concat(r))})),this.modules.languageDetector&&(i.languageDetector=r(this.modules.languageDetector),i.languageDetector.init(i,this.options.detection,this.options)),this.modules.i18nFormat&&(i.i18nFormat=r(this.modules.i18nFormat),i.i18nFormat.init&&i.i18nFormat.init(this)),this.translator=new Kh(this.services,this.options),this.translator.on("*",(function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];e.emit.apply(e,[t].concat(r))})),this.modules.external.forEach((function(t){t.init&&t.init(e)}))}if(this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){var a=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);a.length>0&&"dev"!==a[0]&&(this.options.lng=a[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");var s=["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"];s.forEach((function(t){e[t]=function(){var n;return(n=e.store)[t].apply(n,arguments)}}));var l=["addResource","addResources","addResourceBundle","removeResourceBundle"];l.forEach((function(t){e[t]=function(){var n;return(n=e.store)[t].apply(n,arguments),e}}));var u=Mh(),c=function(){var t=function(t,r){e.isInitialized&&!e.initializedStoreOnce&&e.logger.warn("init: i18next is already initialized. You should call init just once!"),e.isInitialized=!0,e.options.isClone||e.logger.log("initialized",e.options),e.emit("initialized",e.options),u.resolve(r),n(t,r)};if(e.languages&&"v1"!==e.options.compatibilityAPI&&!e.isInitialized)return t(null,e.t.bind(e));e.changeLanguage(e.options.lng,t)};return this.options.resources||!this.options.initImmediate?c():setTimeout(c,0),u}},{key:"loadResources",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rp,r=n,o="string"==typeof e?e:this.language;if("function"==typeof e&&(r=e),!this.options.resources||this.options.partialBundledLanguages){if(o&&"cimode"===o.toLowerCase())return r();var i=[],a=function(e){e&&t.services.languageUtils.toResolveHierarchy(e).forEach((function(e){i.indexOf(e)<0&&i.push(e)}))};if(o)a(o);else{var s=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);s.forEach((function(e){return a(e)}))}this.options.preload&&this.options.preload.forEach((function(e){return a(e)})),this.services.backendConnector.load(i,this.options.ns,r)}else r(null)}},{key:"reloadResources",value:function(e,t,n){var r=Mh();return e||(e=this.languages),t||(t=this.options.ns),n||(n=rp),this.services.backendConnector.reload(e,t,(function(e){r.resolve(),n(e)})),r}},{key:"use",value:function(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&Hh.addPostProcessor(e),"3rdParty"===e.type&&this.modules.external.push(e),this}},{key:"changeLanguage",value:function(e,t){var n=this;this.isLanguageChangingTo=e;var r=Mh();this.emit("languageChanging",e);var o=function(o){e||o||!n.services.languageDetector||(o=[]);var i="string"==typeof o?o:n.services.languageUtils.getBestMatchFromCodes(o);i&&(n.language||(n.language=i,n.languages=n.services.languageUtils.toResolveHierarchy(i)),n.translator.language||n.translator.changeLanguage(i),n.services.languageDetector&&n.services.languageDetector.cacheUserLanguage(i)),n.loadResources(i,(function(e){!function(e,o){o?(n.language=o,n.languages=n.services.languageUtils.toResolveHierarchy(o),n.translator.changeLanguage(o),n.isLanguageChangingTo=void 0,n.emit("languageChanged",o),n.logger.log("languageChanged",o)):n.isLanguageChangingTo=void 0,r.resolve((function(){return n.t.apply(n,arguments)})),t&&t(e,(function(){return n.t.apply(n,arguments)}))}(e,i)}))};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect(o):o(e):o(this.services.languageDetector.detect()),r}},{key:"getFixedT",value:function(e,t,n){var r=this,o=function e(t,o){var i;if("object"!==vh(o)){for(var a=arguments.length,s=new Array(a>2?a-2:0),l=2;l<a;l++)s[l-2]=arguments[l];i=r.options.overloadTranslationOptionHandler([t,o].concat(s))}else i=mh({},o);i.lng=i.lng||e.lng,i.lngs=i.lngs||e.lngs,i.ns=i.ns||e.ns;var u=r.options.keySeparator||".",c=n?"".concat(n).concat(u).concat(t):t;return r.t(c,i)};return"string"==typeof e?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=n,o}},{key:"t",value:function(){var e;return this.translator&&(e=this.translator).translate.apply(e,arguments)}},{key:"exists",value:function(){var e;return this.translator&&(e=this.translator).exists.apply(e,arguments)}},{key:"setDefaultNamespace",value:function(e){this.options.defaultNS=e}},{key:"hasLoadedNamespace",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;var r=this.languages[0],o=!!this.options&&this.options.fallbackLng,i=this.languages[this.languages.length-1];if("cimode"===r.toLowerCase())return!0;var a=function(e,n){var r=t.services.backendConnector.state["".concat(e,"|").concat(n)];return-1===r||2===r};if(n.precheck){var s=n.precheck(this,a);if(void 0!==s)return s}return!!this.hasResourceBundle(r,e)||(!this.services.backendConnector.backend||!(!a(r,e)||o&&!a(i,e)))}},{key:"loadNamespaces",value:function(e,t){var n=this,r=Mh();return this.options.ns?("string"==typeof e&&(e=[e]),e.forEach((function(e){n.options.ns.indexOf(e)<0&&n.options.ns.push(e)})),this.loadResources((function(e){r.resolve(),t&&t(e)})),r):(t&&t(),Promise.resolve())}},{key:"loadLanguages",value:function(e,t){var n=Mh();"string"==typeof e&&(e=[e]);var r=this.options.preload||[],o=e.filter((function(e){return r.indexOf(e)<0}));return o.length?(this.options.preload=r.concat(o),this.loadResources((function(e){n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}},{key:"dir",value:function(e){if(e||(e=this.languages&&this.languages.length>0?this.languages[0]:this.language),!e)return"rtl";return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam"].indexOf(this.services.languageUtils.getLanguagePartFromCode(e))>=0?"rtl":"ltr"}},{key:"createInstance",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return new t(e,n)}},{key:"cloneInstance",value:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rp,o=mh({},this.options,n,{isClone:!0}),i=new t(o),a=["store","services","language"];return a.forEach((function(t){i[t]=e[t]})),i.services=mh({},this.services),i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i.translator=new Kh(i.services,i.options),i.translator.on("*",(function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];i.emit.apply(i,[e].concat(n))})),i.init(o,r),i.translator.options=i.options,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}},{key:"toJSON",value:function(){return{options:this.options,store:this.store,language:this.language,languages:this.languages}}}]),t}(),ip=new op,ap="translation";function sp(e,t){ip.addResourceBundle(e,ap,t,!0,!0)}function lp(e){ip.changeLanguage(e)}function up(e){return ip.getResourceBundle(e,ap)}ip.init({lng:"zh-CN",resources:{}});var cp=ip.t.bind(ip);export{ol as DomEditor,Sl as ELEM_TO_HTML_CONF,vc as PARSE_ELEM_HTML_CONF,pc as PARSE_STYLE_HTML_FN_LIST,dc as PRE_PARSE_HTML_CONF_LIST,nd as RENDER_ELEM_CONF,ed as RENDER_STYLE_HANDLER_LIST,wl as STYLE_TO_HTML_FN_LIST,fc as TEXT_TAGS,sh as Toolbar,nh as coreCreateEditor,lh as coreCreateToolbar,gh as createUploader,Ud as genModalButtonElems,zd as genModalInputElems,Hd as genModalTextareaElems,sp as i18nAddResources,lp as i18nChangeLanguage,up as i18nGetResources,kl as registerElemToHtmlConf,ll as registerMenu,yc as registerParseElemHtmlConf,gc as registerParseStyleHtmlHandler,hc as registerPreParseHtmlConf,rd as registerRenderElemConf,td as registerStyleHandler,xl as registerStyleToHtmlHandler,cp as t};
//# sourceMappingURL=index.esm.js.map
