/*!
  * vue-i18n v9.9.1
  * (c) 2024 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const n="undefined"!=typeof window,r=(e,t=!1)=>t?Symbol.for(e):Symbol(e),a=(e,t,n)=>l({l:e,k:t,s:n}),l=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),o=e=>"number"==typeof e&&isFinite(e),s=e=>"[object Date]"===h(e),c=e=>"[object RegExp]"===h(e),u=e=>L(e)&&0===Object.keys(e).length,i=Object.assign;function f(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const m=Object.prototype.hasOwnProperty;function _(e,t){return m.call(e,t)}const p=Array.isArray,d=e=>"function"==typeof e,g=e=>"string"==typeof e,E=e=>"boolean"==typeof e,v=e=>null!==e&&"object"==typeof e,b=e=>v(e)&&d(e.then)&&d(e.catch),k=Object.prototype.toString,h=e=>k.call(e),L=e=>{if(!v(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function N(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function T(e){let t=e;return()=>++t}function y(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const I=e=>!v(e)||p(e);function C(e,t){if(I(e)||I(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{I(e[r])||I(t[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]})}))}}function O(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const P={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function A(e,t,n={}){const{domain:r,messages:a,args:l}=n,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=r,o}function R(e){throw e}const F=" ",S="\r",D="\n",M=String.fromCharCode(8232),w=String.fromCharCode(8233);function x(e){const t=e;let n=0,r=1,a=1,l=0;const o=e=>t[e]===S&&t[e+1]===D,s=e=>t[e]===w,c=e=>t[e]===M,u=e=>o(e)||(e=>t[e]===D)(e)||s(e)||c(e),i=e=>o(e)||s(e)||c(e)?D:t[e];function f(){return l=0,u(n)&&(r++,a=0),o(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>l,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+l),next:f,peek:function(){return o(n+l)&&l++,l++,t[n+l]},reset:function(){n=0,r=1,a=1,l=0},resetPeek:function(e=0){l=e},skipToPeek:function(){const e=n+l;for(;e!==n;)f();l=0}}}const W=void 0,U=".",$="'";function H(e,t={}){const n=!1!==t.location,r=x(e),a=()=>r.index(),l=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},o=l(),s=a(),c={currentType:14,offset:s,startLoc:o,endLoc:o,lastType:14,lastOffset:s,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},u=()=>c,{onError:i}=t;function f(e,t,r){e.endLoc=l(),e.currentType=t;const a={type:t};return n&&(a.loc=O(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(P.EXPECTED_TOKEN,l(),"")}function p(e){let t="";for(;e.currentPeek()===F||e.currentPeek()===D;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=p(e);return e.skipToPeek(),t}function g(e){if(e===W)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=function(e){if(e===W)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function v(e){p(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function b(e,t=!0){const n=(t=!1,r="",a=!1)=>{const l=e.currentPeek();return"{"===l?"%"!==r&&t:"@"!==l&&l?"%"===l?(e.peek(),n(t,"%",!0)):"|"===l?!("%"!==r&&!a)||!(r===F||r===D):l===F?(e.peek(),n(!0,F,a)):l!==D||(e.peek(),n(!0,D,a)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===W?W:t(n)?(e.next(),n):null}function h(e){return k(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function L(e){return k(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function N(e){return k(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function T(e){let t="",n="";for(;t=L(e);)n+=t;return n}function y(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!b(e))break;t+=n,e.next()}else if(n===F||n===D)if(b(e))t+=n,e.next();else{if(v(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function I(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return C(e,t,4);case"U":return C(e,t,6);default:return P.UNKNOWN_ESCAPE_SEQUENCE,l(),""}}function C(e,t,n){_(e,t);let r="";for(let a=0;a<n;a++){const t=N(e);if(!t){P.INVALID_UNICODE_ESCAPE_SEQUENCE,l(),e.currentChar();break}r+=t}return`\\${t}${r}`}function A(e){d(e);const t=_(e,"|");return d(e),t}function R(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(P.NOT_ALLOW_NEST_PLACEHOLDER,l()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(P.EMPTY_PLACEHOLDER,l()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(P.UNTERMINATED_CLOSING_BRACE,l()),n=S(e,t)||m(t),t.braceNest=0,n;default:let r=!0,a=!0,o=!0;if(v(e))return t.braceNest>0&&(P.UNTERMINATED_CLOSING_BRACE,l()),n=f(t,1,A(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return P.UNTERMINATED_CLOSING_BRACE,l(),t.braceNest=0,M(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){d(e);let t="",n="";for(;t=h(e);)n+=t;return e.currentChar()===W&&(P.UNTERMINATED_CLOSING_BRACE,l()),n}(e)),d(e),n;if(a=E(e,t))return n=f(t,6,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${T(e)}`):t+=T(e),e.currentChar()===W&&(P.UNTERMINATED_CLOSING_BRACE,l()),t}(e)),d(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=e.currentPeek()===$;return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){d(e),_(e,"'");let t="",n="";const r=e=>e!==$&&e!==D;for(;t=k(e,r);)n+="\\"===t?I(e):t;const a=e.currentChar();return a===D||a===W?(P.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,l(),a===D&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!o)return n=f(t,13,function(e){d(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&e!==F&&e!==D;for(;t=k(e,r);)n+=t;return n}(e)),P.INVALID_TOKEN_IN_PLACEHOLDER,l(),n.value,d(e),n}return n}function S(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||a!==D&&a!==F||(P.INVALID_LINKED_FORMAT,l()),a){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,9,".");case":":return d(e),e.next(),f(t,10,":");default:return v(e)?(r=f(t,1,A(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;p(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;p(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),S(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,12,function(e){let t="",n="";for(;t=h(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===F||!t)&&(t===D?(e.peek(),r()):g(t))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?R(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const a=e.currentChar();return"{"!==a&&"%"!==a&&"@"!==a&&"|"!==a&&"("!==a&&")"!==a&&a?a===F?r:a===D||a===U?(r+=a,e.next(),t(n,r)):(r+=a,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&(P.INVALID_LINKED_FORMAT,l()),t.braceNest=0,t.inLinked=!1,M(e,t))}}function M(e,t){let n={type:14};if(t.braceNest>0)return R(e,t)||m(t);if(t.inLinked)return S(e,t)||m(t);switch(e.currentChar()){case"{":return R(e,t)||m(t);case"}":return P.UNBALANCED_CLOSING_BRACE,l(),e.next(),f(t,3,"}");case"@":return S(e,t)||m(t);default:if(v(e))return n=f(t,1,A(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:a}=function(e){const t=p(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return a?f(t,0,y(e)):f(t,4,function(e){d(e);const t=e.currentChar();return"%"!==t&&(P.EXPECTED_TOKEN,l()),e.next(),"%"}(e));if(b(e))return f(t,0,y(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:o}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=o,c.offset=a(),c.startLoc=l(),r.currentChar()===W?f(c,14):M(r,c)},currentOffset:a,currentPosition:l,context:u}}const V=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function j(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function X(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e};return t&&(a.start=n,a.end=n,a.loc={start:r,end:r}),a}function a(e,n,r,a){a&&(e.type=a),t&&(e.end=n,e.loc&&(e.loc.end=r))}function l(e,t){const n=e.context(),l=r(3,n.offset,n.startLoc);return l.value=t,a(l,e.currentOffset(),e.currentPosition()),l}function o(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(5,l,o);return s.index=parseInt(t,10),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function s(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(4,l,o);return s.key=t,e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function c(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(9,l,o);return s.value=t.replace(V,j),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let l=e.nextToken();if(9===l.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(8,l,o);return 12!==t.type?(P.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,s.value="",a(s,l,o),{nextConsumeToken:t,node:s}):(null==t.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,G(t)),s.value=t.value||"",a(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,l=t.nextConsumeToken||e.nextToken()}switch(10!==l.type&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(l)),l=e.nextToken(),2===l.type&&(l=e.nextToken()),l.type){case 11:null==l.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(l)),n.key=function(e,t){const n=e.context(),l=r(7,n.offset,n.startLoc);return l.value=t,a(l,e.currentOffset(),e.currentPosition()),l}(e,l.value||"");break;case 5:null==l.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(l)),n.key=s(e,l.value||"");break;case 6:null==l.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(l)),n.key=o(e,l.value||"");break;case 7:null==l.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(l)),n.key=c(e,l.value||"");break;default:P.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const u=e.context(),i=r(7,u.offset,u.startLoc);return i.value="",a(i,u.offset,u.startLoc),n.key=i,a(n,u.offset,u.startLoc),{nextConsumeToken:l,node:n}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let i=null;do{const r=i||e.nextToken();switch(i=null,r.type){case 0:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(o(e,r.value||""));break;case 5:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(s(e,r.value||""));break;case 7:null==r.value&&(P.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,G(r)),n.items.push(c(e,r.value||""));break;case 8:const a=u(e);n.items.push(a.node),i=a.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function m(e){const t=e.context(),{offset:n,startLoc:l}=t,o=f(e);return 14===t.currentType?o:function(e,t,n,l){const o=e.context();let s=0===l.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(l);do{const t=f(e);s||(s=0===t.items.length),c.cases.push(t)}while(14!==o.currentType);return a(c,e.currentOffset(),e.currentPosition()),c}(e,n,l,o)}return{parse:function(n){const l=H(n,i({},e)),o=l.context(),s=r(0,o.offset,o.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=m(l),e.onCacheKey&&(s.cacheKey=e.onCacheKey(n)),14!==o.currentType&&(P.UNEXPECTED_LEXICAL_ANALYSIS,o.lastStartLoc,n[o.offset]),a(s,l.currentOffset(),l.currentPosition()),s}}}function G(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Y(e,t){for(let n=0;n<e.length;n++)B(e[n],t)}function B(e,t){switch(e.type){case 1:Y(e.cases,t),t.helper("plural");break;case 2:Y(e.items,t);break;case 6:B(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function K(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&B(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function z(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=N(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function J(e){switch(e.t=e.type,e.type){case 0:const t=e;J(t.body),t.b=t.body,delete t.body;break;case 1:const n=e,r=n.cases;for(let e=0;e<r.length;e++)J(r[e]);n.c=r,delete n.cases;break;case 2:const a=e,l=a.items;for(let e=0;e<l.length;e++)J(l[e]);a.i=l,delete a.items,a.static&&(a.s=a.static,delete a.static);break;case 3:case 9:case 8:case 7:const o=e;o.value&&(o.v=o.value,delete o.value);break;case 6:const s=e;J(s.key),s.k=s.key,delete s.key,s.modifier&&(J(s.modifier),s.m=s.modifier,delete s.modifier);break;case 5:const c=e;c.i=c.index,delete c.index;break;case 4:const u=e;u.k=u.key,delete u.key}delete e.type}function Q(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Q(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(Q(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let l=0;l<a&&(Q(e,t.items[l]),l!==a-1);l++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Q(e,t.key),t.modifier?(e.push(", "),Q(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const q=(e,t={})=>{const n=g(t.mode)?t.mode:"normal",r=g(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,l=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",o=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:l}=t,o=!1!==t.location,s={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:l,indentLevel:0};function c(e,t){s.code+=e}function u(e,t=!0){const n=t?a:"";c(l?n+"  ".repeat(e):n)}return o&&e.loc&&(s.source=e.loc.source),{context:()=>s,push:c,indent:function(e=!0){const t=++s.indentLevel;e&&u(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&u(t)},newline:function(){u(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:l,needIndent:o});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),s.length>0&&(c.push(`const { ${N(s.map((e=>`${e}: _${e}`)),", ")} } = ctx`),c.newline()),c.push("return "),Q(c,e),c.deindent(o),c.push("}"),delete e.helpers;const{code:u,map:i}=c.context();return{ast:e,code:u,map:i?i.toJSON():void 0}};function Z(e,t={}){const n=i({},t),r=!!n.jit,a=!!n.minify,l=null==n.optimize||n.optimize,o=X(n).parse(e);return r?(l&&function(e){const t=e.body;2===t.type?z(t):t.cases.forEach((e=>z(e)))}(o),a&&J(o),{ast:o,code:""}):(K(o,n),q(o,n))}const ee=[];ee[0]={w:[0],i:[3,0],"[":[4],o:[7]},ee[1]={w:[1],".":[2],"[":[4],o:[7]},ee[2]={w:[2],i:[3,0],0:[3,0]},ee[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},ee[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},ee[5]={"'":[4,0],o:8,l:[5,0]},ee[6]={'"':[4,0],o:8,l:[6,0]};const te=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ne(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function re(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,te.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const ae=new Map;function le(e,t){return v(e)?e[t]:null}const oe=e=>e,se=e=>"",ce="text",ue=e=>0===e.length?"":N(e),ie=e=>null==e?"":p(e)||L(e)&&e.toString===k?JSON.stringify(e,null,2):String(e);function fe(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function me(e={}){const t=e.locale,n=function(e){const t=o(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(o(e.named.count)||o(e.named.n))?o(e.named.count)?e.named.count:o(e.named.n)?e.named.n:t:t}(e),r=v(e.pluralRules)&&g(t)&&d(e.pluralRules[t])?e.pluralRules[t]:fe,a=v(e.pluralRules)&&g(t)&&d(e.pluralRules[t])?fe:void 0,l=e.list||[],s=e.named||{};o(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function c(t){const n=d(e.messages)?e.messages(t):!!v(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):se)}const u=L(e.processor)&&d(e.processor.normalize)?e.processor.normalize:ue,f=L(e.processor)&&d(e.processor.interpolate)?e.processor.interpolate:ie,m={list:e=>l[e],named:e=>s[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let l="text",o="";1===n.length?v(r)?(o=r.modifier||o,l=r.type||l):g(r)&&(o=r||o):2===n.length&&(g(r)&&(o=r||o),g(a)&&(l=a||l));const s=c(t)(m),u="vnode"===l&&p(s)&&o?s[0]:s;return o?(i=o,e.modifiers?e.modifiers[i]:oe)(u,l):u;var i},message:c,type:L(e.processor)&&g(e.processor.type)?e.processor.type:ce,interpolate:f,normalize:u,values:i({},l,s)};return m}const _e=P.__EXTEND_POINT__,pe=T(_e),de={INVALID_ARGUMENT:_e,INVALID_DATE_ARGUMENT:pe(),INVALID_ISO_DATE_ARGUMENT:pe(),NOT_SUPPORT_NON_STRING_MESSAGE:pe(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:pe(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:pe(),NOT_SUPPORT_LOCALE_TYPE:pe(),__EXTEND_POINT__:pe()};function ge(e,t){return null!=t.locale?ve(t.locale):ve(e.locale)}let Ee;function ve(e){if(g(e))return e;if(d(e)){if(e.resolvedOnce&&null!=Ee)return Ee;if("Function"===e.constructor.name){const t=e();if(b(t))throw Error(de.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Ee=t}throw Error(de.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(de.NOT_SUPPORT_LOCALE_TYPE)}function be(e,t,n){return[...new Set([n,...p(t)?t:v(t)?Object.keys(t):g(t)?[t]:[n]])]}function ke(e,t,n){const r=g(n)?n:Ie,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let l=a.__localeChainCache.get(r);if(!l){l=[];let e=[n];for(;p(e);)e=he(l,e,t);const o=p(t)||!L(t)?t:t.default?t.default:null;e=g(o)?[o]:o,p(e)&&he(l,e,!1),a.__localeChainCache.set(r,l)}return l}function he(e,t,n){let r=!0;for(let a=0;a<t.length&&E(r);a++){const l=t[a];g(l)&&(r=Le(e,t[a],n))}return r}function Le(e,t,n){let r;const a=t.split("-");do{r=Ne(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function Ne(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(p(n)||L(n))&&n[a]&&(r=n[a])}return r}const Te="9.9.1",ye=-1,Ie="en-US",Ce="",Oe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let Pe,Ae,Re;let Fe=null;const Se=e=>{Fe=e},De=()=>Fe;let Me=0;function we(e={}){const t=d(e.onWarn)?e.onWarn:y,n=g(e.version)?e.version:Te,r=g(e.locale)||d(e.locale)?e.locale:Ie,a=d(r)?Ie:r,l=p(e.fallbackLocale)||L(e.fallbackLocale)||g(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,o=L(e.messages)?e.messages:{[a]:{}},s=L(e.datetimeFormats)?e.datetimeFormats:{[a]:{}},u=L(e.numberFormats)?e.numberFormats:{[a]:{}},f=i({},e.modifiers||{},{upper:(e,t)=>"text"===t&&g(e)?e.toUpperCase():"vnode"===t&&v(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&g(e)?e.toLowerCase():"vnode"===t&&v(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&g(e)?Oe(e):"vnode"===t&&v(e)&&"__v_isVNode"in e?Oe(e.children):e}),m=e.pluralRules||{},_=d(e.missing)?e.missing:null,b=!E(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,k=!E(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,N=!!e.unresolving,T=d(e.postTranslation)?e.postTranslation:null,I=L(e.processor)?e.processor:null,C=!E(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,P=d(e.messageCompiler)?e.messageCompiler:Pe,A=d(e.messageResolver)?e.messageResolver:Ae||le,R=d(e.localeFallbacker)?e.localeFallbacker:Re||be,F=v(e.fallbackContext)?e.fallbackContext:void 0,S=e,D=v(S.__datetimeFormatters)?S.__datetimeFormatters:new Map,M=v(S.__numberFormatters)?S.__numberFormatters:new Map,w=v(S.__meta)?S.__meta:{};Me++;const x={version:n,cid:Me,locale:r,fallbackLocale:l,messages:o,modifiers:f,pluralRules:m,missing:_,missingWarn:b,fallbackWarn:k,fallbackFormat:h,unresolving:N,postTranslation:T,processor:I,warnHtmlMessage:C,escapeParameter:O,messageCompiler:P,messageResolver:A,localeFallbacker:R,fallbackContext:F,onWarn:t,__meta:w};return x.datetimeFormats=s,x.numberFormats=u,x.__datetimeFormatters=D,x.__numberFormatters=M,x}function xe(e,t,n,r,a){const{missing:l,onWarn:o}=e;if(null!==l){const r=l(e,n,t,a);return g(r)?r:t}return t}function We(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Ue(e){return t=>function(e,t){const n=t.b||t.body;if(1===(n.t||n.type)){const t=n,r=t.c||t.cases;return e.plural(r.reduce(((t,n)=>[...t,$e(e,n)]),[]))}return $e(e,n)}(t,e)}function $e(e,t){const n=t.s||t.static;if(n)return"text"===e.type?n:e.normalize([n]);{const n=(t.i||t.items).reduce(((t,n)=>[...t,He(e,n)]),[]);return e.normalize(n)}}function He(e,t){const n=t.t||t.type;switch(n){case 3:const r=t;return r.v||r.value;case 9:const a=t;return a.v||a.value;case 4:const l=t;return e.interpolate(e.named(l.k||l.key));case 5:const o=t;return e.interpolate(e.list(null!=o.i?o.i:o.index));case 6:const s=t,c=s.m||s.modifier;return e.linked(He(e,s.k||s.key),c?He(e,c):void 0,e.type);case 7:const u=t;return u.v||u.value;case 8:const i=t;return i.v||i.value;default:throw new Error(`unhandled node type on format message part: ${n}`)}}const Ve=e=>e;let je=Object.create(null);const Xe=e=>v(e)&&(0===e.t||0===e.type)&&("b"in e||"body"in e);const Ge=()=>"",Ye=e=>d(e);function Be(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:l,fallbackLocale:s,messages:c}=e,[u,i]=Je(...t),m=E(i.missingWarn)?i.missingWarn:e.missingWarn,_=E(i.fallbackWarn)?i.fallbackWarn:e.fallbackWarn,d=E(i.escapeParameter)?i.escapeParameter:e.escapeParameter,b=!!i.resolvedMessage,k=g(i.default)||E(i.default)?E(i.default)?l?u:()=>u:i.default:n?l?u:()=>u:"",h=n||""!==k,L=ge(e,i);d&&function(e){p(e.list)?e.list=e.list.map((e=>g(e)?f(e):e)):v(e.named)&&Object.keys(e.named).forEach((t=>{g(e.named[t])&&(e.named[t]=f(e.named[t]))}))}(i);let[N,T,y]=b?[u,L,c[L]||{}]:Ke(e,u,L,s,_,m),I=N,C=u;if(b||g(I)||Xe(I)||Ye(I)||h&&(I=k,C=I),!(b||(g(I)||Xe(I)||Ye(I))&&g(T)))return a?ye:u;let O=!1;const P=Ye(I)?I:ze(e,u,T,I,C,(()=>{O=!0}));if(O)return I;const A=function(e,t,n,r){const{modifiers:a,pluralRules:l,messageResolver:s,fallbackLocale:c,fallbackWarn:u,missingWarn:i,fallbackContext:f}=e,m=r=>{let a=s(n,r);if(null==a&&f){const[,,e]=Ke(f,r,t,c,u,i);a=s(e,r)}if(g(a)||Xe(a)){let n=!1;const l=ze(e,r,t,a,r,(()=>{n=!0}));return n?Ge:l}return Ye(a)?a:Ge},_={locale:t,modifiers:a,pluralRules:l,messages:m};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);o(r.plural)&&(_.pluralIndex=r.plural);return _}(e,T,y,i),R=function(e,t,n){const r=t(n);return r}(0,P,me(A));return r?r(R,u):R}function Ke(e,t,n,r,a,l){const{messages:o,onWarn:s,messageResolver:c,localeFallbacker:u}=e,i=u(e,r,n);let f,m={},_=null;for(let p=0;p<i.length&&(f=i[p],m=o[f]||{},null===(_=c(m,t))&&(_=m[t]),!(g(_)||Xe(_)||Ye(_)));p++){const n=xe(e,t,f,0,"translate");n!==t&&(_=n)}return[_,f,m]}function ze(e,t,n,r,l,o){const{messageCompiler:s,warnHtmlMessage:c}=e;if(Ye(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>r;return e.locale=n,e.key=t,e}const u=s(r,function(e,t,n,r,l,o){return{locale:t,key:n,warnHtmlMessage:l,onError:e=>{throw o&&o(e),e},onCacheKey:e=>a(t,n,e)}}(0,n,l,0,c,o));return u.locale=n,u.key=t,u.source=r,u}function Je(...e){const[t,n,r]=e,a={};if(!(g(t)||o(t)||Ye(t)||Xe(t)))throw Error(de.INVALID_ARGUMENT);const l=o(t)?String(t):(Ye(t),t);return o(n)?a.plural=n:g(n)?a.default=n:L(n)&&!u(n)?a.named=n:p(n)&&(a.list=n),o(r)?a.plural=r:g(r)?a.default=r:L(r)&&i(a,r),[l,a]}function Qe(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:l,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[c,f,m,_]=Ze(...t);E(m.missingWarn)?m.missingWarn:e.missingWarn;E(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=ge(e,m),v=o(e,a,d);if(!g(c)||""===c)return new Intl.DateTimeFormat(d,_).format(f);let b,k={},h=null;for(let u=0;u<v.length&&(b=v[u],k=n[b]||{},h=k[c],!L(h));u++)xe(e,c,b,0,"datetime format");if(!L(h)||!g(b))return r?ye:c;let N=`${b}__${c}`;u(_)||(N=`${N}__${JSON.stringify(_)}`);let T=s.get(N);return T||(T=new Intl.DateTimeFormat(b,i({},h,_)),s.set(N,T)),p?T.formatToParts(f):T.format(f)}const qe=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ze(...e){const[t,n,r,a]=e,l={};let c,u={};if(g(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(de.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();c=new Date(n);try{c.toISOString()}catch(i){throw Error(de.INVALID_ISO_DATE_ARGUMENT)}}else if(s(t)){if(isNaN(t.getTime()))throw Error(de.INVALID_DATE_ARGUMENT);c=t}else{if(!o(t))throw Error(de.INVALID_ARGUMENT);c=t}return g(n)?l.key=n:L(n)&&Object.keys(n).forEach((e=>{qe.includes(e)?u[e]=n[e]:l[e]=n[e]})),g(r)?l.locale=r:L(r)&&(u=r),L(a)&&(u=a),[l.key||"",c,l,u]}function et(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function tt(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:l,localeFallbacker:o}=e,{__numberFormatters:s}=e,[c,f,m,_]=rt(...t);E(m.missingWarn)?m.missingWarn:e.missingWarn;E(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=ge(e,m),v=o(e,a,d);if(!g(c)||""===c)return new Intl.NumberFormat(d,_).format(f);let b,k={},h=null;for(let u=0;u<v.length&&(b=v[u],k=n[b]||{},h=k[c],!L(h));u++)xe(e,c,b,0,"number format");if(!L(h)||!g(b))return r?ye:c;let N=`${b}__${c}`;u(_)||(N=`${N}__${JSON.stringify(_)}`);let T=s.get(N);return T||(T=new Intl.NumberFormat(b,i({},h,_)),s.set(N,T)),p?T.formatToParts(f):T.format(f)}const nt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function rt(...e){const[t,n,r,a]=e,l={};let s={};if(!o(t))throw Error(de.INVALID_ARGUMENT);const c=t;return g(n)?l.key=n:L(n)&&Object.keys(n).forEach((e=>{nt.includes(e)?s[e]=n[e]:l[e]=n[e]})),g(r)?l.locale=r:L(r)&&(s=r),L(a)&&(s=a),[l.key||"",c,l,s]}function at(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const lt="9.9.1",ot=de.__EXTEND_POINT__,st=T(ot),ct={UNEXPECTED_RETURN_TYPE:ot,INVALID_ARGUMENT:st(),MUST_BE_CALL_SETUP_TOP:st(),NOT_INSTALLED:st(),NOT_AVAILABLE_IN_LEGACY_MODE:st(),REQUIRED_VALUE:st(),INVALID_VALUE:st(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:st(),NOT_INSTALLED_WITH_PROVIDE:st(),UNEXPECTED_ERROR:st(),NOT_COMPATIBLE_LEGACY_VUE_I18N:st(),BRIDGE_SUPPORT_VUE_2_ONLY:st(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:st(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:st(),__EXTEND_POINT__:st()};const ut=r("__translateVNode"),it=r("__datetimeParts"),ft=r("__numberParts"),mt=r("__setPluralRules"),_t=r("__injectWithOption"),pt=r("__dispose");function dt(e){if(!v(e))return e;for(const t in e)if(_(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e,l=!1;for(let e=0;e<r;e++){if(n[e]in a||(a[n[e]]={}),!v(a[n[e]])){l=!0;break}a=a[n[e]]}l||(a[n[r]]=e[t],delete e[t]),v(a[n[r]])&&dt(a[n[r]])}else v(e[t])&&dt(e[t]);return e}function gt(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:l}=t,o=L(n)?n:p(r)?{}:{[e]:{}};if(p(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(o[t]=o[t]||{},C(n,o[t])):C(n,o)}else g(e)&&C(JSON.parse(e),o)})),null==a&&l)for(const s in o)_(o,s)&&dt(o[s]);return o}function Et(e){return e.type}function vt(e,t,n){let r=v(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=gt(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),v(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(v(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function bt(e){return t.createVNode(t.Text,null,e,0)}const kt=()=>[],ht=()=>!1;let Lt=0;function Nt(e){return(n,r,a,l)=>e(r,a,t.getCurrentInstance()||void 0,l)}function Tt(e={},r){const{__root:a,__injectWithOption:l}=e,s=void 0===a,u=e.flatJson,f=n?t.ref:t.shallowRef;let m=!E(e.inheritLocale)||e.inheritLocale;const b=f(a&&m?a.locale.value:g(e.locale)?e.locale:Ie),k=f(a&&m?a.fallbackLocale.value:g(e.fallbackLocale)||p(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:b.value),h=f(gt(b.value,e)),N=f(L(e.datetimeFormats)?e.datetimeFormats:{[b.value]:{}}),T=f(L(e.numberFormats)?e.numberFormats:{[b.value]:{}});let y=a?a.missingWarn:!E(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,I=a?a.fallbackWarn:!E(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,O=a?a.fallbackRoot:!E(e.fallbackRoot)||e.fallbackRoot,P=!!e.fallbackFormat,A=d(e.missing)?e.missing:null,R=d(e.missing)?Nt(e.missing):null,F=d(e.postTranslation)?e.postTranslation:null,S=a?a.warnHtmlMessage:!E(e.warnHtmlMessage)||e.warnHtmlMessage,D=!!e.escapeParameter;const M=a?a.modifiers:L(e.modifiers)?e.modifiers:{};let w,x=e.pluralRules||a&&a.pluralRules;w=(()=>{s&&Se(null);const t={version:lt,locale:b.value,fallbackLocale:k.value,messages:h.value,modifiers:M,pluralRules:x,missing:null===R?void 0:R,missingWarn:y,fallbackWarn:I,fallbackFormat:P,unresolving:!0,postTranslation:null===F?void 0:F,warnHtmlMessage:S,escapeParameter:D,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=N.value,t.numberFormats=T.value,t.__datetimeFormatters=L(w)?w.__datetimeFormatters:void 0,t.__numberFormatters=L(w)?w.__numberFormatters:void 0;const n=we(t);return s&&Se(n),n})(),We(w,b.value,k.value);const W=t.computed({get:()=>b.value,set:e=>{b.value=e,w.locale=b.value}}),U=t.computed({get:()=>k.value,set:e=>{k.value=e,w.fallbackLocale=k.value,We(w,b.value,e)}}),$=t.computed((()=>h.value)),H=t.computed((()=>N.value)),V=t.computed((()=>T.value));const j=(e,t,n,r,l,c)=>{let u;b.value,k.value,h.value,N.value,T.value;try{0,s||(w.fallbackContext=a?De():void 0),u=e(w)}finally{s||(w.fallbackContext=void 0)}if("translate exists"!==n&&o(u)&&u===ye||"translate exists"===n&&!u){const[e,n]=t();return a&&O?r(a):l(e)}if(c(u))return u;throw Error(ct.UNEXPECTED_RETURN_TYPE)};function X(...e){return j((t=>Reflect.apply(Be,null,[t,...e])),(()=>Je(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>g(e)))}const G={normalize:function(e){return e.map((e=>g(e)||o(e)||E(e)?bt(String(e)):e))},interpolate:e=>e,type:"vnode"};function Y(e){return h.value[e]||{}}Lt++,a&&n&&(t.watch(a.locale,(e=>{m&&(b.value=e,w.locale=e,We(w,b.value,k.value))})),t.watch(a.fallbackLocale,(e=>{m&&(k.value=e,w.fallbackLocale=e,We(w,b.value,k.value))})));const B={id:Lt,locale:W,fallbackLocale:U,get inheritLocale(){return m},set inheritLocale(e){m=e,e&&a&&(b.value=a.locale.value,k.value=a.fallbackLocale.value,We(w,b.value,k.value))},get availableLocales(){return Object.keys(h.value).sort()},messages:$,get modifiers(){return M},get pluralRules(){return x||{}},get isGlobal(){return s},get missingWarn(){return y},set missingWarn(e){y=e,w.missingWarn=y},get fallbackWarn(){return I},set fallbackWarn(e){I=e,w.fallbackWarn=I},get fallbackRoot(){return O},set fallbackRoot(e){O=e},get fallbackFormat(){return P},set fallbackFormat(e){P=e,w.fallbackFormat=P},get warnHtmlMessage(){return S},set warnHtmlMessage(e){S=e,w.warnHtmlMessage=e},get escapeParameter(){return D},set escapeParameter(e){D=e,w.escapeParameter=e},t:X,getLocaleMessage:Y,setLocaleMessage:function(e,t){if(u){const n={[e]:t};for(const e in n)_(n,e)&&dt(n[e]);t=n[e]}h.value[e]=t,w.messages=h.value},mergeLocaleMessage:function(e,t){h.value[e]=h.value[e]||{};const n={[e]:t};if(u)for(const r in n)_(n,r)&&dt(n[r]);C(t=n[e],h.value[e]),w.messages=h.value},getPostTranslationHandler:function(){return d(F)?F:null},setPostTranslationHandler:function(e){F=e,w.postTranslation=e},getMissingHandler:function(){return A},setMissingHandler:function(e){null!==e&&(R=Nt(e)),A=e,w.missing=R},[mt]:function(e){x=e,w.pluralRules=x}};return B.datetimeFormats=H,B.numberFormats=V,B.rt=function(...e){const[t,n,r]=e;if(r&&!v(r))throw Error(ct.INVALID_ARGUMENT);return X(t,n,i({resolvedMessage:!0},r||{}))},B.te=function(e,t){return j((()=>{if(!e)return!1;const n=Y(g(t)?t:b.value),r=w.messageResolver(n,e);return Xe(r)||Ye(r)||g(r)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),ht,(e=>E(e)))},B.tm=function(e){const t=function(e){let t=null;const n=ke(w,k.value,b.value);for(let r=0;r<n.length;r++){const a=h.value[n[r]]||{},l=w.messageResolver(a,e);if(null!=l){t=l;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},B.d=function(...e){return j((t=>Reflect.apply(Qe,null,[t,...e])),(()=>Ze(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>Ce),(e=>g(e)))},B.n=function(...e){return j((t=>Reflect.apply(tt,null,[t,...e])),(()=>rt(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>Ce),(e=>g(e)))},B.getDateTimeFormat=function(e){return N.value[e]||{}},B.setDateTimeFormat=function(e,t){N.value[e]=t,w.datetimeFormats=N.value,et(w,e,t)},B.mergeDateTimeFormat=function(e,t){N.value[e]=i(N.value[e]||{},t),w.datetimeFormats=N.value,et(w,e,t)},B.getNumberFormat=function(e){return T.value[e]||{}},B.setNumberFormat=function(e,t){T.value[e]=t,w.numberFormats=T.value,at(w,e,t)},B.mergeNumberFormat=function(e,t){T.value[e]=i(T.value[e]||{},t),w.numberFormats=T.value,at(w,e,t)},B[_t]=l,B[ut]=function(...e){return j((t=>{let n;const r=t;try{r.processor=G,n=Reflect.apply(Be,null,[r,...e])}finally{r.processor=null}return n}),(()=>Je(...e)),"translate",(t=>t[ut](...e)),(e=>[bt(e)]),(e=>p(e)))},B[it]=function(...e){return j((t=>Reflect.apply(Qe,null,[t,...e])),(()=>Ze(...e)),"datetime format",(t=>t[it](...e)),kt,(e=>g(e)||p(e)))},B[ft]=function(...e){return j((t=>Reflect.apply(tt,null,[t,...e])),(()=>rt(...e)),"number format",(t=>t[ft](...e)),kt,(e=>g(e)||p(e)))},B}function yt(e={},t){{const t=Tt(function(e){const t=g(e.locale)?e.locale:Ie,n=g(e.fallbackLocale)||p(e.fallbackLocale)||L(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=d(e.missing)?e.missing:void 0,a=!E(e.silentTranslationWarn)&&!c(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!E(e.silentFallbackWarn)&&!c(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!E(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,u=L(e.modifiers)?e.modifiers:{},f=e.pluralizationRules,m=d(e.postTranslation)?e.postTranslation:void 0,_=!g(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,v=!!e.escapeParameterHtml,b=!E(e.sync)||e.sync;let k=e.messages;if(L(e.sharedMessages)){const t=e.sharedMessages;k=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return i(r,t[n]),e}),k||{})}const{__i18n:h,__root:N,__injectWithOption:T}=e,y=e.datetimeFormats,I=e.numberFormats;return{locale:t,fallbackLocale:n,messages:k,flatJson:e.flatJson,datetimeFormats:y,numberFormats:I,missing:r,missingWarn:a,fallbackWarn:l,fallbackRoot:o,fallbackFormat:s,modifiers:u,pluralRules:f,postTranslation:m,warnHtmlMessage:_,escapeParameter:v,messageResolver:e.messageResolver,inheritLocale:b,__i18n:h,__root:N,__injectWithOption:T}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return E(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=E(e)?!e:e},get silentFallbackWarn(){return E(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=E(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,r,a]=e,l={};let o=null,s=null;if(!g(n))throw Error(ct.INVALID_ARGUMENT);const c=n;return g(r)?l.locale=r:p(r)?o=r:L(r)&&(s=r),p(a)?o=a:L(a)&&(s=a),Reflect.apply(t.t,t,[c,o||s||{},l])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,r,a]=e,l={plural:1};let s=null,c=null;if(!g(n))throw Error(ct.INVALID_ARGUMENT);const u=n;return g(r)?l.locale=r:o(r)?l.plural=r:p(r)?s=r:L(r)&&(c=r),g(a)?l.locale=a:p(a)?s=a:L(a)&&(c=a),Reflect.apply(t.t,t,[u,s||c||{},l])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex:(e,t)=>-1};return r.__extender=n,r}}const It={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Ct(e){return t.Fragment}const Ot=t.defineComponent({name:"i18n-t",props:i({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>o(e)||!isNaN(e)}},It),setup(e,n){const{slots:r,attrs:a}=n,l=e.i18n||$t({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(r).filter((e=>"_"!==e)),s={};e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=g(e.plural)?+e.plural:e.plural);const c=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),{})}(n,o),u=l[ut](e.keypath,c,s),f=i({},a),m=g(e.tag)||v(e.tag)?e.tag:Ct();return t.h(m,f,u)}}}),Pt=Ot;function At(e,n,r,a){const{slots:l,attrs:o}=n;return()=>{const n={part:!0};let s={};e.locale&&(n.locale=e.locale),g(e.format)?n.key=e.format:v(e.format)&&(g(e.format.key)&&(n.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?i({},t,{[n]:e.format[n]}):t),{}));const c=a(e.value,n,s);let u=[n.key];p(c)?u=c.map(((e,t)=>{const n=l[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var a;return p(a=r)&&!g(a[0])&&(r[0].key=`${e.type}-${t}`),r})):g(c)&&(u=[c]);const f=i({},o),m=g(e.tag)||v(e.tag)?e.tag:Ct();return t.h(m,f,u)}}const Rt=t.defineComponent({name:"i18n-n",props:i({value:{type:Number,required:!0},format:{type:[String,Object]}},It),setup(e,t){const n=e.i18n||$t({useScope:"parent",__useComponent:!0});return At(e,t,nt,((...e)=>n[ft](...e)))}}),Ft=Rt,St=t.defineComponent({name:"i18n-d",props:i({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},It),setup(e,t){const n=e.i18n||$t({useScope:"parent",__useComponent:!0});return At(e,t,qe,((...e)=>n[it](...e)))}}),Dt=St;function Mt(e){const r=t=>{const{instance:n,modifiers:r,value:a}=t;if(!n||!n.$)throw Error(ct.UNEXPECTED_ERROR);const l=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),o=wt(a);return[Reflect.apply(l.t,l,[...xt(o)]),l]};return{created:(a,l)=>{const[o,s]=r(l);n&&e.global===s&&(a.__i18nWatcher=t.watch(s.locale,(()=>{l.instance&&l.instance.$forceUpdate()}))),a.__composer=s,a.textContent=o},unmounted:e=>{n&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=wt(t);e.textContent=Reflect.apply(n.t,n,[...xt(r)])}},getSSRProps:e=>{const[t]=r(e);return{textContent:t}}}}function wt(e){if(g(e))return{path:e};if(L(e)){if(!("path"in e))throw Error(ct.REQUIRED_VALUE,"path");return e}throw Error(ct.INVALID_VALUE)}function xt(e){const{path:t,locale:n,args:r,choice:a,plural:l}=e,s={},c=r||{};return g(n)&&(s.locale=n),o(a)&&(s.plural=a),o(l)&&(s.plural=l),[t,c,s]}function Wt(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[mt](t.pluralizationRules||e.pluralizationRules);const n=gt(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const Ut=r("global-vue-i18n");function $t(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(ct.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(ct.NOT_INSTALLED);const r=function(e){{const n=t.inject(e.isCE?Ut:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw function(e,...t){return A(e,null,void 0)}(e.isCE?ct.NOT_INSTALLED_WITH_PROVIDE:ct.UNEXPECTED_ERROR);return n}}(n),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(r),l=Et(n),o=function(e,t){return u(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,l);if("legacy"===r.mode&&!e.__useComponent){if(!r.allowComposition)throw Error(ct.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,n,r,a={}){const l="local"===n,o=t.shallowRef(null);if(l&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(ct.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=E(a.inheritLocale)?a.inheritLocale:!g(a.locale),u=t.ref(!l||s?r.locale.value:g(a.locale)?a.locale:Ie),i=t.ref(!l||s?r.fallbackLocale.value:g(a.fallbackLocale)||p(a.fallbackLocale)||L(a.fallbackLocale)||!1===a.fallbackLocale?a.fallbackLocale:u.value),f=t.ref(gt(u.value,a)),m=t.ref(L(a.datetimeFormats)?a.datetimeFormats:{[u.value]:{}}),_=t.ref(L(a.numberFormats)?a.numberFormats:{[u.value]:{}}),v=l?r.missingWarn:!E(a.missingWarn)&&!c(a.missingWarn)||a.missingWarn,b=l?r.fallbackWarn:!E(a.fallbackWarn)&&!c(a.fallbackWarn)||a.fallbackWarn,k=l?r.fallbackRoot:!E(a.fallbackRoot)||a.fallbackRoot,h=!!a.fallbackFormat,N=d(a.missing)?a.missing:null,T=d(a.postTranslation)?a.postTranslation:null,y=l?r.warnHtmlMessage:!E(a.warnHtmlMessage)||a.warnHtmlMessage,I=!!a.escapeParameter,C=l?r.modifiers:L(a.modifiers)?a.modifiers:{},O=a.pluralRules||l&&r.pluralRules;function P(){return[u.value,i.value,f.value,m.value,_.value]}const A=t.computed({get:()=>o.value?o.value.locale.value:u.value,set:e=>{o.value&&(o.value.locale.value=e),u.value=e}}),R=t.computed({get:()=>o.value?o.value.fallbackLocale.value:i.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),i.value=e}}),F=t.computed((()=>o.value?o.value.messages.value:f.value)),S=t.computed((()=>m.value)),D=t.computed((()=>_.value));function M(){return o.value?o.value.getPostTranslationHandler():T}function w(e){o.value&&o.value.setPostTranslationHandler(e)}function x(){return o.value?o.value.getMissingHandler():N}function W(e){o.value&&o.value.setMissingHandler(e)}function U(e){return P(),e()}function $(...e){return o.value?U((()=>Reflect.apply(o.value.t,null,[...e]))):U((()=>""))}function H(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""}function V(...e){return o.value?U((()=>Reflect.apply(o.value.d,null,[...e]))):U((()=>""))}function j(...e){return o.value?U((()=>Reflect.apply(o.value.n,null,[...e]))):U((()=>""))}function X(e){return o.value?o.value.tm(e):{}}function G(e,t){return!!o.value&&o.value.te(e,t)}function Y(e){return o.value?o.value.getLocaleMessage(e):{}}function B(e,t){o.value&&(o.value.setLocaleMessage(e,t),f.value[e]=t)}function K(e,t){o.value&&o.value.mergeLocaleMessage(e,t)}function z(e){return o.value?o.value.getDateTimeFormat(e):{}}function J(e,t){o.value&&(o.value.setDateTimeFormat(e,t),m.value[e]=t)}function Q(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)}function q(e){return o.value?o.value.getNumberFormat(e):{}}function Z(e,t){o.value&&(o.value.setNumberFormat(e,t),_.value[e]=t)}function ee(e,t){o.value&&o.value.mergeNumberFormat(e,t)}const te={get id(){return o.value?o.value.id:-1},locale:A,fallbackLocale:R,messages:F,datetimeFormats:S,numberFormats:D,get inheritLocale(){return o.value?o.value.inheritLocale:s},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(f.value)},get modifiers(){return o.value?o.value.modifiers:C},get pluralRules(){return o.value?o.value.pluralRules:O},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:v},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:b},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:k},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:h},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:y},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:I},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:$,getPostTranslationHandler:M,setPostTranslationHandler:w,getMissingHandler:x,setMissingHandler:W,rt:H,d:V,n:j,tm:X,te:G,getLocaleMessage:Y,setLocaleMessage:B,mergeLocaleMessage:K,getDateTimeFormat:z,setDateTimeFormat:J,mergeDateTimeFormat:Q,getNumberFormat:q,setNumberFormat:Z,mergeNumberFormat:ee};function ne(e){e.locale.value=u.value,e.fallbackLocale.value=i.value,Object.keys(f.value).forEach((t=>{e.mergeLocaleMessage(t,f.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeDateTimeFormat(t,m.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=I,e.fallbackFormat=h,e.fallbackRoot=k,e.fallbackWarn=b,e.missingWarn=v,e.warnHtmlMessage=y}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(ct.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=o.value=e.proxy.$i18n.__composer;"global"===n?(u.value=t.locale.value,i.value=t.fallbackLocale.value,f.value=t.messages.value,m.value=t.datetimeFormats.value,_.value=t.numberFormats.value):l&&ne(t)})),te}(n,o,a,e)}if("global"===o)return vt(a,e,l),a;if("parent"===o){let t=function(e,t,n=!1){let r=null;const a=t.root;let l=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=l;){const t=e;if("composition"===e.mode)r=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(r=e.__composer,n&&r&&!r[_t]&&(r=null))}if(null!=r)break;if(a===l)break;l=l.parent}return r}(r,n,e.__useComponent);return null==t&&(t=a),t}const s=r;let f=s.__getInstance(n);if(null==f){const r=i({},e);"__i18n"in l&&(r.__i18n=l.__i18n),a&&(r.__root=a),f=Tt(r),s.__composerExtend&&(f[pt]=s.__composerExtend(f)),function(e,n,r){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=r;e.__deleteInstance(n);const a=t[pt];a&&(a(),delete t[pt])}),n)}(s,n,f),s.__setInstance(n,f)}return f}const Ht=["locale","fallbackLocale","availableLocales"],Vt=["t","rt","d","n","tm","te"];return Pe=function(e,t){if(g(e)){!E(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||Ve)(e),r=je[n];if(r)return r;const{ast:a,detectError:l}=function(e,t={}){let n=!1;const r=t.onError||R;return t.onError=e=>{n=!0,r(e)},{...Z(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),o=Ue(a);return l?o:je[n]=o}{const t=e.cacheKey;return t?je[t]||(je[t]=Ue(e)):Ue(e)}},Ae=function(e,t){if(!v(e))return null;let n=ae.get(t);if(n||(n=function(e){const t=[];let n,r,a,l,o,s,c,u=-1,i=0,f=0;const m=[];function _(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=re(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!_()){if(l=ne(n),c=ee[i],o=c[l]||c.l||8,8===o)return;if(i=o[0],void 0!==o[1]&&(s=m[o[1]],s&&(a=n,!1===s())))return;if(7===i)return t}}(t),n&&ae.set(t,n)),!n)return null;const r=n.length;let a=e,l=0;for(;l<r;){const e=a[n[l]];if(void 0===e)return null;if(d(a))return null;a=e,l++}return a},Re=ke,e.DatetimeFormat=St,e.I18nD=Dt,e.I18nInjectionKey=Ut,e.I18nN=Ft,e.I18nT=Pt,e.NumberFormat=Rt,e.Translation=Ot,e.VERSION=lt,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(ct.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},n){const a=!E(e.legacy)||e.legacy,l=!E(e.globalInjection)||e.globalInjection,o=!a||!!e.allowComposition,s=new Map,[c,u]=function(e,n,r){const a=t.effectScope();{const t=n?a.run((()=>yt(e))):a.run((()=>Tt(e)));if(null==t)throw Error(ct.UNEXPECTED_ERROR);return[a,t]}}(e,a),i=r("");{const e={get mode(){return a?"legacy":"composition"},get allowComposition(){return o},async install(n,...r){if(n.__VUE_I18N_SYMBOL__=i,n.provide(n.__VUE_I18N_SYMBOL__,e),L(r[0])){const t=r[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let o=null;!a&&l&&(o=function(e,n){const r=Object.create(null);Ht.forEach((e=>{const a=Object.getOwnPropertyDescriptor(n,e);if(!a)throw Error(ct.UNEXPECTED_ERROR);const l=t.isRef(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,l)})),e.config.globalProperties.$i18n=r,Vt.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw Error(ct.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}));const a=()=>{delete e.config.globalProperties.$i18n,Vt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(n,e.global)),function(e,t,...n){const r=L(n[0])?n[0]:{},a=!!r.useI18nComponentName;(!E(r.globalInstall)||r.globalInstall)&&([a?"i18n":Ot.name,"I18nT"].forEach((t=>e.component(t,Ot))),[Rt.name,"I18nN"].forEach((t=>e.component(t,Rt))),[St.name,"I18nD"].forEach((t=>e.component(t,St)))),e.directive("t",Mt(t))}(n,e,...r),a&&n.mixin(function(e,n,r){return{beforeCreate(){const a=t.getCurrentInstance();if(!a)throw Error(ct.UNEXPECTED_ERROR);const l=this.$options;if(l.i18n){const t=l.i18n;if(l.__i18n&&(t.__i18n=l.__i18n),t.__root=n,this===this.$root)this.$i18n=Wt(e,t);else{t.__injectWithOption=!0,t.__extender=r.__vueI18nExtend,this.$i18n=yt(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(l.__i18n)if(this===this.$root)this.$i18n=Wt(e,l);else{this.$i18n=yt({__i18n:l.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;l.__i18nGlobal&&vt(n,l,l),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),r.__setInstance(a,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(ct.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),r.__deleteInstance(e),delete this.$i18n}}}(u,u.__composer,e));const s=n.unmount;n.unmount=()=>{o&&o(),e.dispose(),s()}},get global(){return u},dispose(){c.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=$t,e.vTDirective=Mt,e}({},Vue);
