import{i18nAddResources as t,DomEditor as e,t as n}from"@wangeditor/core";import{Editor as r,Transforms as o,Path as i,Node as a,Point as l,Range as u,Text as c,Element as s}from"slate";import{jsx as f}from"snabbdom";import d from"lodash.throttle";import p,{append as v,on as h,focus as g,attr as y,val as b,html as m,dataset as w,addClass as S,removeClass as x,children as T,each as E,find as N}from"dom7";import"nanoid";import M from"lodash.isequal";t("en",{tableModule:{deleteCol:"Delete column",deleteRow:"Delete row",deleteTable:"Delete table",widthAuto:"Width auto",insertCol:"Insert column",insertRow:"Insert row",insertTable:"Insert table",header:"Header"}}),t("zh-CN",{tableModule:{deleteCol:"删除列",deleteRow:"删除行",deleteTable:"删除表格",widthAuto:"宽度自适应",insertCol:"插入列",insertRow:"插入行",insertTable:"插入表格",header:"表头"}});var O="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function H(t){var e={exports:{}};return t(e,e.exports),e.exports}var P,V,z=function(t){return t&&t.Math==Math&&t},L=z("object"==typeof globalThis&&globalThis)||z("object"==typeof window&&window)||z("object"==typeof self&&self)||z("object"==typeof O&&O)||function(){return this}()||Function("return this")(),R=function(t){try{return!!t()}catch(t){return!0}},A=!R((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),C=Function.prototype.call,I=C.bind?C.bind(C):function(){return C.apply(C,arguments)},j={}.propertyIsEnumerable,D=Object.getOwnPropertyDescriptor,k={f:D&&!j.call({1:2},1)?function(t){var e=D(this,t);return!!e&&e.enumerable}:j},B=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},F=Function.prototype,$=F.bind,_=F.call,G=$&&$.bind(_),q=$?function(t){return t&&G(_,t)}:function(t){return t&&function(){return _.apply(t,arguments)}},U=q({}.toString),W=q("".slice),X=function(t){return W(U(t),8,-1)},Y=L.Object,K=q("".split),J=R((function(){return!Y("z").propertyIsEnumerable(0)}))?function(t){return"String"==X(t)?K(t,""):Y(t)}:Y,Q=L.TypeError,Z=function(t){if(null==t)throw Q("Can't call method on "+t);return t},tt=function(t){return J(Z(t))},et=function(t){return"function"==typeof t},nt=function(t){return"object"==typeof t?null!==t:et(t)},rt=function(t){return et(t)?t:void 0},ot=function(t,e){return arguments.length<2?rt(L[t]):L[t]&&L[t][e]},it=q({}.isPrototypeOf),at=ot("navigator","userAgent")||"",lt=L.process,ut=L.Deno,ct=lt&&lt.versions||ut&&ut.version,st=ct&&ct.v8;st&&(V=(P=st.split("."))[0]>0&&P[0]<4?1:+(P[0]+P[1])),!V&&at&&(!(P=at.match(/Edge\/(\d+)/))||P[1]>=74)&&(P=at.match(/Chrome\/(\d+)/))&&(V=+P[1]);var ft=V,dt=!!Object.getOwnPropertySymbols&&!R((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ft&&ft<41})),pt=dt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,vt=L.Object,ht=pt?function(t){return"symbol"==typeof t}:function(t){var e=ot("Symbol");return et(e)&&it(e.prototype,vt(t))},gt=L.String,yt=function(t){try{return gt(t)}catch(t){return"Object"}},bt=L.TypeError,mt=function(t){if(et(t))return t;throw bt(yt(t)+" is not a function")},wt=function(t,e){var n=t[e];return null==n?void 0:mt(n)},St=L.TypeError,xt=Object.defineProperty,Tt=function(t,e){try{xt(L,t,{value:e,configurable:!0,writable:!0})}catch(n){L[t]=e}return e},Et=L["__core-js_shared__"]||Tt("__core-js_shared__",{}),Nt=H((function(t){(t.exports=function(t,e){return Et[t]||(Et[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.19.3",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),Mt=L.Object,Ot=function(t){return Mt(Z(t))},Ht=q({}.hasOwnProperty),Pt=Object.hasOwn||function(t,e){return Ht(Ot(t),e)},Vt=0,zt=Math.random(),Lt=q(1..toString),Rt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Lt(++Vt+zt,36)},At=Nt("wks"),Ct=L.Symbol,It=Ct&&Ct.for,jt=pt?Ct:Ct&&Ct.withoutSetter||Rt,Dt=function(t){if(!Pt(At,t)||!dt&&"string"!=typeof At[t]){var e="Symbol."+t;dt&&Pt(Ct,t)?At[t]=Ct[t]:At[t]=pt&&It?It(e):jt(e)}return At[t]},kt=L.TypeError,Bt=Dt("toPrimitive"),Ft=function(t,e){if(!nt(t)||ht(t))return t;var n,r=wt(t,Bt);if(r){if(void 0===e&&(e="default"),n=I(r,t,e),!nt(n)||ht(n))return n;throw kt("Can't convert object to primitive value")}return void 0===e&&(e="number"),function(t,e){var n,r;if("string"===e&&et(n=t.toString)&&!nt(r=I(n,t)))return r;if(et(n=t.valueOf)&&!nt(r=I(n,t)))return r;if("string"!==e&&et(n=t.toString)&&!nt(r=I(n,t)))return r;throw St("Can't convert object to primitive value")}(t,e)},$t=function(t){var e=Ft(t,"string");return ht(e)?e:e+""},_t=L.document,Gt=nt(_t)&&nt(_t.createElement),qt=function(t){return Gt?_t.createElement(t):{}},Ut=!A&&!R((function(){return 7!=Object.defineProperty(qt("div"),"a",{get:function(){return 7}}).a})),Wt=Object.getOwnPropertyDescriptor,Xt={f:A?Wt:function(t,e){if(t=tt(t),e=$t(e),Ut)try{return Wt(t,e)}catch(t){}if(Pt(t,e))return B(!I(k.f,t,e),t[e])}},Yt=L.String,Kt=L.TypeError,Jt=function(t){if(nt(t))return t;throw Kt(Yt(t)+" is not an object")},Qt=L.TypeError,Zt=Object.defineProperty,te={f:A?Zt:function(t,e,n){if(Jt(t),e=$t(e),Jt(n),Ut)try{return Zt(t,e,n)}catch(t){}if("get"in n||"set"in n)throw Qt("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},ee=A?function(t,e,n){return te.f(t,e,B(1,n))}:function(t,e,n){return t[e]=n,t},ne=q(Function.toString);et(Et.inspectSource)||(Et.inspectSource=function(t){return ne(t)});var re,oe,ie,ae=Et.inspectSource,le=L.WeakMap,ue=et(le)&&/native code/.test(ae(le)),ce=Nt("keys"),se=function(t){return ce[t]||(ce[t]=Rt(t))},fe={},de=L.TypeError,pe=L.WeakMap;if(ue||Et.state){var ve=Et.state||(Et.state=new pe),he=q(ve.get),ge=q(ve.has),ye=q(ve.set);re=function(t,e){if(ge(ve,t))throw new de("Object already initialized");return e.facade=t,ye(ve,t,e),e},oe=function(t){return he(ve,t)||{}},ie=function(t){return ge(ve,t)}}else{var be=se("state");fe[be]=!0,re=function(t,e){if(Pt(t,be))throw new de("Object already initialized");return e.facade=t,ee(t,be,e),e},oe=function(t){return Pt(t,be)?t[be]:{}},ie=function(t){return Pt(t,be)}}var me={set:re,get:oe,has:ie,enforce:function(t){return ie(t)?oe(t):re(t,{})},getterFor:function(t){return function(e){var n;if(!nt(e)||(n=oe(e)).type!==t)throw de("Incompatible receiver, "+t+" required");return n}}},we=Function.prototype,Se=A&&Object.getOwnPropertyDescriptor,xe=Pt(we,"name"),Te={EXISTS:xe,PROPER:xe&&"something"===function(){}.name,CONFIGURABLE:xe&&(!A||A&&Se(we,"name").configurable)},Ee=H((function(t){var e=Te.CONFIGURABLE,n=me.get,r=me.enforce,o=String(String).split("String");(t.exports=function(t,n,i,a){var l,u=!!a&&!!a.unsafe,c=!!a&&!!a.enumerable,s=!!a&&!!a.noTargetGet,f=a&&void 0!==a.name?a.name:n;et(i)&&("Symbol("===String(f).slice(0,7)&&(f="["+String(f).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Pt(i,"name")||e&&i.name!==f)&&ee(i,"name",f),(l=r(i)).source||(l.source=o.join("string"==typeof f?f:""))),t!==L?(u?!s&&t[n]&&(c=!0):delete t[n],c?t[n]=i:ee(t,n,i)):c?t[n]=i:Tt(n,i)})(Function.prototype,"toString",(function(){return et(this)&&n(this).source||ae(this)}))})),Ne=Math.ceil,Me=Math.floor,Oe=function(t){var e=+t;return e!=e||0===e?0:(e>0?Me:Ne)(e)},He=Math.max,Pe=Math.min,Ve=function(t,e){var n=Oe(t);return n<0?He(n+e,0):Pe(n,e)},ze=Math.min,Le=function(t){return t>0?ze(Oe(t),9007199254740991):0},Re=function(t){return Le(t.length)},Ae=function(t){return function(e,n,r){var o,i=tt(e),a=Re(i),l=Ve(r,a);if(t&&n!=n){for(;a>l;)if((o=i[l++])!=o)return!0}else for(;a>l;l++)if((t||l in i)&&i[l]===n)return t||l||0;return!t&&-1}},Ce={includes:Ae(!0),indexOf:Ae(!1)}.indexOf,Ie=q([].push),je=function(t,e){var n,r=tt(t),o=0,i=[];for(n in r)!Pt(fe,n)&&Pt(r,n)&&Ie(i,n);for(;e.length>o;)Pt(r,n=e[o++])&&(~Ce(i,n)||Ie(i,n));return i},De=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],ke=De.concat("length","prototype"),Be={f:Object.getOwnPropertyNames||function(t){return je(t,ke)}},Fe={f:Object.getOwnPropertySymbols},$e=q([].concat),_e=ot("Reflect","ownKeys")||function(t){var e=Be.f(Jt(t)),n=Fe.f;return n?$e(e,n(t)):e},Ge=function(t,e){for(var n=_e(e),r=te.f,o=Xt.f,i=0;i<n.length;i++){var a=n[i];Pt(t,a)||r(t,a,o(e,a))}},qe=/#|\.prototype\./,Ue=function(t,e){var n=Xe[We(t)];return n==Ke||n!=Ye&&(et(e)?R(e):!!e)},We=Ue.normalize=function(t){return String(t).replace(qe,".").toLowerCase()},Xe=Ue.data={},Ye=Ue.NATIVE="N",Ke=Ue.POLYFILL="P",Je=Ue,Qe=Xt.f,Ze=function(t,e){var n,r,o,i,a,l=t.target,u=t.global,c=t.stat;if(n=u?L:c?L[l]||Tt(l,{}):(L[l]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(a=Qe(n,r))&&a.value:n[r],!Je(u?r:l+(c?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;Ge(i,o)}(t.sham||o&&o.sham)&&ee(i,"sham",!0),Ee(n,r,i,t)}},tn={};tn[Dt("toStringTag")]="z";var en,nn="[object z]"===String(tn),rn=Dt("toStringTag"),on=L.Object,an="Arguments"==X(function(){return arguments}()),ln=nn?X:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=on(t),rn))?n:an?X(e):"Object"==(r=X(e))&&et(e.callee)?"Arguments":r},un=L.String,cn=function(t){if("Symbol"===ln(t))throw TypeError("Cannot convert a Symbol value to a string");return un(t)},sn=/"/g,fn=q("".replace);Ze({target:"String",proto:!0,forced:(en="anchor",R((function(){var t=""[en]('"');return t!==t.toLowerCase()||t.split('"').length>3})))},{anchor:function(t){return e="a",n="name",r=t,o=cn(Z(this)),i="<"+e,""!==n&&(i+=" "+n+'="'+fn(cn(r),sn,"&quot;")+'"'),i+">"+o+"</"+e+">";var e,n,r,o,i}});var dn,pn=function(){var t=Jt(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},vn=L.RegExp,hn=R((function(){var t=vn("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),gn=hn||R((function(){return!vn("a","y").sticky})),yn={BROKEN_CARET:hn||R((function(){var t=vn("^r","gy");return t.lastIndex=2,null!=t.exec("str")})),MISSED_STICKY:gn,UNSUPPORTED_Y:hn},bn=Object.keys||function(t){return je(t,De)},mn=A?Object.defineProperties:function(t,e){Jt(t);for(var n,r=tt(e),o=bn(e),i=o.length,a=0;i>a;)te.f(t,n=o[a++],r[n]);return t},wn=ot("document","documentElement"),Sn=se("IE_PROTO"),xn=function(){},Tn=function(t){return"<script>"+t+"<\/script>"},En=function(t){t.write(Tn("")),t.close();var e=t.parentWindow.Object;return t=null,e},Nn=function(){try{dn=new ActiveXObject("htmlfile")}catch(t){}var t,e;Nn="undefined"!=typeof document?document.domain&&dn?En(dn):((e=qt("iframe")).style.display="none",wn.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Tn("document.F=Object")),t.close(),t.F):En(dn);for(var n=De.length;n--;)delete Nn.prototype[De[n]];return Nn()};fe[Sn]=!0;var Mn,On,Hn=Object.create||function(t,e){var n;return null!==t?(xn.prototype=Jt(t),n=new xn,xn.prototype=null,n[Sn]=t):n=Nn(),void 0===e?n:mn(n,e)},Pn=L.RegExp,Vn=R((function(){var t=Pn(".","s");return!(t.dotAll&&t.exec("\n")&&"s"===t.flags)})),zn=L.RegExp,Ln=R((function(){var t=zn("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Rn=me.get,An=Nt("native-string-replace",String.prototype.replace),Cn=RegExp.prototype.exec,In=Cn,jn=q("".charAt),Dn=q("".indexOf),kn=q("".replace),Bn=q("".slice),Fn=(On=/b*/g,I(Cn,Mn=/a/,"a"),I(Cn,On,"a"),0!==Mn.lastIndex||0!==On.lastIndex),$n=yn.BROKEN_CARET,_n=void 0!==/()??/.exec("")[1];(Fn||_n||$n||Vn||Ln)&&(In=function(t){var e,n,r,o,i,a,l,u=this,c=Rn(u),s=cn(t),f=c.raw;if(f)return f.lastIndex=u.lastIndex,e=I(In,f,s),u.lastIndex=f.lastIndex,e;var d=c.groups,p=$n&&u.sticky,v=I(pn,u),h=u.source,g=0,y=s;if(p&&(v=kn(v,"y",""),-1===Dn(v,"g")&&(v+="g"),y=Bn(s,u.lastIndex),u.lastIndex>0&&(!u.multiline||u.multiline&&"\n"!==jn(s,u.lastIndex-1))&&(h="(?: "+h+")",y=" "+y,g++),n=new RegExp("^(?:"+h+")",v)),_n&&(n=new RegExp("^"+h+"$(?!\\s)",v)),Fn&&(r=u.lastIndex),o=I(Cn,p?n:u,y),p?o?(o.input=Bn(o.input,g),o[0]=Bn(o[0],g),o.index=u.lastIndex,u.lastIndex+=o[0].length):u.lastIndex=0:Fn&&o&&(u.lastIndex=u.global?o.index+o[0].length:r),_n&&o&&o.length>1&&I(An,o[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(o[i]=void 0)})),o&&d)for(o.groups=a=Hn(null),i=0;i<d.length;i++)a[(l=d[i])[0]]=o[l[1]];return o});var Gn=In;Ze({target:"RegExp",proto:!0,forced:/./.exec!==Gn},{exec:Gn});var qn=Array.isArray||function(t){return"Array"==X(t)},Un=function(){},Wn=[],Xn=ot("Reflect","construct"),Yn=/^\s*(?:class|function)\b/,Kn=q(Yn.exec),Jn=!Yn.exec(Un),Qn=function(t){if(!et(t))return!1;try{return Xn(Un,Wn,t),!0}catch(t){return!1}},Zn=!Xn||R((function(){var t;return Qn(Qn.call)||!Qn(Object)||!Qn((function(){t=!0}))||t}))?function(t){if(!et(t))return!1;switch(ln(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return Jn||!!Kn(Yn,ae(t))}:Qn,tr=function(t,e,n){var r=$t(e);r in t?te.f(t,r,B(0,n)):t[r]=n},er=Dt("species"),nr=function(t){return ft>=51||!R((function(){var e=[];return(e.constructor={})[er]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},rr=q([].slice),or=nr("slice"),ir=Dt("species"),ar=L.Array,lr=Math.max;function ur(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function cr(t){var n=t.selection;if(null==n)return!1;var o=ur(r.nodes(t,{match:function(t){return e.checkNodeType(t,"table-cell")}}),1)[0];if(o){var i=ur(o,2)[1],a=r.start(t,i);if(l.equals(n.anchor,a))return!0}return!1}function sr(t,n){var o,i,a=r.nodes(t,{at:n,match:function(t){return"table"===e.getNodeType(t)}}),l=!1;try{for(var u=
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
function(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}(a),c=u.next();!c.done;c=u.next()){c.value;l=!0}}catch(t){o={error:t}}finally{try{c&&!c.done&&(i=u.return)&&i.call(u)}finally{if(o)throw o.error}}return l}Ze({target:"Array",proto:!0,forced:!or},{slice:function(t,e){var n,r,o,i=tt(this),a=Re(i),l=Ve(t,a),u=Ve(void 0===e?a:e,a);if(qn(i)&&(n=i.constructor,(Zn(n)&&(n===ar||qn(n.prototype))||nt(n)&&null===(n=n[ir]))&&(n=void 0),n===ar||void 0===n))return rr(i,l,u);for(r=new(void 0===n?ar:n)(lr(u-l,0)),o=0;l<u;l++,o++)l in i&&tr(r,o,i[l]);return r.length=o,r}});var fr=q(q.bind),dr=Dt("species"),pr=L.Array,vr=function(t,e){return new(function(t){var e;return qn(t)&&(e=t.constructor,(Zn(e)&&(e===pr||qn(e.prototype))||nt(e)&&null===(e=e[dr]))&&(e=void 0)),void 0===e?pr:e}(t))(0===e?0:e)},hr=q([].push),gr=function(t){var e=1==t,n=2==t,r=3==t,o=4==t,i=6==t,a=7==t,l=5==t||i;return function(u,c,s,f){for(var d,p,v=Ot(u),h=J(v),g=function(t,e){return mt(t),void 0===e?t:fr?fr(t,e):function(){return t.apply(e,arguments)}}(c,s),y=Re(h),b=0,m=f||vr,w=e?m(u,y):n||a?m(u,0):void 0;y>b;b++)if((l||b in h)&&(p=g(d=h[b],b,v),t))if(e)w[b]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return b;case 2:hr(w,d)}else switch(t){case 4:return!1;case 7:hr(w,d)}return i?-1:r||o?o:w}},yr={forEach:gr(0),map:gr(1),filter:gr(2),some:gr(3),every:gr(4),find:gr(5),findIndex:gr(6),filterReject:gr(7)},br=yr.map;Ze({target:"Array",proto:!0,forced:!nr("map")},{map:function(t){return br(this,t,arguments.length>1?arguments[1]:void 0)}});var mr=nn?{}.toString:function(){return"[object "+ln(this)+"]"};function wr(t){var e=t.children||[];return 0===e.length?[]:(e[0]||{}).children||[]}function Sr(t){return wr(t).every((function(t){return!!t.isHeader}))}nn||Ee(Object.prototype,"toString",mr,{unsafe:!0});var xr=Te.PROPER,Tr=RegExp.prototype,Er=Tr.toString,Nr=q(pn),Mr=R((function(){return"/a/b"!=Er.call({source:"a",flags:"b"})})),Or=xr&&"toString"!=Er.name;(Mr||Or)&&Ee(RegExp.prototype,"toString",(function(){var t=Jt(this),e=cn(t.source),n=t.flags;return"/"+e+"/"+cn(void 0===n&&it(Tr,t)&&!("flags"in Tr)?Nr(t):n)}),{unsafe:!0});var Hr=Dt("unscopables"),Pr=Array.prototype;null==Pr[Hr]&&te.f(Pr,Hr,{configurable:!0,value:Hn(null)});var Vr,zr=yr.find,Lr=!0;"find"in[]&&Array(1).find((function(){Lr=!1})),Ze({target:"Array",proto:!0,forced:Lr},{find:function(t){return zr(this,t,arguments.length>1?arguments[1]:void 0)}}),Vr="find",Pr[Hr][Vr]=!0;var Rr=Function.prototype,Ar=Rr.apply,Cr=Rr.bind,Ir=Rr.call,jr="object"==typeof Reflect&&Reflect.apply||(Cr?Ir.bind(Ar):function(){return Ir.apply(Ar,arguments)}),Dr=Dt("species"),kr=RegExp.prototype,Br=function(t,e,n,r){var o=Dt(t),i=!R((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),a=i&&!R((function(){var e=!1,n=/a/;return"split"===t&&((n={}).constructor={},n.constructor[Dr]=function(){return n},n.flags="",n[o]=/./[o]),n.exec=function(){return e=!0,null},n[o](""),!e}));if(!i||!a||n){var l=q(/./[o]),u=e(o,""[t],(function(t,e,n,r,o){var a=q(t),u=e.exec;return u===Gn||u===kr.exec?i&&!o?{done:!0,value:l(e,n,r)}:{done:!0,value:a(n,e,r)}:{done:!1}}));Ee(String.prototype,t,u[0]),Ee(kr,o,u[1])}r&&ee(kr[o],"sham",!0)},Fr=Dt("match"),$r=L.TypeError,_r=Dt("species"),Gr=function(t,e){var n,r=Jt(t).constructor;return void 0===r||null==(n=Jt(r)[_r])?e:function(t){if(Zn(t))return t;throw $r(yt(t)+" is not a constructor")}(n)},qr=q("".charAt),Ur=q("".charCodeAt),Wr=q("".slice),Xr=function(t){return function(e,n){var r,o,i=cn(Z(e)),a=Oe(n),l=i.length;return a<0||a>=l?t?"":void 0:(r=Ur(i,a))<55296||r>56319||a+1===l||(o=Ur(i,a+1))<56320||o>57343?t?qr(i,a):r:t?Wr(i,a,a+2):o-56320+(r-55296<<10)+65536}},Yr={codeAt:Xr(!1),charAt:Xr(!0)}.charAt,Kr=function(t,e,n){return e+(n?Yr(t,e).length:1)},Jr=L.Array,Qr=Math.max,Zr=function(t,e,n){for(var r=Re(t),o=Ve(e,r),i=Ve(void 0===n?r:n,r),a=Jr(Qr(i-o,0)),l=0;o<i;o++,l++)tr(a,l,t[o]);return a.length=l,a},to=L.TypeError,eo=function(t,e){var n=t.exec;if(et(n)){var r=I(n,t,e);return null!==r&&Jt(r),r}if("RegExp"===X(t))return I(Gn,t,e);throw to("RegExp#exec called on incompatible receiver")},no=yn.UNSUPPORTED_Y,ro=Math.min,oo=[].push,io=q(/./.exec),ao=q(oo),lo=q("".slice);Br("split",(function(t,e,n){var r;return r="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,n){var r,o,i=cn(Z(this)),a=void 0===n?4294967295:n>>>0;if(0===a)return[];if(void 0===t)return[i];if(!nt(r=t)||!(void 0!==(o=r[Fr])?o:"RegExp"==X(r)))return I(e,i,t,a);for(var l,u,c,s=[],f=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),d=0,p=new RegExp(t.source,f+"g");(l=I(Gn,p,i))&&!((u=p.lastIndex)>d&&(ao(s,lo(i,d,l.index)),l.length>1&&l.index<i.length&&jr(oo,s,Zr(l,1)),c=l[0].length,d=u,s.length>=a));)p.lastIndex===l.index&&p.lastIndex++;return d===i.length?!c&&io(p,"")||ao(s,""):ao(s,lo(i,d)),s.length>a?Zr(s,0,a):s}:"0".split(void 0,0).length?function(t,n){return void 0===t&&0===n?[]:I(e,this,t,n)}:e,[function(e,n){var o=Z(this),i=null==e?void 0:wt(e,t);return i?I(i,e,o,n):I(r,cn(o),e,n)},function(t,o){var i=Jt(this),a=cn(t),l=n(r,i,a,o,r!==e);if(l.done)return l.value;var u=Gr(i,RegExp),c=i.unicode,s=(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.unicode?"u":"")+(no?"g":"y"),f=new u(no?"^(?:"+i.source+")":i,s),d=void 0===o?4294967295:o>>>0;if(0===d)return[];if(0===a.length)return null===eo(f,a)?[a]:[];for(var p=0,v=0,h=[];v<a.length;){f.lastIndex=no?0:v;var g,y=eo(f,no?lo(a,v):a);if(null===y||(g=ro(Le(f.lastIndex+(no?v:0)),a.length))===p)v=Kr(a,v,c);else{if(ao(h,lo(a,p,v)),h.length===d)return h;for(var b=1;b<=y.length-1;b++)if(ao(h,y[b]),h.length===d)return h;v=p=g}}return ao(h,lo(a,p)),h}]}),!!R((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var n="ab".split(t);return 2!==n.length||"a"!==n[0]||"b"!==n[1]})),no);var uo="\t\n\v\f\r                　\u2028\u2029\ufeff",co=q("".replace),so="["+uo+"]",fo=RegExp("^"+so+so+"*"),po=RegExp(so+so+"*$"),vo=function(t){return function(e){var n=cn(Z(e));return 1&t&&(n=co(n,fo,"")),2&t&&(n=co(n,po,"")),n}},ho={start:vo(1),end:vo(2),trim:vo(3)},go=Te.PROPER,yo=ho.trim;function bo(t){return t.length?t[0].tagName.toLowerCase():""}Ze({target:"String",proto:!0,forced:function(t){return R((function(){return!!uo[t]()||"​᠎"!=="​᠎"[t]()||go&&uo[t].name!==t}))}("trim")},{trim:function(){return yo(this)}}),Ze({global:!0},{globalThis:L}),v&&(p.fn.append=v),h&&(p.fn.on=h),g&&(p.fn.focus=g),y&&(p.fn.attr=y),b&&(p.fn.val=b),m&&(p.fn.html=m),w&&(p.fn.dataset=w),S&&(p.fn.addClass=S),x&&(p.fn.removeClass=x),T&&(p.fn.children=T),E&&(p.fn.each=E),N&&(p.fn.find=N);var mo=!1,wo=0,So=0,xo=null,To=null,Eo=p("body");function No(t){mo=!1,To=null,xo=null,Eo.off("mousemove",Mo),Eo.off("mouseup",No)}Eo.on("mousedown",(function(t){var e=t.target;if(("TH"===e.tagName||"TD"===e.tagName)&&"col-resize"===e.style.cursor){e.style.cursor="auto",t.preventDefault(),mo=!0;var n=t.clientX;wo=n;var r=e.getBoundingClientRect().width;So=r,Eo.on("mousemove",Mo),Eo.on("mouseup",No)}}));var Mo=d((function(t){if(mo&&null!=To&&null!=xo){t.preventDefault();var e=t.clientX,n=So+(e-wo);(n=Math.floor(100*n)/100)<30&&(n=30),o.setNodes(To,{width:n.toString()},{at:xo})}}),100);var Oo={type:"table",renderElem:function(t,n,o){var a=function(t,n){if(t.isDisabled())return!1;var o=t.selection;if(null==o)return!0;if(u.isCollapsed(o))return!0;var a=o.anchor,c=o.focus,s=e.findPath(t,n),f=r.start(t,s),d=r.end(t,s),p=l.compare(a,d)<=0&&l.compare(a,f)>=0,v=l.compare(c,d)<=0&&l.compare(c,f)>=0;return!!(p&&v&&i.equals(a.path.slice(0,3),c.path.slice(0,3)))}(o,t),c=t.width,s=void 0===c?"auto":c,d=e.isNodeSelected(o,t),p=wr(t),v=f("div",{className:"table-container","data-selected":d,on:{mousedown:function(n){if("DIV"===n.target.tagName&&n.preventDefault(),!o.isDisabled()){var i=e.findPath(o,t),a=r.start(o,i),l=o.selection;if(null!=l)l.anchor.path[0]!==i[0]&&o.select(a);else o.select(a)}}}},f("table",{width:s,contentEditable:a},f("colgroup",null,p.map((function(t){var e=t.width;return f("col",{width:void 0===e?"auto":e})}))),f("tbody",null,n)));return v}},Ho={type:"table-row",renderElem:function(t,e,n){return f("tr",null,e)}},Po={type:"table-cell",renderElem:function(t,n,r){var o=function(t,n){var r=e.getParentNode(t,n);if(null==r)return!1;var o=e.getParentNode(t,r);return null!=o&&wr(o).some((function(t){return t===n}))}(r,t),i=t,a=i.colSpan,l=void 0===a?1:a,u=i.rowSpan,c=void 0===u?1:u,s=i.isHeader,p=void 0!==s&&s;if(!o)return f("td",{colSpan:l,rowSpan:c},n);var v=f(p?"th":"td",{colSpan:l,rowSpan:c,style:{borderRightWidth:"3px"},on:{mousemove:d((function(n){var o=this.elm;if(null!=o){var i=o.getBoundingClientRect(),a=i.left,l=i.width,u=i.top,c=i.height,s=n.clientX,f=n.clientY;if(!mo)s>a+l-5&&s<a+l&&(f>u&&f<u+c)?(o.style.cursor="col-resize",To=r,xo=e.findPath(r,t)):mo||(o.style.cursor="auto",To=null,xo=null)}}),100)}},n);return v}};var Vo={type:"table",elemToHtml:function(t,e){var n=t.width;return'<table style="width: '+(void 0===n?"auto":n)+';"><tbody>'+e+"</tbody></table>"}},zo={type:"table-row",elemToHtml:function(t,e){return"<tr>"+e+"</tr>"}},Lo={type:"table-cell",elemToHtml:function(t,e){var n=t,r=n.colSpan,o=void 0===r?1:r,i=n.rowSpan,a=void 0===i?1:i,l=n.isHeader,u=void 0!==l&&l,c=n.width,s=u?"th":"td";return"<"+s+' colSpan="'+o+'" rowSpan="'+a+'" width="'+(void 0===c?"auto":c)+'">'+e+"</"+s+">"}};var Ro={selector:"table",preParseHtml:function(t){var e=p(t);if("table"!==bo(e))return t;var n=e.find("tbody");if(0===n.length)return t;var r=e.find("tr");return e.append(r),n.remove(),e[0]}},Ao=yr.filter;Ze({target:"Array",proto:!0,forced:!nr("filter")},{filter:function(t){return Ao(this,t,arguments.length>1?arguments[1]:void 0)}});var Co=Math.floor,Io=q("".charAt),jo=q("".replace),Do=q("".slice),ko=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Bo=/\$([$&'`]|\d{1,2})/g,Fo=function(t,e,n,r,o,i){var a=n+t.length,l=r.length,u=Bo;return void 0!==o&&(o=Ot(o),u=ko),jo(i,u,(function(i,u){var c;switch(Io(u,0)){case"$":return"$";case"&":return t;case"`":return Do(e,0,n);case"'":return Do(e,a);case"<":c=o[Do(u,1,-1)];break;default:var s=+u;if(0===s)return i;if(s>l){var f=Co(s/10);return 0===f?i:f<=l?void 0===r[f-1]?Io(u,1):r[f-1]+Io(u,1):i}c=r[s-1]}return void 0===c?"":c}))},$o=Dt("replace"),_o=Math.max,Go=Math.min,qo=q([].concat),Uo=q([].push),Wo=q("".indexOf),Xo=q("".slice),Yo="$0"==="a".replace(/./,"$0"),Ko=!!/./[$o]&&""===/./[$o]("a","$0");Br("replace",(function(t,e,n){var r=Ko?"$":"$0";return[function(t,n){var r=Z(this),o=null==t?void 0:wt(t,$o);return o?I(o,t,r,n):I(e,cn(r),t,n)},function(t,o){var i=Jt(this),a=cn(t);if("string"==typeof o&&-1===Wo(o,r)&&-1===Wo(o,"$<")){var l=n(e,i,a,o);if(l.done)return l.value}var u=et(o);u||(o=cn(o));var c=i.global;if(c){var s=i.unicode;i.lastIndex=0}for(var f=[];;){var d=eo(i,a);if(null===d)break;if(Uo(f,d),!c)break;""===cn(d[0])&&(i.lastIndex=Kr(a,Le(i.lastIndex),s))}for(var p,v="",h=0,g=0;g<f.length;g++){for(var y=cn((d=f[g])[0]),b=_o(Go(Oe(d.index),a.length),0),m=[],w=1;w<d.length;w++)Uo(m,void 0===(p=d[w])?p:String(p));var S=d.groups;if(u){var x=qo([y],m,b,a);void 0!==S&&Uo(x,S);var T=cn(jr(o,void 0,x))}else T=Fo(y,a,b,m,S,o);b>=h&&(v+=Xo(a,h,b)+T,h=b+y.length)}return v+Xo(a,h)}]}),!!R((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!Yo||Ko);var Jo={selector:"td:not([data-w-e-type]),th:not([data-w-e-type])",parseElemHtml:function(t,e,n){var r=p(t);0===(e=e.filter((function(t){return!!c.isText(t)||!!n.isInline(t)}))).length&&(e=[{text:r.text().replace(/\s+/gm," ")}]);var o=parseInt(r.attr("colSpan")||"1"),i=parseInt(r.attr("rowSpan")||"1"),a=r.attr("width")||"auto";return{type:"table-cell",isHeader:"th"===bo(r),colSpan:o,rowSpan:i,width:a,children:e}}};var Qo={selector:"tr:not([data-w-e-type])",parseElemHtml:function(t,n,r){return{type:"table-row",children:n.filter((function(t){return"table-cell"===e.getNodeType(t)}))}}};var Zo={selector:"table:not([data-w-e-type])",parseElemHtml:function(t,n,r){var o=p(t),i="auto";return"100%"===function(t,e){for(var n="",r=(t.attr("style")||"").split(";"),o=r.length,i=0;i<o;i++){var a=r[i];if(a){var l=a.split(":");l[0].trim()===e&&(n=l[1].trim())}}return n}(o,"width")&&(i="100%"),"100%"===o.attr("width")&&(i="100%"),{type:"table",width:i,children:n.filter((function(t){return"table-row"===e.getNodeType(t)}))}}};var ti=function(){function t(){this.title=n("tableModule.insertTable"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M0 64v896h1024V64H0z m384 576v-192h256v192h-256z m256 64v192h-256v-192h256z m0-512v192h-256V192h256zM320 192v192H64V192h256z m-256 256h256v192H64v-192z m640 0h256v192h-256v-192z m0-64V192h256v192h-256zM64 704h256v192H64v-192z m640 192v-192h256v192h-256z"></path></svg>',this.tag="button",this.showDropPanel=!0,this.$content=null}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.exec=function(t,e){},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||!!e.getSelectedElems(t).some((function(n){var r=e.getNodeType(n);return"pre"===r||("table"===r||("list-item"===r||!!t.isVoid(n)))})))},t.prototype.getPanelContentElem=function(t){var e=this;if(this.$content)return this.$content[0];for(var n=p('<div class="w-e-panel-content-table"></div>'),r=p("<span>0 &times; 0</span>"),o=p("<table></table>"),i=0;i<10;i++){for(var a=p("<tr></tr>"),l=0;l<10;l++){var u=p("<td></td>");u.attr("data-x",l.toString()),u.attr("data-y",i.toString()),a.append(u),u.on("mouseenter",(function(t){var e=t.target;if(null!=e){var n=p(e).dataset(),i=n.x,a=n.y;r[0].innerHTML=i+1+" &times; "+(a+1),o.children().each((function(t){p(t).children().each((function(t){var e=p(t),n=e.dataset(),r=n.x,o=n.y;r<=i&&o<=a?e.addClass("active"):e.removeClass("active")}))}))}})),u.on("click",(function(n){n.preventDefault();var r=n.target;if(null!=r){var o=p(r).dataset(),i=o.x,a=o.y;e.insertTable(t,a+1,i+1)}}))}o.append(a)}return n.append(o),n.append(r),this.$content=n,n[0]},t.prototype.insertTable=function(t,n,r){var i=parseInt(n,10),a=parseInt(r,10);if(i&&a&&!(i<=0||a<=0)){e.isSelectedEmptyParagraph(t)&&o.removeNodes(t,{mode:"highest"});var l=function(t,e){for(var n=[],r=0;r<t;r++){for(var o=[],i=0;i<e;i++){var a={type:"table-cell",children:[{text:""}]};0===r&&(a.isHeader=!0),o.push(a)}n.push({type:"table-row",children:o})}return{type:"table",width:"auto",children:n}}(i,a);o.insertNodes(t,l,{mode:"highest"})}},t}(),ei=function(){function t(){this.title=n("tableModule.deleteTable"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M826.8032 356.5312c-19.328 0-36.3776 15.6928-36.3776 35.0464v524.2624c0 19.328-16 34.56-35.328 34.56H264.9344c-19.328 0-35.5072-15.3088-35.5072-34.56V390.0416c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.6928-33.5104 35.0464V915.712c0 57.9328 44.6208 108.288 102.528 108.288H755.2c57.9328 0 108.0832-50.4576 108.0832-108.288V391.4752c-0.1024-19.2512-17.1264-34.944-36.48-34.944z" p-id="9577"></path><path d="M437.1712 775.7568V390.6048c0-19.328-14.1568-35.0464-33.5104-35.0464s-33.5104 15.616-33.5104 35.0464v385.152c0 19.328 14.1568 35.0464 33.5104 35.0464s33.5104-15.7184 33.5104-35.0464zM649.7024 775.7568V390.6048c0-19.328-17.0496-35.0464-36.3776-35.0464s-36.3776 15.616-36.3776 35.0464v385.152c0 19.328 17.0496 35.0464 36.3776 35.0464s36.3776-15.7184 36.3776-35.0464zM965.0432 217.0368h-174.6176V145.5104c0-57.9328-47.2064-101.76-104.6528-101.76h-350.976c-57.8304 0-105.3952 43.8528-105.3952 101.76v71.5264H54.784c-19.4304 0-35.0464 14.1568-35.0464 33.5104 0 19.328 15.616 33.5104 35.0464 33.5104h910.3616c19.328 0 35.0464-14.1568 35.0464-33.5104 0-19.3536-15.6928-33.5104-35.1488-33.5104z m-247.3728 0H297.3952V145.5104c0-19.328 18.2016-34.7648 37.4272-34.7648h350.976c19.1488 0 31.872 15.1296 31.872 34.7648v71.5264z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){return null==t.selection||null==e.getSelectedNodeByType(t,"table")},t.prototype.exec=function(t,e){this.isDisabled(t)||o.removeNodes(t,{mode:"highest"})},t}(),ni=function(){function t(){this.title=n("tableModule.insertRow"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M707.7888 521.0112h-147.456v-147.456H488.2432v147.456h-147.456v68.8128h147.456v147.456h72.0896v-147.456h147.456zM0 917.504V0h1048.576v917.504H0zM327.68 65.536H65.536v196.608H327.68V65.536z m327.68 0H393.216v196.608h262.144V65.536z m327.68 0h-262.144v196.608h262.144V65.536z m0 258.8672H65.536v462.0288H983.04V324.4032z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||null==e.getSelectedNodeByType(t,"table"))},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var a=ur(r.nodes(t,{match:function(t){return e.checkNodeType(t,"table-cell")},universal:!0}),1),l=ur(a[0],2),u=l[0],c=l[1],s=e.getParentNode(t,u),f=(null==s?void 0:s.children.length)||0;if(0!==f){for(var d={type:"table-row",children:[]},p=0;p<f;p++){d.children.push({type:"table-cell",children:[{text:""}]})}var v=i.parent(c),h=i.next(v);o.insertNodes(t,d,{at:h})}}},t}(),ri=function(){function t(){this.title=n("tableModule.deleteRow"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M907.6736 586.5472L747.1104 425.984l163.84-163.84-78.6432-78.6432-163.84 163.84L507.************ 429.2608 262.144l163.84 163.84-167.1168 167.1168 78.6432 78.6432 167.1168-167.1168 160.5632 160.5632 75.3664-78.6432zM0 917.504V0h1048.576v917.504H0z m983.04-327.68h-22.9376l-65.536-65.536H983.04V327.68h-91.7504l65.536-65.536h26.2144V65.536H65.536v196.608h317.8496l65.536 65.536H65.536v196.608h380.1088l-65.536 65.536H65.536v196.608H983.04v-196.608z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||null==e.getSelectedNodeByType(t,"table-row"))},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var i=ur(r.nodes(t,{match:function(t){return e.checkNodeType(t,"table-row")},universal:!0}),1),a=ur(i[0],2),l=a[0],u=a[1],c=e.getParentNode(t,l);((null==c?void 0:c.children.length)||0)<=1?o.removeNodes(t,{mode:"highest"}):o.removeNodes(t,{at:u})}},t}(),oi={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},ii=qt("span").classList,ai=ii&&ii.constructor&&ii.constructor.prototype,li=ai===Object.prototype?void 0:ai,ui=yr.forEach,ci=function(t,e){var n=[][t];return!!n&&R((function(){n.call(null,e||function(){throw 1},1)}))}("forEach"),si=ci?[].forEach:function(t){return ui(this,t,arguments.length>1?arguments[1]:void 0)},fi=function(t){if(t&&t.forEach!==si)try{ee(t,"forEach",si)}catch(e){t.forEach=si}};for(var di in oi)oi[di]&&fi(L[di]&&L[di].prototype);fi(li);var pi=function(){function t(){this.title=n("tableModule.insertCol"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M327.68 193.3312v186.7776H140.9024v91.7504H327.68v186.7776h88.4736V471.8592h190.0544V380.1088H416.1536V193.3312zM0 917.504V0h1048.576v917.504H0zM655.36 65.536H65.536v720.896H655.36V65.536z m327.68 0h-262.144v196.608h262.144V65.536z m0 262.144h-262.144v196.608h262.144V327.68z m0 262.144h-262.144v196.608h262.144v-196.608z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||null==e.getSelectedNodeByType(t,"table"))},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var i=ur(r.nodes(t,{match:function(t){return e.checkNodeType(t,"table-cell")},universal:!0}),1),a=ur(i[0],2),l=a[0],u=a[1],c=e.getParentNode(t,l);if(null!=c){var f=e.getParentNode(t,c);if(null!=f)(f.children||[]).forEach((function(n,r){s.isElement(n)&&(n.children||[]).forEach((function(n){var i=e.findPath(t,n);if(i.length===u.length&&M(i.slice(-1),u.slice(-1))){var a={type:"table-cell",children:[{text:""}]};0===r&&Sr(f)&&(a.isHeader=!0),o.insertNodes(t,a,{at:i})}}))}))}}},t}(),vi=function(){function t(){this.title=n("tableModule.deleteCol"),this.iconSvg='<svg viewBox="0 0 1048 1024"><path d="M327.68 510.976L393.216 445.44v-13.1072L327.68 366.7968V510.976z m327.68-78.4384l65.536-65.536V507.904L655.36 442.368v-9.8304z m393.216 484.9664V0H0v917.504h1048.576z m-65.536-131.072h-262.144v-52.4288l-13.1072 13.1072-52.4288-52.4288v91.7504H393.216v-91.7504l-52.4288 52.4288-13.1072-13.1072v52.4288H65.536V65.536H327.68v121.2416l36.0448-36.0448 29.4912 29.4912V62.2592h262.144V180.224l49.152-49.152 16.384 16.384V62.2592h262.144V786.432z m-294.912-108.1344l-160.5632-160.5632-167.1168 167.1168-78.6432-78.6432 167.1168-167.1168L288.3584 278.528l78.6432-78.6432 160.5632 160.5632 163.84-163.84 78.6432 78.6432-163.84 163.84 160.5632 160.5632-78.6432 78.6432z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){return""},t.prototype.isActive=function(t){return!1},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||null==e.getSelectedNodeByType(t,"table-cell"))},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var i=ur(r.nodes(t,{match:function(t){return e.checkNodeType(t,"table-cell")},universal:!0}),1),a=ur(i[0],2),l=a[0],u=a[1],c=e.getParentNode(t,l),f=(null==c?void 0:c.children.length)||0;if(!c||f<=1)o.removeNodes(t,{mode:"highest"});else{var d=e.getParentNode(t,c);if(null!=d)(d.children||[]).forEach((function(n){s.isElement(n)&&(n.children||[]).forEach((function(n){var r=e.findPath(t,n);r.length===u.length&&M(r.slice(-1),u.slice(-1))&&o.removeNodes(t,{at:r})}))}))}}},t}(),hi=function(){function t(){this.title=n("tableModule.header"),this.iconSvg='<svg viewBox="0 0 1024 1024"><path d="M704 128l-64 0L384 128 320 128 0 128l0 256 0 64 0 192 0 64 0 256 320 0 64 0 256 0 64 0 320 0 0-256 0-64L1024 448 1024 384 1024 128 704 128zM640 640 384 640 384 448l256 0L640 640zM64 448l256 0 0 192L64 640 64 448zM320 896 64 896l0-192 256 0L320 896zM640 896 384 896l0-192 256 0L640 896zM960 896l-256 0 0-192 256 0L960 896zM960 640l-256 0L704 448l256 0L960 640z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){var n=e.getSelectedNodeByType(t,"table");return null!=n&&Sr(n)},t.prototype.isActive=function(t){return!!this.getValue(t)},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||null==e.getSelectedNodeByType(t,"table"))},t.prototype.exec=function(t,n){if(!this.isDisabled(t)){var r=!n,i=e.getSelectedNodeByType(t,"table");if(null!=i)wr(i).forEach((function(n){return o.setNodes(t,{isHeader:r},{at:e.findPath(t,n)})}))}},t}(),gi=function(){function t(){this.title=n("tableModule.widthAuto"),this.iconSvg='<svg viewBox="0 0 1228 1024"><path d="M862.514337 563.200461H404.581995v121.753478a13.311987 13.311987 0 0 1-6.655993 11.468789 10.23999 10.23999 0 0 1-12.083188-1.433599l-204.799795-179.199821a13.721586 13.721586 0 0 1 0-20.479979l204.799795-179.302221a10.23999 10.23999 0 0 1 12.185588-1.535998 13.209587 13.209587 0 0 1 6.553593 11.673588v115.097485h457.932342V319.693504a11.571188 11.571188 0 0 1 18.841582-10.239989l204.799795 179.19982a13.721586 13.721586 0 0 1 0 20.47998l-204.799795 179.199821a10.23999 10.23999 0 0 1-12.185588 1.535998 13.311987 13.311987 0 0 1-6.655994-11.571188V563.200461zM136.499064 14.951409v993.893406a15.257585 15.257585 0 0 1-15.155185 15.052785H15.155185A15.155185 15.155185 0 0 1 0 1008.844815V14.951409a15.257585 15.257585 0 0 1 15.155185-15.052785h106.086294a15.155185 15.155185 0 0 1 15.257585 15.155185zM1228.798771 14.951409v993.893406a15.257585 15.257585 0 0 1-15.155185 15.052785h-106.188693a15.155185 15.155185 0 0 1-15.155185-15.052785V14.951409a15.257585 15.257585 0 0 1 15.155185-15.052785h106.086293A15.155185 15.155185 0 0 1 1228.798771 15.053809z"></path></svg>',this.tag="button"}return t.prototype.getValue=function(t){var n=e.getSelectedNodeByType(t,"table");return null!=n&&"100%"===n.width},t.prototype.isActive=function(t){return!!this.getValue(t)},t.prototype.isDisabled=function(t){var n=t.selection;return null==n||(!u.isCollapsed(n)||null==e.getSelectedNodeByType(t,"table"))},t.prototype.exec=function(t,e){if(!this.isDisabled(t)){var n={width:e?"auto":"100%"};o.setNodes(t,n,{mode:"highest"})}},t}(),yi={renderElems:[Oo,Ho,Po],elemsToHtml:[Vo,zo,Lo],preParseHtml:[Ro],parseElemsHtml:[Jo,Qo,Zo],menus:[{key:"insertTable",factory:function(){return new ti}},{key:"deleteTable",factory:function(){return new ei}},{key:"insertTableRow",factory:function(){return new ni}},{key:"deleteTableRow",factory:function(){return new ri}},{key:"insertTableCol",factory:function(){return new pi}},{key:"deleteTableCol",factory:function(){return new vi}},{key:"tableHeader",factory:function(){return new hi}},{key:"tableFullWidth",factory:function(){return new gi}}],editorPlugin:function(t){var n=t.insertBreak,l=t.deleteBackward,u=t.deleteForward,c=t.normalizeNode,s=t.insertData,f=t.handleTab,d=t.selectAll,p=t;return p.insertBreak=function(){null==e.getSelectedNodeByType(p,"table")?n():p.insertText("\n")},p.deleteBackward=function(t){if(!cr(p)){var e=p.selection;if(e){var n=r.before(p,e);if(n){var o=sr(p,n),i=sr(p,e);if(o&&!i)return}}l(t)}},p.handleTab=function(){var n;if(e.getSelectedNodeByType(p,"table")){var i=r.above(t);e.checkNodeType(i[0],"table-cell")&&o.select(t,i[1]);var a=r.next(t);if(a)a[0]&&a[0].text&&(a=null!==(n=r.above(t,{at:a[1]}))&&void 0!==n?n:a),o.select(t,a[1]);else{var l=p.children||[],u=l.length;if(e.checkNodeType(l[u-1],"table")){var c=e.genEmptyParagraph();o.insertNodes(p,c,{at:[u]}),p.handleTab()}}}else f()},p.deleteForward=function(t){cr(p)||u(t)},p.normalizeNode=function(t){var n=ur(t,2),r=n[0],i=n[1];if("table"!==e.getNodeType(r))return c([r,i]);if(e.isLastNode(p,r)){var a=e.genEmptyParagraph();o.insertNodes(p,a,{at:[i[0]+1]})}},p.insertData=function(t){if(null!=e.getSelectedNodeByType(p,"table")){var n=t.getData("text/plain");"\n"===n||/<img[^>]+>/.test(t.getData("text/html"))?s(t):r.insertText(p,n)}else s(t)},p.selectAll=function(){var t=p.selection;if(null!=t){var n=e.getSelectedNodeByType(p,"table-cell");if(null!=n){var o=t.anchor,l=t.focus;if(i.equals(o.path.slice(0,3),l.path.slice(0,3)))if(0!==a.string(n).length){var u=e.findPath(p,n),c={anchor:r.start(p,u),focus:r.end(p,u)};p.select(c)}else d();else d()}else d()}else d()},p}};export{yi as default};
//# sourceMappingURL=index.esm.js.map
